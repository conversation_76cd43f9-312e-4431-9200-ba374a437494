server {
    listen       5000;
    server_name  localhost;

    # Dynamic redirect from www to non-www
    if ($${no_value}http_host ~* ^www\.(.*)) {
        return 301 https://$1$request_uri;
    }

    location / {
        auth_basic "Restricted";
        auth_basic_user_file /etc/nginx/.htpasswd;
        resolver ******* valid=60s;
        proxy_pass          http://localhost:5050;

        proxy_buffering        on;
        proxy_cache_valid      200  10m;
        proxy_cache_use_stale  error timeout invalid_header updating
                               http_500 http_502 http_503 http_504;

        client_max_body_size       ${NGINX_VAR_CLIENT_MAX_BODY_SIZE};       # 10m
        client_body_buffer_size    ${NGINX_VAR_CLIENT_BODY_BUFFER_SIZE};    # 128k

        client_body_temp_path      ${NGINX_VAR_CLIENT_BODY_TEMP_PATH};

        proxy_connect_timeout      ${NGINX_VAR_PROXY_CONNECT_TIMEOUT};      # 90
        proxy_send_timeout         ${NGINX_VAR_PROXY_SEND_TIMEOUT};         # 90
        proxy_read_timeout         ${NGINX_VAR_PROXY_READ_TIMEOUT};         # 90

        proxy_buffer_size          ${NGINX_VAR_PROXY_BUFFER_SIZE};          # 4k
        proxy_buffers              ${NGINX_VAR_PROXY_BUFFERS};              # 4 32k
        proxy_busy_buffers_size    ${NGINX_VAR_PROXY_BUSY_BUFFERS_SIZE};    # 64k
        proxy_temp_file_write_size ${NGINX_VAR_PROXY_TEMP_FILE_WRITE_SIZE}; # 64k

        proxy_temp_path            ${NGINX_VAR_PROXY_TEMP_PATH};
    }

    location /api/ {
        auth_basic off;
        proxy_pass          http://localhost:5050;
    }

    location /sitemap.xml.gz {
        auth_basic off;
        resolver               ******* valid=60s;
        proxy_pass             ${NGINX_VAR_SITEMAP_BASE_URL}/sitemap.xml.gz;
        proxy_buffering        on;
        proxy_cache_valid      200  10m;
        proxy_cache_use_stale  error timeout invalid_header updating
                              http_500 http_502 http_503 http_504;
    }

    location /media {
        auth_basic off;
        return 301 https://$${no_value}http_host/media/;
    }

    location ~ ^/media/posts/([0-9]+)/$ {
        rewrite ^/media/posts/([0-9]+)/$ https://ricokatsu.com/media/posts/$1 permanent;
    }

    # location ~ ^/media/posts/([0-9]+)/feed$ {
    #     return 301 https://ricokatsu.com/media/posts/$1;
    # }

    location /media/ {
        # TODO: remove basic auth and $CMS_HTPASSWD variable if not use.
        auth_basic "Restricted";
        auth_basic_user_file /etc/nginx/.htpasswd;
        resolver ******* valid=60s;
        #proxy_set_header X-Forwarded-Token ${NGINX_VAR_CMS_X_FORWARDED_TOKEN};
        proxy_pass ${NGINX_VAR_CMS_BASE_URL};
        proxy_redirect off;
        
        proxy_set_header Host $${no_value}http_host;
        proxy_set_header X-Forwarded-Host $${no_value}http_host;
        proxy_set_header X-Real-IP $${no_value}remote_addr;
        proxy_set_header X-Forwarded-For $${no_value}proxy_add_x_forwarded_for;

        client_max_body_size       ${NGINX_VAR_CLIENT_MAX_BODY_SIZE};       # 10m
        client_body_buffer_size    ${NGINX_VAR_CLIENT_BODY_BUFFER_SIZE};    # 128k

        client_body_temp_path      ${NGINX_VAR_CLIENT_BODY_TEMP_PATH};

        proxy_connect_timeout      ${NGINX_VAR_PROXY_CONNECT_TIMEOUT};      # 90
        proxy_send_timeout         ${NGINX_VAR_PROXY_SEND_TIMEOUT};         # 90
        proxy_read_timeout         ${NGINX_VAR_PROXY_READ_TIMEOUT};         # 90

        proxy_buffer_size          ${NGINX_VAR_PROXY_BUFFER_SIZE};          # 4k
        proxy_buffers              ${NGINX_VAR_PROXY_BUFFERS};              # 4 32k
        proxy_busy_buffers_size    ${NGINX_VAR_PROXY_BUSY_BUFFERS_SIZE};    # 64k
        proxy_temp_file_write_size ${NGINX_VAR_PROXY_TEMP_FILE_WRITE_SIZE}; # 64k

        proxy_temp_path            ${NGINX_VAR_PROXY_TEMP_PATH};
    }

    location /media/wp-admin {
        # Use regex substitution to replace 'web.' in $http_host for dev env
        set $${no_value}temp_host $${no_value}http_host;
        if ($${no_value}temp_host ~* ^web\.(.+)) {
            set $${no_value}temp_host $1;
        }
        set $${no_value}wp_host "media.$${no_value}temp_host";

        return 301 https://$${no_value}wp_host/wp-admin;
    }

    location /health {
        auth_basic off;
        proxy_pass          http://localhost:5050;
    }

    location /guide/ {
        proxy_pass https://${NGINX_VAR_GUIDE_HOST}/;
        proxy_set_header Host ${NGINX_VAR_GUIDE_HOST};
        proxy_set_header X-Forwarded-Host $${no_value}http_host;
        proxy_set_header X-Real-IP $${no_value}remote_addr;
        proxy_set_header X-Forwarded-For $${no_value}proxy_add_x_forwarded_for;
    }

    location = /guide {
        return 301 https://$${no_value}http_host/guide/;
    }

    # access_log    /var/log/nginx/access.log upstreaminfo;
    client_header_timeout 60;
    client_body_timeout   60;
    keepalive_timeout     60;

    ##
    # Compression
    ##
    gzip  on;
    # https://kubernetes.github.io/ingress-nginx/user-guide/nginx-configuration/configmap/#gzip-types
    gzip_types application/atom+xml application/javascript application/x-javascript application/json application/rss+xml application/vnd.ms-fontobject application/x-font-ttf application/x-web-app-manifest+json application/xhtml+xml application/xml font/opentype image/svg+xml image/x-icon text/css text/plain text/x-component;
    # https://docs.nginx.com/nginx/admin-guide/web-server/compression/
    gzip_proxied any;
    # https://kubernetes.github.io/ingress-nginx/user-guide/nginx-configuration/configmap/#gzip-min-length
    gzip_min_length 256;
}
[supervisord]
nodaemon=true
loglevel=error
user=root
# logfile=/dev/null
# logfile_maxbytes=0

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
startretries=5
numprocs=1
startsecs=0
process_name=%(program_name)s_%(process_num)02d
stderr_logfile=/var/log/%(program_name)s_stderr.log
stderr_logfile_maxbytes=10MB
stdout_logfile=/var/log/%(program_name)s_stdout.log
stdout_logfile_maxbytes=10MB

[program:app]
command=node server.js
user=nextjs
autostart=true
autorestart=true
startretries=5
numprocs=1
startsecs=0
redirect_stderr=true
stdout_logfile=/dev/fd/1
stdout_logfile_maxbytes=0
priority=1
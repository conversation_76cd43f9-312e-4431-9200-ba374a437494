# Guest Counselor Booking Page Implementation Guide

## Overview

The Guest Counselor Booking Page (`/guest/counselor-booking/[counselorId]`) allows unregistered users to book counseling sessions with selected counselors. This page handles the complete booking flow for guest users, including consultation form validation, payment method selection, and session scheduling.

## File Structure

```
src/pages/guest/counselor-booking/[counselorId]/
├── index.page.tsx      # Main page component
├── schema.ts           # Form validation schema
└── styles.ts           # Page styling definitions
```

## URL Structure

```
/guest/counselor-booking/{counselorId}?param1=value1&param2=value2
```

### Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `counselorId` | string | Target counselor's unique identifier | `683ea71623b66b00124cad5c` |
| `duration` | number | Session duration in minutes | `60` |
| `meetingType` | string | Type of meeting (`SINGLE` or `PARTNER`) | `SINGLE` |
| `startSlot` | string | ISO timestamp for session start time | `2025-06-15T23:00:00.000Z` |
| `isFilterCounselor` | boolean | Flag indicating counselor filtering | `true` |
| `date` | string | Session date | `2025-06-11T15:00:00.000Z` |
| `tryInitDataLS` | boolean | Flag to initialize from localStorage | `true` |
| `isFailCallback` | boolean | Flag for failed authentication callback | `true` |

## Page Features

### 1. Counselor Information Display
- **Profile Section**: Shows counselor's avatar, name, katakana name, and consultation fields
- **Consultation Fields**: Displays expertise areas as chips
- **Responsive Layout**: Adapts to different screen sizes

### 2. Meeting Configuration
- **Menu Selection**: Dropdown for different consultation menus
- **Date/Time Selection**: Calendar component for scheduling
- **Meeting Type**: Single or partner session options
- **Duration**: Automatically set based on selected menu

### 3. Payment Integration
- **Card Management**: Add, select, and edit payment cards
- **Coupon System**: Apply and manage discount coupons
- **Price Calculation**: Real-time price updates with discounts
- **3D Secure**: Handles secure payment authentication

### 4. Form Validation
- **Real-time Validation**: Immediate feedback on form inputs
- **Required Fields**: Enforces mandatory field completion
- **Date Validation**: Ensures future dates and valid time slots
- **Card Validation**: Validates payment method selection

## Implementation Details

### Component Architecture

```typescript
// Main component structure
const BookingPage = () => {
  // State management
  const [agree, setAgree] = useState(false);
  const [openAddCard, setOpenAddCard] = useState<boolean | ICardsList | string>();
  const [selectedCoupon, setSelectedCoupon] = useState<ICoupon | null>(null);
  const [openConfirmMeeting, setOpenConfirmMeeting] = useState(false);
  
  // Form handling
  const { reset, control, handleSubmit, getValues, setValue, watch } =
    useHookForm<CreateCounselorMeetingValues>();
  
  // Data fetching
  const { detail: counselorDetail } = useFetchDetail<ICounselorItem>();
  const { list: menuList } = useFetchList<ICounselorConsultationMenu>();
  const { list: cardList } = useFetchList<ICardsList>();
  
  // Mutations
  const { mutateAsync: createMeetingNormal } = useMutate();
  const { mutateAsync: createMeeting3D } = useMutate();
  
  return (
    // JSX structure
  );
};
```

### Key Hooks and State Management

#### 1. Form State Management
```typescript
const { reset, control, handleSubmit, getValues, setValue, watch } =
  useHookForm<CreateCounselorMeetingValues>({
    mode: 'all',
    resolver: yupResolver(schema),
    values: {
      type: MeetingType.ONLINE,
      menuId: queryMenuId,
      meetingType: queryMeetingType,
      expectDate: queryCurrentStartSlot,
      consumerNote: undefined,
      hasCard: undefined,
      cardNumber: undefined,
    },
  });
```

#### 2. URL Parameter Processing
```typescript
const { queryMenuId, queryMeetingType } = useMemo(() => {
  let currentMenuId = '';
  if (query?.duration && query?.meetingType) {
    const currentDuration = Number(query?.duration);
    const currentMenu = formattedMenuList?.find(
      (menu) =>
        menu.meetingType === query?.meetingType &&
        typeof currentDuration === 'number' &&
        menu?.unitPrices?.[0]?.duration === currentDuration,
    );
    if (currentMenu?._id) currentMenuId = currentMenu?._id;
  } else if (formattedMenuList) {
    if (formattedMenuList?.[0]) currentMenuId = formattedMenuList?.[0]?._id;
  }
  return {
    queryMenuId: currentMenuId,
    queryMeetingType: (query?.meetingType as IMeetingType) || IMeetingType.SINGLE,
  };
}, [formattedMenuList, query?.duration, query?.meetingType]);
```

#### 3. Card Initialization Logic
```typescript
useEffect(() => {
  const shouldInitialize =
    (!cardInitialized || watch('cardNumber') === undefined) &&
    !isLoadingCards &&
    Array.isArray(list);

  if (shouldInitialize) {
    const defaultCard = list.find(card => card?.details?.default);
    if (defaultCard && typeof defaultCard.id === 'string') {
      setTimeout(() => {
        setValue('hasCard', true);
        setValue('cardNumber', defaultCard.id);
        setCardInitialized(true);
      }, 0);
    }
  }
}, [cardInitialized, isLoadingCards, list, setValue, watch]);
```

### Payment Flow

#### 1. Normal Payment (Free or Zero Amount)
```typescript
const handleCreateMeeting = (values: CreateCounselorMeetingValues) => {
  if (usedCouponPrice === 0) {
    createMeetingNormal(submitValue, {
      onSuccess: () => {
        // Redirect to success page
        Helper.setToken({
          token: webCookie?.guest_token as string,
          email: webCookie?.email as string,
          role: ROLES.CUSTOMER,
        });
        Helper.removeWebCookieByGuest();
        push('/');
      }
    });
  }
};
```

#### 2. 3D Secure Payment
```typescript
const handleCreateMeeting = (values: CreateCounselorMeetingValues) => {
  if (usedCouponPrice > 0) {
    createMeeting3D(submitValue, {
      onSuccess: (res) => {
        const { redirectUrl } = res;
        setCookie('lawyer-web-booking-url', `${window?.location?.pathname}${window?.location?.search}`);
        if (redirectUrl) {
          localStorageHelper.setItem('bookingCounselorFormVal', values);
          window.location.href = redirectUrl;
        }
      }
    });
  }
};
```

### Coupon System

#### 1. Coupon Application
```typescript
const handleCheckCoupon = (code: string) => {
  checkCoupon(
    { code, totalPrice: basicPrice },
    {
      onSuccess: (data) => {
        setOpenCouponModal(false);
        setSelectedCoupon(data);
      },
    },
  );
};
```

#### 2. Price Calculation
```typescript
const { usedCouponPrice } = CaseUtil.getCouponPrice({
  basicPrice,
  couponAmount: selectedCoupon?.couponAmount || 0,
});
```

## Server-Side Rendering (SSR)

### Prerequisites Validation
```typescript
export const getServerSideProps: GetServerSideProps = async ({ query, req, res }) => {
  try {
    const cookies = Helper.getWebCookieByGuest(req, res);
    const { otpId } = cookies;
    
    const { data: consultation } = await api.get(
      apiQuery.consultationDetailByGuest(otpId).apiUrl
    );
    
    const { backgroundOfDivorce, marriageInformation, consumer } = consultation;
    const isValidProfile =
      consumer?.isCompletedProfile &&
      consumer?.phone &&
      backgroundOfDivorce &&
      marriageInformation;
    
    if (!isValidProfile) {
      return {
        redirect: {
          destination: `/guest/consultation-form?counselorId=${counselorId}`,
          permanent: false,
        },
      };
    }
    
    return { props: { dehydratedState: dehydrate(queryClient) } };
  } catch (error) {
    return { props: { dehydratedState: null } };
  }
};
```

## Form Validation Schema

```typescript
const schema = object({
  type: mixed<MeetingType>().oneOf(Object.values(MeetingType)).required(),
  meetingType: mixed<IMeetingType>().oneOf(Object.values(IMeetingType)),
  menuId: string().required(),
  expectDate: string()
    .nullable()
    .required()
    .test('validDay', t('validation.invalidField'), (value) => {
      if (!dayjs(value).isSameOrAfter(dayjs(), 'minute')) {
        return false;
      }
      if (value && !['00', '30'].includes(dayjs(value).format('mm'))) {
        return false;
      }
      return true;
    }),
  consumerNote: string().max(500, t('validation.maxLength', { number: 500 })),
  hasCard: boolean(),
  cardNumber: string().when(['type', 'hasCard'], {
    is: (type: MeetingType, hasCard: boolean) =>
      type === MeetingType.ONLINE && hasCard,
    then: string().required(),
  }),
});
```

## Error Handling

### 1. Authentication Failure
```typescript
useEffect(() => {
  if (isFailCallback) {
    setConfirmModal({
      icon: 'error',
      content: '認証コードの有効期限が切れています。認証コードを再送信してください。',
      confirmText: t('global.ok'),
      hideCancelButton: true,
      onConfirm: () => {
        Helper.removeWebCookieByGuest();
        // Rebuild URL with clean parameters
        const params = new URLSearchParams();
        if (counselorId) params.append('counselorId', String(counselorId));
        // ... other parameter filtering
        push(`/guest/consultation-form${queryString ? `?${queryString}` : ''}`);
      },
    });
  }
}, [isFailCallback, /* other deps */]);
```

### 2. Slot Conflict Handling
```typescript
onError: (error) => {
  if (get(error, 'code') === 'MEETNG_HAS_SLOT_IS_CLOSED') {
    setValue('expectDate', '');
    queryClient.refetchQueries({
      queryKey: ['currentUser', 'consumer', 'calendars'],
    });
  }
}
```

### 3. Missing Card Holder Name
```typescript
const hasMissingHolderNameCard = useMemo(
  () =>
    list.find((card) => card.id === watch('cardNumber')) &&
    !list.find((card) => card.id === watch('cardNumber'))?.details.cardholderName,
  [list, watch('cardNumber')],
);

{hasMissingHolderNameCard && (
  <Alert severity="warning">
    {t('otherMessage.OM02')}
  </Alert>
)}
```

## Responsive Design

### Breakpoints
- **xs**: Mobile (0-600px)
- **tablet**: Tablet (600-960px)
- **sl**: Small laptop (960px+)
- **desktop**: Desktop (1200px+)

### Layout Adaptations
```typescript
const styles = {
  container: {
    pt: 2,
    pb: { xs: 0, tablet: '64px' },
  },
  containerForm: {
    mt: { xs: 2, tablet: 4 },
    display: { sl: 'flex' },
  },
  boxCol: {
    p: 4,
    borderRadius: { xs: 0, tablet: 2 },
    bgcolor: 'white',
    mb: 2,
  },
};
```

## Integration Points

### 1. Component Dependencies
- **CounselorCreateMeeting**: Main form component
- **ConfirmMeetingModal**: Booking confirmation dialog
- **AddCardModal**: Payment card management
- **CouponModal**: Coupon selection interface

### 2. API Endpoints
- `consultationQuery.listByGuest()`: Fetch consultation menus
- `providerQuery.providerDetailByGuest()`: Get counselor details
- `cardQuery.cardsListByGuest()`: Retrieve payment cards
- `caseQuery.counselorCreateMeetingByGuest()`: Create normal booking
- `caseQuery.counselorCreateMeeting3DsByGuest()`: Create 3D secure booking

### 3. State Management
- **Global State**: Modal controls, authentication state
- **Local State**: Form data, UI controls, loading states
- **Query State**: Server data caching via React Query

## Security Considerations

### 1. Guest Authentication
- OTP-based verification system
- Temporary guest tokens
- Session validation before booking

### 2. Payment Security
- 3D Secure authentication for payments
- Secure card data handling
- PCI compliance considerations

### 3. Data Validation
- Server-side validation backup
- Input sanitization
- XSS protection

## Performance Optimizations

### 1. Data Prefetching
```typescript
// SSR prefetching
await queryClient.prefetchQuery({
  queryKey: ['consultation', otpId],
  queryFn: () => consultation,
});
```

### 2. Memoization
```typescript
const formattedMenuList = useMemo(() => {
  return menuList.map((menu) => ({
    ...menu,
    value: `${menu.unitPrices[0].duration}分 - ${menu.title}`,
  }));
}, [menuList]);
```

### 3. Conditional Rendering
```typescript
{counselorDetail && (
  <Box sx={styles.boxCol}>
    {/* Counselor profile content */}
  </Box>
)}
```

## Testing Considerations

### 1. Unit Testing
- Form validation logic
- Price calculation functions
- URL parameter processing
- Error handling scenarios

### 2. Integration Testing
- Payment flow end-to-end
- Coupon application workflow
- Card management operations
- Session booking completion

### 3. Accessibility Testing
- Keyboard navigation
- Screen reader compatibility
- Color contrast validation
- Focus management

## Common Issues and Solutions

### 1. Card Initialization Problems
**Issue**: Cards not properly selected on page load
**Solution**: Enhanced card initialization logic with proper timing

### 2. URL Parameter Handling
**Issue**: Undefined values in URL parameters
**Solution**: Parameter filtering before URL construction

### 3. Timezone Conversion
**Issue**: Incorrect time display for scheduled sessions
**Solution**: Proper UTC to JST conversion using dayjs

### 4. Payment Flow Interruption
**Issue**: Users lose progress during 3D Secure authentication
**Solution**: localStorage backup and restoration mechanisms

## Future Enhancements

### 1. Progressive Web App (PWA)
- Offline form data persistence
- Push notification for booking confirmations
- App-like user experience

### 2. Real-time Availability
- WebSocket integration for live slot updates
- Optimistic UI updates
- Conflict prevention mechanisms

### 3. Enhanced Analytics
- User journey tracking
- Conversion rate optimization
- A/B testing framework integration

### 4. Accessibility Improvements
- Voice navigation support
- High contrast themes
- Internationalization support

---

*Last updated: December 2024*
*Version: 1.0.0* 
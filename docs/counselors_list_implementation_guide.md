# Counselors List Implementation Guide

## Overview

The counselors list page (`/counselors`) is a comprehensive listing and filtering system for displaying available counselors in the lawyer-web application. It supports both basic listing and advanced filtering with query parameters for specific appointment criteria.

## URL Structure

```
/counselors?meetingType=SINGLE&duration=60&startSlot=2025-06-12T00%3A00%3A00.000Z&isFilterCounselor=true&date=2025-06-09T15%3A00%3A00.000Z
```

## Query Parameters

The counselors list page accepts the following query parameters:

| Parameter | Type | Description | Example Values |
|-----------|------|-------------|----------------|
| `meetingType` | string | Type of counseling session | `SINGLE`, `PARTNER` |
| `duration` | number | Session duration in minutes | `30`, `60`, `90`, `120` |
| `startSlot` | string | ISO date string for appointment start time | `2025-06-12T00%3A00%3A00.000Z` |
| `isFilterCounselor` | boolean | Enable filtering mode | `true`, `false` |
| `date` | string | Date for filtering | `2025-06-09T15%3A00%3A00.000Z` |
| `consultationField` | string[] | Array of consultation specialties | Various consultation themes |
| `prefecture` | string[] | Array of prefecture IDs | For lawyer filtering only |

## Key Components

### 1. Main Entry Point: `src/pages/counselors/index.page.tsx`

This is the main page component that:
- Renders the `ProviderList` component
- Handles server-side rendering with prefetched data
- Sets up the layout with `Layout` component

```typescript
const CounselorListPage = () => {
  return <ProviderList providerType={ProviderType.COUNSELOR} />;
};
```

### 2. Core Logic: `src/shared/provider/providers/index.tsx`

The `ProviderList` component handles:
- Query parameter processing
- Conditional data fetching (filtered vs. unfiltered)
- Filter UI rendering
- Counselor list display

#### Key Features:

**Two Modes of Operation:**
1. **Standard Mode** (`isFilterCounselor=false`): Shows all counselors with basic filters
2. **Filter Mode** (`isFilterCounselor=true`): Shows filtered counselors based on appointment criteria

**Data Fetching:**
- Standard mode: Uses `providerQuery.providerList(providerType)` 
- Filter mode: Uses `providerQuery.getCalendarSlotList` with availability filtering

### 3. Meeting Types

Defined in `src/models/consultation/interface.ts`:

```typescript
export enum IMeetingType {
  SINGLE = 'SINGLE',
  PARTNER = 'PARTNER',
}

export enum IMeetingTypeLabel {
  SINGLE = '個人カウンセリング',
  PARTNER = '夫婦カウンセリング',
}
```

### 4. Duration Options

Defined in `src/utils/constants.tsx`:

```typescript
export const LIST_DURATION_TEXT = {
  30: 'クイックに相談したい（30分）',
  60: 'クイックに相談したい（60分）',
  90: 'クイックに相談したい（90分）',
  120: 'クイックに相談したい（120分）',
};
```

## Implementation Details

### Filter Mode Behavior

When `isFilterCounselor=true`, the page:

1. **Displays Filter Summary**: Shows selected criteria including:
   - Consultation date (from `startSlot`)
   - Menu duration (from `duration`)
   - Consultation type (from `meetingType`)
   - Consultation time range
   - Consultation themes (from `consultationField`)

2. **Filters Counselors**: Only shows counselors who:
   - Have availability for the specified `startSlot`
   - Offer menus matching the `meetingType`
   - Have menus with the specified `duration`
   - Specialize in the selected `consultationField` areas

3. **Custom Data Processing**:
```typescript
const listCounselor = isFilterCounselor
  ? calendarList.map((item) => {
      return {
        ...item.providerInfo,
        menus: item.menus.filter(
          (menu) =>
            menu.meetingType === query.meetingType &&
            menu.unitPrices.some(
              (unit) => unit.duration === Number(query.duration),
            ),
        ),
      };
    })
  : list;
```

### API Endpoints

1. **Standard List**: `/account/consumer/providers`
2. **Filtered List**: `/sharing/common/calendars/date/slot`

### Navigation Flow

The counselors list is typically accessed from:
1. **Filter Page**: `/counselors/filter` (CounselorFilterPage)
2. **Direct Navigation**: With pre-populated query parameters

### Filter Page Integration

The filter page (`src/components/Counselor/CounselorFilterPage/CounselorFilterPage.tsx`) allows users to:
- Select consultation date and time
- Choose meeting type (SINGLE/PARTNER)
- Set duration (with dynamic options based on meeting type)
- Pick consultation fields
- Generate filtered results that redirect to the counselors list

Key validation rules:
- For `PARTNER` (夫婦カウンセリング): Only 60, 90, 120 minute options
- For `SINGLE` (個人カウンセリング): All duration options (30, 60, 90, 120)

### Date and Time Handling

- All dates use ISO format with timezone conversion to 'Asia/Tokyo'
- Duration calculations use dayjs for time arithmetic
- Availability filtering considers current time vs. appointment time

## UI Features

### Filter Mode Display

When filtering is active, the page shows:
- Total count of matching counselors
- Filter button to modify criteria
- Detailed filter summary with icons:
  - 📅 Consultation date
  - 📝 Menu duration
  - 👥 Consultation type  
  - 🕐 Consultation time range
  - 📋 Consultation themes

### Responsive Design

- Mobile-first approach with responsive breakpoints
- Tablet and desktop optimizations
- Grid layout for filter information

## Error Handling

- Invalid duration for meeting type triggers toast notifications
- Empty results show appropriate messaging
- Loading states during data fetching

## Related Components

- `CounselorCreateMeeting`: For booking appointments
- `BookingCounselorMenuList`: Menu display component
- `TimeTableField`: Time selection component
- `ProviderItem`: Individual counselor card display

This implementation provides a comprehensive filtering and listing system that integrates tightly with the appointment booking workflow, ensuring users can find and book appropriate counseling sessions based on their specific needs. 
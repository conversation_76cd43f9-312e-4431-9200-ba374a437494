# Counselor Case Create Page - Menu Data Loading

This document explains how menu items are loaded and managed on the `counselor/my-page/cases/[caseId]/create` page.

## Overview

The counselor case creation page (`/src/pages/counselor/my-page/cases/[caseId]/create/index.page.tsx`) allows counselors to create meetings for cases by selecting from their available consultation menus. The menu loading system handles fetching, formatting, and displaying consultation menus with intelligent default selection.

## Data Flow Architecture

### 1. Initial Data Fetching

```typescript
// Main component setup
const { data: currentUser } = useFetchUser<CounselorData>({ enabled: true });
const counselorId = currentUser?._id;

// ⚠️ ISSUE: This incorrectly passes counselor's ID as checkCustomerId
// Should be role-based logic to determine when to include the parameter
const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
  ...consultationQuery.list(counselorId as string, currentUser?._id),
});
```

**Correct Implementation Should Be:**
```typescript
// Role-based API call
const userRole = Helper.getUserRole();
const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
  ...consultationQuery.list(
    counselorId as string, 
    userRole === ROLES.CUSTOMER ? currentUser?._id : undefined
  ),
});
```

**API Endpoint:**
```typescript
// From: src/models/consultation/query.ts
list: (lawyerId: string, userId?: string) => ({
  queryKey: ['public', 'lawyers', lawyerId, 'consultations', userId],
  apiUrl: `/account/consumer/providers/${lawyerId}/consultation-menus${
    userId ? `?checkCustomerId=${userId}` : ''
  }`,
}),
```

**⚠️ API Logic Issue:**
The current implementation has a logic flaw. In the counselor case creation context, the `currentUser` is a **COUNSELOR**, not a **CUSTOMER**. The `checkCustomerId` parameter should only be added when the logged-in user role is `CUSTOMER` (for permission filtering), but counselors accessing their own menus should not include this parameter.

### 2. Data Structure

Each menu item follows the `ICounselorConsultationMenu` interface:

```typescript
export interface ICounselorConsultationMenu {
  _id: string;
  title: string;
  content?: string;
  createdAt: string;
  updatedAt: string;
  unitPrices: [{ duration: number; price: number }];
  meetingType?: IMeetingType;
}
```

### 3. Data Formatting

Raw menu data is formatted for UI display:

```typescript
const formattedMenuList = menuList.map((menu) => ({
  ...menu,
  value: `${menu.unitPrices[0].duration}分 - ${menu.title}`,
}));
```

## Default Menu Selection Logic

### Priority-Based Selection

The system uses `Helper.getDefaultMenuId()` to intelligently select a default menu:

```typescript
useEffect(() => {
  if (menuList.length > 0) {
    // Use priority-based default menu selection
    const defaultMenuId = Helper.getDefaultMenuId(menuList);
    if (defaultMenuId) {
      reset({ menuId: defaultMenuId });
    }
  }
}, [meetingId, menuList, reset]);
```

**Selection Priority Order:**
1. **SINGLE & 60 minutes** (highest priority)
2. **SINGLE & 90 minutes**
3. **SINGLE & 120 minutes**
4. **PARTNER & 90 minutes**
5. **PARTNER & 120 minutes**
6. **First menu in list** (fallback)

### Implementation Details

```typescript
// From: src/utils/helpers.ts
getDefaultMenuId(
  menuList: Array<{
    _id: string;
    meetingType?: string;
    unitPrices: Array<{ duration: number; price: number }>;
  }>,
): string | undefined {
  if (!menuList || menuList.length === 0) {
    return undefined;
  }

  // Priority 1: SINGLE & 60 minutes
  const singleMenu60 = menuList.find(
    (menu) =>
      menu.meetingType === 'SINGLE' && menu.unitPrices?.[0]?.duration === 60,
  );
  if (singleMenu60) return singleMenu60._id;

  // Continue through priority levels...
  // Fallback to first menu
  return menuList[0]?._id;
}
```

## Menu Rendering System

### 1. OutlineMenu Component

The `OutlineMenu` utility transforms menu data into radio button options:

```typescript
// From: src/shared/provider/my-page/outline-menu/index.tsx
const OutlineMenu = <T extends Props>(data: T[]): IDataOfRadio[] => {
  const formattedMenuListRadioList = Helper.convertArrayToOptions(data, [
    { key: '_id' },
    { key: 'value' },
    {
      alias: 'description',
      getValue: (value) => value?.meetingType as string,
    },
    {
      alias: 'duration',
      getValue: (value) => value?.unitPrices?.[0]?.duration as unknown as string,
    },
    {
      alias: 'price', 
      getValue: (value) => value?.unitPrices?.[0]?.price as unknown as string,
    },
    { key: 'content' },
    { key: 'meetingType' },
  ]);
  
  // Format for responsive display with meeting type, price, duration
  return formattedMenuListRadioList?.map((item) => ({
    ...item,
    description: /* Meeting type display logic */,
    endAdornment: /* Price, duration, and info button */,
  }));
};
```

### 2. Radio Button Implementation

```typescript
<Radio
  type="outline"
  data={renderDataOfMenu}
  control={control}
  name="menuId"
  required
  columns={1}
  row={false}
  sx={{
    marginTop: 0,
  }}
/>
```

## Responsive Display Features

### Desktop View
- Shows meeting type, duration, price, and info button inline
- Displays as: `相談タイプ：[Type] | [Duration]分 | [Price]円 | ℹ️`

### Mobile View  
- Stacked layout with meeting type and pricing on separate lines
- Price and duration highlighted in orange (`#FFA700`)

### Info Modal
Each menu item includes an info button that displays detailed content:

```typescript
onClick={() => {
  setConfirmModal({
    icon: 'info',
    onConfirm: () => {},
    content: item?.content ? item?.content : '内容がありません',
    hideCancelButton: true,
    contentAlign: 'center',
    confirmText: '戻る',
  });
}}
```

## Form Integration

### Watch System
The form watches for menu selection changes to update related fields:

```typescript
const watchMenuId = useWatch({ name: 'menuId', control });
const watchMenu = formattedMenuList.find((menu) => menu._id === watchMenuId);
const basicPrice = watchMenu?.unitPrices[0].price || 0;

useEffect(() => {
  if (watchMenuId) {
    setValue('finalizedDate', '');
    setValue('discount', '' as unknown as number);
  }
}, [setValue, watchMenuId]);
```

### Payment Calculation
Menu selection automatically updates pricing calculations:

```typescript
const handleSubmitForm = (values: CreateMeetingValues) => {
  const payload: ICounselorCreateMeeting = {
    // ... other fields
    paymentInfo: {
      discount: discount || 0,
      duration: watchMenu?.unitPrices[0].duration || 0,
      basicPrice,
    },
    consultationMenu: {
      title: watchMenu?.title || '',
      unitPrices: [
        {
          price: watchMenu?.unitPrices[0].price || 0,
          duration: watchMenu?.unitPrices[0].duration || 0,
        },
      ],
      content: watchMenu?.value,
      meetingType: watchMenu?.meetingType,
    },
  };
};
```

## Error Handling & Loading States

### Loading Management
- `useFetchList` provides loading states for UI feedback
- Menu selection is disabled during loading
- Default selection waits for data to be available

### Error Scenarios
- Empty menu list: No default selection, form validation prevents submission  
- API errors: Handled by the `useFetchList` hook with error states
- Invalid selections: Form validation ensures valid menu ID

## Security & Permissions

### Authentication Requirements
- Requires authenticated counselor user
- **ISSUE**: Menu data incorrectly includes counselor ID as `checkCustomerId` parameter
- The `checkCustomerId` parameter should only be added when `userRole === ROLES.CUSTOMER`
- Server-side validation ensures counselor can only access their own menus

### Role-Based Parameter Logic
```typescript
// Correct usage pattern:
const userRole = Helper.getUserRole();
const checkCustomerId = userRole === ROLES.CUSTOMER ? currentUser?._id : undefined;

// API calls should be:
// For CUSTOMER: `/account/consumer/providers/${counselorId}/consultation-menus?checkCustomerId=${customerId}`
// For COUNSELOR: `/account/consumer/providers/${counselorId}/consultation-menus` (no checkCustomerId)
```

### Data Validation
- Form validation ensures required menu selection
- Menu ID validation against available options
- Price and duration constraints enforced

## Performance Optimizations

### Query Caching
- Uses React Query for caching menu data
- Query key includes counselor ID and customer ID for proper cache invalidation
- Automatic refetch on authentication state changes

### Cleanup
```typescript
useEffect(() => {
  return () => {
    queryClient.removeQueries({ queryKey: ['currentUser', 'calendars'] });
  };
}, []);
```

## Integration Points

### Related Components
- **TimeTableField**: Uses menu duration for calendar slot selection
- **PaymentTooltipButton**: Displays pricing information and calculations
- **NumberField**: Discount input with menu price validation

### API Dependencies
- `GET /account/consumer/providers/{counselorId}/consultation-menus`
- Authentication token required
- **Conditional parameter**: `checkCustomerId` should only be included for `CUSTOMER` role users

**API Endpoint Variations:**
```typescript
// For CUSTOMER users (booking flow)
GET /account/consumer/providers/{counselorId}/consultation-menus?checkCustomerId={customerId}

// For COUNSELOR users (case management)  
GET /account/consumer/providers/{counselorId}/consultation-menus
```

### State Management
- Form state managed by `react-hook-form`
- Global modal state for info displays
- Query state managed by React Query

This architecture provides a robust, user-friendly menu selection system with intelligent defaults, responsive design, and comprehensive error handling. 
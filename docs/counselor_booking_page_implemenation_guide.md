# Counselor Booking Page Implementation Guide

## Overview

The counselor booking page (`/customer/counselor-booking/[counselorId]`) is a comprehensive React-based booking system that allows customers to schedule counseling sessions with specific counselors. The page implements a complete booking flow with form validation, payment integration, 3D Secure authentication, and real-time calendar availability.

## File Structure

```
src/pages/customer/counselor-booking/[counselorId]/
├── index.page.tsx          # Main page component
├── schema.ts               # Form validation schema (Yup)
└── styles.ts               # Material-UI styling definitions

src/components/
├── Case/CounselorCreateMeeting/     # Main booking form component
├── Guest/Calendar/                  # Guest calendar component
├── Case/ConfirmMeetingModal/        # Booking confirmation modal
├── Case/CouponModal/                # Coupon management modal
└── Customer/AddCardModal/           # Payment card management modal
```

## Core Technologies

- **Frontend Framework**: Next.js with TypeScript
- **UI Library**: Material-UI (MUI)
- **Form Handling**: React Hook Form with Yup validation
- **State Management**: React Query for server state, local state with hooks
- **Payment Processing**: 3D Secure authentication support
- **Internationalization**: i18next for multi-language support

## Route Configuration

### Dynamic Route
- **Path**: `/customer/counselor-booking/[counselorId]`
- **Parameters**:
  - `counselorId`: String - Unique identifier for the counselor
  - `duration`: Number (optional) - Session duration in minutes
  - `meetingType`: String (optional) - Type of meeting
  - `startSlot`: String (optional) - ISO timestamp for appointment slot
  - `menuId`: String (optional) - Selected consultation menu ID

### Server-Side Rendering (SSR)
The page implements `getServerSideProps` with authentication and form completion checks:

```typescript
export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  // Validates user authentication
  // Checks consultation form completion
  // Redirects to consultation form if incomplete
  // Pre-fetches consultation data
}
```

## Component Architecture

### Main Page Component (`BookingPage`)

#### Key Features
1. **Dynamic Menu Loading**: Fetches counselor-specific consultation menus
2. **Calendar Integration**: Real-time availability checking
3. **Payment Processing**: Support for multiple payment cards and 3D Secure
4. **Coupon System**: Discount code validation and application
5. **Form Validation**: Comprehensive input validation with error handling

#### State Management
```typescript
const [agree, setAgree] = useState(false);
const [openAddCard, setOpenAddCard] = useState<boolean | ICardsList | string>();
const [openCouponModal, setOpenCouponModal] = useState(false);
const [selectedCoupon, setSelectedCoupon] = useState<ICoupon | null>(null);
const [openConfirmMeeting, setOpenConfirmMeeting] = useState(false);
```

#### Data Fetching
```typescript
// Counselor consultation menus
const { list: menuList, isLoading: isLoadingMenu } = useFetchList<ICounselorConsultationMenu>({
  ...consultationQuery.list(counselorId as string, currentUser?._id),
});

// Counselor profile details
const { detail: counselorDetail } = useFetchDetail<ICounselorItem>({
  ...providerQuery.providerDetail(counselorId as string),
});

// User's payment cards
const { list, refetch: refetchCardList } = useFetchList<ICardsList>(cardQuery.cardsList);
```

### Form Schema (`schema.ts`)

Implements comprehensive validation using Yup:

```typescript
const schema = object({
  type: mixed<MeetingType>().oneOf(Object.values(MeetingType)).required(),
  meetingType: mixed<IMeetingType>().oneOf(Object.values(IMeetingType)),
  menuId: string().required(),
  expectDate: string()
    .nullable()
    .required()
    .test('validDay', t('validation.invalidField'), (value) => {
      // Validates future dates and 30-minute intervals
    }),
  consumerNote: string().max(500),
  hasCard: boolean(),
  cardNumber: string().when(['type', 'hasCard'], {
    // Conditional validation for online meetings
  }),
});
```

## User Interface Components

### 1. Counselor Information Display
- **Avatar**: Circular profile image
- **Name Display**: Full name and katakana pronunciation
- **Specializations**: Dynamic chip list of consultation fields

### 2. Booking Form (`CounselorCreateMeeting`)
Located at `src/components/Case/CounselorCreateMeeting/index.tsx`

#### Form Fields
```typescript
// Meeting method selection (online/offline)
<Radio name="type" data={MEETING_TYPE_TEXT} />

// Meeting type (single/couple counseling)
<Radio name="meetingType" data={MEETING_TYPE_OPTIONS} />

// Consultation menu selection
<Radio name="menuId" data={listRadio} type="outline" />

// Date and time picker with real-time availability
<TimeTableField name="expectDate" duration={duration} />

// Optional notes
<TextField name="consumerNote" multiline minRows={5} />
```

### 3. Payment Section
- **Card Selection**: Dropdown with existing payment methods
- **Add New Card**: Modal-based card addition
- **Coupon Application**: Code-based discount system
- **Price Display**: Dynamic pricing with coupon calculations

### 4. Calendar Integration
Real-time availability checking with:
- 60-minute advance booking requirement
- 30-minute time slot intervals
- Visual availability indicators
- Navigation between weeks/months

## Business Logic

### Menu Selection Logic
```typescript
const queryMenuId = useMemo(() => {
  // Priority-based menu selection
  // 1. URL parameters (duration + meetingType)
  // 2. Default menu selection using Helper.getDefaultMenuId()
}, [formattedMenuList, query?.duration, query?.meetingType]);
```

### Menu Filtering Logic

The booking page implements sophisticated menu filtering based on URL parameters and user selection in the "相談タイプ" (consultation type) field:

#### Data Flow
1. **API Fetch**: Raw menu list from counselor consultation API
2. **Formatting**: Adds display value (`${duration}分 - ${title}`)
3. **UI Filtering**: Applies filtering based on form selection and URL parameters

#### Filtering Rules

The filtering logic is applied through the `filteredMenuList` useMemo hook:

```typescript
const filteredMenuList = useMemo(() => {
  if (!watchMeetingType) return formattedMenuList;
  
  const meetingTypeParam = Array.isArray(query?.meetingType)
    ? query.meetingType[0]
    : query?.meetingType;
  const urlMeetingType = meetingTypeParam && meetingTypeParam.trim() !== ''
    ? meetingTypeParam
    : null;
  
  return formattedMenuList.filter((menu) => {
    // Always filter by meeting type
    if (menu.meetingType !== watchMeetingType) return false;

    // Apply duration filter based on URL meetingType parameter
    if (urlMeetingType === 'SINGLE' && watchMeetingType === 'SINGLE') {
      // URL has SINGLE and user selected SINGLE: apply duration filter
      return (menu.unitPrices[0]?.duration || 0) >= Number(query?.duration || 0);
    } else if (urlMeetingType === 'PARTNER' && watchMeetingType === 'PARTNER') {
      // URL has PARTNER and user selected PARTNER: apply duration filter for 90/120 only
      const urlDuration = Number(query?.duration || 0);
      if (urlDuration === 90 || urlDuration === 120) {
        const menuDuration = menu.unitPrices[0]?.duration || 0;
        return menuDuration === 90 || menuDuration === 120;
      }
      // For duration=60 or other values, no filtering
      return true;
    }

    // For all other cases, no duration filter
    return true;
  });
}, [formattedMenuList, watchMeetingType, query?.duration, query?.meetingType]);
```

#### Filtering Scenarios

**1. URL: `duration=90&meetingType=SINGLE`**
- When user selects "個人カウンセリング" (SINGLE): Shows only SINGLE menus with duration ≥ 90
- When user selects "夫婦カウンセリング" (PARTNER): Shows all PARTNER menus (no duration filtering)

**2. URL: `duration=90&meetingType=PARTNER`**
- When user selects "個人カウンセリング" (SINGLE): Shows all SINGLE menus (no duration filtering)
- When user selects "夫婦カウンセリング" (PARTNER): Shows both 90-minute AND 120-minute PARTNER menus

**3. URL: `duration=60&meetingType=PARTNER`**
- When user selects "個人カウンセリング" (SINGLE): Shows all SINGLE menus (no duration filtering)
- When user selects "夫婦カウンセリング" (PARTNER): Shows all PARTNER menus (no duration filtering)

**4. URL: `duration=60` (no meetingType)**
- When user selects either type: Shows all menus of that type (no duration filtering)

#### Key Filtering Rules Summary

- **Duration filtering only applies when URL `meetingType` matches user selection**
- **SINGLE menus**: Use greater-than-or-equal filtering (`duration >= urlDuration`)
- **PARTNER menus**: Special logic for duration=90/120 (shows both 90 and 120 minute menus)
- **PARTNER menus with duration=60**: No filtering applied
- **Cross-type selection**: No duration filtering (e.g., URL has SINGLE, user selects PARTNER)

### Price Calculation
```typescript
const { usedCouponPrice } = CaseUtil.getCouponPrice({
  basicPrice: watchMenu?.unitPrices[0].price || 0,
  couponAmount: recalculatedCouponAmount,
});
```

### Payment Processing
The system supports two payment flows:

#### 1. Standard Payment (No 3D Secure)
```typescript
createMeetingNormal(submitValue, {
  onSuccess: () => {
    // Success handling with form completion check
    // Redirects to appropriate success page
  }
});
```

#### 2. 3D Secure Payment
```typescript
createMeeting3D(submitValue, {
  onSuccess: (res) => {
    // Redirects to 3D Secure authentication
    window.location.href = res.redirectUrl;
  }
});
```

## Modal Components

### 1. Confirmation Modal (`ConfirmMeetingModal`)
- **Purpose**: Final booking confirmation before payment
- **Data Display**: Complete booking summary
- **Payment Card**: Final card selection for 3D Secure eligible cards

### 2. Coupon Modal (`CouponModal`)
- **Purpose**: Coupon code entry and validation
- **Features**: Real-time coupon validation
- **Integration**: Automatic price recalculation

### 3. Add Card Modal (`AddCardModal`)
- **Purpose**: Payment method management
- **Features**: Card addition and editing
- **Integration**: Automatic card list refresh

## Error Handling

### Form Validation Errors
- Real-time validation with React Hook Form
- Custom validation messages via i18next
- Field-specific error display

### API Error Handling
```typescript
onError: (error) => {
  if (get(error, 'code') === 'MEETNG_HAS_SLOT_IS_CLOSED') {
    setValue('expectDate', '');
    queryClient.refetchQueries({
      queryKey: ['currentUser', 'consumer', 'calendars'],
    });
  }
}
```

### User Experience Considerations
- Loading states for all async operations
- Disabled states for invalid forms
- Toast notifications for user feedback
- Responsive design for mobile and desktop

## Security Features

### Authentication
- Server-side authentication validation
- Token-based API requests
- Automatic redirects for unauthenticated users

### Payment Security
- 3D Secure authentication support
- Card holder name validation
- Secure card storage indicators

### Form Security
- Input sanitization and validation
- XSS protection through React's built-in escaping
- CSRF protection via Next.js

## Performance Optimizations

### Data Fetching
- React Query for efficient caching
- Selective query invalidation
- Background refetching for real-time updates

### Component Optimization
- useMemo for expensive calculations
- Conditional rendering for large components
- Lazy loading for modal components

### Bundle Optimization
- Dynamic imports for modals
- Tree shaking for unused dependencies
- Code splitting at the page level

## Responsive Design

### Breakpoint System
```typescript
sx={{
  fontSize: { xs: 14, tablet: 16, desktop: 18 },
  padding: { xs: 2, tablet: 4 },
  display: { xs: 'block', sl: 'flex' }
}}
```

### Mobile-First Approach
- Touch-optimized form controls
- Mobile-specific modal presentations
- Responsive grid layouts

## Integration Points

### External Services
- **Payment Gateway**: 3D Secure authentication
- **Calendar Service**: Real-time availability
- **Notification Service**: Email confirmations
- **Analytics**: Conversion tracking scripts

### Internal APIs
- **Consultation API**: Menu and pricing data
- **Calendar API**: Availability checking
- **Payment API**: Card management and processing
- **Coupon API**: Discount validation
- **User API**: Profile and consultation history

## Testing Considerations

### Unit Testing
- Form validation logic
- Price calculation functions
- Component state management

### Integration Testing
- API response handling
- Payment flow completion
- Error state management

### End-to-End Testing
- Complete booking workflow
- Payment processing
- Mobile responsiveness

## Future Enhancements

### Potential Improvements
1. **Real-time Notifications**: WebSocket integration for calendar updates
2. **Advanced Scheduling**: Recurring appointment support
3. **Enhanced Analytics**: User behavior tracking
4. **Accessibility**: WCAG 2.1 compliance improvements
5. **Performance**: Server-side caching for static data

### Scalability Considerations
- Microservice architecture preparation
- CDN integration for static assets
- Database optimization for high-volume bookings
- Caching strategies for frequently accessed data

## Conclusion

The counselor booking page represents a sophisticated booking system with comprehensive features for online counseling services. The implementation demonstrates modern React patterns, robust error handling, secure payment processing, and excellent user experience design. The modular architecture allows for easy maintenance and future enhancements while maintaining high performance and security standards. 
# Counselor Filter Page Implementation Guide

## Overview

The Counselor Filter Page (`/counselors/filter`) is a sophisticated filtering and appointment booking interface that allows users to set specific criteria for finding available counseling sessions. This page combines form filtering with a visual time selection interface to streamline the appointment booking process.

## URL Structure

```
/counselors/filter
/counselors/filter?date=2025-06-09T15%3A00%3A00.000Z&consultationField=stress&meetingType=SINGLE&duration=60
```

## File Structure

### Main Entry Point
- **File**: `src/pages/counselors/filter/index.page.tsx`
- **Purpose**: Next.js page component that wraps the filter functionality

### Core Components
- **Main Component**: `src/components/Counselor/CounselorFilterPage/CounselorFilterPage.tsx`
- **Schema**: `src/components/Counselor/CounselorFilterPage/schema.ts`
- **Styles**: `src/components/Counselor/CounselorFilterPage/styles.ts`
- **Dialog**: `src/components/Counselor/CounselorFilterPage/ConsultationFieldDialog.tsx`
- **Calendar Header**: `src/components/Counselor/CounselorFilterPage/CalendarHeader.tsx`
- **Legend**: `src/components/Counselor/CounselorFilterPage/components/TimeTableExplan.tsx`

## Key Features

### 1. Filter Form
The page provides a comprehensive filtering form with the following fields:

#### Date Selection
- **Component**: `DatePicker`
- **Range**: Today to 90 days in the future
- **Format**: "YYYY年MM月DD日 (ddd)" (Japanese date format)
- **Timezone**: Asia/Tokyo

#### Consultation Theme Selection
- **Component**: Custom dialog-based multi-select
- **Data Source**: Fetched from `/resources/consultations` API
- **UI**: Custom dropdown with `ConsultationFieldDialog`
- **Display**: Selected themes joined with " • " separator

#### Meeting Type Selection
- **Component**: `Radio` buttons
- **Options**:
  - `SINGLE`: 個人カウンセリング (Individual Counseling)
  - `PARTNER`: 夫婦カウンセリング (Couple Counseling)

#### Duration Selection
- **Component**: `Select` dropdown
- **Dynamic Options**: Changes based on meeting type
  - **Individual**: 30, 60, 90, 120 minutes
  - **Couple**: 60, 90, 120 minutes only
- **Auto-adjustment**: Automatically sets to 60 minutes if current selection becomes invalid

### 2. Visual Time Selection

#### Calendar Display
- **Component**: Custom `TimeTable` with `CalendarHeader`
- **Date Range**: Shows available dates based on filter criteria
- **Header**: Displays weekdays with color coding:
  - Red: Sunday
  - Blue: Saturday  
  - Black: Monday-Friday
- **Current Day Highlighting**: Yellow background for today's date

#### Time Slot Visualization
- **Legend**: 
  - Gray circle: No availability (空きなし)
  - Border circle: Available (空きあり)
  - Orange circle: Current booking (現在の予約)
- **Auto-scroll**: Automatically scrolls to earliest available time slot
- **Filtering**: Only shows future time slots that are marked as "OPEN"

### 3. State Management

#### Form State
```typescript
interface FilterFormValues {
  date: string;
  consultationField: string[];
  meetingType: string;
  duration: number;
}
```

#### Component State
- `filter`: Current filter parameters for API calls
- `timeSelect`: Selected time slot
- `open`: Dialog visibility for consultation field selection
- `skipSlot`: Calculated value for auto-scroll positioning

#### Query Parameter Synchronization
The component automatically syncs with URL query parameters:
- `date`: ISO date string
- `consultationField`: Array of consultation field IDs
- `meetingType`: SINGLE or PARTNER
- `duration`: Number in minutes

## API Integration

### 1. Consultation Fields API
- **Endpoint**: `/resources/consultations`
- **Parameters**: `{ providerType: 'COUNSELOR' }`
- **Usage**: Populates consultation theme options

### 2. Calendar Slots API
- **Endpoint**: `/sharing/common/calendars/date/slot`
- **Parameters**: Filter form values
- **Response**: Array of calendar slots with availability
- **Real-time**: Refetches when filter parameters change

## Navigation Flow

### 1. Filter Submission
When users submit the filter form:
```typescript
function submit(data: FilterFormValues) {
  // Format date for Tokyo timezone
  const formattedDateTime = handleFormattedDate(date || '');
  
  // Update internal state
  setFilter(prev => ({ ...prev, ...data, date: formattedDateTime }));
  
  // Navigate with query parameters
  router.push({
    pathname: '/counselors/filter',
    query: { ...data, date: formattedDateTime }
  });
}
```

### 2. Time Selection and Booking
When users select a time slot and confirm:
```typescript
async function handleSubmitTimeSelect() {
  // Validate slot availability
  const { data, isSuccess } = await refetch();
  
  // Check if selected time is still available
  const isSlotStartAvailable = slots.some(slot => 
    slot.start === start && slot.status === 'OPEN'
  );
  
  // Redirect to counselors list with booking parameters
  router.push({
    pathname: '/counselors',
    query: {
      consultationField: filter.consultationField,
      meetingType: filter.meetingType,
      duration: filter.duration,
      startSlot: dayjs(start).toISOString(),
      isFilterCounselor: true,
      date: filter.date,
    },
  });
}
```

## Error Handling & Validation

### 1. Form Validation
- **Schema**: Yup-based validation in `schema.ts`
- **Real-time**: Form validation on field changes
- **Required Fields**: All form fields are required for submission

### 2. Time Slot Validation
- **Availability Check**: Validates slot availability before booking
- **Toast Notifications**: User-friendly error messages
- **Auto-refresh**: Refetches calendar data to ensure accuracy

### 3. Business Logic Validation
- **Duration Restrictions**: Automatically adjusts duration based on meeting type
- **Future Dates Only**: Filters out past time slots
- **Working Hours**: Respects defined working time boundaries

## Responsive Design

### Mobile Optimization
- **Breakpoint**: 768px
- **Layout**: Stacked form elements with adjusted spacing
- **Time Table**: Reduced spacing and adjusted scroll behavior
- **Header**: Compact calendar header for mobile screens

### Desktop Features
- **Enhanced Spacing**: Larger padding and margins
- **Time Column**: Wider time gutter (88px vs mobile)
- **Sticky Elements**: Fixed positioning for better UX

## Key Components Deep Dive

### ToolbarBottom
Fixed bottom toolbar that appears when a time slot is selected:
- **Actions**: Cancel (Reset) and Next (Submit)
- **Positioning**: Fixed at bottom of screen
- **Responsive**: Adjusts button sizing for mobile/desktop

### ConsultationFieldDialog
Modal dialog for selecting consultation themes:
- **Multi-select**: Checkbox-based selection
- **Search**: Filtered list of available consultation fields
- **Actions**: Clear, Cancel, and Confirm buttons

### CalendarHeader  
Displays the week view header:
- **Color Coding**: Different colors for weekdays vs weekends
- **Today Highlighting**: Special styling for current date
- **Date Range**: Shows the selected filter date range

### TimeTableExplan
Legend component explaining time slot states:
- **Visual Legend**: Color-coded explanations
- **Compact**: Minimal design to conserve space
- **Responsive**: Adapts to mobile/desktop layouts

## Integration with Main Counselors List

The filter page seamlessly integrates with the main counselors list (`/counselors`) by:

1. **Query Parameters**: Passing filter criteria as URL parameters
2. **Filter Flag**: Setting `isFilterCounselor: true` to enable filtered mode
3. **Time Slot**: Including specific `startSlot` for appointment booking
4. **Breadcrumb Navigation**: Maintaining filter state for back navigation

## Performance Optimizations

### 1. Memoization
- **CalendarHeader**: Memoized to prevent unnecessary re-renders
- **Options Lists**: Computed duration options are memoized

### 2. Efficient Scrolling
- **Auto-scroll**: Calculates optimal scroll position based on earliest slot
- **Skip Calculation**: Optimizes scroll performance with pre-calculated offsets

### 3. Query Optimization
- **Stale Time**: Set to 0 for calendar data to ensure freshness
- **Conditional Fetching**: Only fetches when filter parameters change

## Accessibility Features

### 1. Keyboard Navigation
- **Form Fields**: All inputs are keyboard accessible
- **Button States**: Clear focus indicators

### 2. Screen Reader Support
- **Semantic HTML**: Proper form labels and structure
- **ARIA Labels**: Descriptive labels for complex interactions

### 3. Color Accessibility
- **High Contrast**: Time slot states use distinct visual patterns
- **Color Independence**: Visual cues don't rely solely on color

## Development Notes

### Timezone Handling
The application consistently uses Asia/Tokyo timezone for all date operations:
```typescript
function handleFormattedDate(date: string) {
  const tokyoStartOfDay = dayjs.utc(date).tz('Asia/Tokyo').startOf('day');
  return tokyoStartOfDay.utc().toISOString();
}
```

### State Synchronization
The component maintains synchronization between:
- Form state (react-hook-form)
- Filter state (local component state)
- URL parameters (Next.js router)
- API calls (React Query)

### Error Recovery
- **Graceful Degradation**: Shows appropriate messages when no slots available
- **Retry Logic**: Users can modify filters and retry
- **Validation Messages**: Clear feedback for invalid selections

This implementation provides a comprehensive, user-friendly interface for filtering and booking counseling appointments while maintaining excellent performance and accessibility standards. 
# ProviderItem Component Implementation Guide

## Overview

The `ProviderItem` component is a versatile card component that displays individual provider information in the counselors/lawyers list. It supports both lawyer and counselor types with different layouts, information displays, and interaction patterns.

## Component Location

- **Main Component**: `src/components/Provider/ProviderList/ProviderItem/index.tsx`
- **Styles**: `src/components/Provider/ProviderList/ProviderItem/styles.ts`
- **Skeleton**: `src/components/Provider/ProviderList/ProviderItem/Skeleton.tsx`

## Key Features

### 1. Dual Provider Type Support

The component automatically adapts its layout and content based on the provider type:

- **Lawyers** (`ProviderType.LAWYER`): Shows office information, business hours, location
- **Counselors** (`ProviderType.COUNSELOR`): Shows menu pricing, certifications, ratings

### 2. Responsive Design

- **Mobile**: Compact layout with stacked elements
- **Tablet+**: Expanded layout with side-by-side content
- Uses Material-UI breakpoints for consistent responsive behavior

### 3. Authentication-Aware Navigation

The component handles different user states:
- **Guest Users**: Redirected to registration or guest consultation form
- **Authenticated Users**: Direct access to consultation forms
- **Draft Users**: Special handling for email verification

## Data Interface

### Input Props

```typescript
interface ProviderItemProps {
  data: ILawyerItem | ICounselorItem;
}
```

### Provider Types

#### ILawyerItem
```typescript
interface ILawyerItem extends IProviderItem {
  office: {
    _id: string;
    fullName: string;
    katakanaName: string;
    stationToTheOffice?: string;
    timeToTheOffice?: number;
    businessHours?: string;
    address?: {
      postCode?: string;
      address1?: { _id: string; value: string };
      address2?: string;
      address3?: string;
      address4?: string;
    };
  };
  type: ProviderType.LAWYER;
  hasOnlineSupport?: boolean;
}
```

#### ICounselorItem
```typescript
interface ICounselorItem extends IProviderItem {
  nickname?: string;
  birthday?: string;
  gender?: GenderType;
  career?: string;
  type: ProviderType.COUNSELOR;
  certificate?: string[];
  numberSolutionCases?: number;
  isAvailableToday?: boolean;
  menus?: Menu[];
}
```

## Component Structure

### 1. Header Section
- **Avatar/Image**: Carousel for desktop, single image for mobile
- **Name Display**: 
  - Counselors: Nickname (if available) or full name
  - Lawyers: Full name + katakana name
- **Status Chips**: Video availability, online support, same-day availability
- **Ratings**: Only for counselors

### 2. Information Section

#### For Counselors:
- **Personal Info**: Age group, gender, career years
- **Experience**: Total counseling cases
- **Certifications**: Displayed as chips
- **Menu Pricing Table**: Up to 3 menus with overflow indicator

```typescript
const renderBasicInfo = () => {
  if (isCounselor) {
    // Shows age, gender, career, total cases
    // Displays menu pricing table with duration and prices
    // Handles menu overflow (shows "+その他X個のメニュー")
  }
  // For lawyers: shows office info, station access, business hours
};
```

#### For Lawyers:
- **Office Address**: Full postal address
- **Station Access**: Nearest station and walking time
- **Business Hours**: Operating hours

### 3. Content Section
- **Catchphrase**: Highlighted with quote icon
- **Introduction**: Truncated text with line clamping
- **Action Buttons**: "詳細を見る" (View Details) and "相談する" (Consult)

## Navigation Logic

### handleContactLawyer Function

This function implements complex routing logic based on user authentication state:

```typescript
const handleContactLawyer = () => {
  const webCookie = Helper.getWebCookie();
  const webCookieByGuest = Helper.getWebCookieByGuest();
  const role = get(webCookie, 'role');
  const isDraftUser = get(webCookieByGuest, 'isDraftUser');
  
  if (isDraftUser || !role) {
    // Guest user logic
    if (isLawyer) {
      push('/register');
    } else {
      // Counselor guest flow with query preservation
      push({
        pathname: '/guest/consultation-form',
        query: {
          counselorId: data._id,
          duration: query?.duration,
          meetingType: query?.meetingType,
          startSlot: query?.startSlot,
          isFilterCounselor: query?.isFilterCounselor,
          date: query?.date,
          isVerifyEmail: !!isDraftUser,
        },
      });
    }
  } else {
    // Authenticated user logic
    push(
      isLawyer
        ? `/customer/consultation-form?lawyerId=${data._id}`
        : {
            pathname: '/customer/consultation-form',
            query: {
              counselorId: data._id,
              duration: query?.duration,
              meetingType: query?.meetingType,
              startSlot: query?.startSlot,
            },
          },
    );
  }
};
```

### Query Parameter Preservation

The component intelligently preserves filter query parameters during navigation:
- `duration`: Session duration
- `meetingType`: SINGLE or PARTNER
- `startSlot`: Selected time slot
- `isFilterCounselor`: Filter mode flag
- `date`: Selected date

## Responsive Behavior

### Mobile Layout (`xs`)
- Compact 64x64 avatar
- Stacked information layout
- Full-width buttons
- Simplified menu display

### Tablet+ Layout (`tablet`)
- Large 152x152 avatar carousel
- Side-by-side layout
- Expanded menu pricing table
- Enhanced spacing and typography

## Special Features

### 1. Menu Pricing Display (Counselors)

Shows a structured table with:
- Duration + Meeting Type (e.g., "60分 個人カウンセリング")
- Price (formatted with commas)
- Alternating row colors
- Overflow handling for more than 3 menus

### 2. Status Indicators

Dynamic chip display based on provider capabilities:
- **紹介動画あり** (Introduction Video Available)
- **オンライン対応可** (Online Support Available) - Lawyers
- **本日相談可** (Available Today) - Counselors

### 3. Free Consultation Badge

Special styling for providers offering free initial consultations:
- **初回無料** (First Time Free) - Lawyers
- **オンラインで気軽に** (Easily Online) - Counselors

## Styling and Theming

### Key Style Features

1. **Card Layout**: White background with rounded corners
2. **Color Scheme**: Primary colors for icons, heading colors for text
3. **Typography**: Responsive font sizes with proper hierarchy
4. **Button Styling**: Consistent with app-wide button themes
5. **Line Clamping**: Text truncation for long descriptions

### CSS Classes

- `.line-clamp`: Text truncation utility
- `.tabletStyle`: Responsive component sizing
- `.shadow`: Drop shadow effects for buttons

## Dependencies

### External Components
- `AvatarCarousel`: Image display with navigation
- `ChipList`: Consultation fields and certificates
- `IconText`: Icon + text combinations
- `Rating`: Star ratings display

### Icons Used
- `MapIcon`: Address information
- `StationIcon`: Station access
- `WorkingHourIcon`: Business hours
- `MeetingNoteIcon`: Menu pricing
- `QuoteIcon`: Catchphrase
- `ArrowRightIcon`: Button navigation
- And more...

## Error Handling

- **Missing Images**: Fallback to default avatar
- **Missing Data**: Displays "-" for optional fields
- **Navigation Errors**: Graceful handling with proper user feedback

## Integration Points

### With Provider List
- Used in `src/components/Provider/ProviderList/index.tsx`
- Renders within a Stack layout with spacing
- Supports pagination and loading states

### With Navigation Context
- Uses `useNavigationState` for state management
- Preserves user selection across navigation

### With Query Parameters
- Automatically reads and preserves filter state
- Maintains booking flow continuity

## Usage Example

```typescript
import ProviderItem from 'components/Provider/ProviderList/ProviderItem';

// In a list component
{providers.map((provider) => (
  <ProviderItem key={provider._id} data={provider} />
))}
```

This component serves as the core building block for provider listings, offering a rich, interactive interface that adapts to different provider types and user states while maintaining consistent UX patterns throughout the application. 
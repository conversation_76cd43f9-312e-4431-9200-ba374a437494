# Counselor Detail Page Implementation Guide

## Overview

The counselor detail page (`/counselors/[counselorId]`) displays comprehensive information about a specific counselor and allows users to book consultations. This page serves both authenticated users and guests, with slight differences in data fetching and booking flows.

## Architecture

### File Structure

```
src/
├── pages/
│   └── counselors/
│       └── [counselorId]/
│           └── index.page.tsx          # Main route page
├── shared/
│   └── provider/
│       └── providers/
│           └── [providerId]/
│               └── index.tsx           # Shared detail component
├── components/
│   ├── Guest/
│   │   └── Calendar/                   # Calendar for guest users
│   ├── Provider/
│   │   └── Calendar/                   # Calendar for logged-in users
│   └── LawyerDetail/
│       └── BookingButton/              # Booking action component
└── models/
    └── provider/
        ├── query.ts                    # API queries
        └── interface.ts                # TypeScript interfaces
```

## Main Implementation

### 1. Route Page (`/pages/counselors/[counselorId]/index.page.tsx`)

The main route acts as a thin wrapper that:
- Extracts `counselorId` from URL parameters
- Fetches calendar data for the current day
- Renders the shared `ProviderDetailPage` component with counselor-specific props

```typescript
const CounselorDetailPage = () => {
  const { query } = useRouter();
  const counselorId = query.counselorId as string;
  const startTime = dayjs().startOf('d').toISOString();
  
  const { detail: calendarDetail } = useFetchDetail<ICalendarDetail>({
    ...providerQuery.getCalendarList(counselorId as string, startTime),
  });

  return (
    <ProviderDetailPage
      providerId={counselorId}
      providerType={ProviderType.COUNSELOR}
      calendarDetail={calendarDetail}
    />
  );
};
```

### 2. Static Generation

The page uses **Static Site Generation (SSG)** with Incremental Static Regeneration (ISR):

```typescript
export async function getStaticPaths() {
  // Fetch all counselors at build time
  const { data } = await api.get(
    providerQuery.providerList(ProviderType.COUNSELOR).apiUrl,
    { params: { limit: 9999, providerType: [ProviderType.COUNSELOR] } }
  );
  
  return {
    paths: data.docs.map((doc: { _id: string }) => ({
      params: { counselorId: doc._id },
    })),
    fallback: 'blocking',
  };
}

export async function getStaticProps({ params }: any) {
  const qClient = new QueryClient();
  const providerId = params.counselorId as string;

  // Pre-fetch all necessary data
  const promises = [
    Helper.prefetchDetail(providerQuery.providerDetailByGuest(providerId), qClient),
    Helper.prefetchList(providerQuery.providerSolutions(providerId), qClient),
    Helper.prefetchList(providerQuery.providerArticleList(providerId), qClient),
    Helper.prefetchList(articleQuery.pickupArticleList, qClient),
    Helper.prefetchList(providerQuery.providerConsultationsByGuest(providerId), qClient),
  ];
  
  await Promise.all(promises);
  
  return {
    props: { dehydratedState: dehydrate(qClient) },
    revalidate: 1, // Regenerate page every 1 second
  };
}
```

## Shared Provider Detail Component

### 3. Core Component (`/shared/provider/providers/[providerId]/index.tsx`)

This component handles both lawyers and counselors using a unified interface:

#### Key Features:
- **Conditional Authentication**: Uses different API endpoints based on login status
- **Responsive Design**: Different layouts for mobile and desktop
- **Provider Type Detection**: Renders counselor-specific or lawyer-specific content
- **Real-time Data**: Client-side refetching for dynamic content

#### Authentication-Aware Data Fetching:

```typescript
const providerDetailQuery = useMemo(() => {
  const webCookie = Helper.getWebCookie();
  const isLoggedIn = !!webCookie?.role;

  return isLoggedIn
    ? providerQuery.providerDetail(providerId)
    : providerQuery.providerDetailByGuest(providerId);
}, [providerId]);
```

#### Counselor-Specific Elements:

1. **Special Chips**: Shows "紹介動画あり" (Introduction video available) and "本日相談可" (Available today)
2. **Meeting Style Ratings**: Three-dimensional rating system for communication style
3. **Extended Introduction**: Additional sections for specialties, hobbies, etc.
4. **Interactive Calendar**: Full calendar component for available slots
5. **Review System**: Customer rating and review display

#### Layout Structure:

```typescript
return (
  <Box>
    <NextSeo {...seoConfig.providerDetail({ ...detail, fullName: displayName })} />
    
    {/* Hero Section */}
    <Container maxWidth="md">
      <Box sx={styles.infoContainer}>
        {/* Avatar Carousel */}
        <AvatarCarousel images={detail.images} />
        
        {/* Basic Information */}
        <Typography>カウンセラー</Typography>
        
        {/* Special Chips for Counselors */}
        {!isLawyer && (
          <Box>
            {detail?.videoUrl?.value && <Chip label="紹介動画あり" />}
            {isTodayOpen(calendarDetail) && <Chip label="本日相談可" />}
          </Box>
        )}
        
        {/* Name and Rating */}
        <Typography>{displayName}</Typography>
        {!isLawyer && <Rating rate={{avgRating, totalReview}} />}
        
        {/* Calendar Component */}
        {!isLawyer && calendarDetail && <Calendar />}
        
        {/* Booking Button */}
        <BookingButton 
          detailProvider={detail}
          providerType={providerType}
          freeConsultationMenu={detail.freeConsultationMenu}
        />
        
        {/* Meeting Style (Counselors Only) */}
        {!isLawyer && detail?.meetingStyle && (
          <Grid container>
            <Grid item><BaseLevelRating label="話し方" value={speakingRate} /></Grid>
            <Grid item><BaseLevelRating label="言葉づかい" value={communicationRate} /></Grid>
            <Grid item><BaseLevelRating label="アドバイス" value={adviceRate} /></Grid>
          </Grid>
        )}
        
        {/* Introduction and Video */}
        {catchphrase && <Typography>{catchphrase}</Typography>}
        {introduction && <Typography>{introduction}</Typography>}
        {videoUrl && <VideoThumbnail videoUrl={videoUrl} />}
        
        {/* Extended Introduction (Counselors Only) */}
        {extendIntroduction && (
          <>
            {renderExtendIntroduction('得意な内容・項目', favoriteSubject)}
            {renderExtendIntroduction('大切にしていること', importantInCounseling)}
            {renderExtendIntroduction('過去の実例', solutionCases)}
            {renderExtendIntroduction('趣味', hobbies)}
          </>
        )}
      </Box>
      
      {/* Articles Section */}
      {!isEmpty(articleList) && <ArticleList data={articleList} />}
      
      {/* Reviews Section (Counselors Only) */}
      {!isLawyer && <RatingListProvider detail={detail} />}
    </Container>
  </Box>
);
```

## Calendar Implementation

### 4. Calendar Component (`/components/Guest/Calendar/index.tsx`)

The calendar shows available time slots and allows users to select consultation times:

#### Key Features:
- **Menu Integration**: Dynamically filters available slots based on selected consultation menu
- **Real-time Updates**: Fetches fresh availability data
- **Meeting Type Support**: Handles both individual and couple counseling
- **Duration-based Filtering**: Shows slots that accommodate selected consultation duration

#### Core Logic:

```typescript
const Calendar = () => {
  const [startTime, setStartTime] = useState(dayjs().startOf('d').toISOString());
  
  // Fetch consultation menus
  const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
    ...(webCookie?.token
      ? consultationQuery?.list(counselorId)
      : consultationQuery.listByGuest(counselorId)
    ),
  });
  
  // Fetch calendar availability
  const { detail: calendarDetails, isFetching } = useFetchDetail<ICalendarListCustomer>({
    ...caseQuery.getCalendarDetailByGuest(counselorId, startTime),
  });
  
  return (
    <Box>
      {/* Menu Selection */}
      <Radio
        name="menuId"
        label="料金表"
        data={formattedMenuList}
        control={control}
        type="outline"
      />
      
      {/* Time Table */}
      <TimeTableFieldWithoutDialog
        name="expectDate"
        control={control}
        duration={duration}
        startTime={startTime}
        onNavigate={(date) => setStartTime(date)}
        data={calendarDetails.slots}
        disabledFutureMinutes={60}
      />
    </Box>
  );
};
```

## Booking Flow

### 5. Booking Button (`/components/LawyerDetail/BookingButton/index.tsx`)

Handles the booking initiation with different flows for guests and authenticated users:

#### Authentication Flow:

```typescript
const handleClickContact = () => {
  const webCookie = Helper.getWebCookie();
  const webCookieByGuest = Helper.getWebCookieByGuest();
  const role = get(webCookie, 'role');
  const isDraftUser = get(webCookieByGuest, 'isDraftUser');
  
  if (isDraftUser || !role) {
    // Guest flow - redirect to guest consultation form
    push({
      pathname: '/guest/consultation-form',
      query: { counselorId, duration, meetingType, startSlot, menuId }
    });
  } else if (!isCompletedFields) {
    // Incomplete profile - redirect to consultation form
    push({
      pathname: '/customer/consultation-form',
      query: { counselorId, duration, meetingType, startSlot, menuId }
    });
  } else {
    // Complete profile - proceed to booking
    push({
      pathname: '/customer/counselor-booking/[counselorId]',
      query: { counselorId, duration, meetingType, startSlot, menuId }
    });
  }
};
```

## Menu Data Fetching Strategy

### 6. Guest vs Logged-in User Menu Fetching

The system implements a **dual-endpoint strategy** for fetching consultation menu data based on user authentication status:

#### API Endpoint Differences:

```typescript
// For Logged-in Users
const loggedInEndpoint = `/account/consumer/providers/${providerId}/consultation-menus?checkCustomerId=${userId}`;

// For Guest Users  
const guestEndpoint = `/account/guest/providers/${providerId}/consultation-menus`;
```

#### Query Method Selection:

**In Calendar Component (`/components/Guest/Calendar/index.tsx`):**
```typescript
// Get current user data for logged-in users
const { data: currentUser } = useFetchUser({ enabled: !!webCookie?.token });

const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
  ...(webCookie?.token
    ? consultationQuery?.list(query?.counselorId as string, currentUser?._id)     // Logged-in with user ID
    : consultationQuery.listByGuest(query?.counselorId as string)), // Guest
});
```

**In Customer Booking Page:**
```typescript
// /pages/customer/counselor-booking/[counselorId]/index.page.tsx
const { data: currentUser } = useFetchUser({ enabled: true });

const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
  ...consultationQuery.list(counselorId as string, currentUser?._id), // Always authenticated with user ID
});
```

**In Guest Booking Page:**
```typescript
// /pages/guest/counselor-booking/[counselorId]/index.page.tsx  
const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
  ...consultationQuery.listByGuest(counselorId as string), // Always guest
});
```

#### Authentication Patterns:

**Logged-in Users:**
- Use **JWT tokens** in Authorization headers
- Access user-specific pricing and menu availability
- Can see personalized recommendations

**Guest Users:**
- Use **OTP tokens** as query parameters (`?guest_token=${otpId}`)
- Get public menu information only
- Limited access to certain features

#### Query Definitions (`/models/consultation/query.ts`):

```typescript
const consultationQuery = {
  // For authenticated users
  list: (counselorId: string) => ({
    queryKey: ['public', 'lawyers', counselorId, 'consultations'],
    apiUrl: `/account/consumer/providers/${counselorId}/consultation-menus`,
  }),
  
  // For guest users
  listByGuest: (counselorId: string) => ({
    queryKey: ['public', 'lawyers', counselorId, 'consultations'], // Same cache key!
    apiUrl: `/account/guest/providers/${counselorId}/consultation-menus`,
  }),
};
```

#### Cache Strategy:

Both endpoints use the **same React Query cache key**, allowing seamless transitions when users log in:

```typescript
queryKey: ['public', 'lawyers', counselorId, 'consultations']
```

This means if a guest user loads menu data and then logs in, the cached data can be reused until the next refetch.

#### Static Pre-fetching:

**In Counselor Detail Page (`getStaticProps`):**
```typescript
// Always pre-fetches guest data at build time
Helper.prefetchList(
  providerQuery.providerConsultationsByGuest(providerId),
  qClient,
  { providerId }
),
```

**Client-side Refetching:**
The page then refetches with the appropriate endpoint based on user authentication status during client-side hydration.

#### Related Guest Endpoints:

The same pattern is used across multiple data types:

```typescript
// Calendar data
const calendarEndpoints = {
  authenticated: `/sharing/consumer/provider/${counselorId}/calendars`,
  guest: `/sharing/guest/provider/${counselorId}/calendars`,
};

// Coupon checking  
const couponEndpoints = {
  authenticated: `/coupon/consumer/coupons/check`,
  guest: `/coupon/guest/coupons/check?guest_token=${otpId}`,
};

// Booking creation
const bookingEndpoints = {
  authenticated: `request/consumer/counselor/meeting`,
  guest: `request/guest/counselor/meeting?guest_token=${otpId}`,
};

// Card management
const cardEndpoints = {
  authenticated: `/payment/consumer/cards`,
  guest: `/payment/guest/cards?guest_token=${otpId}`,
};
```

This consistent pattern ensures guests can access the same core functionality as authenticated users while maintaining proper security boundaries.

## API Integration

### 7. Data Models and Queries

#### Provider Query Definitions (`/models/provider/query.ts`):

```typescript
const providerQuery = {
  // List all counselors
  providerList: (type: ProviderType) => ({
    queryKey: ['public', 'provider', type],
    apiUrl: '/account/consumer/providers',
  }),
  
  // Get counselor details (authenticated)
  providerDetail: (providerId: string) => ({
    queryKey: ['public', 'provider', providerId],
    apiUrl: `/account/consumer/providers/${providerId}`,
  }),
  
  // Get counselor details (guest)
  providerDetailByGuest: (providerId: string) => ({
    queryKey: ['public', 'provider', providerId],
    apiUrl: `/account/guest/providers/${providerId}`,
  }),
  
  // Get consultation menus
  providerConsultationsByGuest: (providerId: string) => ({
    queryKey: ['public', 'providers', providerId, 'consultations'],
    apiUrl: `/account/guest/providers/${providerId}/consultation-menus`,
  }),
  
  // Get calendar availability
  getCalendarList: (providerId: string, startTime: string) => ({
    queryKey: ['public', 'providers', 'calendars', providerId, startTime],
    apiUrl: `/sharing/common/provider/${providerId}/calendars/date`,
    customParams: {
      startTime,
      endTime: dayjs(startTime).add(7, 'd').toISOString(),
    },
  }),
};
```

#### Counselor Interface (`/models/provider/interface.ts`):

```typescript
export interface ICounselorItem extends IProviderItem {
  type: ProviderType.COUNSELOR;
  nickname?: string;
  career?: string;
  numberSolutionCases?: number;
  certificate?: string[];
  extendIntroduction?: {
    favoriteSubject?: string;
    hobbies?: string;
    importantInCounseling?: string;
    solutionCases?: string;
  };
  meetingStyle?: {
    speakingRate: number;
    communicationRate: number;
    adviceRate: number;
  };
  avgRating: number;
  totalReview: number;
}
```

## Common Issues and Solutions

### 8. Default Menu Selection Fallback Issue

**Issue**: When URL parameters contain `meetingType` and `duration` that don't match any available menu, the form would be left with no menu selected, breaking the booking flow.

**Root Cause**: The original logic used an `else if` condition that only applied default selection when URL parameters were missing, not when they failed to match any menu.

**Solution**: Fixed the logic to always fall back to `Helper.getDefaultMenuId()` when no matching menu is found:

```typescript
// ❌ Original Logic (broken)
const queryMenuId = useMemo(() => {
  let currentMenuId = '';
  if (query?.duration && query?.meetingType) {
    // Try to find matching menu
    const currentMenu = formattedMenuList?.find(/* matching logic */);
    if (currentMenu?._id) currentMenuId = currentMenu?._id;
    // ❌ If no match found, currentMenuId stays empty!
  } else if (formattedMenuList.length > 0) {
    // ❌ This only runs when URL params are missing
    const defaultMenuId = Helper.getDefaultMenuId(formattedMenuList);
    if (defaultMenuId) currentMenuId = defaultMenuId;
  }
  return currentMenuId;
}, [formattedMenuList, query?.duration, query?.meetingType]);

// ✅ Fixed Logic
const queryMenuId = useMemo(() => {
  let currentMenuId = '';
  
  // Priority 1: Try to match URL parameters if they exist
  if (query?.duration && query?.meetingType) {
    const currentMenu = formattedMenuList?.find(/* matching logic */);
    if (currentMenu?._id) currentMenuId = currentMenu?._id;
  }
  
  // Priority 2: If no URL match found or URL parameters don't exist, use default logic
  if (!currentMenuId && formattedMenuList.length > 0) {
    const defaultMenuId = Helper.getDefaultMenuId(formattedMenuList);
    if (defaultMenuId) currentMenuId = defaultMenuId;
  }
  
  return currentMenuId;
}, [formattedMenuList, query?.duration, query?.meetingType]);
```

**Files Fixed**:
- `src/components/Guest/Calendar/index.tsx` - Calendar component menu selection

**Files Already Correct**:
- `src/pages/customer/counselor-booking/[counselorId]/index.page.tsx` - Customer booking page
- `src/pages/guest/counselor-booking/[counselorId]/index.page.tsx` - Guest booking page

This fix ensures that users always have a valid menu selected, preventing broken booking flows when URL parameters don't match available options.

## State Management

### 8. Navigation Context

The page uses a navigation context to maintain state across booking flows:

```typescript
const { state: counselorDetail, setState } = useNavigationState();

// Store selected booking parameters
setState({
  duration,
  meetingType,
  startSlot,
  menuId,
  expectDate,
});
```

## Performance Optimizations

### 9. Key Performance Features

1. **Static Generation**: Pre-builds pages for all counselors
2. **Incremental Regeneration**: Updates content every 1 second
3. **Data Pre-fetching**: Loads all necessary data at build time
4. **Query Deduplication**: React Query prevents duplicate API calls
5. **Code Splitting**: Lazy loads modal components
6. **Image Optimization**: Uses Next.js Image component with proper sizing

### 10. Caching Strategy

```typescript
// Calendar data caching
const { detail: calendarDetail } = useFetchDetail({
  ...providerQuery.getCalendarList(counselorId, startTime),
  staleTime: 60 * 1000, // Cache for 1 minute
});

// Provider detail caching
export async function getStaticProps() {
  return {
    props: { dehydratedState: dehydrate(qClient) },
    revalidate: 1, // ISR every 1 second
  };
}
```

## SEO Implementation

### 11. Search Engine Optimization

```typescript
<NextSeo
  {...seoConfig.providerDetail({ 
    ...detail, 
    fullName: displayName 
  })}
/>
```

The page includes:
- Dynamic meta titles and descriptions
- Structured data for counselor profiles
- Open Graph tags for social sharing
- Canonical URLs to prevent duplicate content

## Mobile Responsiveness

### 12. Responsive Design Features

- **Breakpoint-based Layouts**: Different layouts for mobile/tablet/desktop
- **Touch-friendly Calendar**: Optimized touch targets for mobile users
- **Collapsible Sections**: Accordion-style content on mobile
- **Optimized Images**: Different image sizes for different devices

## Error Handling

### 13. Error States and Fallbacks

```typescript
// Fallback for missing counselor
export async function getStaticPaths() {
  return {
    paths: [...],
    fallback: 'blocking', // Generate page on first request if not pre-built
  };
}

// Loading states
{detail ? (
  <Typography>{detail.fullName}</Typography>
) : (
  <Skeleton variant="text" width={100} />
)}
```

## Testing Considerations

### 14. Key Testing Areas

1. **Static Generation**: Ensure all counselor pages build correctly
2. **Authentication Flows**: Test guest vs logged-in user experiences
3. **Calendar Integration**: Verify availability data loads correctly
4. **Booking Flows**: Test complete booking process
5. **Responsive Design**: Test on various device sizes
6. **Performance**: Monitor page load times and bundle sizes
7. **Menu Data Fetching**: Verify correct endpoint usage for guests vs authenticated users

## Future Enhancements

### 15. Potential Improvements

1. **Real-time Availability**: WebSocket integration for live calendar updates
2. **Advanced Filtering**: More sophisticated counselor filtering options
3. **Video Consultations**: Direct integration with video calling platforms
4. **AI Recommendations**: Machine learning-based counselor matching
5. **Progressive Web App**: Offline capability and push notifications

This implementation provides a robust, scalable foundation for the counselor detail page that handles both guest and authenticated user flows while maintaining excellent performance and user experience. 
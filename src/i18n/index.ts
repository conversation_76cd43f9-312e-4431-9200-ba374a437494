import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import en from './en.json';
import ja from './ja.json';

i18n.use(initReactI18next).init({
  resources: {
    en: {
      translation: en,
    },
    ja: {
      translation: ja,
    },
  },
  lng: 'ja',
  fallbackLng: 'ja',
  interpolation: {
    escapeValue: false,
  },
});

export const t = i18n.t as (key: string, options?: unknown) => string;

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  ...i18n,
  t,
};

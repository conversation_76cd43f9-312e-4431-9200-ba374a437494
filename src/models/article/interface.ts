import type { ILawyerProfile, IListItem } from 'hooks/types';
import type { ProviderType } from 'utils/constants';

export interface IPickupArticleDetail {
  _id: string;
  attribute: IListItem[];
  consultation: IListItem[];
  title: string;
  status: string;
  subTitle: string;
  image: {
    key?: string;
    originUrl?: string;
    thumbSharing?: string;
  };
  updatedAt: string;
  createdAt: string;
  leadParagraph: string;
  contents?: {
    subImage: {
      key?: string;
      originUrl?: string;
    };
    subTitle: string;
    content: string;
  }[];
  providers: ILawyerProfile[];
}

export interface IArticleDetail {
  _id: string;
  attribute: IListItem[];
  consultation: IListItem[];
  title: string;
  status: string;
  subTitle: string;
  image: {
    key?: string;
    originUrl?: string;
    thumbSharing?: string;
  };
  updatedAt: string;
  createdAt: string;
  leadParagraph: string;
  contents: [
    {
      subImage: {
        key?: string;
        originUrl?: string;
      };
      subTitle: string;
      content: string;
    },
  ];
  provider: ILawyerProfile;
}

export interface IPickupArticleListItem {
  _id: string;
  title: string;
  image: {
    key?: string;
    originUrl?: string;
    thumbSharing?: string;
  };
  updatedAt: string;
  leadParagraph: string;
}

export interface IArticleListItem {
  _id: string;
  provider: {
    _id: string;
    images: { originUrl: string; key: string }[];
    fullName: string;
    katakanaName: string;
    type: ProviderType;
  };
  title: string;
  image: {
    key?: string;
    originUrl?: string;
  };
  updatedAt: string;
  leadParagraph: string;
}

export interface IExternalArticleListItem {
  _id: string;
  status: string;
  title: string;
  image: {
    key?: string;
    originUrl?: string;
  };
  updatedAt: string;
  createdAt: string;
  link: string;
}

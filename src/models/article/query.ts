const articleQuery = {
  articleList: {
    queryKey: ['public', 'articles'],
    apiUrl: '/cms/consumer/interviews',
  },
  articleDetail: (articleId: string) => ({
    queryKey: ['public', 'articles', articleId],
    apiUrl: `/cms/consumer/interviews/${articleId}`,
  }),
  pickupArticleList: {
    queryKey: ['public', 'pickup-articles'],
    apiUrl: '/cms/consumer/pickup-interviews',
  },
  pickupArticleDetail: (pickupArticleId: string) => ({
    queryKey: ['public', 'pickup-articles', pickupArticleId],
    apiUrl: `/cms/consumer/pickup-interviews/${pickupArticleId}`,
  }),
  externalArticleList: {
    queryKey: ['public', 'external-article'],
    apiUrl: '/cms/consumer/articles',
  },
};

export default articleQuery;

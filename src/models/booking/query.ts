import { t } from 'i18n';
import errors from 'utils/errors';

const bookingQuery = {
  create: {
    apiUrl: 'request/consumer/meeting',
    meta: {
      NOT_TOAST_CODES: [errors.INVALID_BOOKING_UNITPRICE],
    },
  },
  create3Ds: {
    apiUrl: 'request/consumer/meeting/3ds',
    meta: {
      NOT_TOAST_CODES: [errors.INVALID_BOOKING_UNITPRICE],
    },
  },
  updateStatus: (bookingId: string) => ({
    apiUrl: `request/provider/bookings/${bookingId}/status`,
    successMessage: t('validation.updateBookingStatusSuccess'),
    method: 'put',
  }),
  addMemo: (bookingId: string) => ({
    apiUrl: `request/provider/bookings/${bookingId}/note`,
    successMessage: t('validation.editMemoSuccess'),
    method: 'put',
  }),
  confirm: (bookingId: string) => ({
    apiUrl: `request/provider/bookings/${bookingId}/confirm`,
    successMessage: t('validation.confirmBookingSuccess'),
  }),
};

export default bookingQuery;

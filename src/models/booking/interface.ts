import type { MeetingType } from 'utils/constants';

export interface ICreateBooking {
  expectDate: { start: string; end: string }[];
  provider: string;
  consumerNote?: string;
  type: MeetingType;
  paymentInfo: {
    unitPrice: number;
    duration: number;
    card?: string;
    couponCode?: string;
  };
}

export interface IBookingForm {
  firstChoice: string;
  secondChoice: string;
  thirdChoice: string;
  consumerNote: string;
  duration: number;
  type: MeetingType;
}

export interface IUpdateBookingStatus {
  bookingId: string;
  status: string;
}

export interface IAddBookingMemo {
  bookingId: string;
  note: string;
}

export interface IConfirmBooking {
  bookingId: string;
  finalizedBookingDate: string;
}

export interface IRequest3DsRes {
  status?: 'success' | 'failure';
  redirectUrl: string;
}

export type ActionTds =
  | 'create_meeting_lawyer_3ds'
  | 'create_meeting_counselor_3ds'
  | 'update_card_of_meeting_need_3ds';

export interface IExtraTdsPayload {
  needFillForm?: boolean;
  counselorId?: string;
  lawyerId?: string;
  caseId?: string;
}

export interface ICreateConsultation {
  title: string;
  content?: string; // optional
}

export interface IUpdateConsultation extends ICreateConsultation {
  _id: string;
}

export interface IConsultationItem {
  _id: string;
  title: string;
  content?: string;
  consumers?: {
    _id: string;
    katakanaName: string;
    fullName: string;
  }[];
  createdAt: string;
  updatedAt: string;
  unitPrices?: { duration: number; price: number }[];
}

export interface IDeleteConsultation {
  _id: string;
}

export interface IAddUnitPrice {
  unitPrices: { duration: number; price: number }[];
}

export enum IMeetingType {
  SINGLE = 'SINGLE',
  PARTNER = 'PARTNER',
}

export enum IMeetingTypeLabel {
  SINGLE = '個人カウンセリング',
  PARTNER = '夫婦カウンセリング',
}

export interface ICreateConsultationMenu {
  title: string;
  content?: string;
  unitPrices: { duration: number; price: number }[];
  meetingType?: IMeetingType | string;
}

export interface ICounselorConsultationMenu {
  _id: string;
  title: string;
  content?: string;
  createdAt: string;
  updatedAt: string;
  unitPrices: [{ duration: number; price: number }];
  meetingType?: IMeetingType;
}

import { t } from 'i18n';

const consultationQuery = {
  create: {
    apiUrl: '/account/provider/consultation-menus',
    successMessage: t('validation.updateConsultationMenuSuccess'),
  },
  update: (menuId: string) => ({
    apiUrl: `/account/provider/consultation-menus/${menuId}`,
    successMessage: t('validation.updateConsultationMenuSuccess'),
    method: 'put',
  }),
  // apiUrl is a function because this is a delete query for multi item in a page
  delete: {
    apiUrl: ({ _id }: { _id: string }) =>
      `/account/provider/consultation-menus/${_id}`,
    method: 'delete',
    successMessage: t('validation.itemDeleted'),
    onMutate: () => ({ action: 'deleteConsultation' }),
  },
  addUnitPrice: {
    apiUrl: '/account/provider/consultation-menus/unit-price',
    method: 'put',
    defaultToast: true,
  },
  list: (lawyerId: string, userId?: string) => ({
    queryKey: ['public', 'lawyers', lawyerId, 'consultations', userId],
    apiUrl: `/account/consumer/providers/${lawyerId}/consultation-menus${
      userId ? `?checkCustomerId=${userId}` : ''
    }`,
  }),
  listByGuest: (lawyerId: string) => ({
    queryKey: ['public', 'lawyers', lawyerId, 'consultations'],
    apiUrl: `/account/guest/providers/${lawyerId}/consultation-menus`,
  }),
};

export default consultationQuery;

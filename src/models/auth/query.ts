import { ROLES } from 'utils/constants';

const authQuery = {
  forgotPassword: (role: string) => ({
    apiUrl:
      role === ROLES.LAWYER
        ? 'account/provider/forgotPassword'
        : '/account/consumer/forgotPassword',
  }),
  resetPassword: (role: string) => ({
    apiUrl:
      role === ROLES.LAWYER
        ? 'account/provider/resetPassword'
        : 'account/consumer/resetPassword',
    defaultToast: true,
  }),
  login: (role: string) => ({
    apiUrl:
      role === ROLES.CUSTOMER
        ? '/account/consumer/login'
        : '/account/provider/login',
  }),
  setProviderFirstPassword: {
    apiUrl: '/account/provider/setFirstPassword',
  },
  additionalEmail: {
    apiUrl: '/account/provider/me/additional-email',
    method: 'put',
    defaultToast: true,
  },
};

export default authQuery;

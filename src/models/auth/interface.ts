import type { ROLES } from 'utils/constants';

export interface ForgotPasswordPayload {
  email: string;
}

export interface ResetPasswordPayload {
  token: string;
  password: string;
}

export interface LoginResponse {
  provider?: {
    type: Exclude<ROLES, 'CUSTOMER'>;
  };
  token: {
    token: string;
    type: string;
  };
}

export interface AdditionEmailPayload {
  additionalEmail: string[];
}

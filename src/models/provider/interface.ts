import type { ISlotItem } from 'components/TimeTable/utils';
import type { IProviderItem } from 'hooks/types';
import type { IMeetingType } from 'models/consultation/interface';
import type { GenderType, ProviderType } from 'utils/constants';

export interface ILawyerItem extends IProviderItem {
  office: {
    _id: string;
    fullName: string;
    katakanaName: string;
    stationToTheOffice?: string;
    timeToTheOffice?: number;
    businessHours?: string;
    address?: {
      postCode?: string;
      address1?: {
        _id: string;
        value: string;
      };
      address2?: string;
      address3?: string;
      address4?: string;
    };
  };
  type: ProviderType.LAWYER;
  hasOnlineSupport?: boolean | undefined;
}
interface Menu {
  title: string;
  content?: string;
  unitPrices: {
    duration: number;
    price: number;
  }[];
  meetingType: IMeetingType | string;
}
export interface ICounselorItem extends IProviderItem {
  nickname?: string;
  birthday?: string;
  gender?: GenderType;
  career?: string;
  type: ProviderType.COUNSELOR;
  certificate?: string[];
  numberSolutionCases?: number;
  meetingStyle?: {
    speakingRate: number;
    communicationRate: number;
    adviceRate: number;
  };
  extendIntroduction?: {
    favoriteSubject?: string;
    hobbies?: string;
    importantInCounseling?: string;
    solutionCases?: string;
  };
  isAvailableToday?: boolean;
  menus?: Menu[];
}

export type ICounselorItemWithCalendar = {
  date: string;
  _id: string;
  dateOfWeek: string;
  menus: Menu[];
  provider: string;
  providerInfo: ICounselorItem;
  slots: ISlotItem[];
};

export interface JobPayload {
  barAssociation: string;
  consultationField: string[];
  attribute: string[];
}

export interface CounselorJobPayload {
  career: string;
  numberSolutionCases?: number;
  certificate?: string[];
  consultationField?: string[];
}

export interface IRecommendListItem {
  _id: string;
  fullName: string;
  katakanaName: string;
  images: { originUrl: string; key: string }[];
  createdAt: string;
  nickname?: string;
  avgRating: number;
  totalReview: number;
}

export interface ICalendarDetail {
  date: string;
  status: string;
}

export interface BankInformation {
  bankId: string;
  bankName: string;
  bankCode: string;
  branchId: string;
  branchName: string;
  accountNumber: string;
  holderName: string;
  accountType: {
    key: string;
    labelEN: string;
    labelJP: string;
  };
}

import dayjs from 'dayjs';
import type { ProviderType } from 'utils/constants';

const providerQuery = {
  providerList: (type: ProviderType) => ({
    queryKey: ['public', 'provider', type],
    apiUrl: '/account/consumer/providers',
  }),
  providerDetail: (providerId: string) => ({
    queryKey: ['public', 'provider', providerId],
    apiUrl: `/account/consumer/providers/${providerId}`,
  }),
  providerDetailByGuest: (providerId: string) => ({
    queryKey: ['public', 'provider', providerId],
    apiUrl: `/account/guest/providers/${providerId}`,
  }),
  providerConsultations: (providerId: string) => ({
    queryKey: ['public', 'providers', providerId, 'consultations'],
    apiUrl: `/account/consumer/providers/${providerId}/consultation-menus`,
    customParams: {
      providerId,
    },
    omitKeys: ['counselorId'],
  }),
  providerConsultationsByGuest: (providerId: string) => ({
    queryKey: ['public', 'providers', providerId, 'consultations'],
    apiUrl: `/account/guest/providers/${providerId}/consultation-menus`,
    customParams: {
      providerId,
    },
    omitKeys: ['counselorId'],
  }),
  providerArticleList: (providerId: string) => ({
    queryKey: ['public', 'articles', 'providerId', providerId],
    apiUrl: `/account/consumer/providers/${providerId}/interviews`,
  }),
  providerReviewsList: (providerId: string) => ({
    queryKey: ['public', 'reviews', 'providerId', providerId],
    apiUrl: `/review/consumer/provider/${providerId}/reviews`,
  }),
  providerSolutions: (providerId: string) => ({
    queryKey: ['public', 'providers', providerId, 'solutions'],
    apiUrl: `/account/consumer/providers/${providerId}/solutions`,
  }),
  updateProfile: {
    apiUrl: '/account/provider/me',
    method: 'put',
    defaultToast: true,
  },
  updateJob: {
    apiUrl: '/account/provider/me/job-information',
    method: 'put',
    defaultToast: true,
  },
  updateDivorce: {
    apiUrl: '/account/provider/me/family-information',
    method: 'put',
    defaultToast: true,
  },
  recommendCounselorList: {
    queryKey: ['currentUser', 'recommend-counselors'],
    apiUrl: '/account/consumer/recommend-counselors',
  },
  getCalendarList: (providerId: string, startTime: string) => {
    return {
      queryKey: ['public', 'providers', 'calendars', providerId, startTime],
      apiUrl: `/sharing/common/provider/${providerId}/calendars/date`,
      customParams: {
        startTime,
        endTime: dayjs(startTime).add(7, 'd').toISOString(),
      },
    };
  },
  getCalendarSlotList: {
    queryKey: ['public', 'providers', 'calendars', 'date', 'slot'],
    apiUrl: `/sharing/common/calendars/date/slot`,
  },
  updateBank: {
    apiUrl: '/account/provider/me/bank-info',
    method: 'put',
    defaultToast: true,
  },
};

export default providerQuery;

import { t } from 'i18n';

const solutionQuery = {
  create: {
    apiUrl: 'account/provider/solutions',
    defaultToast: true,
  },
  update: (solutionId: string) => ({
    apiUrl: `account/provider/solutions/${solutionId}`,
    defaultToast: true,
    method: 'put',
  }),
  delete: {
    apiUrl: ({ _id }: { _id: string }) => `account/provider/solutions/${_id}`,
    method: 'delete',
    successMessage: t('validation.deleteSolutionSuccess'),
    onMutate: () => ({ action: 'deleteSolution' }),
  },
};

export default solutionQuery;

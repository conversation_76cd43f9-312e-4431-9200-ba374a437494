import type { IListItem } from 'hooks/types';
import type { GenderType, RadioOptionsType } from 'utils/constants';

export interface ICreateSolution {
  title: string;
  consultationField: string[];
  background: string;
  customer: {
    age: string;
    gender: GenderType;
  };
  partner: {
    age: string;
    gender: GenderType;
  };
  children: RadioOptionsType;
  content: string;
  benefit: string;
  point: string;
}

export interface ISolutionItem {
  _id: string;
  title: string;
  consultationField: IListItem[];
  background: string;
  customer: {
    age: string;
    gender: GenderType;
  };
  partner: {
    age: string;
    gender: GenderType;
  };
  children: RadioOptionsType;
  content: string;
  benefit: string;
  point: string;
}

export interface IUpdateSolution extends ICreateSolution {
  _id: string;
}

export interface IDeleteSolution {
  _id: string;
}

const newsQuery = {
  newsList: {
    queryKey: ['public', 'news'],
    apiUrl: 'sharing/common/news',
    customParams: {
      page: 1,
      limit: 3,
      sort: 'order.desc_createdAt.desc',
    },
  },
  newsListPaginated: {
    queryKey: ['public', 'news', 'paginated'],
    apiUrl: 'sharing/common/news',
    customParams: {
      limit: 20,
      sort: 'order.desc_createdAt.desc',
    },
    useUrlQuery: true,
  },
  newsDetail: {
    queryKey: ['public', 'news', 'detail'],
    apiUrl: 'sharing/common/news',
    getPath: (id: string) => `${id}`,
  },
};

export default newsQuery;

import type { WorkDaysType } from 'components/Provider/CounselorWorkDaysModal/schema';
import type { IMeetingType } from 'models/consultation/interface';
import type { ICoupon } from 'models/coupon/interface';
import type {
  CaseStatusType,
  MeetingStatusType,
  PaymentMethod,
  PaymentStatusType,
  SlotStatus,
} from 'utils/constants';

export interface UpdateWorkDaysPayload {
  weeklySchedule: WorkDaysType;
}

export interface ISlot {
  start: string;
  end: string;
  status: SlotStatus;
}

export interface ISlotMeeting {
  finalizedDate?: string;
  _id: string;
  status: Exclude<
    MeetingStatusType,
    | MeetingStatusType.CANCELED_BY_PROVIDER
    | MeetingStatusType.CANCELED_BY_CONSUMER
    | MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR
    | MeetingStatusType.WAITING_FOR_REPLY_FROM_CUSTOMER
  >;
  startSlot: string;
  endSlot: string;
  consumerNote?: string;
  consultationMenu: {
    content: string;
    title: string;
    unitPrices: [{ duration: number; price: number }];
    meetingType: IMeetingType | string;
  };
  case: {
    _id: string;
    status: CaseStatusType;
  };
  consumer: {
    _id: string;
    fullName: string;
  };
  paymentInfo: {
    duration: number;
    unitPrice: number;
    method: PaymentMethod;
    finalPrice: number;
    status: PaymentStatusType;
    discount?: number;
    couponInfo?: {
      coupon: Omit<ICoupon, 'couponAmount'>;
      couponAmount: number;
    };
  };
  meetingUrl?: string;
  expectDate: [{ start: string; end: string }];
  extendMenus?: { duration: number; price: number }[];
}
export interface IBusySlot {
  start: string;
  end: string;
  status: Exclude<
    MeetingStatusType,
    | MeetingStatusType.CANCELED_BY_PROVIDER
    | MeetingStatusType.CANCELED_BY_CONSUMER
    | MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR
  >;
  meeting: ISlotMeeting;
}

export interface ICalendarDetail {
  busySlot: IBusySlot[];
  slots: ISlot[];
}

export interface UpdateSlotStatusPayload {
  status: SlotStatus;
  slots: { start: string; end: string }[];
}

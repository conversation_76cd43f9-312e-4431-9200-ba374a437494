import dayjs from 'dayjs';
import { SlotStatus } from 'utils/constants';
import Helper from 'utils/helpers';

import type { UpdateSlotStatusPayload } from './interface';

const shiftQuery = {
  updateWorkDays: {
    apiUrl: '/account/provider/me/weekly-schedule',
    method: 'put',
    defaultToast: true,
  },
  calendarList: (startTime: string) => {
    const formatStartTime =
      new Date(startTime) <= new Date()
        ? dayjs().startOf('d').toISOString()
        : startTime;
    const endDate = dayjs(formatStartTime).add(7, 'days');
    return {
      queryKey: ['currentUser', 'calendars', formatStartTime],
      apiUrl: '/sharing/provider/calendars',
      staleTime: 2 * 60 * 1000,
      keepPreviousData: false,
      customParams: {
        startTime: formatStartTime,
        endTime: endDate.toISOString(),
      },
    };
  },
  updateSlotStatus: {
    apiUrl: '/sharing/provider/calendars/slotStatus',
    method: 'put',
    onSuccess: (
      data: { isModified: boolean },
      varialbes: UpdateSlotStatusPayload,
    ) => {
      if (varialbes.slots.length > 1) {
        if (data.isModified) {
          Helper.toast(
            varialbes.status === SlotStatus.OPEN
              ? '出勤に変更しました'
              : '欠勤に変更しました',
          );
        } else
          Helper.toast('スケジュールの変更に失敗しました', { type: 'error' });
      }
    },
  },
  hideTutorial: {
    apiUrl: '/account/provider/me/show-tutorial',
    method: 'put',
  },
};

export default shiftQuery;

import dayjs from 'dayjs';
import { t } from 'i18n';
import errors from 'utils/errors';

const caseQuery = {
  customerCaseList: {
    queryKey: ['currentUser', 'cases-list'],
    apiUrl: '/request/consumer/cases',
    keepPreviousData: true,
  },
  providerCaseList: {
    queryKey: ['currentUser', 'lawyer-cases-list'],
    apiUrl: '/request/provider/cases',
    keepPreviousData: true,
  },
  providerCaseDetail: (caseId: string) => ({
    queryKey: ['currentUser', 'provider-cases', caseId],
    apiUrl: `request/provider/cases/${caseId}`,
  }),
  lawyerMeetingList: (caseId: string) => ({
    queryKey: ['currentUser', 'meeting-list'],
    apiUrl: `request/provider/cases/${caseId}/meetings`,
    omitKeys: ['tab'],
  }),
  addMemo: (caseId: string) => ({
    apiUrl: `request/provider/cases/${caseId}/note`,
    successMessage: t('validation.editMemoSuccess'),
    method: 'put',
  }),
  addPrivateNote: (caseId: string) => ({
    apiUrl: `request/provider/cases/${caseId}/private-note`,
    successMessage: '完了しました。',
    method: 'put',
  }),
  createMeeting: {
    apiUrl: 'request/provider/meeting',
    defaultToast: true,
    meta: {
      NOT_TOAST_CODES: [errors.LAWYER_INVALID_BOOKING_UNITPRICE],
    },
  },
  updateMeeting: (meetingId: string) => ({
    apiUrl: `request/provider/meeting/${meetingId}`,
    defaultToast: true,
    method: 'put',
  }),
  updateProviderMeetingStatus: (meetingId: string) => ({
    apiUrl: `request/provider/meeting/${meetingId}/status`,
    method: 'put',
    meta: {
      NOT_TOAST_CODES: ['E_VALIDATION_FAILURE'],
    },
  }),
  customerCaseDetail: (caseId: string) => ({
    queryKey: ['currentUser', 'cases', caseId],
    apiUrl: `request/consumer/cases/${caseId}`,
  }),
  customerMeetingList: (caseId: string) => ({
    queryKey: ['currentUser', 'meeting-list'],
    apiUrl: `request/consumer/cases/${caseId}/meetings`,
  }),
  finishMeeting: {
    apiUrl: ({ meetingId }: { meetingId: string }) =>
      `request/provider/meeting/${meetingId}/finish`,
  },
  cancelMeeting: {
    apiUrl: ({ meetingId }: { meetingId: string }) =>
      `request/provider/meeting/${meetingId}/cancel`,
    successMessage: t('validation.cancelMeetingSuccess'),
  },
  updateCaseStatus: (caseId: string) => ({
    apiUrl: `request/provider/cases/${caseId}/status`,
    method: 'put',
    successMessage: t('validation.assignedSuccess'),
  }),
  updateMeetingStatus: (caseId: string) => ({
    apiUrl: `request/provider/cases/${caseId}/status`,
    method: 'put',
  }),
  getMeetingDetail: (meetingId: string) => ({
    queryKey: ['currentUser', 'lawyer-meeting', meetingId],
    apiUrl: `request/provider/meeting/${meetingId}`,
  }),
  acceptCase: (caseId: string) => ({
    apiUrl: `request/provider/cases/${caseId}/accept`,
  }),
  declineCase: (caseId: string) => ({
    apiUrl: `request/provider/cases/${caseId}/decline`,
  }),
  confirmPayment: {
    apiUrl: ({ meetingId }: { meetingId: string }) =>
      `payment/provider/meeting/${meetingId}/confirm`,
    successMessage: t('validation.confirmPaymentSuccess'),
  },
  lawyerConfirmMeeting: (meetingId: string) => ({
    apiUrl: `request/provider/meeting/${meetingId}/confirm`,
    defaultToast: true,
  }),
  updatePaymentMethod: (meetingId: string) => ({
    apiUrl: `request/provider/meeting/${meetingId}/payment-method`,
    method: 'put',
    defaultToast: true,
  }),
  updateMeetingCard: (meetingId: string) => ({
    apiUrl: `request/consumer/meeting/${meetingId}/card`,
    method: 'put',
    successMessage: t('validation.changeCardSuccess'),
  }),
  updateMeetingCard3Ds: (meetingId: string) => ({
    apiUrl: `request/consumer/meeting/${meetingId}/card/3ds`,
    method: 'put',
    successMessage: t('validation.changeCardSuccess'),
  }),
  reviewProvider: (caseId: string) => ({
    apiUrl: `request/consumer/cases/${caseId}/review`,
  }),
  cancelReviewProvider: (caseId: string) => ({
    apiUrl: `request/consumer/cases/${caseId}/review/cancel`,
  }),
  reviewCustomer: (caseId: string) => ({
    apiUrl: `request/provider/cases/${caseId}/review`,
  }),
  cancelReviewCustomer: (caseId: string) => ({
    apiUrl: `request/provider/cases/${caseId}/review/cancel`,
  }),
  counselorCreateMeeting: {
    apiUrl: 'request/consumer/counselor/meeting',
  },
  counselorCreateMeeting3Ds: {
    apiUrl: 'request/consumer/counselor/meeting/3ds',
  },
  counselorCreateMeetingByGuest: (otpId: string) => ({
    apiUrl: `request/guest/counselor/meeting?guest_token=${otpId}`,
  }),
  counselorCreateMeeting3DsByGuest: (otpId: string) => ({
    apiUrl: `request/guest/counselor/meeting/3ds?guest_token=${otpId}`,
  }),
  getCalendarDetail: (counselorId: string, startTime: string) => {
    return {
      queryKey: [
        'currentUser',
        'consumer',
        'calendars',
        counselorId,
        startTime,
      ],
      apiUrl: `/sharing/consumer/provider/${counselorId}/calendars`,
      staleTime: 60 * 1000,
      customParams: {
        startTime,
        endTime: dayjs(startTime).add(7, 'd').toISOString(),
      },
    };
  },
  getCalendarDetailByGuest: (counselorId: string, startTime: string) => {
    return {
      queryKey: [
        'currentUser',
        'consumer',
        'calendars',
        counselorId,
        startTime,
      ],
      apiUrl: `/sharing/guest/provider/${counselorId}/calendars`,
      staleTime: 60 * 1000,
      customParams: {
        startTime,
        endTime: dayjs(startTime).add(7, 'd').toISOString(),
      },
    };
  },
  counselorConfirmMeeting: (meetingId: string) => ({
    apiUrl: `request/provider/counselor/meeting/${meetingId}/confirm`,
    defaultToast: true,
  }),
  providerCustomerConsultation: (customerId: string) => ({
    queryKey: ['currentUser', 'customer', customerId, 'consultation'],
    apiUrl: `/account/provider/consumers/${customerId}/consultation-record`,
  }),
  providerCounselorCreateMeeting: {
    apiUrl: '/request/provider/counselor/meeting',
    defaultToast: true,
  },
  customerCancelMeetingOfCounselor: {
    apiUrl: ({ meetingId }: { meetingId: string }) =>
      `request/consumer/meeting/${meetingId}/cancel`,
    successMessage: t('validation.cancelMeetingSuccess'),
  },
  removeCoupon: (meetingId: string) => ({
    apiUrl: `/request/provider/meeting/${meetingId}/remove-coupon`,
    method: 'put',
    successMessage: 'クーポンを削除しました',
  }),
};
export default caseQuery;

import type { IExtraTdsPayload } from 'models/booking/interface';
import type { IMeetingType } from 'models/consultation/interface';
import type { ICoupon } from 'models/coupon/interface';
import type {
  CaseStatusType,
  MeetingStatusType,
  MeetingType,
  PaymentMethod,
  PaymentStatusType,
  ProviderType,
  ValidCardNiceType,
} from 'utils/constants';

export interface ICaseListItem {
  _id: string;
  provider: {
    _id: string;
    fullName: string;
    katakanaName: string;
    nickname?: string;
    type: ProviderType;
  };
  status: CaseStatusType;
  createdAt: string;
  updatedAt: string;
  finalizedDate?: string;
  duration?: number;
}

export interface IProviderCaseListItem {
  _id: string;
  consumer: {
    _id: string;
    fullName: string;
    katakanaName: string;
  };
  status: CaseStatusType;
  createdAt: string;
  updatedAt: string;
  finalizedDate?: string;
  duration?: string;
}
export interface ICaseDetail {
  _id: string;
  status: CaseStatusType;
  hasPaymentError: boolean;
  reviewProvider?: IReviewObject;
  roomInfo?: {
    roomId: string;
  };
  provider: {
    type: ProviderType;
    _id: string;
    fullName: string;
    katakanaName: string;
    nickname?: string;
    email: string;
    images: {
      key: string;
      originUrl: string;
      thumbSmall: string;
      thumbMedium: string;
      thumbLarge: string;
    }[];
    avgRating: number;
    totalReview: number;
  };
}
export interface IMeetingsItem {
  consultationMenu?: {
    title: string;
    content?: string;
    unitPrices: {
      duration: number;
      price: number;
    }[];
    meetingType: IMeetingType | string;
  };
  _id: string;
  status: MeetingStatusType;
  finalizedDate?: string;
  type: MeetingType;
  meetingUrl?: string;
  expectDate:
    | [
        { start: string; end: string },
        { start: string; end: string },
        { start: string; end: string },
      ]
    | [{ start: string; end: string }];
  consumerNote?: string;
  paymentInfo: {
    couponInfo?: {
      coupon: Omit<ICoupon, 'couponAmount'>;
      couponAmount: number;
    };
    method: PaymentMethod;
    duration: number;
    status: PaymentStatusType;
    unitPrice: number;
    finalPrice?: number;
    discount?: number;
    card?: {
      details: {
        brand: ValidCardNiceType;
        default: boolean;
        expireMonth: string;
        expireYear: string;
        kind: string;
        lastNumber: string;
      };
      id: string;
      kind: string;
      type: string;
    };
  };
  cancelReason?: string;
  extendMenus?: { duration: number; price: number }[];
}
export interface IProviderCaseDetail {
  canClosed: boolean;
  hasPaymentError: boolean;
  _id: string;
  status: CaseStatusType;
  roomInfo?: {
    roomId: string;
  };
  consumer: {
    _id: string;
    fullName: string;
    katakanaName: string;
    email: string;
  };
  memo?: {
    note: string;
    updatedAt: string;
  };
  privateMemo?: {
    note: string;
    updatedAt: string;
  };
  reviewConsumer?: IReviewObject;
}

export interface IAddCaseMemo {
  caseId: string;
  note: string;
}

export interface IAddPrivateNote {
  caseId: string;
  privateNote: string;
}

export interface ICreateMeeting {
  caseId: string;
  meetingUrl?: string;
  finalizedDate?: string;
  type: MeetingType;
  paymentInfo: {
    unitPrice?: number;
    duration: number;
    discount: number;
    method: string;
  };
}

export interface IUpdateCaseStatus {
  status: Omit<CaseStatusType, 'WAITING_ANWSER_FROM_PROVIDER'>;
}

export interface IUpdateMeetingStatus {
  status: MeetingStatusType;
}

export interface IConfirmMeeting {
  meetingUrl?: string;
  finalizedDate: string;
  type: MeetingType;
  paymentInfo: {
    unitPrice: number;
    duration: number;
    discount: number;
    method: string;
  };
}

export interface IUpdatePaymentMethod {
  paymentInfo: {
    method: string;
  };
}

export interface IReviewObject {
  point?: number;
  comments?: string;
  isShowAgain?: boolean;
  updatedAt?: string;
}
export interface IReviewCustomer {
  reviewConsumer: IReviewObject;
}

export interface IReviewLawyer {
  reviewProvider: IReviewObject;
}

export interface CounselorCreateMeetingPayload {
  expectDate: [{ start: string; end: string }];
  startSlot: string;
  provider: string;
  consumerNote?: string;
  paymentInfo: {
    basicPrice: number;
    duration: number;
    card?: string;
    couponCode?: string;
  };
  consultationMenu: {
    title: string;
    content?: string;
    unitPrices: [
      {
        duration: number;
        price: number;
      },
    ];
  };
  extraPayload?: IExtraTdsPayload;
}

export interface ICalendarItem {
  status: string;
  start: string;
  end: string;
  systemStatus: string;
}
export interface ICalendarListCustomer {
  slots: ICalendarItem[];
}

export interface ICalendarList {
  date: string;
  slots: ICalendarItem[];
}

export interface ICounselorConfirmMeeting {
  caseId?: string;
  finalizedDate: string;
  meetingUrl: string;
  startSlot: string;
  paymentInfo: {
    basicPrice: number;
    duration: number;
    discount: number;
  };
  consultationMenu: {
    title?: string;
    unitPrices: {
      duration: number;
      price: number;
    }[];
  };
}

export interface ICounselorUpdateMeeting {
  meetingUrl: string;
  finalizedDate: string;
  startSlot: string;
  consultationMenu: {
    title: string;
    content?: string;
    unitPrices: [{ duration: number; price: number }];
    meetingType?: IMeetingType | string;
  };
  paymentInfo: {
    duration: number;
    discount?: number;
  };
  extendMenus?: { duration: number; price: number }[] | null;
}

export interface ICounselorCreateMeeting {
  finalizedDate: string;
  meetingUrl: string;
  startSlot: string;
  caseId: string;
  paymentInfo: {
    basicPrice: number;
    duration: number;
    discount: number;
  };
  consultationMenu: {
    title: string;
    content?: string;
    unitPrices: [
      {
        duration: number;
        price: number;
      },
    ];
    meetingType?: IMeetingType | string;
  };
}

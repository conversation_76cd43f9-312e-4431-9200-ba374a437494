import type { IListResult } from 'hooks/types';

import type { ICoupon } from './interface';

const couponQuery = {
  list: {
    queryKey: ['currentUser', 'coupon-list'],
    apiUrl: '/coupon/consumer/coupons',
    select: (data: unknown) => {
      const couponData = data as IListResult<ICoupon>;
      const formatDocs = couponData.docs.map((doc) => ({
        ...doc,
        id: doc._id,
      }));
      return {
        ...couponData,
        docs: formatDocs,
      };
    },
  },
  checkCoupon: {
    apiUrl: '/coupon/consumer/coupons/check',
    method: 'patch',
  },
  checkCouponByGuest: (otpId: string) => ({
    apiUrl: `/coupon/guest/coupons/check?guest_token=${otpId}`,
    method: 'patch',
  }),
};

export default couponQuery;

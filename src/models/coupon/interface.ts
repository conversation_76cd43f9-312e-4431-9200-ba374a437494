export interface ICoupon {
  id: string;
  _id: string;
  amount: number;
  code: string;
  title: string;
  currency: '¥' | '%';
  description?: string;
  isAvailable: boolean;
  rules: {
    timezone: string;
    end?: string;
    limitPerUser?: number;
    maxServiceUsage?: number;
    maxDiscountAmount?: number;
  };
  // true: unlimited, number: limited
  availableCount: number | boolean;
  tags?: {
    id: number;
    name: string;
  }[];
  couponAmount: number;
}

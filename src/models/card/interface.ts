import type { ValidCardNiceType } from 'utils/constants';

export interface AddCardPayload {
  token: string;
}

export interface ICardsList {
  id: string;
  _id: string;
  kind: string;
  type: string;
  details: {
    kind: string;
    expireMonth: string;
    expireYear: string;
    lastNumber: string;
    default: boolean;
    brand: ValidCardNiceType;
    cardholderName?: string;
  };
}

export interface EditCardPayload {
  cardExpire: string;
  cardholderName: string;
  defaultCard: boolean;
}

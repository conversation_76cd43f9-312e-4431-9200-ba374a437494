import { t } from 'i18n';

const cardQuery = {
  getCardToken: {
    apiUrl: '/api/veritrans',
    axiosConfig: {
      baseURL: '/',
    },
  },
  addCard: {
    apiUrl: '/payment/consumer/add-card',
    successMessage: t('validation.addCardSuccess'),
  },
  addCardByGuest: (otpId: string) => ({
    apiUrl: `/payment/guest/add-card?guest_token=${otpId}`,
    successMessage: t('validation.addCardSuccess'),
  }),
  editCard: {
    apiUrl: ({ _id }: { _id: string }) => `/payment/consumer/card/${_id}`,
    method: 'post',
    successMessage: t('validation.addCardSuccess'),
  },
  editCardByGuest: {
    apiUrl: ({ _id }: { _id: string }) => `/payment/guest/card/${_id}`,
    method: 'post',
    successMessage: t('validation.addCardSuccess'),
  },
  cardsList: {
    queryKey: ['currentUser', 'cards'],
    apiUrl: '/payment/consumer/cards',
    omitKeys: ['page', 'limit'],
  },
  cardsListByGuest: (otpId: string) => ({
    queryKey: ['currentUser', 'cards'],
    apiUrl: `/payment/guest/cards/?guest_token=${otpId}`,
    omitKeys: ['page', 'limit'],
    enabled: !!otpId,
  }),
  setPrimary: {
    apiUrl: ({ _id }: { _id: string }) =>
      `/payment/consumer/card/${_id}/set-primary`,
    successMessage: t('validation.setPrimarySuccess'),
    method: 'put',
  },
  deleteCard: {
    apiUrl: ({ _id }: { _id: string }) => `/payment/consumer/card/${_id}`,
    method: 'delete',
    successMessage: t('validation.deleteCardSuccess'),
  },
};

export default cardQuery;

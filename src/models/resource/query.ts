import type { IListResult, IPrefecture } from 'hooks/types';
import Helper from 'utils/helpers';

import type { IBank, IBranches } from './interface';

const resourceQuery = {
  prefectures: {
    queryKey: ['public', 'prefectures'],
    apiUrl: '/sharing/common/prefectures',
    staleTime: Infinity,
    useUrlQuery: false,
    select: (data: any) => {
      const prefectureData = data as IListResult<IPrefecture>;
      const formatDocs = prefectureData.docs.map((doc) => ({
        ...doc,
        value: doc.nameJp,
      }));
      return {
        ...prefectureData,
        ...Helper.convertArrayToEntities<IPrefecture>(formatDocs),
        docs: formatDocs,
      };
    },
    customParams: {
      page: 1,
      limit: 1000,
      sort: 'order.asc',
    },
  },
  consultations: {
    queryKey: ['public', 'consultations'],
    apiUrl: 'sharing/common/consultations',
    staleTime: Infinity,
    useUrlQuery: false,
    customParams: {
      page: 1,
      limit: 1000,
      sort: 'order.asc',
    },
  },
  attributes: {
    queryKey: ['public', 'attributes'],
    apiUrl: 'sharing/common/attributes',
    staleTime: Infinity,
    useUrlQuery: false,
    customParams: {
      page: 1,
      limit: 1000,
    },
  },
  divorceCauses: {
    queryKey: ['public', 'divorceCauses'],
    apiUrl: 'sharing/common/causes-of-divorce',
    staleTime: Infinity,
    useUrlQuery: false,
    customParams: {
      page: 1,
      limit: 1000,
      sort: 'order.asc',
    },
  },
  ages: {
    queryKey: ['public', 'ages'],
    apiUrl: '/sharing/common/ages',
    staleTime: Infinity,
    useUrlQuery: false,
    customParams: {
      page: 1,
      limit: 1000,
    },
  },
  banks: {
    queryKey: ['banks'],
    apiUrl: '/banks',
    axiosConfig: {
      baseURL: process.env.BANK_SERVICE_URL,
    },
    customParams: {
      limit: 1500,
    },
    staleTime: Infinity,
    select: (data: unknown) => {
      const bankData = data as IListResult<IBank>;
      const formatDocs = bankData.docs.map((doc) => ({
        ...doc,
        value: doc.bankName,
      }));
      return {
        ...bankData,
        ...Helper.convertArrayToEntities<IBank>(formatDocs),
        docs: formatDocs,
      };
    },
  },
  branchesByBank: (bankId: string) => ({
    queryKey: ['banks', bankId, 'branches'],
    apiUrl: `/banks/${bankId}/branches`,
    axiosConfig: {
      baseURL: process.env.BANK_SERVICE_URL,
    },
    staleTime: Infinity,
    customParams: {
      limit: 999,
    },
    select: (data: unknown) => {
      const branchData = data as IListResult<IBranches>;
      const formatDocs = branchData.docs.map((doc) => ({
        ...doc,
        value: doc.branchName,
      }));
      return {
        ...branchData,
        ...Helper.convertArrayToEntities<IBranches>(formatDocs),
        docs: formatDocs,
      };
    },
  }),
};

export default resourceQuery;

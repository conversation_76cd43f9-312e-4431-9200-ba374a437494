export interface IPrefecture {
  _id: string;
  value: string;
  nameEn: string;
  nameJp: string;
}

export interface ICause {
  createdAt: string;
  hasExtra: false;
  order: number;
  updatedAt: string;
  value: string;
  _id: string;
}

export interface IAge {
  _id: string;
  order: number;
  value: string;
  min: number;
  max: number;
  createdAt: string;
  updatedAt: string;
}

export interface SignedUrlItem {
  key: string;
  isPublic: boolean;
  contentType: string;
  status: string;
  originalName: string;
  originUrl: string;
  url: string;
}
export interface SignedUrlPayload {
  fileName: string;
  isPublic: boolean;
  contentType: string;
  group: string;
}

export interface SignedUrlResponse {
  key: string;
  url: string;
}

export interface IBank {
  _id: string;
  bankName: string;
  bankCode: string;
}
export interface IBranches {
  _id: string;
  branchName: string;
  branchCode: string;
}

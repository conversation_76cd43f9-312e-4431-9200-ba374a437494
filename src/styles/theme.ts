import type {
  PaletteOptions as MuiPaletteOptions,
  Shadows,
} from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { Noto_Sans_JP } from '@next/font/google';
import type React from 'react';

export const notoSans = Noto_Sans_JP({
  weight: ['400', '500', '700', '900'],
  subsets: ['latin'],
  display: 'swap',
  fallback: ['sans-serif'],
});
declare module '@mui/material/styles/createPalette' {
  export interface Palette {
    neutral4: string;
    white: string;
    backgroundColor: string;
    tertiary: string;
    heading: string;
    hint: string;
    neutral8: string;
    neutral7: string;
    neutral6: string;
    neutral3: string;
    neutral2: string;
    icon: string;
    divine: string;
    placeholder: string;
    steelBlue: string;
    latte: string;
    lemon: string;
    aliceBlue: string;
    honeydew: string;
    // New home color
    orange: string;
    gold: string;
    yellow: PaletteColorOptions;
    silver: PaletteColorOptions;
    gray: string;
  }

  export interface PaletteOptions {
    neutral4: string;
    white?: string;
    backgroundColor: string;
    tertiary: string;
    heading: string;
    hint: string;
    neutral8: string;
    neutral7: string;
    neutral6: string;
    neutral3: string;
    neutral2: string;
    icon: string;
    divine: string;
    placeholder: string;
    steelBlue: string;
    latte: string;
    lemon: string;
    aliceBlue: string;
    honeydew: string;
    // New home color
    orange: string;
    gold: string;
    yellow: PaletteColorOptions;
    silver: PaletteColorOptions;
    gray: string;
  }
}

declare module '@mui/material/styles' {
  interface Palette {
    white: string;
    button: string;
    backgroundColor: string;
    tertiary: string;
    heading: string;
    hint: string;
    neutral8: string;
    neutral7: string;
    neutral6: string;
    neutral4: string;
    neutral3: string;
    neutral2: string;
    icon: string;
    divine: string;
    placeholder: string;
  }

  // allow configuration using `createTheme`
  interface PaletteOptions {
    white?: string;
    button?: string;
    backgroundColor: string;
    tertiary: string;
    heading: string;
    hint: string;
    neutral8: string;
    neutral7: string;
    neutral6: string;
    neutral3: string;
    neutral2: string;
    icon: string;
    divine: string;
    placeholder: string;
  }
}
declare module '@mui/material/styles' {
  interface TypographyVariants {
    sectionLabel: React.CSSProperties;
  }

  // allow configuration using `createTheme`
  interface TypographyVariantsOptions {
    sectionLabel?: React.CSSProperties;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    sectionLabel: true;
  }
}
// Update the Button's color prop options
declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    white: true;
    button: true;
    neutral4: true;
    backgroundColor: true;
    yellow: true;
    silver: true;
    gray: true;
  }
  interface ButtonPropsVariantOverrides {
    whiteOutlined: true;
    tertiary: true;
    shadow: true;
  }
}

declare module '@mui/material/SvgIcon' {
  interface SvgIconPropsColorOverrides {
    neutral4: true;
    white: true;
    backgroundColor: true;
    tertiary: true;
    heading: true;
    hint: true;
    neutral8: true;
    neutral7: true;
    neutral6: true;
    neutral3: true;
    neutral2: true;
    icon: true;
    divine: true;
    placeholder: true;
  }
}

declare module '@mui/material/AppBar' {
  interface AppBarPropsColorOverrides {
    white: true;
  }
}

declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    fcol: true;
    fsf: true;
    ecol: true;
    ncol: true;
    se: true;
    mw: true;
    xtablet: true;
    ac: true;
    lmw: true;
    tablet: true;
    sl: true;
    hc: true;
    ex: true;
    xd: true;
  }
}

declare module '@mui/material/Chip' {
  interface ChipPropsVariantOverrides {
    orangeChip: true;
  }
}

const palette: MuiPaletteOptions = {
  primary: {
    main: '#FCAB28',
    contrastText: '#fff',
  },
  secondary: {
    main: '#FBCD00',
  },
  tertiary: '#ffcd00',
  error: {
    main: '#db5a42',
  },
  success: {
    main: '#51b873',
  },
  text: {
    primary: '#887f70',
  },
  heading: '#625b50',
  hint: '#b0aca2',
  white: '#fff',
  neutral8: '#2a2c2d',
  neutral7: '#54585a',
  neutral6: '#7e8487',
  icon: '#a8b0b4',
  neutral4: '#d2dce1',
  neutral3: '#dbe3e7',
  neutral2: '#e4eaed',
  divine: '#edf1f3',
  backgroundColor: '#f6f8f9',
  placeholder: '#c1bcb3',
  steelBlue: '#3D8FB8',
  latte: '#fdf6e2',
  lemon: '#fff4c7',
  aliceBlue: '#E0EEF5',
  honeydew: '#dcf4e4',
  // New home color
  orange: '#FCAB28',
  gold: '#FDC84B',
  yellow: {
    main: '#FBCD00',
    contrastText: '#464646',
  },
  silver: { main: '#CEDAE0', contrastText: '#464646' },
  gray: '#cdd9df',
  action: {
    disabledOpacity: 0.5,
  },
};

const basicTheme = createTheme({
  palette,
  breakpoints: {
    values: {
      xs: 0,
      fcol: 452,
      fsf: 464,
      mw: 560,
      sm: 600,
      se: 736,
      ecol: 752,
      xtablet: 760,
      tablet: 768,
      ncol: 848,
      md: 888,
      lmw: 900,
      xd: 910,
      ac: 1008,
      sl: 1032,
      hc: 1036,
      lg: 1136,
      ex: 1160,
      xl: 1440,
    },
  },
});

const whiteOutlinedMediumBtn = {
  border: 'solid 1px #d2dce1',
  borderRadius: '20px',
  padding: '7px 15px',
  color: '#54585a',
  fontSize: '14px',
  fontWeight: 500,
  backgroundColor: 'white',
  '& .MuiTypography-root': {
    fontWeight: 500,
  },
};

const whiteOutlinedSmallBtn = {
  border: 'solid 1px #d2dce1',
  borderRadius: '20px',
  padding: '7px 15px',
  color: '#54585a',
  fontWeight: 500,
  backgroundColor: 'white',
  '& .MuiTypography-root': {
    fontWeight: 500,
  },
  '& svg': {
    width: '20px',
    height: '20px',
  },
};

const containedMediumBtn = {
  padding: '8px 16px',
  fontWeight: 700,
  fontSize: '14px',
  '&.tabletStyle': {
    '@media (max-width: 768px)': {
      fontWeight: 500,
      paddingTop: 6,
      paddingBottom: 6,
    },
  },
  '&.shadow': {
    paddingTop: '6px',
    paddingBottom: '6px',
    borderRadius: '24px',
    border: 'solid 2 #fff',
    boxShadow: '0px 4px #e4dfdb',
    '.MuiButton-endIcon': {
      position: 'absolute',
      margin: 0,
      right: '12px',
    },
    // '@media (max-width: 768px)': {
    //   paddingTop: '9px',
    //   paddingBottom: '9px',
    //   border: 'solid 2px #fff',
    //   fontSize: 18,
    //   lineHeight: '26px',
    //   boxShadow: '0px 4px #e4dfdb',
    //   '.MuiButton-endIcon': {
    //     right: '8px',
    //   },
    //   '&.tabletStyle': {
    //     paddingTop: '6px',
    //     paddingBottom: '6px',
    //     border: 'solid 2px #fff',
    //     fontSize: 14,
    //     lineHeight: '24px',
    //     boxShadow: '0px 4px #e4dfdb',
    //     '.MuiButton-endIcon': {
    //       right: '8px',
    //     },
    //   },
    // },
  },
};

const smallInput = {
  '& .MuiOutlinedInput-input': {
    padding: '10px 12px',
    lineHeight: '20px',
    '&.MuiSelect-select': {
      minHeight: 'unset',
    },
  },
  '& .MuiSelect-icon': {
    width: '20px',
    height: '20px',
    top: 'calc(50% - 10px)',
  },
};

const smallIconBtn = {
  padding: '5px',
  svg: {
    width: '20px',
    height: '20px',
  },
};

// Create a theme instance.
const theme = createTheme({
  components: {
    MuiCssBaseline: {
      styleOverrides: () => ({
        body: {
          whiteSpace: 'inherit',
        },
      }),
    },
    MuiTab: {
      styleOverrides: {
        root: {
          '&:not(.Mui-selected)': {
            color: '#c1bcb3',
          },
          [basicTheme.breakpoints.down('tablet')]: {
            '&.tabletStyle': {
              minHeight: 'auto',
              fontSize: 12,
            },
          },
        },
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: '#e4eaed',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          backgroundColor: '#edf1f3',
          fontSize: '12px',
          fontWeight: 500,
          padding: '4px 8px',
          height: 'unset',
          borderRadius: '6px',
          '&.dark': {
            backgroundColor: '#7e8487',
            color: '#fff',
          },
          '& .MuiChip-label': {
            padding: 0,
            lineHeight: 1.33,
          },
        },
      },
      variants: [
        {
          props: { size: 'medium' },
          style: {
            [basicTheme.breakpoints.down('tablet')]: {
              '&.tabletStyle': {
                padding: '2px 4px',
                fontSize: '10px',
                borderRadius: '4px',
                '.MuiChip-label': {
                  lineHeight: 1.6,
                },
              },
            },
          },
        },
        {
          props: { variant: 'orangeChip' },
          style: {
            borderRadius: '6px',
            background: '#FDEACE',
            color: '#EF950B',
            fontSize: '12px',
            fontStyle: 'normal',
            fontWeight: 500,
            lineHeight: '16px',
            padding: '4px 8px',
          },
        },
      ],
    },
    MuiRadio: {
      styleOverrides: {
        root: {
          padding: '8px',
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: '16px',
          margin: 0,
          '&:not(.MuiDialog-paperFullScreen)': {
            width: 'calc(100% - 32px)',
          },
        },
      },
    },
    MuiDialogContentText: {
      styleOverrides: {
        root: {
          color: '#887f70',
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          backgroundColor: '#f6f8f9',
          padding: 0,
          '&:not(.Mui-error):not(.Mui-disabled)': {
            '&:hover': {
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#f5af3c',
              },
            },
          },
          '&.Mui-disabled': {
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: '#f6f8f9',
            },
          },
          '& .MuiSelect-icon': {
            width: '24px',
            height: '24px',
            right: '12px',
            top: 'calc(50% - 12px)',
          },
        },
        input: {
          padding: '12px 16px',
          fontSize: 16,
          lineHeight: '24px',
          height: 'unset',
          borderRadius: '8px',
          '&.Mui-disabled': {
            backgroundColor: '#f6f8f9',
            color: 'rgba(193, 188, 179, 0.5)',
            WebkitTextFillColor: 'unset',
            '+ .MuiInputAdornment-root': {
              opacity: 0.5,
            },
          },
        },
        notchedOutline: {
          padding: 0,
          borderWidth: '2px',
          borderColor: '#f6f8f9',
        },
      },
      variants: [
        {
          props: {
            size: 'small',
          },
          style: smallInput,
        },
        {
          props: {
            size: 'medium',
          },
          style: {
            [basicTheme.breakpoints.down('tablet')]: {
              '&.tabletStyle': smallInput,
            },
          },
        },
      ],
    },
    // MuiLink-underlineHover
    MuiLink: {
      styleOverrides: {
        underlineHover: {
          '&:hover': {
            textUnderlineOffset: '2px',
          },
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          '&.whiteOutlined': {
            border: 'solid 1px #d2dce1',
            backgroundColor: '#fff',
            '&:hover': {
              backgroundColor: '#edf1f3',
            },
            '&.Mui-disabled>svg': {
              color: '#d2dce1',
              cursor: 'not-allowed',
            },
          },
        },
      },
      variants: [
        {
          props: {
            size: 'small',
          },
          style: smallIconBtn,
        },
        {
          props: {
            size: 'medium',
          },
          style: {
            padding: '7px',
            [basicTheme.breakpoints.down('tablet')]: {
              '&.tabletStyle': smallIconBtn,
            },
          },
        },
        {
          props: {
            size: 'large',
          },
          style: {
            padding: '15px',
            [basicTheme.breakpoints.down('tablet')]: {
              '&.tabletStyle': {
                padding: '7px',
              },
              '&.responsiveSmallTablet': smallIconBtn,
            },
          },
        },
      ],
    },
    MuiSvgIcon: {
      variants: [
        {
          props: {
            fontSize: 'medium',
          },
          style: {
            width: '24px',
            height: '24px',
            '&.tabletStyle': {
              [basicTheme.breakpoints.down('tablet')]: {
                width: '20px',
                height: '20px',
              },
            },
          },
        },
        {
          props: {
            fontSize: 'large',
          },
          style: {
            width: '32px',
            height: '32px',
            '&.tabletStyle': {
              [basicTheme.breakpoints.down('tablet')]: {
                width: '28px',
                height: '28px',
              },
            },
          },
        },
      ],
    },
    MuiButton: {
      styleOverrides: {
        root: {
          boxShadow: 'none',
          fontSize: '14px',
          borderRadius: '28px',
          lineHeight: '24px',
          '&.MuiButton-whiteOutlined': {
            '& svg': {
              color: '#54585a',
            },
          },
          '&.Mui-disabled': {
            color: '#a8b0b4',
            backgroundColor: '#e4eaed',
            boxShadow: 'none',
            '& svg': {
              color: '#a8b0b4',
            },
          },
        },
        startIcon: {
          marginLeft: 0,
          '& .MuiIcon-fontSizeMedium': {
            fontSize: '24px',
          },
        },
      },
      variants: [
        {
          props: {
            variant: 'text',
            size: 'medium',
          },
          style: {
            fontSize: '14px',
            padding: '8px 16px',
            '& .MuiButton-endIcon': {
              marginLeft: 0,
              svg: {
                width: 20,
                height: 20,
              },
            },
          },
        },
        {
          props: {
            variant: 'contained',
            size: 'medium',
          },
          style: {
            ...containedMediumBtn,

            '&.tabletStyle': {
              [basicTheme.breakpoints.down('tablet')]: {
                ...containedMediumBtn,
                padding: '6px 12px',
                fontSize: '14px',
                lineHeight: 'normal',
                '.MuiButton-startIcon': {
                  marginRight: '4px',
                },
                svg: {
                  width: '20px',
                  height: '20px',
                },
              },
            },
          },
        },
        {
          props: {
            variant: 'contained',
            size: 'large',
          },
          style: {
            height: 56,
            fontSize: 14,
            borderRadius: 30,
            fontWeight: 700,
            lineHeight: 'normal',
            paddingLeft: 16,
            paddingRight: 16,
            '.MuiButton-endIcon': {
              position: 'absolute',
              right: 12,
              marginRight: 0,
            },
            '&.shadow': {
              fontSize: 22,
              paddingTop: 10,
              paddingBottom: 13,
              height: 59,
              '&.tabletStyle': {
                [basicTheme.breakpoints.down('tablet')]: {
                  paddingTop: 6,
                  paddingBottom: 6,
                  fontSize: 14,
                  '.MuiButton-endIcon': {
                    position: 'absolute',
                    right: 9,
                    marginRight: 0,
                    svg: {
                      width: 24,
                      height: 24,
                    },
                  },
                },
              },
            },
            '&.tabletStyle': {
              [basicTheme.breakpoints.down('tablet')]: {
                height: 40,
                paddingTop: 8,
                paddingBottom: 8,
                fontSize: 14,
                '.MuiButton-endIcon': {
                  position: 'absolute',
                  right: 10,
                  marginRight: 0,
                },
              },
            },
          },
        },
        {
          props: {
            variant: 'contained',
          },
          style: () => ({
            color: '#464646',
            '&.shadow': {
              border: 'solid 2px #fff',
              boxShadow: '0px 4px #e4dfdb',
            },
            '&.Mui-disabled': {
              color: '#625B50',
              backgroundColor: '#CEDAE0',
              boxShadow: 'none',
              '& svg': {
                color: '#625B50',
              },
            },
          }),
        },
        {
          props: {
            variant: 'contained',
            color: 'secondary',
          },
          style: () => ({
            '&:hover': {
              backgroundColor: theme.palette.secondary.main,
            },
          }),
        },
        {
          props: {
            variant: 'outlined',
          },
          style: ({ theme: _theme }) => ({
            borderRadius: '30px',
            border: `solid 2px ${_theme.palette.gold}`,
            lineHeight: '30px',
            '&.shadow': {
              boxShadow: '0px 4px #e4dfdb',
            },
            '&:hover': {
              border: `solid 2px ${_theme.palette.gold}`,
            },
          }),
        },
        {
          props: {
            variant: 'contained',
            size: 'small',
          },
          style: {
            fontSize: 14,
            fontWeight: 700,
            paddingTop: 8,
            paddingBottom: 8,
            '&.shadow': {
              paddingTop: 6,
              paddingBottom: 6,
            },
          },
        },
        {
          props: {
            variant: 'outlined',
            color: 'primary',
            size: 'medium',
          },
          style: ({ theme: _theme }) => ({
            borderRadius: '20px',
            height: 40,
            fontSize: 14,
            paddingLeft: 14,
            paddingRight: 14,
            fontWeight: 700,
            '.MuiButton-endIcon': {
              position: 'absolute',
              margin: 0,
              right: '10px',
            },
          }),
        },
        {
          props: {
            variant: 'outlined',
            size: 'large',
          },
          style: ({ theme: _theme }) => ({
            height: 56,
            borderRadius: '30px',
            fontSize: 14,
            paddingTop: 10,
            paddingBottom: 13,
            fontWeight: 700,
            '&.shadow': {
              height: 59,
              fontSize: 22,
            },
            '.MuiButton-endIcon': {
              position: 'absolute',
              margin: 0,
              right: '10px',
            },
            '@media (max-width: 768px)': {
              '&.tabletStyle': {
                borderRadius: '20px',
                height: 40,
                fontSize: 14,
                paddingLeft: 14,
                paddingRight: 14,
                paddingTop: 6,
                paddingBottom: 6,
                fontWeight: 700,
                '.MuiButton-endIcon': {
                  right: '9px',
                  svg: {
                    width: 24,
                    height: 24,
                  },
                },
              },
            },
          }),
        },
        {
          props: {
            variant: 'whiteOutlined',
            size: 'small',
          },
          style: {
            border: 'solid 1px #d2dce1',
            borderRadius: '20px',
            padding: '7px 15px',
            color: '#54585a',
            backgroundColor: 'white',
            '& svg': {
              width: '20px',
              height: '20px',
            },
          },
        },
        {
          props: {
            variant: 'whiteOutlined',
            size: 'medium',
          },
          style: {
            ...whiteOutlinedMediumBtn,
            [basicTheme.breakpoints.down('tablet')]: {
              '&.tabletStyle': {
                border: 'solid 1px #d2dce1',
                borderRadius: '20px',
                padding: '5px 11px',
                color: '#54585a',
                backgroundColor: 'white',
                fontWeight: 500,
                '.MuiButton-startIcon': {
                  marginRight: '4px',
                },
                '& svg': {
                  width: '20px',
                  height: '20px',
                },
              },
            },
          },
        },
        {
          props: {
            variant: 'whiteOutlined',
            size: 'large',
          },
          style: {
            border: 'solid 1px #d2dce1',
            borderRadius: '28px',
            padding: '15px 15px',
            color: '#54585a',
            backgroundColor: 'white',
            fontWeight: 'bold',
            [basicTheme.breakpoints.down('tablet')]: {
              '&.tabletStyle': whiteOutlinedMediumBtn,
              '&.responsiveSmallTablet': whiteOutlinedSmallBtn,
            },
          },
        },
        {
          props: {
            variant: 'tertiary',
          },
          style: {
            color: basicTheme.palette.primary.main,
            backgroundColor: '#fdf3d8',
            '&:hover': {
              backgroundColor: '#FCEDC5',
            },
            '&.Mui-disabled': {
              color: '#a8b0b4',
              backgroundColor: '#e4eaed',
              boxShadow: 'none',
            },
          },
        },
        {
          props: {
            variant: 'tertiary',
            size: 'medium',
          },
          style: {
            paddingTop: '8px',
            paddingBottom: '8px',
            [basicTheme.breakpoints.down('tablet')]: {
              '&.tabletStyle': {
                fontSize: '14px',
                padding: '6px 12px',
                lineHeight: 'normal',
                '& .MuiButton-startIcon': {
                  marginRight: '4px',
                },
                svg: {
                  width: '20px',
                  height: '20px',
                },
              },
            },
          },
        },
      ],
    },
    MuiTypography: {
      styleOverrides: {
        root: ({ ownerState }) => {
          if (ownerState.className?.includes('40-to-28')) {
            return {
              fontSize: 40,
              [basicTheme.breakpoints.down('tablet')]: {
                fontSize: 28,
              },
            };
          }
          if (ownerState.className?.includes('32-to-24')) {
            return {
              fontSize: 32,
              [basicTheme.breakpoints.down('tablet')]: {
                fontSize: 24,
              },
            };
          }
          if (ownerState.className?.includes('28-to-20')) {
            return {
              fontSize: 28,
              [basicTheme.breakpoints.down('tablet')]: {
                fontSize: 20,
              },
            };
          }
          if (ownerState.className?.includes('24-to-18')) {
            return {
              fontSize: 24,
              [basicTheme.breakpoints.down('tablet')]: {
                fontSize: 18,
              },
            };
          }
          if (ownerState.className?.includes('18-to-16')) {
            return {
              fontSize: 18,
              [basicTheme.breakpoints.down('tablet')]: {
                fontSize: 16,
              },
            };
          }
          if (ownerState.className?.includes('16-to-14')) {
            return {
              fontSize: 16,
              [basicTheme.breakpoints.down('tablet')]: {
                fontSize: 14,
              },
            };
          }
          if (ownerState.className?.includes('14-to-12')) {
            return {
              fontSize: 14,
              [basicTheme.breakpoints.down('tablet')]: {
                fontSize: 12,
              },
            };
          }
          if (ownerState.className?.includes('12-to-10')) {
            return {
              fontSize: 12,
              [basicTheme.breakpoints.down('tablet')]: {
                fontSize: 10,
              },
            };
          }
          return {};
        },
      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 44,
          height: 24,
          padding: 0,
          '& .MuiSwitch-switchBase': {
            padding: 0,
            margin: 2,
            transitionDuration: '300ms',
            '&.Mui-checked': {
              transform: 'translateX(20px)',
              color: '#fff',
              '& + .MuiSwitch-track': {
                backgroundColor: basicTheme.palette.primary.main,
                opacity: 1,
                border: 0,
              },
              '&.Mui-disabled + .MuiSwitch-track': {
                opacity: 0.5,
              },
            },
            '&.Mui-focusVisible .MuiSwitch-thumb': {
              color: '#33cf4d',
              border: '6px solid #fff',
            },
          },
          '& .MuiSwitch-thumb': {
            boxSizing: 'border-box',
            width: 20,
            height: 20,
          },
          '& .MuiSwitch-track': {
            borderRadius: 26 / 2,
            backgroundColor: '#d2dce1',
            opacity: 1,
          },
        },
      },
    },
  },
  palette,
  typography: {
    fontFamily: notoSans.style.fontFamily,
    allVariants: {
      lineHeight: 'normal',
      whiteSpace: 'pre-line',
      wordBreak: 'break-word',
    },
    sectionLabel: {
      fontWeight: 'bold',
      fontSize: 32,
      color: basicTheme.palette.primary.main,
      lineHeight: '47px',
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      fcol: 452,
      fsf: 464,
      mw: 560,
      sm: 600,
      se: 736,
      ecol: 752,
      xtablet: 760,
      tablet: 768,
      lmw: 800,
      ncol: 848,
      md: 888,
      xd: 910,
      ac: 1008,
      sl: 1032,
      hc: 1036,
      lg: 1136,
      ex: 1160,
      xl: 1440,
    },
  },
  shadows: [
    ...createTheme({}).shadows.map((shadow, i) =>
      i === 0 ? '0px 2px 8px 0px rgba(0, 0, 0, 0.1);' : shadow,
    ),
  ] as Shadows,
  shape: {
    borderRadius: 8,
  },
});

export default theme;

*, *:before, *:after {
  -webkit-box-sizing: border-box; 
  -moz-box-sizing: border-box; 
  box-sizing: border-box;
}

.pointer, a {
  cursor: pointer;
}

a {
  text-decoration: none;
}

.divider {
  margin-left: auto;
  margin-right: auto;
  background: linear-gradient(to right, #fdc84b 50%, #cedae0 50%);
  width: 104px;
  height: 4px;
}

.logo-divider {
  margin-left: auto;
  margin-right: auto;
  background: linear-gradient(to right, #fdc84b 50%, #cedae0 50%);
  width: 144px;
  height: 8px;
}

.scale {
  transition: all 0.2s ease-in-out;
}
.scale:hover {
  transform: scale(1.04)
}

.display-webkit {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}

.two-line {
  -webkit-line-clamp: 2;
}

.three-line {
  -webkit-line-clamp: 3;
}

.four-line {
  -webkit-line-clamp: 4;
}

.five-line {
  -webkit-line-clamp: 5;
}

.break-word {
  word-break: break-word;
}

.break-all {
  word-break: break-all;
}

.fit-content {
  width: fit-content;
}

.inline-block {
  display: inline-block;
}

/** Classes for the displayed toast **/
.Toastify__toast {
  padding: 16px;
  border-radius: 8px;
}

.Toastify__toast-body {
  padding: 0;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/** Used to position the icon **/
.Toastify__toast-icon {
  width: 24px;
}

@media only screen and (max-width: 768px) {
  .logo-divider {
    width: 102px;
    height: 6px;
  }
}

@media only screen and (max-width: 480px) {
  .Toastify__toast-container {
    padding: 8px;
  }
  .logo-divider {
    width: 102px;
    height: 6px;
  }
}

:root {
  --toastify-toast-width: 360px;
  --toastify-color-success: #51b873;
  --toastify-color-error: #db5a42;
}

.truncate-text__ellipsis{
  color: #FCAB28;
  cursor: pointer;
}
import type { SxProps, Theme } from '@mui/material/styles';

const styles: Record<string, SxProps<Theme>> = {
  container: {
    py: 2,
    '& .mypage-menu': {
      mt: 2,
    },
  },
  logoutButton: {
    mt: 2,
    p: 4,
    borderRadius: 2,
    bgcolor: 'white',
    color: 'heading',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      bgcolor: 'rgba(0, 0, 0, 0.04)',
    },
  },
};

export default styles;

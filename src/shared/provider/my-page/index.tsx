import { Box, Container, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import MyPageMenu from 'components/UI/MyPageMenu';
import useAuth from 'hooks/useAuth';
import useGlobalState from 'hooks/useGlobalState';
import { t } from 'i18n';
import {
  AddEmailIcon,
  MyPageAppointmentIcon,
  MyPageProfileIcon,
  MyPageShiftManagementIcon,
} from 'icons';
import { ProviderType } from 'utils/constants';

import styles from './styles';

const lawyerMenus = [
  {
    path: '/lawyer/my-page/cases',
    label: t('mypage.cases'),
    icon: <MyPageAppointmentIcon />,
  },
  {
    path: '/lawyer/my-page/profile',
    label: t('mypage.profile'),
    icon: <MyPageProfileIcon />,
  },
  {
    path: '/lawyer/my-page/mail-setting',
    label: 'メール送信先設定',
    icon: <AddEmailIcon />,
  },
];

const counselorMenus = [
  {
    path: '/counselor/my-page/cases',
    label: t('mypage.cases'),
    icon: <MyPageAppointmentIcon />,
  },
  {
    path: '/counselor/my-page/shift',
    label: 'シフト設定',
    icon: <MyPageShiftManagementIcon />,
  },
  {
    path: '/counselor/my-page/profile',
    label: t('mypage.profile'),
    icon: <MyPageProfileIcon />,
  },
];

const ProviderMyPage = ({ providerType }: { providerType: ProviderType }) => {
  const { logOut } = useAuth();
  const { setConfirmModal } = useGlobalState();

  const handleLogout = () => {
    logOut();
  };
  return (
    <Container disableGutters maxWidth="lg" sx={styles.container}>
      <Box p="16px 32px" borderRadius={2} bgcolor="white">
        <Breadcrumbs omitIndexList={[0]} />
        <Typography fontSize={32} fontWeight="bold" mt="12px" color="heading">
          {t('header.mypage')}
        </Typography>
      </Box>

      <MyPageMenu
        menu={
          providerType === ProviderType.LAWYER ? lawyerMenus : counselorMenus
        }
      />
      <Box
        sx={styles.logoutButton}
        className="pointer"
        onClick={() =>
          setConfirmModal({
            title: 'logout.title',
            onConfirm: handleLogout,
            content: 'logout.message',
          })
        }
      >
        <Typography fontWeight={500}>{t('global.logout')}</Typography>
      </Box>
    </Container>
  );
};

export default ProviderMyPage;

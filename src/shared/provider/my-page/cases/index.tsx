import { Box, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import CaseTag from 'components/Case/CaseTag';
import CustomTable from 'components/CustomTable';
import dayjs from 'dayjs';
import { useFetchList } from 'hooks';
import { t } from 'i18n';
import type { MRT_ColumnDef } from 'material-react-table';
import type { IProviderCaseListItem } from 'models/case/interface';
import caseQuery from 'models/case/query';
import { useRouter } from 'next/router';
import React, { useMemo } from 'react';
import {
  CaseStatusType,
  MomentFormat,
  PROVIDER_MY_PAGE_PATH,
  ProviderType,
} from 'utils/constants';

const ProviderCaseList = ({ providerType }: { providerType: ProviderType }) => {
  const { push } = useRouter();
  const {
    list = [],
    total,
    isFetching,
    isLoading,
  } = useFetchList<IProviderCaseListItem>(caseQuery.providerCaseList);

  const isLawyer = providerType === ProviderType.LAWYER;
  const columns: MRT_ColumnDef<IProviderCaseListItem>[] = useMemo(
    () => [
      {
        accessorKey: '_id',
        header: t('caseList.caseId'),
        enableSorting: false,
      },
      {
        accessorKey: 'consumer.fullName',
        header: t('caseList.customerName'),
        enableSorting: false,
      },
      {
        accessorKey: 'finalizedDate',
        header: t('caseList.finalizedDate'),
        enableSorting: false,
        Cell: ({ cell }) =>
          cell.getValue() ? (
            <Typography fontSize={{ xs: 14, lg: 16 }}>
              {`${dayjs(cell.getValue<string>()).format(
                MomentFormat.JP_YEAR_MONTH_DATE_DAY,
              )}
              ${dayjs(cell.getValue<string>()).format(
                MomentFormat.JP_HOUR_MINUTE,
              )}`}
            </Typography>
          ) : null,
      },
      {
        accessorKey: 'duration',
        header: 'オンライン面談時間',
        enableSorting: false,
        Cell: ({ cell }) => (
          <Typography fontSize={{ tablet: 14, lg: 16 }}>
            {cell.getValue<string>() && `${cell.getValue<string>()}分間`}
          </Typography>
        ),
      },
      {
        accessorKey: 'status',
        header: t('caseList.status'),
        enableSorting: false,
        Cell: ({ cell }) => (
          <CaseTag status={cell.getValue<CaseStatusType>()} />
        ),
      },
    ],
    [],
  );
  const columnsTable = isLawyer
    ? columns.filter((item) => item.accessorKey !== 'duration')
    : columns;

  return (
    <Box>
      <Box bgcolor="white" px={4} py={2} borderRadius={2}>
        <Breadcrumbs omitIndexList={[0]} />
        <Typography fontWeight="bold" fontSize={32} color="heading" mt="11px">
          {t('caseList.title')}
        </Typography>
      </Box>
      <CustomTable
        total={total}
        isFetching={isFetching}
        isLoading={isLoading}
        data={list}
        columns={columnsTable}
        onRowClick={(row) => {
          if (
            row.original.status === CaseStatusType.WAITING_ANWSER_FROM_PROVIDER
          ) {
            push(
              `${PROVIDER_MY_PAGE_PATH[providerType]}/my-page/cases/${row.id}/confirm`,
            );
          } else
            push(
              `${PROVIDER_MY_PAGE_PATH[providerType]}/my-page/cases/${row.id}`,
            );
        }}
      />
    </Box>
  );
};

export default ProviderCaseList;

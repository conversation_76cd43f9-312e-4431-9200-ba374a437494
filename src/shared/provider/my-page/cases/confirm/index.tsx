import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Stack, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import CaseTag from 'components/Case/CaseTag';
import ConfirmModal from 'components/ConfirmModal/modal';
import type { IBlock } from 'components/Customer/Profile/blocks';
import {
  divorceBlock,
  marriageBlock,
  partnerBlock,
  profileBlock,
  propertyBlock,
} from 'components/Customer/Profile/blocks';
import { TextField } from 'components/Form';
import Label from 'components/Form/Label';
import BackButton from 'components/UI/BackButton';
import ViewEditBlock from 'components/UI/ViewEditBlock';
import type { Dayjs } from 'dayjs';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import useMutate from 'hooks/useMutate';
import i18n, { t } from 'i18n';
import { ChatIcon, KidIcon } from 'icons';
import { get, isEmpty, isUndefined } from 'lodash';
import type { IProviderCaseDetail } from 'models/case/interface';
import caseQuery from 'models/case/query';
import chatQuery from 'models/chat';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import type { GenderType, ISchoolType } from 'utils/constants';
import {
  Gender,
  PROVIDER_MY_PAGE_PATH,
  ProviderType,
  RadioOptions,
  SchoolTypeOptions,
} from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

import styles from './styles';

const lawyerCancelSchema = object({
  cancelReason: string()
    .max(200, i18n.t('validation.maxLength', { number: 200 }))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
});

const counselorCancelSchema = object({
  cancelReason: string()
    .required(t('validation.requiredField'))
    .max(200, i18n.t('validation.maxLength', { number: 200 }))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
});

// Confirm case that has a first meeting
const ProviderConfirmCasePage = ({
  providerType,
}: {
  providerType: ProviderType;
}) => {
  const [openCancelModal, setOpenCancelModal] = useState(false);
  const { query, replace } = useRouter();
  const caseId = query.caseId as string;
  const isLawyer = providerType === ProviderType.LAWYER;
  const customerBlockProfile = isLawyer
    ? profileBlock
    : {
        title: profileBlock.title,
        icon: profileBlock.icon,
        fields: profileBlock.fields.filter(
          (item) => item.path !== 'email' && item.path !== 'phone',
        ),
      };

  const { data: currentUser } = useFetchUser({ enabled: false });
  const { mutateAsync: createRoom, isLoading: isCreatingRoom } = useMutate<
    unknown,
    { roomId: string }
  >(chatQuery.createRoom);
  const { detail: caseDetail } = useFetchDetail<IProviderCaseDetail>({
    ...caseQuery.providerCaseDetail(caseId),
    enabled: false,
  });

  const { detail: customerConsultation } = useFetchDetail<ConsultationRecord>(
    apiQuery.lawyerCustomerConsultation(caseDetail.consumer._id),
  );

  const { mutateAsync: declineCase, isLoading: isDeclining } = useMutate(
    caseQuery.declineCase(caseId),
  );
  const { mutateAsync: acceptCase, isLoading: isAccepting } = useMutate(
    caseQuery.acceptCase(caseId),
  );

  const { detail: customerProfile } = useFetchDetail<CustomerData>(
    apiQuery.lawyerCustomerProfile(caseDetail.consumer._id),
  );

  const handleAcceptCase = () => {
    acceptCase(null, {
      onSuccess: () =>
        replace(
          `${PROVIDER_MY_PAGE_PATH[providerType]}/my-page/cases/${caseId}`,
        ),
    });
  };

  const handleDeclineCase = (values: { cancelReason?: string }) => {
    setOpenCancelModal(false);
    declineCase(values, {
      onSuccess: () =>
        replace(
          `${PROVIDER_MY_PAGE_PATH[providerType]}/my-page/cases/${caseId}`,
        ),
    });
  };

  const handleCreateRoom = () => {
    if (caseDetail.roomInfo?.roomId) {
      setTimeout(() =>
        window.open(
          `${PROVIDER_MY_PAGE_PATH[providerType]}/messages/${caseDetail.roomInfo?.roomId}`,
          '_blank',
        ),
      );
    } else {
      createRoom(
        {
          type: 'ONE_BY_ONE_HAS_CASE',
          memberIds: [
            `CONSUMER_${caseDetail.consumer._id}`,
            `${providerType}_${currentUser?._id}`,
          ],
        },
        {
          onSuccess: ({ roomId }) =>
            setTimeout(() =>
              window.open(
                `${PROVIDER_MY_PAGE_PATH[providerType]}/messages/${roomId}`,
                '_blank',
              ),
            ),
        },
      );
    }
  };

  const kids = customerConsultation?.kids?.kids;

  const childrenFields = useMemo(
    () =>
      (kids || []).map((_: unknown, index: number) => [
        { label: t('consultationRecord.kidOrder', { number: index + 1 }) },
        {
          label: t('consultationRecord.kidGender'),
          path: `kids.kids[${index}].gender`,
          renderValue: (value: GenderType) => Gender[value],
        },
        {
          label: t('consultationRecord.kidAge'),
          path: `kids.kids[${index}].age`,
          renderValue: (value: Dayjs) =>
            !isUndefined(value) &&
            t('consultationRecord.ageNumber', { number: value }),
        },
        {
          label: t('consultationRecord.schoolType'),
          path: `kids.kids[${index}].schoolType`,
          renderValue: (value: ISchoolType) =>
            value && SchoolTypeOptions[value],
        },
      ]),
    [kids],
  );

  const kidsFields: IBlock = {
    title: t('consultationRecord.kidInformation'),
    icon: <KidIcon />,
    fields: [
      {
        label: t('consultationRecord.kids'),
        path: 'kids.kids',
        renderValue: (value: unknown) =>
          value && (
            <>
              <Typography>
                {isEmpty(value) ? RadioOptions.no : RadioOptions.yes}
              </Typography>
              <Typography fontSize={14} mt={1} color="hint">
                {t('consultationRecord.under18')}
              </Typography>
            </>
          ),
      },
    ].concat(childrenFields.flat() as never),
  };

  const { control, handleSubmit, resetField } = useForm<
    InferType<typeof lawyerCancelSchema>
  >({
    resolver: yupResolver(lawyerCancelSchema),
    values: {
      cancelReason: '',
    },
  });

  const {
    control: counselorControl,
    handleSubmit: handleCounselorSubmit,
    resetField: resetCounselorField,
  } = useForm<InferType<typeof counselorCancelSchema>>({
    resolver: yupResolver(counselorCancelSchema),
    values: {
      cancelReason: `この度は面談のリクエストをいただきありがとうございました。
      \n大変申し訳ないのですが、リクエストいただいた面談日時の都合が合わないため面談をキャンセルさせていただきました。
      \nお手数をお掛けいたしますが、改めて別日でリクエストをいただけますと幸いです。引き続きよろしくお願いいたします。`,
    },
  });

  useEffect(() => {
    if (openCancelModal) {
      resetField('cancelReason', { keepError: false });
      resetCounselorField('cancelReason', { keepError: false });
    }
  }, [openCancelModal, resetCounselorField, resetField]);
  return (
    <div>
      <Box borderRadius={2} p="16px 32px" bgcolor="white">
        <Breadcrumbs
          omitIndexList={[0, 3]}
          transformLabel={{
            [`${PROVIDER_MY_PAGE_PATH[providerType]}/my-page/cases/[caseId]/confirm`]:
              get(caseDetail, 'consumer.fullName'),
          }}
        />
        <Box display="flex" justifyContent="space-between" mt="12px">
          <Stack direction="row" flex={1}>
            <Box m="3px 4px 0px -4px">
              <BackButton
                onClick={() =>
                  replace(
                    `${PROVIDER_MY_PAGE_PATH[providerType]}/my-page/cases`,
                  )
                }
              />
            </Box>
            <Box>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography fontSize={32} fontWeight="bold" color="heading">
                  {caseDetail.consumer.fullName}
                </Typography>
                <Box>
                  <CaseTag status={caseDetail.status} />
                </Box>
              </Stack>
              {isLawyer ? (
                <Typography fontSize={14} mt="1px">
                  {caseDetail.consumer.katakanaName} &nbsp;• &nbsp;
                  {caseDetail.consumer.email}
                </Typography>
              ) : (
                <Typography fontSize={14} mt="1px">
                  {caseDetail.consumer.katakanaName}
                </Typography>
              )}
            </Box>
          </Stack>
          <Box>
            <LoadingButton
              variant="tertiary"
              loading={isCreatingRoom}
              fullWidth
              startIcon={<ChatIcon />}
              color="primary"
              className="tabletStyle"
              onClick={handleCreateRoom}
            >
              チャットする
            </LoadingButton>
          </Box>
        </Box>
      </Box>
      <ViewEditBlock
        formId="profile-block"
        detail={customerProfile}
        block={customerBlockProfile}
        hideEditModeButton
      />
      <ViewEditBlock
        detail={customerConsultation}
        block={divorceBlock}
        formId="divorce-block"
        hideEditModeButton
      />

      <ViewEditBlock
        detail={customerConsultation}
        block={partnerBlock}
        formId="partner-block"
        hideEditModeButton
      />

      <ViewEditBlock
        detail={customerConsultation}
        block={marriageBlock}
        formId="marriage-block"
        hideEditModeButton
      />

      <ViewEditBlock
        detail={customerConsultation}
        block={propertyBlock}
        formId="property-block"
        hideEditModeButton
      />

      <ViewEditBlock
        detail={customerConsultation}
        block={kidsFields}
        formId="kid-block"
        hideEditModeButton
      />
      <Box sx={styles.confirmWrapper}>
        <Typography color="heading">{t('confirmCase.confirmText')}</Typography>
        <Box flex={1}>
          <Box display="flex" justifyContent="right">
            <LoadingButton
              variant="outlined"
              fullWidth
              sx={{ width: 112, ml: 1 }}
              onClick={() => setOpenCancelModal(true)}
              loading={isDeclining}
            >
              {t('confirmCase.decline')}
            </LoadingButton>
            <LoadingButton
              variant="contained"
              color="secondary"
              fullWidth
              sx={{ width: 112, ml: 1 }}
              loading={isAccepting}
              onClick={handleAcceptCase}
            >
              {t('confirmCase.accept')}
            </LoadingButton>
          </Box>
        </Box>
      </Box>
      <ConfirmModal
        open={openCancelModal}
        title={
          providerType === ProviderType.COUNSELOR
            ? '面談キャンセル'
            : '案件終了'
        }
        confirmText="確定"
        onCancel={() => setOpenCancelModal(false)}
        confirmButtonProps={{ form: 'cancel-meeting', type: 'submit' }}
        content={
          <Box>
            {providerType === ProviderType.COUNSELOR ? (
              <Typography>
                この面談リクエストをキャンセルすると{' '}
                <Typography
                  color="text.primary"
                  fontWeight={700}
                  component="span"
                  fontSize={14}
                >
                  {caseDetail.consumer.fullName}
                </Typography>
                様の案件は終了します。{'\n'}(
                <Typography
                  color="text.primary"
                  fontWeight={700}
                  component="span"
                  fontSize={14}
                >
                  {caseDetail.consumer.fullName}
                </Typography>
                様から再度面談リクエストが届いた場合、面談を作成できるようになります。)
              </Typography>
            ) : (
              <Typography fontSize={14} color="text.primary" textAlign="center">
                <Typography
                  color="text.primary"
                  fontWeight={700}
                  component="span"
                  fontSize={14}
                >
                  {caseDetail.consumer.fullName}
                </Typography>
                様の案件を終了します。{'\n'}（
                <Typography
                  color="text.primary"
                  fontWeight={700}
                  component="span"
                  fontSize={14}
                >
                  {caseDetail.consumer.fullName}
                </Typography>
                様から再度面談リクエストが届いた場合、面談を作成できるようになります。)
              </Typography>
            )}
            <Box mt={2}>
              <form
                id="cancel-meeting"
                onSubmit={
                  providerType === ProviderType.COUNSELOR
                    ? handleCounselorSubmit(handleDeclineCase)
                    : handleSubmit(handleDeclineCase)
                }
              >
                <Label
                  labelCol={12}
                  label="キャンセル理由"
                  required={providerType === ProviderType.COUNSELOR}
                />
                {providerType === ProviderType.COUNSELOR ? (
                  <TextField
                    labelCol={0}
                    name="cancelReason"
                    control={counselorControl}
                    maxLength={200}
                    multiline
                    minRows={7}
                    placeholder="キャンセル理由"
                  />
                ) : (
                  <TextField
                    labelCol={0}
                    name="cancelReason"
                    control={control}
                    maxLength={200}
                    multiline
                    minRows={7}
                    placeholder="キャンセル理由"
                  />
                )}
              </form>
            </Box>
          </Box>
        }
      />
    </div>
  );
};

export default ProviderConfirmCasePage;

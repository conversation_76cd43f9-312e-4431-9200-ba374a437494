import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, SvgIcon, Typography } from '@mui/material';
import type { IDataOfRadio } from 'components/Form/Radio';
import useBreakpoint from 'hooks/useBreakpoint';
import useGlobalState from 'hooks/useGlobalState';
import { InfoIcon } from 'icons';
import { ConsultationMenuMeetingType } from 'utils/constants';
import Helper from 'utils/helpers';

interface Props {
  value: string;
  _id: string;
  title: string;
  content?: string;
  createdAt: string;
  updatedAt: string;
  unitPrices: [
    {
      duration: number;
      price: number;
    },
  ];
  meetingType?: string;
}

const OutlineMenu = <T extends Props>(data: T[]): IDataOfRadio[] => {
  const isBreakpoint = useBreakpoint({});
  const formattedMenuListRadioList = Helper.convertArrayToOptions(data, [
    { key: '_id' },
    { key: 'value' },
    {
      alias: 'description',
      getValue: (value) => value?.meetingType as string,
    },
    {
      getValue: (value) => {
        return value?.unitPrices?.[0]?.duration as unknown as string;
      },
      alias: 'duration',
    },
    {
      getValue: (value) => {
        return value?.unitPrices?.[0]?.price as unknown as string;
      },
      alias: 'price',
    },
    {
      key: 'content',
    },
    {
      key: 'meetingType',
    },
  ]);
  const { setConfirmModal } = useGlobalState();

  return formattedMenuListRadioList?.map((item) => {
    return {
      ...item,
      meetingType: item.meetingType,
      description: !isBreakpoint ? (
        `相談タイプ：${
          ConsultationMenuMeetingType[
            `${
              item?.description || ''
            }` as keyof typeof ConsultationMenuMeetingType
          ] as string
        }`
      ) : (
        <>
          <span>{`相談タイプ：${
            ConsultationMenuMeetingType[
              `${
                item?.description || ''
              }` as keyof typeof ConsultationMenuMeetingType
            ] as string
          }`}</span>
          <Stack direction="row" gap={1} mt={1}>
            <Typography color="#FFA700" fontWeight={700}>
              {`${Helper.addComma(item?.price as string | number)}円` as string}
            </Typography>
            <Typography color="#FFA700" fontWeight={700}>
              {item.duration}分
            </Typography>
          </Stack>
        </>
      ),
      endAdornment: !isBreakpoint ? (
        <>
          <span>{`${item?.duration}分`}</span>
          <span
            style={{
              marginLeft: 8,
              marginRight: 8,
            }}
          >
            {`${Helper.addComma(item?.price as string | number)}円` as string}
          </span>
          <IconButton
            sx={{
              zIndex: 1,
              p: '2px',
              svg: { color: 'icon' },
            }}
            onClick={() => {
              setConfirmModal({
                icon: 'info',
                onConfirm: () => {},
                content: item?.content ? item?.content : '内容がありません',
                hideCancelButton: true,
                contentAlign: 'center',
                confirmText: '戻る',
              });
            }}
          >
            <SvgIcon
              sx={{
                width: 16,
                height: 16,
              }}
              viewBox="0 0 16 16"
              inheritViewBox
              component={InfoIcon}
            />
          </IconButton>
        </>
      ) : (
        <IconButton
          sx={{
            zIndex: 1,
            p: '2px',
            svg: { color: 'icon' },
          }}
          onClick={() => {
            setConfirmModal({
              icon: 'info',
              onConfirm: () => {},
              content: item?.content ? item?.content : '内容がありません',
              hideCancelButton: true,
              contentAlign: 'center',
              confirmText: '戻る',
            });
          }}
        >
          <SvgIcon
            sx={{
              width: 16,
              height: 16,
            }}
            viewBox="0 0 16 16"
            inheritViewBox
            component={InfoIcon}
          />
        </IconButton>
      ),
    };
  }) as IDataOfRadio[];
};

export default OutlineMenu;

import { Box, Container, Stack, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import SolutionForm from 'components/SolutionCases/SolutionForm';
import BackButton from 'components/UI/BackButton';
import { useFetchDetail, useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ISolution } from 'hooks/types';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import type {
  ICreateSolution,
  IUpdateSolution,
} from 'models/solution/interface';
import solutionQuery from 'models/solution/query';
import { useRouter } from 'next/router';
import { useMemo } from 'react';

const ProviderEditSolution = () => {
  const { query, back } = useRouter();
  const solutionId = query.solutionId as string;
  const fetchSolutionDetail = useFetchDetail({
    ...apiQuery.lawyerSolutionDetail(solutionId),
  });
  const solutionDetail = fetchSolutionDetail.detail as ISolution;

  const { refetch: refetchList } = useFetchList({
    ...apiQuery.solutionDetail(solutionId),
    enabled: false,
    omitKeys: ['solutionId'],
  });
  const { mutateAsync: updateSolution, isLoading } = useMutate<IUpdateSolution>(
    solutionQuery.update(solutionId),
  );
  const defaultValues = useMemo(
    () => ({
      ...solutionDetail,
      consultationField: solutionDetail.consultationField?.map(
        (field) => field._id,
      ),
    }),
    [solutionDetail],
  );

  const handleUpdateSolution = (values: ICreateSolution, isDirty: boolean) => {
    if (isDirty) {
      updateSolution(
        { _id: solutionId, ...values },
        {
          onSuccess: () => {
            refetchList();
            fetchSolutionDetail.refetch();
            back();
          },
        },
      );
    } else {
      back();
    }
  };

  return (
    <div>
      <Box p="16px 32px" borderRadius={2} bgcolor="white">
        <Breadcrumbs omitIndexList={[0, 3]} />
        <Stack direction="row" mt="11px">
          <Box m="5px 4px 0px -4px">
            <BackButton />
          </Box>
          <Typography fontSize={32} fontWeight="bold" color="heading">
            {t('breadcrumb.solution')}
          </Typography>
        </Stack>
      </Box>
      <Container
        maxWidth="md"
        sx={{ mt: 2, bgcolor: 'white', p: 4, borderRadius: 2 }}
        disableGutters
      >
        <SolutionForm
          loading={isLoading}
          onSubmit={handleUpdateSolution}
          defaultValues={defaultValues}
          onCancel={back}
        />
      </Container>
    </div>
  );
};

export default ProviderEditSolution;

import { Box, Stack, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import ConsultationMenuForm from 'components/Lawyer/ConsultationMenuForm';
import BackButton from 'components/UI/BackButton';
import { useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import type { ICreateConsultation } from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import { useRouter } from 'next/router';

const ProviderAddConsultation = () => {
  const { back } = useRouter();
  const { mutateAsync: createConsultation, isLoading } =
    useMutate<ICreateConsultation>(consultationQuery.create);
  const { refetch } = useFetchList({
    ...apiQuery.consultationMenu,
    enabled: false,
  });
  const handleCreateConsultationMenu = async (values: ICreateConsultation) => {
    await createConsultation(values);
    refetch();
    back();
  };

  const handleCancel = () => {
    back();
  };

  return (
    <div>
      <Box p="16px 32px" borderRadius={2} bgcolor="white">
        <Breadcrumbs omitIndexList={[0]} />
        <Stack direction="row" mt="11px">
          <Box m="5px 4px 0px -4px">
            <BackButton />
          </Box>
          <Typography fontSize={32} fontWeight="bold" color="heading">
            {t('breadcrumb.consultationMenu')}
          </Typography>
        </Stack>
      </Box>
      <ConsultationMenuForm
        onCancel={handleCancel}
        onSubmit={handleCreateConsultationMenu}
        loading={isLoading}
      />
    </div>
  );
};

export default ProviderAddConsultation;

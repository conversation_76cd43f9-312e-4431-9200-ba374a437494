import { Box, Container, Stack, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import SolutionForm from 'components/SolutionCases/SolutionForm';
import BackButton from 'components/UI/BackButton';
import { useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import type { ICreateSolution } from 'models/solution/interface';
import solutionQuery from 'models/solution/query';
import { useRouter } from 'next/router';

const ProviderAddSolution = () => {
  const { back } = useRouter();

  const { refetch: refetchSolutionList } = useFetchList({
    ...apiQuery.solutionList,
    enabled: false,
  });
  const { mutateAsync: createSolution, isLoading } = useMutate<ICreateSolution>(
    solutionQuery.create,
  );
  const handleCreateSolution = (values: ICreateSolution) => {
    createSolution(values, {
      onSuccess: () => {
        refetchSolutionList();
        back();
      },
    });
  };

  return (
    <div>
      <Box p="16px 32px" borderRadius={2} bgcolor="white">
        <Breadcrumbs omitIndexList={[0]} />
        <Stack direction="row">
          <Box m="5px 4px 0px -4px">
            <BackButton />
          </Box>
          <Typography fontSize={32} fontWeight="bold" color="heading">
            {t('breadcrumb.solution')}
          </Typography>
        </Stack>
      </Box>
      <Container
        maxWidth="md"
        sx={{ mt: 2, bgcolor: 'white', p: 4, borderRadius: 2 }}
        disableGutters
      >
        <SolutionForm
          onCancel={back}
          onSubmit={handleCreateSolution}
          loading={isLoading}
        />
      </Container>
    </div>
  );
};

export default ProviderAddSolution;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  pageContainer: {
    paddingBottom: { xs: 2, tablet: 4 },
  },
  listContainer: {
    mt: { xs: 1, tablet: 2 },
    '.lawyer-list': {
      mt: 2,
    },
  },
  filterTitle: {
    fontWeight: 'bold',
    color: 'heading',
    fontSize: { xs: 14, tablet: 16 },
  },
  filterText: {
    display: '-webkit-box',
    WebkitLineClamp: '1',
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    flex: 1,
    fontSize: { xs: 14, tablet: 16 },
    mt: { xs: '4px', tablet: 0 },
    ml: { tablet: 1 },
    whiteSpace: 'normal',
  },
  filterRow: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    p: { xs: 1, tablet: 3 },
    cursor: 'pointer',
    '&:hover': {
      bgcolor: 'divine',
      borderRadius: { xs: 1, tablet: 2 },
    },
    '& svg': {
      color: 'primary.main',
    },
  },
  filterDivider: {
    bgcolor: 'divine',
    height: '1px',
    width: 'calc(100% - 48px)',
    mx: 3,
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import {
  Box,
  Button,
  Container,
  Grid,
  Stack,
  SvgIcon,
  Typography,
} from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import LawyerList from 'components/Provider/ProviderList';
import dayjs from 'dayjs';
import { useFetchList } from 'hooks';
import type { IListItem } from 'hooks/types';
import { t } from 'i18n';
import {
  ArrowRightIcon,
  CacaIcon,
  CalendarIcon,
  ClockIcon,
  CoverAreaIcon,
  MapIcon,
  MeetingNoteIcon,
  MenuIcon,
} from 'icons';
import { isArray, isEmpty } from 'lodash';
import type { IMeetingType } from 'models/consultation/interface';
import { IMeetingTypeLabel } from 'models/consultation/interface';
import type {
  ICounselorItem,
  ICounselorItemWithCalendar,
  ILawyerItem,
} from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import type { IPrefecture } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import { useCallback, useState } from 'react';
import {
  getFilterMenuText,
  PROVIDER_FILTER_LIMIT,
  PROVIDER_LIMIT,
  ProviderType,
} from 'utils/constants';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const CheckboxModal = dynamic(
  () => import('components/Provider/CheckboxModal'),
);

const consultationFieldText = {
  [ProviderType.COUNSELOR]: '得意分野',
  [ProviderType.LAWYER]: t('lawyerList.consultationField'),
};

const ProviderList = ({
  providerType,
  enabled,
}: {
  providerType: ProviderType;
  enabled?: boolean;
}) => {
  const { query, push, pathname, isReady } = useRouter();
  const prefecture = [query.prefecture || []].flat();
  const consultationField = [query.consultationField || []].flat();
  const [openFilterModal, setOpenFilterModal] = useState<string | boolean>();
  const isPrefectureModal = openFilterModal === 'prefecture';
  const isFilterCounselor = query.isFilterCounselor === 'true';
  const {
    list,
    total = 0,
    isLoading,
  } = useFetchList<ILawyerItem | ICounselorItem>({
    ...providerQuery.providerList(providerType),
    enabled,
    customParams: {
      providerType: [providerType],
      limit: PROVIDER_LIMIT,
    },
  });

  const { list: calendarList, total: totalCouFilter } =
    useFetchList<ICounselorItemWithCalendar>({
      ...providerQuery.getCalendarSlotList,
      enabled: isFilterCounselor,
      customParams: {
        limit: PROVIDER_FILTER_LIMIT,
        consultationField: isArray(query.consultationField)
          ? query.consultationField
          : [query.consultationField],
        meetingType: !isArray(query.meetingType)
          ? [query.meetingType]
          : query.meetingType,
      },
    });
  const listCounselor = isFilterCounselor
    ? calendarList.map((item) => {
        return {
          ...item.providerInfo,
          menus: item.menus.filter(
            (menu) =>
              menu.meetingType === query.meetingType &&
              menu.unitPrices.some(
                (unit) => unit.duration === Number(query.duration),
              ),
          ),
        };
      })
    : list;

  const { ids: prefectureIds, entities: prefectureEntities } =
    useFetchList<IPrefecture>(resourceQuery.prefectures);
  const { ids: consultationIds, entities: consultationEntities } =
    useFetchList<IPrefecture>({
      ...resourceQuery.consultations,
      customParams: {
        ...resourceQuery.consultations.customParams,
        providerType: [providerType],
      },
    });

  const renderFilterString = useCallback(
    (value: string[], ids: string[], entities: Record<string, IListItem>) => {
      // query not ready or not load resource yet
      if (!isReady || isEmpty(ids)) {
        return '';
      }
      if (isEmpty(value)) {
        return t('global.noSelected');
      }
      if (value.length === ids.length) {
        return t('global.selectAll');
      }
      return value.map((pre) => entities[pre]?.value).join(' • ');
    },
    [isReady],
  );
  const handleFilter = useCallback(
    (values: string[]) => {
      let queryObj = { ...query };
      delete queryObj.page;
      if (isPrefectureModal) {
        queryObj = {
          ...queryObj,
          prefecture: values,
        };
      } else {
        queryObj = {
          ...queryObj,
          consultationField: values,
        };
      }
      push({
        pathname,
        query: queryObj,
      });
      setOpenFilterModal(false);
    },
    [isPrefectureModal, pathname, push, query],
  );

  return (
    <Container maxWidth="lg" disableGutters sx={styles.pageContainer}>
      <NextSeo {...seoConfig.providerList} />
      <Container maxWidth="md" disableGutters sx={styles.listContainer}>
        <>
          {providerType === ProviderType.LAWYER ? (
            <>
              <Box
                sx={{
                  borderRadius: { xs: 0, tablet: 2 },
                  padding: { xs: '8px 16px 13px', tablet: '16px 32px' },
                }}
                bgcolor="white"
                mb={{ xs: 1, tablet: 2 }}
              >
                <Breadcrumbs />
                <Typography
                  fontSize={{ xs: 24, tablet: 32 }}
                  fontWeight={700}
                  mt={{ xs: '4px', tablet: '12px' }}
                  color="heading"
                >
                  {providerType === ProviderType.LAWYER
                    ? '弁護士を探す'
                    : 'カウンセラーを探す'}
                </Typography>
              </Box>
              <Box p={1} borderRadius={{ xs: 0, tablet: 2 }} bgcolor="white">
                {providerType === ProviderType.LAWYER && (
                  <>
                    <Box
                      onClick={() => setOpenFilterModal('prefecture')}
                      sx={styles.filterRow}
                    >
                      <Stack direction={{ tablet: 'row' }} spacing={1}>
                        <Stack direction="row" spacing={1}>
                          <SvgIcon
                            className="tabletStyle"
                            component={MapIcon}
                          />
                          <Typography sx={styles.filterTitle}>
                            {t('lawyerList.prefecture')} :
                          </Typography>
                        </Stack>
                        <Typography component="span" sx={styles.filterText}>
                          {renderFilterString(
                            prefecture,
                            prefectureIds,
                            prefectureEntities,
                          )}
                        </Typography>
                      </Stack>
                      <SvgIcon
                        className="tabletStyle"
                        component={ArrowRightIcon}
                      />
                    </Box>
                    <Box sx={styles.filterDivider} />
                  </>
                )}
                <Box
                  onClick={() => setOpenFilterModal('consultations')}
                  sx={styles.filterRow}
                >
                  <Stack direction={{ tablet: 'row' }} spacing={1}>
                    <Stack direction="row" spacing={1}>
                      <SvgIcon
                        className="tabletStyle"
                        component={CoverAreaIcon}
                      />
                      <Typography sx={styles.filterTitle}>
                        {consultationFieldText[providerType]} :
                      </Typography>
                    </Stack>
                    <Typography component="span" sx={styles.filterText}>
                      {renderFilterString(
                        consultationField,
                        consultationIds,
                        consultationEntities,
                      )}
                    </Typography>
                  </Stack>
                  <SvgIcon className="tabletStyle" component={ArrowRightIcon} />
                </Box>
              </Box>
            </>
          ) : (
            <Box
              sx={{
                borderRadius: { xs: 0, tablet: 2 },
                padding: { xs: '16px', tablet: '32px' },
              }}
              bgcolor="white"
              mb={{ xs: 1, tablet: 2 }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <Typography
                  fontWeight={700}
                  color="heading"
                  fontSize={{ xs: 20, tablet: 24 }}
                >
                  {t('counselorList.total_first')}
                  <b>{isFilterCounselor ? totalCouFilter : total}</b>
                  {t('counselorList.total_after')}
                </Typography>

                <Button
                  sx={{
                    bgcolor: '#fdf3d8',
                    ':hover': {
                      bgcolor: '#fdf3d8',
                    },
                    padding: {
                      xs: '6px 12px',
                      tablet: '8px 16px',
                    },
                    marginTop: '16px',
                    alignSelf: 'center',
                    minWidth: {
                      xs: '200px',
                      tablet: '280px',
                    },
                    minHeight: {
                      xs: '48px',
                      tablet: '56px',
                    },
                  }}
                  onClick={() => {
                    push({
                      pathname: '/counselors/filter',
                      query: isFilterCounselor ? { ...query } : {},
                    });
                  }}
                >
                  <Typography fontSize={18} fontWeight={500} color="#FFA700">
                    {t('counselorList.filterBtn')}
                  </Typography>
                </Button>
              </Box>
              {isFilterCounselor && (
                <Box mt={2}>
                  <Grid container spacing={{ xs: '12px', tablet: 2 }}>
                    <Grid item sm={6}>
                      <Stack
                        direction="row"
                        alignItems="center"
                        spacing={'10px'}
                      >
                        <Stack direction="row" spacing={'10px'}>
                          <SvgIcon
                            sx={{
                              width: {
                                md: '24px',
                                xs: '16px',
                              },
                              height: {
                                md: '24px',
                                xs: '16px',
                              },
                            }}
                            component={CalendarIcon}
                          />{' '}
                          <Typography
                            width={{ md: 90, xs: 68 }}
                            fontWeight={700}
                            fontSize={{ xs: 12, tablet: 16 }}
                            color="heading"
                          >
                            相談日:
                          </Typography>
                        </Stack>
                        <Typography>
                          {query.startSlot
                            ? dayjs(query.startSlot as string)
                                .tz('Asia/Tokyo')
                                .format('YYYY年MM月DD日 (ddd)')
                            : ''}
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item sm={6}>
                      <Stack
                        direction="row"
                        alignItems="center"
                        spacing={'10px'}
                      >
                        <Stack direction="row" spacing={'10px'}>
                          <SvgIcon
                            sx={{
                              width: {
                                md: '24px',
                                xs: '16px',
                              },
                              height: {
                                md: '24px',
                                xs: '16px',
                              },
                            }}
                            component={MeetingNoteIcon}
                          />
                          <Typography
                            width={{ md: 90, xs: 68 }}
                            fontWeight={700}
                            fontSize={{ xs: 12, tablet: 16 }}
                            color="heading"
                          >
                            メニュー :
                          </Typography>
                        </Stack>
                        <Typography>
                          {getFilterMenuText(
                            query.meetingType as string,
                            Number(query.duration),
                          )}
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item sm={6}>
                      <Stack
                        direction="row"
                        alignItems="center"
                        spacing={'10px'}
                      >
                        <Stack direction="row" spacing={'10px'}>
                          <SvgIcon
                            sx={{
                              width: {
                                md: '24px',
                                xs: '16px',
                              },
                              height: {
                                md: '24px',
                                xs: '16px',
                              },
                            }}
                            component={CacaIcon}
                          />
                          <Typography
                            width={{ md: 90, xs: 68 }}
                            fontWeight={700}
                            fontSize={{ xs: 12, tablet: 16 }}
                            color="heading"
                          >
                            相談タイプ :
                          </Typography>
                        </Stack>
                        <Typography>
                          {IMeetingTypeLabel[query.meetingType as IMeetingType]}
                        </Typography>
                      </Stack>
                    </Grid>
                    <Grid item sm={6}>
                      <Stack
                        direction="row"
                        alignItems="center"
                        spacing={'10px'}
                      >
                        <Stack direction="row" spacing={'10px'}>
                          <SvgIcon
                            sx={{
                              width: {
                                md: '24px',
                                xs: '16px',
                              },
                              height: {
                                md: '24px',
                                xs: '16px',
                              },
                            }}
                            component={ClockIcon}
                          />

                          <Typography
                            width={{ md: 90, xs: 68 }}
                            fontWeight={700}
                            fontSize={{ xs: 12, tablet: 16 }}
                            color="heading"
                          >
                            相談時間 :
                          </Typography>
                        </Stack>
                        <Typography>
                          {' '}
                          {query.startSlot
                            ? dayjs
                                .utc(query.startSlot as string)
                                .tz('Asia/Tokyo')
                                .format('HH時mm分')
                            : ''}
                          ~{' '}
                          {query.startSlot
                            ? dayjs
                                .utc(query.startSlot as string)
                                .tz('Asia/Tokyo')
                                .add(Number(query.duration), 'minute')
                                .format('HH時mm分')
                            : ''}
                        </Typography>
                      </Stack>
                    </Grid>
                  </Grid>
                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={'10px'}
                    mt={2}
                  >
                    <Stack direction="row" spacing={'10px'} alignItems="center">
                      <Typography
                        sx={{
                          display: 'flex',
                          height: { table: 24, xs: 16 },
                          width: { table: 24, xs: 16 },
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <MenuIcon />
                      </Typography>

                      <Typography
                        width={{ md: 90, xs: 68 }}
                        fontWeight={700}
                        fontSize={{ xs: 12, tablet: 16 }}
                        color="heading"
                      >
                        相談テーマ :
                      </Typography>
                    </Stack>
                    <Typography>
                      {isArray(query.consultationField)
                        ? query.consultationField
                            .map((item) => item.replace(/\n/g, ' '))
                            .join(' • ')
                        : query.consultationField?.replace(/\n/g, ' ')}
                    </Typography>
                  </Stack>
                </Box>
              )}
            </Box>
          )}
        </>
        <Typography
          fontSize={{ xs: 14, tablet: 16 }}
          fontWeight={500}
          ml={{ xs: 2, md: 0 }}
          mt={{ xs: 3, md: 4 }}
        >
          {providerType === ProviderType.LAWYER &&
            t('lawyerList.lawyerTotal', { total })}
        </Typography>

        <LawyerList
          showPagination={!isFilterCounselor}
          limit={isFilterCounselor ? PROVIDER_FILTER_LIMIT : PROVIDER_LIMIT}
          list={providerType === ProviderType.LAWYER ? list : listCounselor}
          total={isFilterCounselor ? totalCouFilter : total}
          loading={isLoading}
        />
        {openFilterModal && (
          <CheckboxModal
            icon={isPrefectureModal ? <MapIcon /> : <CoverAreaIcon />}
            title={
              isPrefectureModal
                ? t('lawyerList.prefecture')
                : consultationFieldText[providerType]
            }
            open={!!openFilterModal}
            entities={
              isPrefectureModal ? prefectureEntities : consultationEntities
            }
            onClose={() => setOpenFilterModal(false)}
            ids={isPrefectureModal ? prefectureIds : consultationIds}
            defaultValues={isPrefectureModal ? prefecture : consultationField}
            onSubmit={handleFilter}
          />
        )}
      </Container>
    </Container>
  );
};

export default ProviderList;

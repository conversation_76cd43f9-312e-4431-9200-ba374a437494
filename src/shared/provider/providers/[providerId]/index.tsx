import {
  Box,
  Button,
  Chip,
  Container,
  Divider,
  Grid,
  Skeleton,
  Stack,
  SvgIcon,
  Typography,
} from '@mui/material';
import ArticleList from 'components/Article/ArticleList';
import TopArticleList from 'components/Article/ArticleList/TopArticleList';
import Breadcrumbs from 'components/Breadcrumbs';
import BaseLevelRating from 'components/Form/LevelRating/BaseLevelRating';
import Calendar from 'components/Guest/Calendar';
import BookingButton from 'components/LawyerDetail/BookingButton';
import SolutionList from 'components/LawyerDetail/SolutionList';
import RatingListProvider from 'components/Provider/RatingList';
import VideoThumbnail from 'components/Provider/VideoThumbnail';
import AvatarCarousel from 'components/UI/AvatarCarousel';
import BorderLabel from 'components/UI/BorderLabel';
import ChipList from 'components/UI/ChipList';
import IconText from 'components/UI/IconText';
import InfoText from 'components/UI/InfoText';
import Rating from 'components/UI/Rating';
import dayjs from 'dayjs';
import { useFetchList } from 'hooks';
import type {
  IArticleListItem,
  IPickupArticleListItem,
  ISolution,
} from 'hooks/types';
import useFetchDetail from 'hooks/useFetchDetail';
import i18n from 'i18n';
import {
  ArrowRightIcon,
  CheckListIcon,
  MapIcon,
  MyPageProfileIcon,
  OutlinedBuilding,
  QuoteIcon,
  StationIcon,
  WorkingHourIcon,
} from 'icons';
import { isEmpty } from 'lodash';
import articleQuery from 'models/article/query';
import type {
  ICalendarDetail,
  ICounselorItem,
  ILawyerItem,
} from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import Link from 'next/link';
import { NextSeo } from 'next-seo';
import { useMemo, useRef } from 'react';
import { Gender, PROVIDER_LIST_PATH, ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const ProviderDetailPage = ({
  providerId,
  providerType,
  calendarDetail,
}: {
  providerId: string;
  providerType: ProviderType;
  calendarDetail?: ICalendarDetail;
}) => {
  const { t } = i18n;

  // Determine which query to use based on user authentication state
  const providerDetailQuery = useMemo(() => {
    const webCookie = Helper.getWebCookie();
    const isLoggedIn = !!webCookie?.role;

    return isLoggedIn
      ? providerQuery.providerDetail(providerId)
      : providerQuery.providerDetailByGuest(providerId);
  }, [providerId]);

  // Why do I call it again on client: There are some fields that QC and maybe partner wants it update at once (freeConsultationMenu)
  const { detail } = useFetchDetail<ILawyerItem | ICounselorItem>(
    providerDetailQuery,
  );

  const { list: articleList } = useFetchList<IArticleListItem>({
    ...providerQuery.providerArticleList(providerId),
    omitKeys: ['counselorId'],
    customParams: {
      sort: 'updatedAt.desc',
      providerId,
    },
  });

  const { list: solutionList } = useFetchList<ISolution>({
    ...providerQuery.providerSolutions(providerId),
    enabled: false,
  });
  const { list: pickupArticleList } = useFetchList<IPickupArticleListItem>({
    ...articleQuery.pickupArticleList,
    omitKeys: ['counselorId'],
    customParams: {
      sort: 'updatedAt.desc',
      providerId,
    },
  });
  const refSectionReview = useRef<HTMLDivElement>(null);

  const { catchphrase, introduction, consultationField } = detail;
  const videoUrl = detail?.videoUrl?.value;
  const isLawyer = detail.type === ProviderType.LAWYER;
  const address = isLawyer && detail.office.address;
  const stationToTheOffice = isLawyer && detail.office.stationToTheOffice;
  const timeToTheOffice = isLawyer && detail.office.timeToTheOffice;
  const businessHours = isLawyer && detail.office.businessHours;
  const displayName = (!isLawyer && detail.nickname) || detail.fullName;
  const extendIntroduction = !isLawyer ? detail.extendIntroduction : undefined;

  const renderOfficeInfo = () => {
    if (detail.type === ProviderType.LAWYER) {
      return (
        <Stack spacing="6px" mt={2}>
          <IconText icon={OutlinedBuilding}>{detail.office?.fullName}</IconText>
          <IconText icon={MapIcon}>
            {address
              ? t('lawyerList.address', {
                  postCode: address?.postCode,
                  address1: address?.address1?.value,
                  address2: address?.address2,
                  address3: address?.address3,
                  address4: address?.address4,
                })
              : '-'}
          </IconText>
          <IconText icon={StationIcon}>
            {stationToTheOffice && timeToTheOffice
              ? t('lawyerList.access', {
                  stationToTheOffice,
                  timeToTheOffice,
                })
              : '-'}
          </IconText>
          <IconText icon={WorkingHourIcon}>{businessHours || '-'}</IconText>
        </Stack>
      );
    }
    return (
      <Stack spacing="6px" mt={2} sx={{ svg: { color: 'primary.main' } }}>
        <IconText icon={MyPageProfileIcon}>
          {detail?.birthday
            ? Math.floor(dayjs().diff(dayjs(detail.birthday), 'years') / 10) *
              10
            : '-'}
          代 /{detail.gender ? Gender[detail.gender] : '-'}
        </IconText>
        <IconText icon={OutlinedBuilding}>
          {detail?.career
            ? `カウンセラー歴：${dayjs().diff(dayjs(detail.career), 'years')}年`
            : '-'}
        </IconText>
        <IconText icon={CheckListIcon}>
          {detail?.numberSolutionCases
            ? `カウンセリング総件数：約${detail?.numberSolutionCases}件`
            : '-'}
        </IconText>
      </Stack>
    );
  };

  const renderExtendIntroduction = (label: string, text: string) => (
    <Stack gap="4px">
      <Typography
        fontWeight={700}
        fontSize={{ xs: 14, tablet: 16 }}
        mt={{ xs: '12px', tablet: 2 }}
        color="primary"
      >
        {label}
      </Typography>
      <Typography fontSize={{ xs: 14, tablet: 16 }}>{text}</Typography>
    </Stack>
  );

  const isTodayOpen = (dataCalendar: { date: string; status: string }[]) => {
    const today = new Date().toISOString().slice(0, 10);
    return (
      dataCalendar &&
      dataCalendar.some(
        (appointment: { date: string; status: string }) =>
          appointment.date === today && appointment.status === 'OPEN',
      )
    );
  };

  return (
    <Box>
      <NextSeo
        {...seoConfig.providerDetail({ ...detail, fullName: displayName })}
      />
      <Box minHeight="calc(100vh - 371px)">
        <Container disableGutters sx={{ mt: 2, px: { xs: 2, lg: 0 } }}>
          <Breadcrumbs
            transformLabel={{
              '/counselors/[counselorId]': displayName || (
                <Skeleton variant="text" width={100} />
              ),
              '/lawyers/[lawyerId]': displayName || (
                <Skeleton variant="text" width={100} />
              ),
            }}
          />
        </Container>
        <Container maxWidth="md" disableGutters>
          <Box sx={styles.infoContainer}>
            {!detail.isPublic && (
              <InfoText
                text={t('lawyerProfile.inactiveLawyer')}
                justifyContent="center"
              />
            )}
            <Box display="flex">
              <Box pl={{ xs: 1, tablet: 0 }}>
                <AvatarCarousel
                  carouselProps={{
                    maxWidth: { xs: 96, tablet: 152 },
                  }}
                  imageContainerProps={{
                    width: { xs: 96, tablet: 152 },
                    height: { xs: 96, tablet: 152 },
                  }}
                  navContainerProps={{
                    minWidth: { xs: 112, tablet: 'unset' },
                    maxWidth: 'calc(100% - 16px)',
                    margin: { xs: '8px 0px 0px -8px', tablet: '8px auto 0px' },
                    display:
                      detail?.images && detail?.images.length > 1
                        ? 'flex'
                        : 'none',
                  }}
                  imageProps={{
                    sizes: '(min-width: 768px) 20%, 27%',
                  }}
                  images={detail.images}
                />
              </Box>
              <Box ml={{ xs: 3, tablet: 6 }} flex={1}>
                <Typography
                  fontWeight={500}
                  color="primary"
                  fontSize={{ xs: 10, tablet: 16 }}
                >
                  {isLawyer
                    ? t('lawyerProfile.barAssociation', {
                        association: detail.barAssociation,
                      })
                    : 'カウンセラー'}
                </Typography>
                {/* Counselor: Introduction video available, Available today tag */}
                {!isLawyer &&
                  (detail?.videoUrl?.value ||
                    (calendarDetail &&
                      Array.isArray(calendarDetail) &&
                      isTodayOpen(calendarDetail))) && (
                    <Box
                      sx={{
                        marginTop: { xs: '8px', tablet: '16px' },
                        marginBottom: { xs: '0', md: '5px' },
                        gap: '4px',
                        display: 'flex',
                      }}
                    >
                      {detail?.videoUrl?.value && (
                        <Chip
                          key={'counselor video url'}
                          variant="orangeChip"
                          label="紹介動画あり"
                          component={'div'}
                        />
                      )}
                      {calendarDetail &&
                        Array.isArray(calendarDetail) &&
                        isTodayOpen(calendarDetail) && (
                          <Chip
                            key={'available today'}
                            variant="orangeChip"
                            label="本日相談可"
                            component={'div'}
                          />
                        )}
                    </Box>
                  )}
                {/* Lawyer: Introduction video available */}
                {isLawyer &&
                  (detail?.videoUrl?.value || detail?.hasOnlineSupport) && (
                    <Box
                      sx={{
                        marginTop: { xs: '8px', tablet: '16px' },
                        marginBottom: { xs: 0, tablet: '5px' },
                        gap: '4px',
                        display: 'flex',
                      }}
                    >
                      {detail?.videoUrl?.value && (
                        <Chip
                          key={'lawyer video url'}
                          variant="orangeChip"
                          label="紹介動画あり"
                          component={'div'}
                        />
                      )}
                      {detail?.hasOnlineSupport && (
                        <Chip
                          key={'lawyer support online'}
                          variant="orangeChip"
                          label="オンライン対応可"
                          component={'div'}
                        />
                      )}
                    </Box>
                  )}
                <Typography
                  mt={{ xs: '4px', tablet: '5px' }}
                  fontSize={{ xs: 20, tablet: 32 }}
                  fontWeight="bold"
                  color="heading"
                  component="h1"
                >
                  {displayName}
                </Typography>
                {!isLawyer && !detail.nickname && (
                  <Typography
                    fontWeight={500}
                    fontSize={{ xs: 14, tablet: 16 }}
                    component="h2"
                  >
                    {detail.katakanaName}
                  </Typography>
                )}
                {!isLawyer && (
                  <Box
                    sx={{ cursor: 'pointer', width: 'fit-content' }}
                    onClick={() =>
                      refSectionReview.current?.scrollIntoView({
                        behavior: 'smooth',
                      })
                    }
                  >
                    <Rating
                      size="lg"
                      rate={{
                        avgRating: detail.avgRating,
                        totalReview: detail.totalReview,
                      }}
                      sx={{
                        marginTop: ' 4px',
                        marginBottom: { xs: 1, md: 2 },
                      }}
                    />
                  </Box>
                )}
                {!isLawyer && (detail.certificate || []).length > 0 && (
                  <Box display={{ xs: 'none', tablet: 'block' }}>
                    <ChipList
                      data={detail.certificate || []}
                      maxLines={5}
                      chipProps={{ className: 'tabletStyle dark' }}
                      stackProps={{ gap: '4px', mt: 1 }}
                    />
                  </Box>
                )}
                {!isEmpty(consultationField) && (
                  <Box
                    display={{
                      xs: !isLawyer ? 'none' : 'block',
                      tablet: 'block',
                    }}
                  >
                    <ChipList
                      data={consultationField || []}
                      maxLines={5}
                      labelPath="value"
                      stackProps={{ gap: '4px', mt: 1 }}
                    />
                  </Box>
                )}
                <Box display={{ xs: 'none', tablet: 'block' }}>
                  {renderOfficeInfo()}
                </Box>
              </Box>
            </Box>
            {!isLawyer && (detail.certificate || []).length > 0 && (
              <Box display={{ xs: 'block', tablet: 'none' }}>
                <ChipList
                  data={detail.certificate || []}
                  maxLines={5}
                  chipProps={{ className: 'tabletStyle dark' }}
                  stackProps={{
                    gap: '4px',
                    mt: '12px',
                    mb: '-4px',
                  }}
                />
              </Box>
            )}
            {!isLawyer && !isEmpty(consultationField) && (
              <Box display={{ xs: 'block', tablet: 'none' }}>
                <ChipList
                  data={consultationField || []}
                  maxLines={5}
                  labelPath="value"
                  stackProps={{ gap: '4px', mt: '12px', mb: '-4px' }}
                />
              </Box>
            )}
            <Box display={{ xs: 'block', tablet: 'none' }}>
              {renderOfficeInfo()}
            </Box>
            <Divider sx={{ my: { xs: 2, tablet: 3 } }} />
            {!isLawyer && calendarDetail && <Calendar />}
            {detail.isPublic && (
              <Box mt={{ xs: 3, tablet: 2 }}>
                <BookingButton
                  detailProvider={detail}
                  lawyerId={providerId}
                  providerType={providerType}
                  freeConsultationMenu={detail.freeConsultationMenu}
                />
              </Box>
            )}
            <Divider sx={{ my: { xs: 2, tablet: 3 } }} />
            {!isLawyer && detail?.meetingStyle && (
              <Box mt={{ xs: 2, tablet: 4 }} mb={{ xs: 2, tablet: 4 }}>
                <Grid container spacing="30px" direction="row">
                  <Grid item xs={12} tablet={4}>
                    <BaseLevelRating
                      label="話し方"
                      value={detail?.meetingStyle?.speakingRate}
                      minText="穏やか"
                      maxText="テンポが速い"
                      labelProps={{ color: 'heading' }}
                      disabled
                    />
                  </Grid>
                  <Grid item xs={12} tablet={4}>
                    <BaseLevelRating
                      label="言葉づかい"
                      value={detail?.meetingStyle?.communicationRate}
                      minText="丁寧"
                      maxText="フレンドリー"
                      labelProps={{ color: 'heading' }}
                      disabled
                    />
                  </Grid>
                  <Grid item xs={12} tablet={4}>
                    <BaseLevelRating
                      label="アドバイス"
                      value={detail?.meetingStyle?.adviceRate}
                      minText="ソフト"
                      maxText="ストレート"
                      labelProps={{ color: 'heading' }}
                      disabled
                    />
                  </Grid>
                </Grid>
              </Box>
            )}
            {catchphrase && (
              <Stack direction="row" spacing={1}>
                <SvgIcon component={QuoteIcon} className="tabletStyle" />
                <Typography
                  flex={1}
                  fontSize={{ xs: 16, tablet: 24 }}
                  fontWeight="bold"
                  color="primary"
                >
                  {catchphrase}
                </Typography>
              </Stack>
            )}
            {introduction && (
              <Typography
                fontSize={{ xs: 14, tablet: 16 }}
                mt={{ xs: '12px', tablet: '20px' }}
              >
                {introduction}
              </Typography>
            )}
            {videoUrl && (
              <Box mt={{ xs: 3, tablet: 4 }}>
                <VideoThumbnail videoUrl={videoUrl} />
              </Box>
            )}
            {(extendIntroduction?.favoriteSubject ||
              extendIntroduction?.hobbies ||
              extendIntroduction?.importantInCounseling ||
              extendIntroduction?.solutionCases) && (
              <Divider sx={{ mt: 3, mb: 1 }} />
            )}
            {extendIntroduction?.favoriteSubject &&
              renderExtendIntroduction(
                '得意な内容・項目',
                extendIntroduction.favoriteSubject,
              )}
            {extendIntroduction?.importantInCounseling &&
              renderExtendIntroduction(
                '大切にしていること',
                extendIntroduction.importantInCounseling,
              )}
            {extendIntroduction?.solutionCases &&
              renderExtendIntroduction(
                '過去の実例',
                extendIntroduction.solutionCases,
              )}
            {extendIntroduction?.hobbies &&
              renderExtendIntroduction('趣味', extendIntroduction.hobbies)}
          </Box>
          {!isEmpty(articleList) && (
            <Box
              bgcolor="white"
              p={{ xs: 2, tablet: 4 }}
              mt={{ xs: 1, tablet: 2 }}
              borderRadius={{ tablet: 2 }}
              sx={styles.topArticle}
            >
              <BorderLabel>{t('lawyerProfile.article')}</BorderLabel>
              <Box mt={{ xs: 2, tablet: 4 }}>
                <TopArticleList
                  loading={false}
                  showLawyer
                  pathname="/articles/[articleId]"
                  data={articleList.slice(0, 1)}
                />
                {articleList.length > 1 && (
                  <>
                    <Divider sx={{ my: { xs: 2, tablet: 4 } }} />
                    <ArticleList
                      data={articleList.slice(1, 6)}
                      showLawyer
                      divider
                      spacing={{ xs: 2, tablet: 4 }}
                      loading={false}
                      pathname="/articles/[articleId]"
                    />
                  </>
                )}
              </Box>
            </Box>
          )}
          {!isEmpty(pickupArticleList) && (
            <Box
              p={{ xs: 2, tablet: 4 }}
              bgcolor="white"
              borderRadius={{ tablet: 2 }}
              mt={{ xs: 1, tablet: 2 }}
            >
              <BorderLabel>{t('lawyerProfile.pickupArticle')}</BorderLabel>
              <Box mt={{ xs: 2, tablet: 4 }}>
                <ArticleList
                  data={pickupArticleList.slice(0, 1)}
                  loading={false}
                  pathname="/pickup-articles/[articleId]"
                />
              </Box>
              <Divider sx={{ mt: { xs: '20px', tablet: 3 } }} />
              <Box sx={styles.seeMore}>
                <Link href="/pickup-articles" legacyBehavior>
                  <Button color="primary" endIcon={<ArrowRightIcon />}>
                    {t('global.seeMore')}
                  </Button>
                </Link>
              </Box>
            </Box>
          )}
          {!isEmpty(solutionList) && (
            <Box sx={styles.solutionList}>
              <BorderLabel>{t('lawyerProfile.solution')}</BorderLabel>
              <SolutionList
                data={solutionList.slice(0, 4)}
                lawyerId={providerId}
                spacing={{ xs: '12px', tablet: 3 }}
                sx={{
                  mt: { xs: 2, tablet: 4 },
                }}
              />
              {solutionList.length > 4 && (
                <>
                  <Divider sx={{ mt: { xs: '22px', tablet: 3 } }} />
                  <Box sx={styles.seeMore}>
                    <Link
                      href={{
                        pathname: '/lawyers/[lawyerId]/solutions',
                        query: { lawyerId: providerId },
                      }}
                      legacyBehavior
                    >
                      <Button color="primary" endIcon={<ArrowRightIcon />}>
                        {t('global.seeMore')}
                      </Button>
                    </Link>
                  </Box>
                </>
              )}
            </Box>
          )}

          {!isLawyer && (
            <RatingListProvider ref={refSectionReview} detail={detail} />
          )}
        </Container>
      </Box>
      <Box sx={styles.actions}>
        <Stack
          sx={{
            ...styles.actionWrapper,
            maxWidth: 762,
            alignItems: 'center',
          }}
        >
          <Box width={1} maxWidth={358} display="flex" flexDirection="column">
            <Typography sx={{ ...styles.actionText }} align="center">
              離婚なら
            </Typography>
            <Button
              variant="outlined"
              size="large"
              LinkComponent={Link}
              href={PROVIDER_LIST_PATH.LAWYER}
              className="shadow tabletStyle"
              fullWidth
              sx={{ maxWidth: 358 }}
              endIcon={
                <ArrowRightIcon color="#fdc84b" width={32} height={32} />
              }
            >
              弁護士を探す
            </Button>
          </Box>
          <Box maxWidth={358} width={1} display="flex" flexDirection="column">
            <Typography
              sx={styles.actionText}
              className="tabletStyle"
              align="center"
            >
              夫婦問題・修復なら
            </Typography>
            <Button
              LinkComponent={Link}
              href={PROVIDER_LIST_PATH.COUNSELOR}
              variant="outlined"
              size="large"
              fullWidth
              sx={{ maxWidth: 358 }}
              className="shadow tabletStyle"
              endIcon={
                <ArrowRightIcon color="#fdc84b" width={32} height={32} />
              }
            >
              カウンセラーを探す
            </Button>
          </Box>
        </Stack>
      </Box>
    </Box>
  );
};

export default ProviderDetailPage;

import Helper from './helpers';

/**
 * Get the appropriate LINE URL based on user login state
 * @returns LINE URL for logged in or non-logged in users
 */
export const getLineUrl = (): string => {
  const webCookie = Helper.getWebCookie();
  const isLoggedIn = !!webCookie;

  if (isLoggedIn) {
    // After login URL
    return 'https://s.lmes.jp/landing-qr/2006801672-5wnY6OZr?uLand=8PH6Ro';
  }
  // Before login URL
  return 'https://s.lmes.jp/landing-qr/2006801672-5wnY6OZr?uLand=vfUcLB';
};

export default getLineUrl;

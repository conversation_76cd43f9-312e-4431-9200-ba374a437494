/* eslint-disable no-console */
import { LOCAL_STORAGE_KEY } from './constants';

export class LocalStorageHelper {
  private prefix: string;

  /**
   * Initializes the LocalStorageHelper with a specific prefix.
   * @param prefix - The prefix to use for all keys in local storage.
   */
  constructor(prefix: string) {
    this.prefix = prefix;
  }

  /**
   * Saves a value to local storage under a prefixed key.
   * @param key - The key to store the value under.
   * @param value - The value to store (of any type).
   */
  setItem<T>(key: string, value: T): void {
    const prefixedKey = `${this.prefix}:${key}`;
    const stringValue = JSON.stringify(value);
    localStorage.setItem(prefixedKey, stringValue);
  }

  /**
   * Retrieves a value from local storage by its prefixed key.
   * @param key - The key to retrieve the value from.
   * @returns The retrieved value or null if not found.
   */
  getItem<T>(key: string): T | null {
    const prefixedKey = `${this.prefix}:${key}`;
    const stringValue = localStorage.getItem(prefixedKey);
    if (stringValue === null) {
      return null;
    }
    try {
      return JSON.parse(stringValue) as T;
    } catch (error) {
      console.error('Error parsing JSON from localStorage', error);
      return null;
    }
  }

  /**
   * Removes an item from local storage by its prefixed key.
   * @param key - The key of the item to remove.
   */
  removeItem(key: string): void {
    const prefixedKey = `${this.prefix}_${key}`;
    localStorage.removeItem(prefixedKey);
  }

  /**
   * Clears all items from local storage that have the specified prefix.
   */
  clear(): void {
    const keys = Object.keys(localStorage);
    // eslint-disable-next-line no-restricted-syntax
    for (const key of keys) {
      if (key.startsWith(`${this.prefix}_`)) {
        localStorage.removeItem(key);
      }
    }
  }
}

const localStorageHelper = new LocalStorageHelper(LOCAL_STORAGE_KEY);

export default localStorageHelper;

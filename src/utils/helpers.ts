/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable import/no-cycle */
import 'dayjs/locale/ja';

import type { FetchQueryOptions, QueryClient } from '@tanstack/react-query';
import type { AxiosRequestConfig } from 'axios';
import { deleteCookie, getCookie, setCookie } from 'cookies-next';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { t } from 'i18n';
import { WhiteErrorIcon, WhiteSuccessIcon } from 'icons';
import { get, isBoolean, isEmpty, isNumber, pickBy } from 'lodash';
import type { NextApiRequest, NextApiResponse } from 'next';
import ReactGA from 'react-ga4';
import type { ToastContent, ToastOptions } from 'react-toastify';
import { toast } from 'react-toastify';
import api from 'utils/api';

import { ROLES } from './constants';

dayjs.locale('ja');

const toastIcon = {
  success: WhiteSuccessIcon,
  error: WhiteErrorIcon,
};
const Helper = {
  getWebCookie: (
    req?: NextApiRequest,
    res?: NextApiResponse,
  ): Record<string, string> => {
    const cookies = JSON.parse(
      (getCookie('lawyer-web-cookie', req && res ? { req, res } : {}) ||
        null) as string,
    );
    return cookies;
  },
  getWebCookieByGuest: (
    req?: NextApiRequest,
    res?: NextApiResponse,
  ): Record<string, string> => {
    const cookies = JSON.parse(
      (getCookie(
        'lawyer-web-cookie-by-guest',
        req && res ? { req, res } : {},
      ) || null) as string,
    );
    return cookies;
  },
  getUserRole: () => {
    const webCookie = Helper.getWebCookie();
    return get(webCookie, 'role');
  },
  isCustomer: () => {
    return Helper.getUserRole() === ROLES.CUSTOMER;
  },
  setToken: (data: Record<string, string>): void =>
    setCookie('lawyer-web-cookie', data, {
      path: '/',
      maxAge: process.env.SESSION_TIME
        ? Number(process.env.SESSION_TIME)
        : 7776000,
    }),
  removeWebCookie: (): void => deleteCookie('lawyer-web-cookie', { path: '/' }),
  removeWebCookieByGuest: (): void =>
    deleteCookie('lawyer-web-cookie-by-guest', { path: '/' }),
  convertObjectToOptions: (
    obj: Record<string, string>,
  ): { _id: string; value: string }[] => {
    return Object.keys(obj).map((key) => ({
      _id: key,
      value: obj[key] as string,
    }));
  },
  getExternalLink: (url: string | undefined) => {
    if (url) {
      const formatUrl =
        url.startsWith('https://') || url.startsWith('http://')
          ? url
          : `http://${url}`;
      return formatUrl;
    }
    return url;
  },
  getTokenConfig: (req: unknown, res: unknown) => {
    const cookies = Helper.getWebCookie(
      req as NextApiRequest,
      res as NextApiResponse,
    );
    if (cookies) {
      const { token } = cookies;
      return {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
    }
    return {};
  },
  getLineUrl: (): string => {
    const webCookie = Helper.getWebCookie();
    const isLoggedIn = !!webCookie?.token;

    if (isLoggedIn) {
      // Already logged in URL
      return 'https://s.lmes.jp/landing-qr/2006801672-5wnY6OZr?uLand=8PH6Ro';
    }
    // Not logged in URL
    return 'https://s.lmes.jp/landing-qr/2006801672-5wnY6OZr?uLand=vfUcLB';
  },
  checkValidImage: (file: File, config?: { maxSize: number; type: string }) => {
    const maxSize = config?.maxSize || 5;
    const type = config?.type || 'image';
    if (!file.type.startsWith(type)) {
      Helper.toast(t('validation.invalidField'), { type: 'error' });
      return false;
    }
    if (file.size > 1000000 * maxSize) {
      Helper.toast(t('validation.invalidFileSize', { size: maxSize }), {
        type: 'error',
      });
      return false;
    }
    return true;
  },
  formatUrl: (url: string) => {
    if (typeof url !== 'string') {
      return undefined;
    }
    return url.startsWith('https://') || url.startsWith('http://')
      ? url
      : `http://${url}`;
  },
  convertArrayToEntities: <T>(array: (T & { _id?: string; id?: string })[]) => {
    const ids: string[] = [];
    const entities = (array || []).reduce((acc, cur) => {
      if (cur._id) {
        ids.push(cur._id);
        return { ...acc, [cur._id]: cur };
      }
      if (cur.id) {
        ids.push(cur.id);
        return { ...acc, [cur.id]: cur };
      }
      return acc;
    }, {});
    return {
      ids,
      entities,
    };
  },
  checkIsEmptyObject: (object: object | null | undefined) => {
    return isEmpty(
      pickBy(object, (i) => !isEmpty(i) || isNumber(i) || isBoolean(i)),
    );
  },
  formatCardNumberText: (lastNumber: string) => {
    if (lastNumber) {
      return lastNumber
        .replace(/(.{4}).{2}/, '$1••')
        .replace(/(.{4})/g, '$1 ')
        .replaceAll('*', '•');
    }
    return '';
  },
  toast: (
    message: ToastContent<unknown>,
    options?: ToastOptions<{}> | undefined,
  ) => {
    const type = get(options, 'type', 'success');
    return toast(message, {
      type,
      icon: toastIcon[type as never],
      ...options,
    });
  },
  getYouTubeVideoIdFromUrl: (url: string) => {
    // Our regex pattern to look for a youTube ID
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    // Match the url with the regex
    const match = url.match(regExp);
    // Return the result
    return match && match[2]?.length === 11 ? match[2] : undefined;
  },
  getVimeoIdFromUrl: (url: string) => {
    // Look for a string with 'vimeo', then whatever, then a
    // forward slash and a group of digits.
    const match = /vimeo.*\/(\d+)/i.exec(url);
    // If the match isn't null (i.e. it matched)
    if (match) {
      // The grouped/matched digits from the regex
      return match[1];
    }
    return undefined;
  },
  getVideoThumnail: (url: string) => {
    const youtubeId = Helper.getYouTubeVideoIdFromUrl(url);
    if (youtubeId) {
      return `http://i3.ytimg.com/vi/${youtubeId}/maxresdefault.jpg`;
    }
    return undefined;
  },
  prefetchDetail: (
    apiQuery: { queryKey: string[]; apiUrl: string },
    queryClient: QueryClient,
    options?: FetchQueryOptions<any, unknown, any, unknown[]>,
    axiosConfig?: AxiosRequestConfig,
  ) => {
    return queryClient.fetchQuery(
      apiQuery.queryKey,
      async () => {
        const { data } = await api.get(apiQuery.apiUrl, axiosConfig);
        return data;
      },
      options,
    );
  },
  prefetchList: (
    apiQuery: { queryKey: string[]; apiUrl: string },
    queryClient: QueryClient,
    params?: Record<string, unknown>,
    options?: FetchQueryOptions<any, unknown, any, unknown[]>,
    axiosConfig?: AxiosRequestConfig,
  ) => {
    const queryParams = { ...(params || {}) };
    if (!queryParams.limit) {
      queryParams.limit = 10;
    }
    if (!queryParams.page) {
      queryParams.page = 1;
    }
    return queryClient.fetchQuery(
      [...apiQuery.queryKey, queryParams],
      async () => {
        const { data } = await api.get(apiQuery.apiUrl, {
          params: queryParams,
          ...axiosConfig,
        });
        return data;
      },
      options,
    );
  },
  addComma: (value: string | number) =>
    value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','),
  getBasicPrice: (unitPrice?: string | number, duration?: string | number) =>
    Math.floor((Number(unitPrice || 0) * Number(duration || 0)) / 30),
  setEventTrackingUserId: ({
    userId,
    userRole,
  }: {
    userId?: string;
    userRole?: ROLES;
  }) => {
    if (userId && userRole) {
      if (userRole === ROLES.CUSTOMER) {
        ReactGA.set({ userId });
      } else ReactGA.set({ provider_id: userId });
    }
  },
  setEventTracking: (data: Record<string, any>) => {
    ReactGA.set(data);
  },
  checkOverlapTimeRange: (
    startDay1: Dayjs,
    endDay1: Dayjs,
    startDay2: Dayjs,
    endDay2: Dayjs,
  ) => {
    return startDay1 < endDay2 && endDay1 > startDay2;
  },

  calculateRoundedRating(rating: number) {
    const decimalPart = rating % 1;
    const decimalPartRounded = Math.round(decimalPart * 10) / 10;
    const integerPart = Math.floor(rating);

    if (decimalPartRounded >= 0.1 && decimalPartRounded <= 0.4) {
      return integerPart;
    }
    if (decimalPartRounded >= 0.5 && decimalPartRounded <= 0.9) {
      return integerPart + 0.5;
    }
    return rating;
  },

  formatRating(rating: number) {
    const decimalPart = rating % 1;
    const integerPart = Math.floor(rating);
    if (!decimalPart) {
      return `${integerPart}.0`;
    }
    return `${rating}`;
  },

  convertArrayToOptions: <T>(
    array: T[],
    fields: Array<{
      key?: keyof T;
      alias?: string;
      getValue?: (value: T) => string;
    }>,
  ): Array<Record<string, string>> => {
    return array.map((item) => {
      const result: Record<string, string> = {};
      fields.forEach(({ key, alias, getValue }) => {
        const value = item[key as unknown as keyof T];

        if ((alias ?? key) as string)
          result[(alias ?? key) as string] = String(value ?? ''); // Default to ""

        if (alias && getValue) {
          result[alias] = getValue(item);
        }
      });
      return result;
    });
  },

  /**
   * Determines the default menu selection based on priority logic
   * Priority order:
   * 1. SINGLE & 60 minutes
   * 2. SINGLE & 90 minutes
   * 3. SINGLE & 120 minutes
   * 4. PARTNER & 90 minutes
   * 5. PARTNER & 120 minutes
   * 6. First menu in the list (fallback)
   */
  getDefaultMenuId(
    menuList: Array<{
      _id: string;
      meetingType?: string;
      unitPrices: Array<{ duration: number; price: number }>;
    }>,
  ): string | undefined {
    if (!menuList || menuList.length === 0) {
      return undefined;
    }

    // Priority 1: SINGLE & 60 minutes
    const singleMenu60 = menuList.find(
      (menu) =>
        menu.meetingType === 'SINGLE' && menu.unitPrices?.[0]?.duration === 60,
    );
    if (singleMenu60) return singleMenu60._id;

    // Priority 2: SINGLE & 90 minutes
    const singleMenu90 = menuList.find(
      (menu) =>
        menu.meetingType === 'SINGLE' && menu.unitPrices?.[0]?.duration === 90,
    );
    if (singleMenu90) return singleMenu90._id;

    // Priority 3: SINGLE & 120 minutes
    const singleMenu120 = menuList.find(
      (menu) =>
        menu.meetingType === 'SINGLE' && menu.unitPrices?.[0]?.duration === 120,
    );
    if (singleMenu120) return singleMenu120._id;

    // Priority 4: PARTNER & 90 minutes
    const partnerMenu90 = menuList.find(
      (menu) =>
        menu.meetingType === 'PARTNER' && menu.unitPrices?.[0]?.duration === 90,
    );
    if (partnerMenu90) return partnerMenu90._id;

    // Priority 5: PARTNER & 120 minutes
    const partnerMenu120 = menuList.find(
      (menu) =>
        menu.meetingType === 'PARTNER' &&
        menu.unitPrices?.[0]?.duration === 120,
    );
    if (partnerMenu120) return partnerMenu120._id;

    // Priority 6: Fallback to first menu
    return menuList[0]?._id;
  },
};

export default Helper;

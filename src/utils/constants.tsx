import i18n from 'i18n';
import {
  AmexCardIcon,
  CashIcon,
  CloseIcon,
  DinerCardIcon,
  EditIcon,
  MasterCardIcon,
  PaymentCardIcon,
  TransferIcon,
  VisaCardIcon,
} from 'icons';
import JCBCardIcon from 'icons/JCBCardIcon';
import { IMeetingType } from 'models/consultation/interface';

export const NON_LOGIN_ROUTE = [
  '/register',
  '/login',
  '/forgot-password',
  '/set-password',
];

export enum ValidCardNiceType {
  'Visa' = 'Visa',
  'MasterCard' = 'MasterCard',
  'Mastercard' = 'Mastercard',
  'JCB' = 'JCB',
  'Diners Club' = 'Diners Club',
  'American Express' = 'American Express',
}
export enum MeetingType {
  'IN_PERSON' = 'IN_PERSON',
  'ONLINE' = 'ONLINE',
}

export enum GenderType {
  male = 'male',
  female = 'female',
  other = 'other',
}

export enum RadioOptionsType {
  yes = 'yes',
  no = 'no',
  other = 'other',
}
export enum RadioOptions {
  yes = '有',
  no = '無',
  other = '無回答',
}
export enum RadioYesNoOptions {
  yes = '可',
  no = '不可',
}

export enum YesNoOptionsType {
  yes = 'yes',
  no = 'no',
}
export enum YesNoOptions {
  yes = 'はい',
  no = 'いいえ',
}

export enum Gender {
  male = '男性',
  female = '女性',
  other = 'その他',
}

export enum ISchoolType {
  public = 'public',
  private = 'private',
  national = 'national',
}

export enum SchoolTypeOptions {
  public = '公立',
  private = '私立',
  national = '国立',
}

export const ANNUAL_INCOME = [
  '300万未満',
  '300万以上~400万未満',
  '400万以上~500万未満',
  '500万以上~600万未満',
  '600万以上~700万未満',
  '700万以上~800万未満',
  '800万以上~900万未満',
  '900万以上~1000万未満',
  '1000万以上',
];

export const MONTHLY_INCOME = [
  '20万未満',
  '20万以上-40万未満',
  '40万-60万未満',
  '60万-80万未満',
  '80万-100万未満',
  '100万以上',
];

export const DIVORCE_REASON = [
  '浮気・不倫(不貞行為)',
  '悪意の遺棄※',
  '3年以上の生死不明',
  '配偶者が強度の精神病にかかり、 回復の見込みがない',
  'その他\n(婚姻を継続し難い重大な事由)',
];
export const Regex = {
  PASSWORD_POLICY:
    // eslint-disable-next-line no-useless-escape
    /^(?=.*[a-z])(?=.*[A-Z])[A-Za-z\d!\"#$%&'()*+,-./:;<=>?@^_`{|}~\[\]]{8,}$/,
  KATAKANA: /^[ｧ-ﾝﾞﾟァ-・ヽヾ゛゜ー()-.（-）]+$/,
  // eslint-disable-next-line no-useless-escape
  URL: /^(https?:\/\/)?((([a-z\d]([a-z\d-]*[a-z\d])*)\.)+[a-z]{2,}|((\d{1,3}\.){3}\d{1,3}))(\:\d+)?(\/[-a-z\d%_.~+]*)*(\?[;&a-z\d%_.~+=-]*)?(\#[-a-z\d_]*)?$/i,
  PASSWORD: /^[a-zA-Z0-9!@#$%^&*-?_]{8,}$/,
  PHONE: /^[0-9]{10,13}$/,
  WHITESPACE: /\s/,
  EMAIL:
    // eslint-disable-next-line no-useless-escape, no-control-regex
    /[\s]{0,}((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))[\s]{0,}$/,
};

export enum MomentFormat {
  MONTH_YEAR_SHORT = 'MM/YY',
  YEAR_MONTH_DATE = 'YYYY/MM/DD',
  JP_YEAR_MONTH_DATE = 'YYYY年MM月DD日',
  YEAR_MONTH = 'YYYY/MM',
  YEAR_MONTH_DASH = 'YYYY-MM',
  YEAR_MONTH_DATE_HOUR = 'YYYY/MM/DD HH:mm',
  YEAR_MONTH_DATE_HOUR_DASH = 'YYYY-MM-DD HH:mm',
  HOUR_YEAR_MONTH_DATE = 'HH:mm YYYY-MM-DD',
  JP_YEAR_MONTH_DATE_HOUR = 'YYYY年MM月DD日 HH:mm',
  YEAR_MONTH_DATE_HOUR_MS = 'YYYY/MM/DD HH:mm:ss',
  JP_YEAR_MONTH_DATE_HOUR_MS = 'YYYY年MM月DD日(ddd)HH時mm分',
  JP_YEAR_MONTH_DATE_DAY = 'YYYY年MM月DD日(ddd)',
  JP_HOUR_MINUTE = 'HH時mm分',
  DOT_YEAR_MONTH_DATE = 'YYYY.MM.DD',
  JP_YEAR = 'YYYY年',
  JP_MONTH_DATE = 'MM月DD日',
  JP_DAY_NAME = 'ddd',
  JP_YEAR_MONTH_DATE_TIME = 'YYYY年MM月DD日 HH時mm分',
}

export enum ROLES {
  LAWYER = 'LAWYER',
  COUNSELOR = 'COUNSELOR',
  CUSTOMER = 'CUSTOMER',
  GUEST = 'GUEST',
}

export const API_PATH = {
  LAWYER: 'provider',
  CUSTOMER: 'consumer',
  COUNSELOR: 'provider',
  GUEST: 'guest',
};

export const FOOTER_ROUTES = [
  {
    url: '/company-info',
    text: i18n.t('footer.companyOverview'),
  },
  {
    url: '/policy',
    text: i18n.t('footer.policy'),
  },
  {
    url: '/terms',
    text: i18n.t('footer.term'),
  },
  {
    url: '/commercial-transactions-law',
    text: i18n.t('footer.commercial'),
  },
  {
    url: '/contact-us',
    text: i18n.t('footer.inquiry'),
  },
  {
    url: '/faq',
    text: i18n.t('footer.faq'),
  },
  {
    url: '/media/',
    text: i18n.t('footer.media'),
    newTab: true,
  },
];

export const PURPOSE_OF_USE = ['法律相談', 'カウンセリング'];

export const PENSION_TYPES = ['国民年金', '厚生年金'];

export const SCHOOL_TYPES = ['公立', '国立', '私立'];

export const LAWYER_AGES = [
  '20 代 〜 30 代',
  '30 代 〜 40 ',
  '40 代 〜 50 ',
  '50 代 〜 60 ',
  '60 代 以上',
];

export const CONSULTATION_REDIRECT_STEP = {
  0: '/customer/consultation-form',
  1: '/customer/consultation-form?step=profile',
  2: '/customer/consultation-form?step=divorce-background',
  3: '/customer/consultation-form?step=partner-information',
  4: '/customer/consultation-form?step=marriage-information',
  5: '/customer/consultation-form?step=property-information',
  6: '/customer/consultation-form?step=kids-information',
  7: '/customer/consultation-form?step=expect-lawyer',
  8: '/customer/recommend',
};

export enum BookingStatusType {
  WAITING_ANWSER_FROM_PROVIDER = 'WAITING_ANWSER_FROM_PROVIDER',
  SCHEDULE_IS_DECIDED = 'SCHEDULE_IS_DECIDED',
  SCHEDULE_IS_ADJUSTED = 'SCHEDULE_IS_ADJUSTED',
  FINISHED = 'FINISHED',
  ASSIGNED = 'ASSIGNED',
  CANCELED_ASSIGNED_BY_CONSUMER = 'CANCELED_ASSIGNED_BY_CONSUMER',
  CANCELED_ASSIGNED_BY_PROVIDER = 'CANCELED_ASSIGNED_BY_PROVIDER',
  CANCELED_BY_CONSUMER = 'CANCELED_BY_CONSUMER',
  CANCELED_BY_PROVIDER = 'CANCELED_BY_PROVIDER',
  // TODO: Update if there is feature
  // CANCELED_BY_OFFICE = 'CANCELED_BY_OFFICE',
  // CANCELED_BY_ADMIN = 'CANCELED_BY_ADMIN',
}

export const BOOKING_STATUS_TAG = {
  WAITING_ANWSER_FROM_PROVIDER: {
    label: '未回答',
    color: '#fff4c7',
    textColor: '#e6b900',
  },
  SCHEDULE_IS_ADJUSTED: {
    label: '面談日程調整中',
    color: '#fdebce',
    textColor: '#ef950b',
  },
  SCHEDULE_IS_DECIDED: {
    label: '面談実施予定',
    color: '#dcf4e4',
    textColor: '#44a765',
  },
  CANCELED_BY_PROVIDER: {
    label: '面談キャンセル(弁護士)',
    textColor: '#e45d44',
    color: '#fbe8e4',
  },
  CANCELED_BY_CONSUMER: {
    label: '面談キャンセル(相談者)',
    textColor: '#e45d44',
    color: '#fbe8e4',
  },
  FINISHED: {
    label: '面談実施済み',
    color: '#e0eef5',
    textColor: '#3d8fb8',
  },
  ASSIGNED: {
    label: '受任',
    color: '#f1e8f7',
    textColor: '#9857c7',
  },
  CANCELED_ASSIGNED_BY_CONSUMER: {
    label: '受任なし(相談者)',
    textColor: 'neutral7',
    color: 'neutral2',
  },
  CANCELED_ASSIGNED_BY_PROVIDER: {
    label: '受任なし(弁護士)',
    textColor: 'neutral7',
    color: 'neutral2',
  },
};

export enum CaseStatusType {
  WAITING_ANWSER_FROM_PROVIDER = 'WAITING_ANWSER_FROM_PROVIDER',
  SCHEDULE_IS_ADJUSTED = 'SCHEDULE_IS_ADJUSTED',
  ASSIGNED = 'ASSIGNED',
  CANCELED_BY_CONSUMER = 'CANCELED_BY_CONSUMER',
  CANCELED_BY_PROVIDER = 'CANCELED_BY_PROVIDER',
  CLOSED = 'CLOSED',
}

export enum MeetingStatusType {
  WAITING_ANWSER_FROM_PROVIDER = 'WAITING_ANWSER_FROM_PROVIDER',
  WAITING_FOR_REPLY_FROM_CUSTOMER = 'WAITING_FOR_REPLY_FROM_CUSTOMER',
  SCHEDULE_IS_DECIDED = 'SCHEDULE_IS_DECIDED',
  CANCELED_BY_PROVIDER = 'CANCELED_BY_PROVIDER',
  CANCELED_BY_CONSUMER = 'CANCELED_BY_CONSUMER',
  FINISHED = 'FINISHED',
  CANCELED_BY_PROVIDER_COUNSELOR = 'CANCELED_BY_PROVIDER_COUNSELOR',
  // COMPLETE_PAYMENT = 'COMPLETE_PAYMENT',
  // PAYMENT_ERROR = 'PAYMENT_ERROR',
}

export const CASE_STATUS_TAG = {
  WAITING_ANWSER_FROM_PROVIDER: {
    label: '未回答',
    textColor: '#e6b900',
    color: '#fff4c7',
  },
  SCHEDULE_IS_ADJUSTED: {
    label: '面談実施中',
    textColor: '#ef950b',
    color: '#fdebce',
  },
  ASSIGNED: {
    label: '受任',
    color: '#f1e8f7',
    textColor: '#9857c7',
  },
  CANCELED_BY_CONSUMER: {
    label: '受任なし(相談者)',
    textColor: 'neutral7',
    color: 'neutral2',
  },
  CANCELED_BY_PROVIDER: {
    label: '受任なし(弁護士)',
    textColor: 'neutral7',
    color: 'neutral2',
  },
  CLOSED: {
    label: '案件終了',
    textColor: 'neutral7',
    color: 'neutral2',
  },
};

export const CASE_MEETING_TAG = {
  WAITING_ANWSER_FROM_PROVIDER: {
    label: '未回答',
    textColor: '#e6b900',
    color: '#fff4c7',
  },
  WAITING_FOR_REPLY_FROM_CUSTOMER: {
    label: '折り返し待ち',
    textColor: '#D255C6',
    color: '#F6DFF4',
  },
  SCHEDULE_IS_DECIDED: {
    label: '面談実施予定',
    textColor: '#3d8fb8',
    color: '#e0eef5',
  },
  FINISHED: {
    label: '面談実施済み',
    color: '#dcf4e4',
    textColor: '#44a765',
  },
  CANCELED_BY_PROVIDER: {
    label: '面談キャンセル(弁護士)',
    textColor: '#E45D44',
    color: '#FBE8E4',
  },
  CANCELED_BY_CONSUMER: {
    label: '面談キャンセル(相談者)',
    textColor: '#E45D44',
    color: '#FBE8E4',
  },
  CANCELED_BY_PROVIDER_COUNSELOR: {
    label: '面談キャンセル(カウンセラー)',
    textColor: '#E45D44',
    color: '#FBE8E4',
  },
};

export const CardIcon = {
  Visa: <VisaCardIcon />,
  MasterCard: <MasterCardIcon />,
  Mastercard: <MasterCardIcon />,
  JCB: <JCBCardIcon />,
  'Diners Club': <DinerCardIcon />,
  'American Express': <AmexCardIcon />,
};

export const MEETING_DURATION = {
  30: '30分',
  60: '60分',
  90: '90分',
  120: '120分',
};

export const LIST_DURATION = {
  30: '30',
  60: '60',
  90: '90',
  120: '120',
};
export const LIST_DURATION_TEXT = {
  30: 'クイック相談（30分）',
  60: 'カウンセリング（60分）',
  90: 'カウンセリング（90分）',
  120: 'カウンセリング（120分）',
};

// Function to get menu text based on meeting type
export const getMenuText = (meetingType: string): string => {
  if (meetingType === 'SINGLE') {
    return 'カウンセリング';
  }
  if (meetingType === 'PARTNER') {
    return '夫婦カウンセリング';
  }
  return 'カウンセリング'; // fallback
};

// Function to format menu text for filter summary: "<menuText>（<duration>分）"
export const getFilterMenuText = (
  meetingType: string,
  duration: number,
): string => {
  const menuText = getMenuText(meetingType);
  return `${menuText}（${duration}分）`;
};

// Function to format menu text for provider item: "<duration>分 <menuText>"
export const getProviderMenuText = (
  meetingType: string,
  duration: number,
): string => {
  const menuText = getMenuText(meetingType);
  return `${duration}分 ${menuText}`;
};

export const MEETING_TYPE_TEXT = {
  IN_PERSON: '対面での面談',
  ONLINE: 'オンライン面談',
};

export const MAX_LIMIT = 9999;

export const PAYMENT_METHOD = {
  CREDIT_CARD: 'クレジットカード',
  CASH: '現金支払い',
  BANK_TRANSFER: '外部決済',
};

export enum PaymentStatusType {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  CANCELED = 'CANCELED',
  ERROR = 'ERROR',
}

export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',
  CASH = 'CASH',
  BANK_TRANSFER = 'BANK_TRANSFER',
}

export const PAYMENT_METHOD_TEXT = {
  CREDIT_CARD: i18n.t('caseDetail.creditCard'),
  CASH: i18n.t('caseDetail.cashPayment'),
  BANK_TRANSFER: i18n.t('caseDetail.bankTransfer'),
};

export const PAYMENT_TEXT = {
  [PaymentStatusType.PENDING]: {
    color: '#e6b900',
    text: i18n.t('caseDetail.paymentPending'),
  },
  [PaymentStatusType.COMPLETED]: {
    color: 'success.main',
    text: i18n.t('caseDetail.paymentComplete'),
  },
  [PaymentStatusType.CANCELED]: {
    color: 'icon',
    text: i18n.t('caseDetail.paymentCancel'),
  },
  [PaymentStatusType.ERROR]: {
    color: 'error.main',
    text: i18n.t('caseDetail.paymentError'),
  },
};

export const PAYMENT_METHOD_OPTIONS = [
  {
    _id: PaymentMethod.CREDIT_CARD,
    value: PAYMENT_METHOD.CREDIT_CARD,
    icon: <PaymentCardIcon />,
  },
  {
    _id: PaymentMethod.CASH,
    value: PAYMENT_METHOD.CASH,
    icon: <CashIcon />,
  },
  {
    _id: PaymentMethod.BANK_TRANSFER,
    value: PAYMENT_METHOD.BANK_TRANSFER,
    icon: <TransferIcon />,
  },
];

export const dropdownMenus = [
  {
    icon: EditIcon,
    label: i18n.t('global.edit'),
    key: 'edit',
  },
  {
    icon: CloseIcon,
    label: i18n.t('confirmMeeting.cancelMeeting'),
    key: 'cancel',
    iconProps: {
      sx: {
        color: 'error.main',
      },
    },
    labelProps: {
      sx: {
        color: 'error.main',
      },
    },
  },
];

export enum ProviderType {
  LAWYER = 'LAWYER',
  COUNSELOR = 'COUNSELOR',
}

export const PROVIDER_MY_PAGE_PATH = {
  LAWYER: '/lawyer',
  COUNSELOR: '/counselor',
};

export const PROVIDER_LIST_PATH = {
  LAWYER: '/lawyers',
  COUNSELOR: '/counselors',
};

export const WORK_TIMES = [
  '08:00',
  '08:30',
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '13:00',
  '13:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30',
  '17:00',
  '17:30',
  '18:00',
  '18:30',
  '19:00',
  '19:30',
  '20:00',
  '20:30',
  '21:00',
  '21:30',
  '22:00',
  '22:30',
  '23:00',
  '23:30',
];

export enum SlotStatus {
  CLOSED_BY_PROVIDER = 'CLOSED_BY_PROVIDER',
  CLOSED_BY_GAP_TIME = 'CLOSED_BY_GAP_TIME',
  OPEN = 'OPEN',
  OPEN_HAS_MEETING = 'OPEN_HAS_MEETING',
  CLOSED_BY_MEETING = 'CLOSED_BY_MEETING',
}

export const DATE_COLOR: Record<number, string> = {
  0: 'error',
  6: 'steelBlue',
  5: 'text.primary',
  4: 'text.primary',
  3: 'text.primary',
  2: 'text.primary',
  1: 'text.primary',
};

export const DATE_COLOR_HEADING: Record<number, string> = {
  0: 'error',
  6: 'steelBlue',
  5: 'heading',
  4: 'heading',
  3: 'heading',
  2: 'heading',
  1: 'heading',
};

export const PREFERENCE_GENDER = {
  male: '男性',
  female: '女性',
  other: '希望なし',
} as const;

export const YES_NO_OPTIONS = {
  yes: 'はい',
  no: 'いいえ',
  notSure: 'わからない',
};

export const INQUIRY_CATEGORY = {
  REGISTER_AS_AN_EXPERT: '専門家としての登録を希望',
  ABOUT_THE_SERVICE: 'サービスについて',
  OTHERS: 'その他',
} as const;

export const PROVIDER_LIMIT = 20;
export const PROVIDER_FILTER_LIMIT = 9999;

export enum BankAccountType {
  saving = 'saving',
  standard = 'standard',
}
export enum BankAccountOption {
  saving = '当座',
  standard = '普通',
}

export const BANK_ACCOUNT_TYPE: {
  [key: string]: { key: string; labelEN: string; labelJP: string };
} = {
  STANDARD: {
    key: 'standard',
    labelEN: 'standard',
    labelJP: '普通',
  },
  SAVING: {
    key: 'saving',
    labelEN: 'saving',
    labelJP: '当座',
  },
};

export const BANK_ACCOUNT_TYPE_TEXT = {
  STANDARD: '普通',
  SAVING: '当座',
};

export const ARTICLE_SORT_ORDER = 'order.desc';

export const LOCAL_STORAGE_KEY = 'lawyer-web';

export enum ConsultationMenuMeetingType {
  SINGLE = '個人カウンセリング',
  PARTNER = '夫婦カウンセリング',
}

export const MEETING_TYPE_OPTIONS = [
  {
    _id: IMeetingType.SINGLE,
    value: ConsultationMenuMeetingType.SINGLE,
  },
  {
    _id: IMeetingType.PARTNER,
    value: ConsultationMenuMeetingType.PARTNER,
  },
];

export const COUNTDOWN_RESEND_OTP = 60;

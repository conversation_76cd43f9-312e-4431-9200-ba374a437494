/* eslint-disable import/no-cycle */
import type { Mutation, QueryClientConfig } from '@tanstack/react-query';
import { MutationCache, QueryCache, QueryClient } from '@tanstack/react-query';
import { t } from 'i18n';
import { get } from 'lodash';
import Router from 'next/router';
import Helper from 'utils/helpers';

import errors from './errors';

const handleError = (
  error: unknown,
  _: unknown,
  context?: unknown,
  mutation?: Mutation<unknown, unknown, unknown, unknown>,
) => {
  const NOT_TOAST_CODES = get(
    mutation?.meta,
    'NOT_TOAST_CODES',
    [],
  ) as string[];
  let errorMessage: string = get(error, 'error') as unknown as string;
  const errorCode = get(error, 'code');
  const actionContext = get(context, 'action');

  if (context && actionContext) {
    errorMessage = errors[`${actionContext}_${errorCode}` as never];
  }
  if (errorMessage === 'Network Error') {
    errorMessage = t('global.networkError');
  }
  if (errorCode === 'E_UNAUTHORIZED_ACCESS') {
    Helper.removeWebCookie();
    setTimeout(() => Router.reload());
  }
  const guestWebCookie = Helper.getWebCookieByGuest();
  const notToastErrorCode = NOT_TOAST_CODES.concat([
    'CASE_CLOSED_ERROR',
    'MEETING_PAYMENT_ERROR',
    'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT',
    'PAYMENT_SERVICE_CARD_HAS_MEETING_NOT_CLOSED',
  ]);
  const notToastOfGuestErrorCode = NOT_TOAST_CODES.concat(['OBJECT_NOT_FOUND']);
  if (
    guestWebCookie &&
    (!errorCode || (errorCode && !notToastOfGuestErrorCode.includes(errorCode)))
  ) {
    Helper.toast(errorMessage, { type: 'error', toastId: errorMessage });
  } else if (
    guestWebCookie &&
    errorCode &&
    notToastOfGuestErrorCode.includes(errorCode)
  ) {
    Helper.removeWebCookieByGuest();
    if (window.location.href.indexOf('/guest/counselor-booking') > -1) {
      window.location.href = `${window.location.href}&isFailCallback=${true}`;
    }
    if (window.location.href.indexOf('/guest/counselor-form') > -1) {
      setTimeout(() => Router.reload(), 0);
    }
  }

  if (
    !guestWebCookie &&
    (!errorCode ||
      (errorCode &&
        !notToastErrorCode.includes(errorCode) &&
        !notToastErrorCode.includes(errorMessage)))
  ) {
    if (window.location.href.indexOf('/guest') < 0)
      Helper.toast(errorMessage, { type: 'error', toastId: errorMessage });
    if (
      window.location.href.indexOf('/guest') > -1 &&
      errorCode &&
      !notToastOfGuestErrorCode.includes(errorCode)
    ) {
      Helper.toast(errorMessage, { type: 'error', toastId: errorMessage });
    }
  }
};

export const queryClientConfig: QueryClientConfig = {
  defaultOptions: {
    queries: {
      structuralSharing: true,
      refetchOnWindowFocus: false,
      retry: false,
      suspense: false,
      networkMode: 'offlineFirst',
    },
    mutations: {
      networkMode: 'offlineFirst',
    },
  },
  mutationCache: new MutationCache({
    onError: (error, variables, context, mutation) => {
      handleError(error, variables, context, mutation);
    },
  }),
  queryCache: new QueryCache({
    onError: (error, query) => handleError(error, query),
  }),
};

const queryClient = new QueryClient(queryClientConfig);

export default queryClient;

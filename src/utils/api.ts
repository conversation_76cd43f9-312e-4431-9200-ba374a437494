/* eslint-disable import/no-cycle */
import type { AxiosHeaders } from 'axios';
import axios from 'axios';
import { get } from 'lodash';

import Helper from './helpers';

const api = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_API_SERVER_BASE_URL}`,
});

api.interceptors.request.use((config) => {
  const requestConfig = {
    ...config,
  };
  const webCookie = Helper.getWebCookie();
  requestConfig.headers = (requestConfig.headers ?? {}) as AxiosHeaders;
  requestConfig.headers.set(
    'Accept-Timezone',
    Intl.DateTimeFormat().resolvedOptions().timeZone,
  );
  if (webCookie?.token) {
    requestConfig.headers.set('Authorization', `Bearer ${webCookie?.token}`);
  }

  return requestConfig;
});

api.interceptors.response.use(
  (response) => response.data,
  ({ message, response }) => {
    if (
      get(response, 'data.error.code') === 209 &&
      typeof window !== 'undefined'
    ) {
      // destroyCookie(null, 'nw-cookie');
      // window.location = "/";
    }
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject({
      data: get(response, 'data.data'),
      error:
        get(response, 'data.message') || get(response, 'data.error', message),
      code: get(response, 'data.code', response?.status || -1),
    });
  },
);

export default api;

import type { IArticleDetail, IPickupArticleDetail } from 'hooks/types';
import { get } from 'lodash';
import type { ICounselorItem, ILawyerItem } from 'models/provider/interface';

import { ProviderType } from './constants';

const seoConfig = {
  default: {
    title: 'リコ活 | 離婚、夫婦問題・修復も無料で相談',
    description:
      'あなたの悩みにぴったりの、弁護士とカウンセラーに無料相談。リコ活独自の専門家とカルテで、離婚、夫婦問題・修復も解決',
    type: 'website',
    openGraph: {
      site_name: 'リコ活 | 離婚、夫婦問題・修復も無料で相談',
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_DOMAIN}/images/ricokatsu-og.jpg`,
          width: 1200,
          height: 630,
          alt: 'ricokatsu-og',
        },
      ],
    },
    twitter: {
      cardType: 'summary_large_image',
      site: 'リコ活',
      handle: 'リコ活',
    },
  },
  providerList: {
    title: '専門家一覧 | リコ活',
  },
  articleList: {
    title: '記事一覧 | リコ活',
  },
  companyInfo: {
    title: '運営会社 | リコ活',
  },
  policy: {
    title: 'プライバシーポリシー | リコ活',
  },
  term: {
    title: '利用規約 | リコ活',
  },
  contactUs: {
    title: 'お問い合わせ | リコ活',
  },
  aboutUs: {
    title: 'リコ活とは',
  },
  qAndA: {
    title: 'ガイド(Q&A) | リコ活',
  },
  commercial: {
    title: '特定商取引法に基づく表記 | リコ活',
  },
  providerDetail: (data: ILawyerItem | ICounselorItem) => ({
    description:
      'あなたの悩みにぴったりの、弁護士とカウンセラーに無料相談。リコ活独自の専門家とカルテで、離婚、夫婦問題・修復も解決',
    site_name: 'リコ活 | 離婚、夫婦問題・修復も無料で相談',
    title:
      data.type === ProviderType.LAWYER
        ? `${data.fullName} | ${data.office.fullName} | リコ活`
        : `${data.fullName} | リコ活`,
    openGraph: {
      images: [
        {
          url:
            get(data, 'images[0].image.thumbMedium') ||
            get(data, 'images[0].thumbSharing') ||
            get(data, 'images[0].originUrl') ||
            `${process.env.NEXT_PUBLIC_DOMAIN}/images/ricokatsu-og.jpg`,
          alt: get(data, 'fullName'),
          width: 200,
          height: 200,
        },
      ],
    },
    twitter: {
      cardType: 'summary',
    },
  }),
  articleDetail: (data: IArticleDetail) => ({
    title: `${data.title} | リコ活`,
    description: data.leadParagraph,
    site_name: 'リコ活 | 離婚、夫婦問題・修復も無料で相談',
    openGraph: {
      images: [
        {
          url:
            get(data, 'image.thumbMedium') ||
            get(data, 'image.thumbSharing') ||
            get(data, 'image.originUrl') ||
            `${process.env.NEXT_PUBLIC_DOMAIN}/images/ricokatsu-og.jpg`,
          alt: get(data, 'title'),
        },
      ],
    },
  }),
  pickupArticleDetail: (data: IPickupArticleDetail) => ({
    title: `${data.title} | リコ活`,
    description: data.leadParagraph,
    site_name: 'リコ活 | 離婚、夫婦問題・修復も無料で相談',
    openGraph: {
      images: [
        {
          url:
            get(data, 'image.thumbMedium') ||
            get(data, 'image.thumbSharing') ||
            get(data, 'image.originUrl') ||
            `${process.env.NEXT_PUBLIC_DOMAIN}/images/ricokatsu-og.jpg`,
          alt: get(data, 'title'),
        },
      ],
    },
  }),
};

export default seoConfig;

import type { ICoupon } from 'models/coupon/interface';

import { MeetingStatusType, PaymentStatusType } from './constants';

const CaseUtil = {
  getBasicPrice: (unitPrice?: string | number, duration?: string | number) =>
    Math.floor((Number(unitPrice || 0) * Number(duration || 0)) / 30),
  getDisplayPaymentStatus: ({
    paymentStatus,
    meetingStatus,
  }: {
    paymentStatus: PaymentStatusType;
    meetingStatus: MeetingStatusType;
  }) => {
    if (paymentStatus === PaymentStatusType.PENDING) {
      if (
        [
          MeetingStatusType.CANCELED_BY_CONSUMER,
          MeetingStatusType.CANCELED_BY_PROVIDER,
          MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
        ].includes(meetingStatus)
      ) {
        return PaymentStatusType.CANCELED;
      }
      return PaymentStatusType.PENDING;
    }
    return PaymentStatusType[paymentStatus];
  },
  getCouponPrice: ({
    basicPrice,
    couponAmount,
  }: {
    basicPrice: number;
    couponAmount: number;
  }) => {
    const usedCouponPrice = Math.max(0, basicPrice - couponAmount);
    return { couponAmount, usedCouponPrice };
  },

  calculateFinalCouponAmount: ({
    totalPrice,
    issuedCoupon,
  }: {
    issuedCoupon: Omit<ICoupon, 'couponAmount'>;
    totalPrice: number;
  }) => {
    let couponAmount = issuedCoupon.amount;

    if (issuedCoupon.currency === '%') {
      couponAmount = Math.floor((totalPrice * couponAmount) / 100);
    }

    // Check if the coupon amount is greater than the maximum allowable discount
    const { maxDiscountAmount } = issuedCoupon.rules;
    if (maxDiscountAmount && couponAmount > maxDiscountAmount) {
      couponAmount = maxDiscountAmount;
    }

    return couponAmount;
  },
};

export default CaseUtil;

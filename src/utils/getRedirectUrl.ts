import { NON_LOGIN_ROUTE, ROLES } from './constants';

const removeTrailingStalsh = (url: string) => url.replace(/\/$/, '');

const getRedirecttUrl = ({
  role,
  nextUrl,
}: {
  role?: ROLES;
  nextUrl: string;
}) => {
  const nextUrlWithoutSlash = removeTrailingStalsh(nextUrl) || '/';
  const defaultRedirectUrl = '/';
  const lawyerRedirectUrl = '/lawyer/my-page';
  const counselorRedirectUrl = '/counselor/my-page';

  const isLawyerRoute =
    nextUrlWithoutSlash.startsWith('/lawyer') &&
    !nextUrlWithoutSlash.startsWith('/lawyers');

  const isCounselorRoute =
    nextUrlWithoutSlash.startsWith('/counselor') &&
    !nextUrlWithoutSlash.startsWith('/counselors');

  const isCustomerRoute =
    nextUrlWithoutSlash.startsWith('/customer') &&
    !['/customer/verify-email'].includes(nextUrlWithoutSlash);
  const isNonLoginRoute = NON_LOGIN_ROUTE.includes(nextUrlWithoutSlash);

  if (isLawyerRoute || isCustomerRoute || isCounselorRoute) {
    if (
      !role ||
      (isLawyerRoute && role !== ROLES.LAWYER) ||
      (isCustomerRoute && role !== ROLES.CUSTOMER) ||
      (isCounselorRoute && role !== ROLES.COUNSELOR)
    ) {
      return defaultRedirectUrl;
    }
  }
  if (isNonLoginRoute && role) {
    return defaultRedirectUrl;
  }
  if (
    (NON_LOGIN_ROUTE.concat('/').includes(nextUrlWithoutSlash) ||
      nextUrlWithoutSlash.startsWith('/counselors') ||
      nextUrlWithoutSlash.startsWith('/lawyers') ||
      nextUrlWithoutSlash.startsWith('/articles') ||
      nextUrlWithoutSlash.startsWith('/pickup-articles')) &&
    role &&
    [ROLES.LAWYER, ROLES.COUNSELOR].includes(role)
  ) {
    return role === ROLES.LAWYER ? lawyerRedirectUrl : counselorRedirectUrl;
  }
  return undefined;
};

export default getRedirecttUrl;

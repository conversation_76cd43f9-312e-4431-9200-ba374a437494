import type { ROLES } from 'utils/constants';

export type IMessagesUnread = {
  receiverId: string;
  senderId: string;
  isSendMail: boolean;
  receiverEmail: string;
  senderEmail: string;
  messageId: string;
  roomPath: string;
};

export type IRoom = {
  id: string;
  status: string;
  type: string;
  currentMembers: string[];
  allMembers: string[];
  numberOfUnreadMessages: Record<string, number>;
  readingTime: Record<string, string>;
  lastMessage: IMessage;
  updatedAt: string;
  createdAt: string;
  messagesUnread?: IMessagesUnread[];
};

export type IMessage = {
  id: string;
  type: 'TEXT' | 'IMAGE' | 'VIDEO' | 'FILE';
  value: string;
  updatedAt: string;
  createdAt: string;
  senderId: string;
  attachments: IAttachmentItem[];
};

export type IAttachmentItem = {
  url: string;
  name: string;
  contentType: string;
  id: string;
  status: string;
  width?: number;
  height?: number;
  size: number;
  type: FileType;
  duration?: number;
};

export type IUserDoc = {
  email: string;
  id: string;
  numberOfUnreadMessages: Record<string, number>;
  role: ROLES;
  avatar?: string;
  unreadRooms: string[];
  fullNameKata?: string;
  fullName?: string;
  status: 'ACTIVE' | 'INACTIVE';
  code?: string;
  forceLogoutAt?: string;
};

export enum FileType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  FILE = 'FILE',
}

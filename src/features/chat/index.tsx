// import EmptyMessage from '@icons/graphics/empty-message.svg';
// import EmptyConversation from '@icons/graphics/no-conversation.svg';
import {
  Avatar,
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  IconButton,
  Typography,
} from '@mui/material';
import type { InfiniteData } from '@tanstack/react-query';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import Message from 'features/chat/components/Message';
import RoomList from 'features/chat/components/RoomList';
import useGetMessageList from 'features/chat/hooks/useGetMessageList';
import useGetRoomList from 'features/chat/hooks/useGetRoomList';
import useSubscribeMessage from 'features/chat/hooks/useSubscribeMessage';
import { chatService } from 'features/chat/service';
import type { IMessage, IUserDoc } from 'features/chat/types';
import { useFetchUser } from 'hooks';
import useMutate from 'hooks/useMutate';
import { ChatArrowDown, ChatArrowLeft, EmptyChat } from 'icons';
import type { IRoomInfo } from 'models/chat/types';
import type {
  SignedUrlItem,
  SignedUrlPayload,
} from 'models/resource/interface';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import { ROLES } from 'utils/constants';

import EmptyPage from './components/EmptyPage';
import MessageInput from './components/MessageInput';
import useSubscribeRoomList from './hooks/useSubscribeRoomList';
import styles from './styles';
import Utils from './utils';

const ChatFeature = ({
  roomDetail: serverRoomDetail,
  role,
}: {
  roomDetail: IRoomInfo;
  role: ROLES;
}) => {
  const [backButtonDisplay, setBackButtonDisplay] = useState({
    display: false,
    isNewMessage: false,
  });
  const { query, replace } = useRouter();
  const roomId = (query.roomId && query.roomId[0]) || '';
  const isDetailPage = !!roomId;
  const qClient = useQueryClient();
  const messageListRef = useRef<HTMLDivElement>(null);
  const { ref: messageLoaderRef, inView } = useInView();
  const { data: currentUser } = useFetchUser({
    enabled: true,
  });
  const firebaseUserId = currentUser?.firebaseUserId;

  const { data: roomUsers } = useQuery<Record<string, IUserDoc>>({
    queryKey: ['currentUser', 'rooms', 'members'],
    enabled: false,
    staleTime: Infinity,
  });

  const serverOtherMember = serverRoomDetail?.memberInfo?.find(
    (member) => member.memberId !== firebaseUserId,
  );

  const { mutateAsync: signUrls } = useMutate<
    { signedUrls: SignedUrlPayload[] },
    { signedUrls: SignedUrlItem[] }
  >({
    apiUrl: '/media/common/signedUrls',
    method: 'post',
    retry: true,
  });
  const { mutateAsync: uploadToS3 } = useMutation<
    unknown,
    unknown,
    {
      url: string;
      file: File;
    }
  >({
    mutationKey: ['currentUser', 'uploadingToS3'],
    mutationFn: (params) =>
      axios.put(params.url, params.file, {
        headers: {
          'Content-Type': params.file.type,
        },
      }),
    retry: true,
  });

  const messageData = qClient.getQueryData<InfiniteData<IMessage[]>>([
    'currentUser',
    'messages',
    roomId,
  ]);
  const messageStartAfter =
    messageData?.pages[0] && messageData?.pages[0][0]?.createdAt;

  const {
    data,
    fetchNextPage,
    isFetchingNextPage: isFetchingNextMessagePage,
    isLoading: isLoadingMessages,
  } = useGetMessageList({ roomId, firebaseUserId });

  const { mutateAsync: readRoom } = useMutation({
    mutationFn: chatService.readRoom,
  });
  const {
    data: roomData,
    isLoading: isLoadingConversations,
    isFetched,
  } = useGetRoomList({
    firebaseUserId,
  });
  const hasMoreMessagePage = (data as { hasMore?: boolean })?.hasMore;

  const { data: snapShot, remove } = useSubscribeMessage({
    roomId,
    firebaseUserId,
    startAfter: messageStartAfter,
  });
  useSubscribeRoomList({ firebaseUserId });

  useEffect(() => {
    return () => {
      if (snapShot) {
        snapShot();
        remove();
      }
    };
  }, [remove, snapShot]);

  useEffect(() => {
    setBackButtonDisplay({ display: false, isNewMessage: false });
  }, [roomId]);

  useEffect(() => {
    if (roomId && isFetched && typeof window !== 'undefined') {
      window.document.getElementById(`room-${roomId}`)?.scrollIntoView();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFetched]);

  const messageList = data?.pages.flat() || [];
  const roomList = useMemo(
    () => roomData?.pages.flat() || [],
    [roomData?.pages],
  );

  const lastMessage = messageList[0];

  const roomDetail = useMemo(
    () => roomList.find((room) => room.id === roomId),
    [roomId, roomList],
  );
  const otherMemberId = roomDetail?.currentMembers.find(
    (id) => id !== firebaseUserId,
  );
  const otherMember =
    otherMemberId && roomUsers ? roomUsers[otherMemberId] : serverOtherMember;
  const unReadMessageNumber =
    roomDetail?.numberOfUnreadMessages &&
    !!firebaseUserId &&
    (roomDetail?.numberOfUnreadMessages[firebaseUserId] || 0);

  useEffect(() => {
    if (
      unReadMessageNumber &&
      unReadMessageNumber > 0 &&
      roomId &&
      firebaseUserId
    ) {
      readRoom({ roomId, userId: firebaseUserId });
    }
  }, [firebaseUserId, readRoom, roomId, unReadMessageNumber]);

  useEffect(() => {
    if (lastMessage && lastMessage.senderId === firebaseUserId) {
      setTimeout(() => messageListRef.current?.scrollTo(0, 0), 0);
    }
    if (
      lastMessage &&
      lastMessage.senderId !== firebaseUserId &&
      (messageListRef.current?.scrollTop || 0) < -100
    ) {
      setBackButtonDisplay({
        display: true,
        isNewMessage: true,
      });
    }
  }, [firebaseUserId, lastMessage]);

  useEffect(() => {
    if (inView && hasMoreMessagePage) {
      fetchNextPage();
    }
  }, [fetchNextPage, inView, hasMoreMessagePage]);

  const handleSendMessage = async ({
    attachments,
    message,
  }: {
    attachments: {
      url: string;
      name: string;
      contentType: string;
      file: File;
      id: string;
      width?: number;
      height?: number;
      size: number;
      duration?: number;
    }[];
    message?: string;
  }) => {
    const sendTime = new Date();
    if (attachments.length > 0) {
      const { id } = chatService.addLocalFileDataToQueryCache(
        ['currentUser', 'messages', roomId],
        {
          senderId: firebaseUserId,
          value: message || '',
          attachments: attachments.map((attachment) => ({
            url: attachment.url,
            name: attachment.name,
            contentType: attachment.contentType,
            id: attachment.id,
            width: attachment.width,
            height: attachment.height,
            size: attachment.size,
            duration: attachment.duration,
            type: Utils.getFileTypeFromContentType(attachment.contentType),
          })),
          roomId,
          createdAt: sendTime.toISOString(),
          updatedAt: sendTime.toISOString(),
          status: 'PENDING',
        },
        roomId,
      );
      signUrls(
        {
          signedUrls: attachments.map((attachment) => ({
            fileName: attachment.name,
            contentType: attachment.contentType,
            isPublic: true,
            group: 'image',
          })),
        },
        {
          onSuccess: async (signedUrlResult) => {
            const attachmentPayload: any[] = [];
            const promises: Promise<unknown>[] = [];
            signedUrlResult.signedUrls.forEach((signedUrlItem, index) => {
              const uploadAttachment = attachments[index];
              if (uploadAttachment) {
                attachmentPayload.push({
                  url: signedUrlItem.originUrl,
                  contentType: uploadAttachment.contentType,
                  name: uploadAttachment.name,
                  size: uploadAttachment.size,
                  type: Utils.getFileTypeFromContentType(
                    uploadAttachment.contentType,
                  ),
                  id: uploadAttachment.id,
                  ...(uploadAttachment.width && uploadAttachment.height
                    ? {
                        width: uploadAttachment.width,
                        height: uploadAttachment.height,
                      }
                    : {}),
                });
                promises.push(
                  uploadToS3({
                    url: signedUrlItem.url,
                    file: (uploadAttachment as { file: File }).file,
                  }),
                );
              }
            });
            await Promise.all(promises);
            chatService.sendMessage({
              id,
              value: message || '',
              roomId,
              senderId: firebaseUserId || '',
              receiverId: otherMemberId || serverOtherMember?.memberId || '',
              receiverEmail:
                otherMember?.email || serverOtherMember?.email || '',
              roomPath: Utils.getRoomPath({
                roomId,
                receiverRole:
                  otherMember?.role ||
                  serverOtherMember?.role ||
                  ROLES.CUSTOMER,
              }),
              attachments: attachmentPayload,
            });
          },
        },
      );
    } else if (roomId && message && message.trim()) {
      await chatService.sendMessage({
        value: message.trim(),
        roomId,
        senderId: firebaseUserId || '',
        receiverId: otherMemberId || serverOtherMember?.memberId || '',
        receiverEmail: otherMember?.email || serverOtherMember?.email || '',
        roomPath: Utils.getRoomPath({
          roomId,
          receiverRole:
            otherMember?.role || serverOtherMember?.role || ROLES.CUSTOMER,
        }),
      });
    }
  };

  if (isLoadingConversations) {
    return (
      <Box display="flex" justifyContent="center" marginTop={7}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container
      sx={{
        height: {
          xs: isDetailPage ? 'unset' : 'calc(100vh - 68px)',
          tablet: 'calc(100vh - 98px)',
        },
        maxWidth: 1216,
        padding: { xs: 0, tablet: '24px 16px' },
        ...(isDetailPage
          ? {
              '@media (max-width: 768px)': {
                position: 'fixed',
                width: '100%',
                top: '76px',
                bottom: '0px',
                maxHeight: '100%',
              },
            }
          : {}),
      }}
    >
      {!isDetailPage && !isLoadingConversations && roomList.length === 0 ? (
        <EmptyPage />
      ) : (
        <Box display="flex" gap={{ xs: 0, tablet: 2 }} sx={{ height: '100%' }}>
          {isLoadingConversations && (
            <Box display="flex" justifyContent="center">
              <CircularProgress />
            </Box>
          )}
          <RoomList
            data={roomList}
            firebaseUserId={firebaseUserId}
            isLoading={isLoadingConversations}
            roomId={roomId}
            roomUsers={roomUsers}
          />
          {isDetailPage ? (
            <Box
              display={{ xs: isDetailPage ? 'flex' : 'none', tablet: 'flex' }}
              padding={0}
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                width: 1,
                borderRadius: 2,
                overflow: 'hidden',
              }}
            >
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                padding={{ xs: '8px 16px', tablet: 2 }}
                bgcolor="white"
              >
                <Box display="flex" alignItems="center" gap={1}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <IconButton
                      size="small"
                      onClick={() => replace(`/${role.toLowerCase()}/messages`)}
                      sx={{
                        display: { tablet: 'none' },
                        svg: { width: '24px', height: '24px' },
                        p: 0,
                        borderRadius: 1,
                      }}
                    >
                      <ChatArrowLeft />
                    </IconButton>
                    <Avatar
                      alt=""
                      sx={{
                        width: { xs: 40, tablet: 48 },
                        height: { xs: 40, tablet: 48 },
                      }}
                      src={otherMember?.avatar || '/images/default-avatar.png'}
                    />
                    <Typography
                      fontWeight={700}
                      fontSize={{ xs: 14, tablet: 16 }}
                      color="heading"
                    >
                      {otherMember?.fullName}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Divider />
              {messageList.length === 0 && (
                <Box
                  display="flex"
                  alignItems="center"
                  flexDirection="column"
                  justifyContent="center"
                  padding="36px 16px"
                  sx={{ width: '100%', flex: 1 }}
                  bgcolor="white"
                >
                  {isLoadingMessages ? (
                    <Box display="flex" justifyContent="center" padding={16}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <>
                      <EmptyChat />
                      <Typography
                        fontSize={{ xs: 14, tablet: 16 }}
                        marginTop={2}
                      >
                        会話を開始しましょう
                      </Typography>
                    </>
                  )}
                </Box>
              )}
              {!isLoadingMessages && messageList.length > 0 && (
                <Box
                  display="flex"
                  flexDirection="column-reverse"
                  gap="4px"
                  padding={2}
                  ref={messageListRef}
                  sx={{
                    overflowY: 'auto',
                    width: '100%',
                    flex: 1,
                  }}
                  bgcolor="white"
                  onScroll={(e) => {
                    if (
                      (e.target as unknown as { scrollTop: number }).scrollTop >
                      -1
                    ) {
                      setBackButtonDisplay({
                        display: false,
                        isNewMessage: false,
                      });
                    } else if (
                      (e.target as unknown as { scrollTop: number }).scrollTop <
                      -100
                    ) {
                      setBackButtonDisplay((prevState) => ({
                        ...prevState,
                        display: true,
                      }));
                    }
                  }}
                >
                  {isLoadingMessages && (
                    <Box
                      display="flex"
                      justifyContent="center"
                      padding={'16px'}
                    >
                      <CircularProgress />
                    </Box>
                  )}
                  {!isLoadingMessages && messageList.length === 0 && (
                    <Box>
                      <EmptyChat />
                      <Typography color="gray-5" marginTop={4}>
                        会話を開始しましょう
                      </Typography>
                    </Box>
                  )}
                  {!isLoadingMessages &&
                    messageList.length > 0 &&
                    messageList.map((message, index) => {
                      const prevMessage = messageList[index + 1];
                      const nextMessage = messageList[index - 1];

                      let time = null;
                      time = Utils.formatMessageTime(
                        message.createdAt,
                        prevMessage?.createdAt,
                      );
                      let isStart = true;
                      if (prevMessage) {
                        isStart =
                          time !== null ||
                          (prevMessage &&
                            prevMessage?.senderId !== message?.senderId);
                      }

                      const isEnd =
                        index === 0 ||
                        Utils.formatMessageTime(
                          nextMessage?.createdAt,
                          message.createdAt,
                        ) !== null ||
                        (nextMessage &&
                          nextMessage.senderId !== message.senderId);
                      return (
                        <Message
                          currentUserId={firebaseUserId}
                          data={{
                            ...message,
                            profileImage: otherMember?.avatar,
                          }}
                          displayTime={time}
                          isEndMessage={isEnd && !isStart}
                          isOnlyOne={isEnd && isStart}
                          isStartMessage={isStart && !isEnd}
                          key={message.id}
                        />
                      );
                    })}
                  {hasMoreMessagePage && (
                    <Box
                      justifyContent="center"
                      padding="16px"
                      display="flex"
                      ref={isFetchingNextMessagePage ? null : messageLoaderRef}
                    >
                      <CircularProgress />
                    </Box>
                  )}
                </Box>
              )}
              <Divider />
              <Box position="relative">
                {backButtonDisplay.display &&
                  (backButtonDisplay.isNewMessage ? (
                    <Button
                      sx={styles.newMessageButton}
                      startIcon={<ChatArrowDown />}
                      onClick={() => {
                        messageListRef.current?.scrollTo({
                          top: 0,
                          behavior: 'smooth',
                        });
                      }}
                    >
                      新着メッセージ
                    </Button>
                  ) : (
                    <IconButton
                      sx={styles.newMessageButton}
                      onClick={() => {
                        messageListRef.current?.scrollTo({
                          top: 0,
                          behavior: 'smooth',
                        });
                      }}
                    >
                      <ChatArrowDown />
                    </IconButton>
                  ))}
                <MessageInput key={roomId} onSubmit={handleSendMessage} />
              </Box>
            </Box>
          ) : (
            <Box
              display={{ xs: !isDetailPage ? 'none' : 'flex', tablet: 'flex' }}
              bgcolor="white"
              sx={{
                flex: 1,
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 2,
                width: '100%',
              }}
            >
              <EmptyChat />
              <Typography fontSize={{ xs: 14, tablet: 16 }} marginTop={2}>
                チャットルームを選択してください
              </Typography>
            </Box>
          )}
        </Box>
      )}
    </Container>
  );
};

export default ChatFeature;

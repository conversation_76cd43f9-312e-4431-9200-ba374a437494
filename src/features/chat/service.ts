import type {
  InfiniteData,
  MutationFunction,
  QueryFunction,
  QueryFunctionContext,
  QueryKey,
} from '@tanstack/react-query';
import type {
  DocumentSnapshot,
  FirestoreDataConverter,
  QueryDocumentSnapshot,
  Timestamp,
} from 'firebase/firestore';
import {
  arrayRemove,
  arrayUnion,
  collection,
  doc,
  getDoc,
  getDocs,
  increment,
  limit,
  onSnapshot,
  orderBy,
  query,
  runTransaction,
  serverTimestamp,
  startAfter,
  where,
  writeBatch,
} from 'firebase/firestore';
import { chunk } from 'lodash';
import queryClient from 'utils/queryClient';

import { auth, db } from './firebase';
import type { IAttachmentItem, IMessage, IRoom, IUserDoc } from './types';

const PER_PAGE = 20;
// Marketmatch needs to fetch all conversation list based on AC of the ticket
const ROOM_PER_PAGE = 200;

interface DatabaseType {
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  lastMessage?: Omit<IMessage, 'createdAt' | 'updatedAt'> & {
    createdAt: Timestamp;
    updatedAt: Timestamp;
  };
}
interface LocalType {
  createdAt?: string;
  updatedAt?: string;
  lastMessage?: IMessage;
}

const genericConverter = <T>(): FirestoreDataConverter<T & LocalType> => ({
  toFirestore: (item) => ({
    updatedAt: serverTimestamp(),
    createdAt: serverTimestamp(),
    ...item,
  }),
  fromFirestore: (
    snapshot: QueryDocumentSnapshot<T & DatabaseType>,
    options,
  ) => {
    const data = snapshot.data(options);
    const lastMessage = data.lastMessage
      ? {
          ...data.lastMessage,
          createdAt: data.lastMessage.createdAt.toDate().toISOString(),
          updatedAt: data.lastMessage.updatedAt.toDate().toISOString(),
        }
      : undefined;
    return {
      ...data,
      lastMessage,
      createdAt: data.createdAt
        ? data.createdAt.toDate().toISOString()
        : undefined,
      updatedAt: data.updatedAt
        ? data.updatedAt.toDate().toISOString()
        : undefined,
    };
  },
});

const sendMessage: MutationFunction<
  { id: string },
  {
    value: string;
    roomId: string;
    senderId: string;
    receiverId: string;
    attachments?: IAttachmentItem[];
    id?: string;
    receiverEmail: string;
    roomPath: string;
  }
> = async (data) => {
  const currentUserId = auth.currentUser?.uid;
  const roomRef = doc(collection(db, 'rooms'), data.roomId);
  let messageRef;
  if (data.id) {
    messageRef = doc(collection(roomRef, 'messages'), data.id);
  } else messageRef = doc(collection(roomRef, 'messages'));
  const batch = writeBatch(db);
  batch.update(roomRef, {
    updatedAt: serverTimestamp(),
    [`numberOfUnreadMessages.${data.receiverId}`]: increment(1),
    [`numberOfUnreadMessages.${currentUserId}`]: 0,
    lastMessage: {
      ...data,
      id: roomRef.id,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      type: 'TEXT',
    },
    totalUnreadMessages: increment(1),
    messagesUnread: arrayUnion({
      receiverId: data.receiverId,
      senderId: currentUserId,
      isSendMail: false,
      receiverEmail: data.receiverEmail,
      messageId: messageRef.id,
      roomPath: data.roomPath,
    }),
  });
  const receiverRef = doc(collection(db, 'users'), data.receiverId);
  batch.update(receiverRef, {
    unreadRooms: arrayUnion(data.roomId),
    [`numberOfUnreadMessages.${data.roomId}`]: increment(1),
  });

  batch.set(
    messageRef.withConverter(genericConverter<IMessage>()),
    { ...data, id: messageRef.id },
    { merge: true },
  );
  await batch.commit();
  return { id: messageRef.id };
};

const getMessages = async (
  context: QueryFunctionContext<QueryKey, string> & { roomId: string },
) => {
  let date = new Date();
  if (context.pageParam) {
    date = new Date(context.pageParam);
  }
  const snapshot = await getDocs(
    query(
      collection(doc(collection(db, 'rooms'), context.roomId), 'messages'),
      orderBy('createdAt', 'desc'),
      where('createdAt', '<', date),
      limit(PER_PAGE),
    ).withConverter(genericConverter<IMessage>()),
  );
  return snapshot.docs.map((d) => ({ ...d.data(), id: d.id }));
};

const addMessageDataToQueryCache = <T>(
  key: QueryKey,
  data: T & { id: string; updatedAt: string },
) => {
  const cache =
    queryClient.getQueryData<
      InfiniteData<(T & { id: string; updatedAt: string })[]>
    >(key);
  const currentItems = cache?.pages.flat() || [];
  if (data.id === currentItems[0]?.id) {
    currentItems[0] = { ...data };
  } else {
    const existedMessageIndex = currentItems.findIndex(
      (item) => item.id === data.id,
    );
    if (existedMessageIndex && currentItems[existedMessageIndex]) {
      if (currentItems[existedMessageIndex]?.updatedAt !== data.updatedAt) {
        currentItems.splice(existedMessageIndex, 1);
        currentItems.unshift(data);
      } else {
        currentItems[existedMessageIndex] = { ...data };
      }
    } else {
      currentItems.unshift(data);
    }
  }
  const newData: T[][] = [];
  for (let i = 0; i < currentItems.length; i += PER_PAGE) {
    const currentPage = currentItems.slice(i, i + PER_PAGE);
    newData.push(currentPage);
  }
  queryClient.setQueryData<InfiniteData<T[]>>(key, (oldData) => {
    return {
      ...oldData,
      pageParams: oldData?.pageParams || [],
      pages: newData,
    };
  });
};

const addLocalFileDataToQueryCache = <T>(
  key: QueryKey,
  data: T,
  roomId: string,
) => {
  const roomRef = doc(collection(db, 'rooms'), roomId);
  const messageRef = doc(collection(roomRef, 'messages'));
  const cache =
    queryClient.getQueryData<InfiniteData<(T & { id: string })[]>>(key);
  const currentItems = cache?.pages.flat() || [];

  currentItems.unshift({ ...data, id: messageRef.id });
  const newData: T[][] = [];
  for (let i = 0; i < currentItems.length; i += PER_PAGE) {
    const currentPage = currentItems.slice(i, i + PER_PAGE);
    newData.push(currentPage);
  }
  queryClient.setQueryData<InfiniteData<T[]>>(key, (oldData) => {
    return {
      ...oldData,
      pageParams: oldData?.pageParams || [],
      pages: newData,
    };
  });
  return { id: messageRef.id };
};

const addConversationToQueryCache = <T>(
  key: QueryKey,
  data: T & { id: string; lastMessage: IMessage },
) => {
  const cache =
    queryClient.getQueryData<
      InfiniteData<(T & { lastMessage: IMessage; id: string })[]>
    >(key);
  let currentItems = cache?.pages.flat() || [];
  const itemIndex = currentItems.findIndex((item) => item.id === data.id);
  if (
    itemIndex >= 0 &&
    currentItems[itemIndex]?.lastMessage.updatedAt ===
      data.lastMessage.updatedAt
  ) {
    currentItems[itemIndex] = { ...data };
  } else {
    currentItems = currentItems.filter((item) => item.id !== data.id);
    currentItems.unshift(data);
  }

  const newData: T[][] = [];
  for (let i = 0; i < currentItems.length; i += PER_PAGE) {
    const currentPage = currentItems.slice(i, i + PER_PAGE);
    newData.push(currentPage);
  }

  queryClient.setQueryData<InfiniteData<T[]>>(key, (oldData) => {
    return {
      pageParams: oldData?.pageParams || [],
      pages: newData,
    };
  });
};

const readRoom = async ({
  roomId,
  userId,
}: {
  roomId: string;
  userId?: string;
}) => {
  const roomRef = doc(collection(db, 'rooms'), roomId);
  const currentUserRef = doc(collection(db, 'users'), userId);

  await runTransaction(db, async (transaction) => {
    const userDoc = await transaction.get(currentUserRef);
    const userData = userDoc.data() as IUserDoc;
    const roomDoc = await transaction.get(roomRef);
    const roomData = roomDoc.data() as IRoom;
    const roomTotalUnread = userData.numberOfUnreadMessages
      ? userData.numberOfUnreadMessages[roomId] || 0
      : 0;
    const messagesUnread = roomData?.messagesUnread || [];

    transaction.update(roomRef, {
      messagesUnread: messagesUnread
        .filter((item) => item.receiverId !== userId)
        .filter(Boolean),
      totalUnreadMessages: increment(-roomTotalUnread),
      [`numberOfUnreadMessages.${userId}`]: 0,
      [`readingTime.${userId}`]: serverTimestamp(),
    });
    transaction.update(currentUserRef, {
      unreadRooms: arrayRemove(roomId),
      [`numberOfUnreadMessages.${roomId}`]: 0,
    });
  });
};

const attachMessageListener = (
  payload: QueryFunctionContext<QueryKey, QueryKey> & {
    startAfter?: string;
    roomId: string;
  },
): (() => void) => {
  const messageQuery = query(
    collection(doc(collection(db, 'rooms'), payload.roomId), 'messages'),
    where(
      'createdAt',
      '>',
      payload.startAfter ? new Date(payload.startAfter) : new Date(),
    ),
  ).withConverter(genericConverter<IMessage>());
  return onSnapshot(messageQuery, (snap) => {
    const changes = snap.docChanges();
    changes.forEach((change) => {
      if (change.type === 'added') {
        const data = { ...change.doc.data(), id: change.doc.id };
        addMessageDataToQueryCache(
          ['currentUser', 'messages', payload.roomId],
          data,
        );
      }
    });
  });
};

const userDocConverter = <T>(): FirestoreDataConverter<
  T & { forceLogoutAt?: Timestamp }
> => ({
  toFirestore: (item) => item,
  fromFirestore: (
    snapshot: QueryDocumentSnapshot<T & { forceLogoutAt?: Timestamp }>,
    options,
  ) => {
    const data = snapshot.data(options);
    return {
      ...data,
    };
  },
});

const attachFirebaseUserListener = () => {
  const currentUserId = auth.currentUser?.uid;
  const userQuery = query(
    collection(db, 'users'),
    where('id', '==', currentUserId),
  ).withConverter(userDocConverter());
  const startListenTime = new Date().toISOString();
  return onSnapshot(userQuery, (snap) => {
    const changes = snap.docChanges();
    changes.forEach((change) => {
      const data = change.doc.data();
      queryClient.setQueryData(['currentUser', 'userDoc'], {
        ...data,
        forceLogoutAt: data.forceLogoutAt
          ? data.forceLogoutAt?.toDate().toISOString()
          : startListenTime,
      });
    });
  });
};

const attachConversationListener = () => {
  const currentUserId = auth.currentUser?.uid;
  const conversationQuery = query(
    collection(db, 'rooms'),
    where('status', '==', 'ACTIVE'),
    where('currentMembers', 'array-contains', currentUserId),

    where('updatedAt', '>', new Date()),
    orderBy('updatedAt', 'desc'),
    orderBy('lastMessage.updatedAt', 'desc'),
  ).withConverter(genericConverter<IRoom>());
  return onSnapshot(conversationQuery, (snap) => {
    const changes = snap.docChanges();
    changes.forEach(async (change) => {
      if (['added', 'modified'].includes(change.type)) {
        const data = { ...change.doc.data(), id: change.doc.id };
        const otherMembers = data.currentMembers.filter(
          (id) => id !== currentUserId,
        );
        const usersData = queryClient.getQueryData<Record<string, IUserDoc>>([
          'currentUser',
          'rooms',
          'members',
        ]);
        const promises: Promise<DocumentSnapshot<IUserDoc>>[] = [];
        otherMembers.forEach((member) => {
          if ((usersData && !usersData[member]) || !usersData) {
            const userRef = doc(collection(db, 'users'), member).withConverter(
              genericConverter<IUserDoc>(),
            );
            promises.push(getDoc(userRef));
          }
        });
        const userDocs = await Promise.all(promises);
        userDocs.map((userDoc) =>
          queryClient.setQueryData<Record<string, IUserDoc | undefined>>(
            ['currentUser', 'rooms', 'members'],
            (prev) => ({
              ...prev,
              ...{ [userDoc.id]: userDoc.data() },
            }),
          ),
        );
        addConversationToQueryCache(['currentUser', 'rooms'], data);
      }
    });
  });
};

const getRoomList: QueryFunction<IRoom[], string[]> = async (params) => {
  const currentUserId = auth.currentUser?.uid;

  const snapshot = await getDocs(
    query(
      collection(db, 'rooms'),
      where('status', '==', 'ACTIVE'),
      where('currentMembers', 'array-contains', currentUserId),
      orderBy('lastMessage.updatedAt', 'desc'),
      startAfter(params.pageParam ? new Date(params.pageParam) : new Date()),
      limit(ROOM_PER_PAGE),
    ).withConverter(genericConverter<IRoom>()),
  );
  const allUserIds = snapshot.docs.flatMap((d) =>
    d.data().currentMembers.filter((id) => id !== currentUserId),
  );
  const chunkUserIds = chunk(allUserIds, 30);
  // eslint-disable-next-line no-restricted-syntax
  for (const ids of chunkUserIds) {
    if (ids.length > 0) {
      // eslint-disable-next-line no-await-in-loop
      const userSnapshot = await getDocs(
        query(collection(db, 'users'), where('id', 'in', ids)).withConverter(
          genericConverter<IUserDoc>(),
        ),
      );
      userSnapshot.docs.map((userDoc) =>
        queryClient.setQueryData<Record<string, IUserDoc>>(
          ['currentUser', 'rooms', 'members'],
          (prev) => ({
            ...prev,
            ...{ [userDoc.id]: userDoc.data() },
          }),
        ),
      );
    }
  }

  return snapshot.docs.map((d) => ({ ...d.data(), id: d.id }));
};

export const chatService = {
  sendMessage,
  readRoom,
  getMessages,
  attachMessageListener,
  attachConversationListener,
  attachFirebaseUserListener,
  addLocalFileDataToQueryCache,
  addMessageDataToQueryCache,
  getRoomList,
  PER_PAGE,
  ROOM_PER_PAGE,
};

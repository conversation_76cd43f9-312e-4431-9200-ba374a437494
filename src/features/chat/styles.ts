import type { SxProps, Theme } from '@mui/material';

const styles = {
  newMessageButton: {
    padding: '12px',
    position: 'absolute',
    top: '-64px',
    transform: 'translate(-50%, 0px)',
    left: '50%',
    backgroundColor: '#fff',
    borderRadius: '32px',
    boxShadow: '0px 3px 8px 0px rgba(91, 107, 128, 0.18)',
    color: 'heading',
    fontSize: '12px',
    lineHeight: '16px',
    '&:hover': {
      bgcolor: 'white',
    },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

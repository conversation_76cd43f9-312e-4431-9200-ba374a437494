import type { UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { chatService } from 'features/chat/service';
import type { Unsubscribe } from 'firebase/firestore';

const useSubscribeRoomList = ({
  firebaseUserId,
  ...options
}: { firebaseUserId?: string } & UseQueryOptions<Unsubscribe>) => {
  return useQuery({
    queryKey: ['currentUser', 'snapshot', 'rooms'],
    enabled: !!firebaseUserId,
    refetchOnReconnect: false,
    queryFn: chatService.attachConversationListener,
    staleTime: Infinity,
    ...options,
  });
};

export default useSubscribeRoomList;

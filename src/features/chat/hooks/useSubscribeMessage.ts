import type { UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { chatService } from 'features/chat/service';
import type { Unsubscribe } from 'firebase/auth';

const useSubscribeMessage = ({
  roomId,
  firebaseUserId,
  startAfter,
  ...options
}: {
  roomId?: string;
  firebaseUserId?: string;
  startAfter?: string;
} & UseQueryOptions<Unsubscribe>) => {
  return useQuery<Unsubscribe>({
    queryKey: ['currentUser', 'snapshot', 'messages', roomId],
    enabled: !!roomId && !!firebaseUserId,
    staleTime: Infinity,
    queryFn: (payload) =>
      chatService.attachMessageListener({
        ...payload,
        startAfter,
        roomId: roomId || '',
      }),
    ...options,
  });
};

export default useSubscribeMessage;

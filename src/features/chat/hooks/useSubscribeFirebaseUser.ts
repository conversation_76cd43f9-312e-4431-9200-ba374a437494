import type { UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { chatService } from 'features/chat/service';
import type { Unsubscribe } from 'firebase/auth';

const useSubscribeFirebaseUser = ({
  firebaseUserId,
  ...options
}: { firebaseUserId?: string } & UseQueryOptions<Unsubscribe>) => {
  return useQuery({
    queryKey: ['currentUser', 'snapshot', 'userDoc'],
    enabled: !!firebaseUserId,
    refetchOnReconnect: false,
    queryFn: chatService.attachFirebaseUserListener,
    staleTime: Infinity,
    ...options,
  });
};

export default useSubscribeFirebaseUser;

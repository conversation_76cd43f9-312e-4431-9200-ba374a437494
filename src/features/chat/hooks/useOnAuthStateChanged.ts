import { auth } from 'features/chat/firebase';
import useAuthSignInWithCustomToken from 'features/chat/hooks/useAuthSignInWithCustomToken';
import { onAuthStateChanged } from 'firebase/auth';
import { useFetchUser } from 'hooks';
import useMutate from 'hooks/useMutate';
import chatQuery from 'models/chat';
import { useEffect } from 'react';
import type { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';
import queryClient from 'utils/queryClient';

import useAuthSignOut from './useAuthSignOut';

const useOnAuthStateChanged = () => {
  const { data: currentUser, fetchStatus } = useFetchUser({ enabled: true });
  const { mutateAsync: signInWithCustomToken } =
    useAuthSignInWithCustomToken(auth);
  const { mutateAsync: signOutFirebase } = useAuthSignOut(auth);
  const { mutateAsync: getFirebaseToken } = useMutate<
    unknown,
    { firebaseToken: string }
  >(chatQuery.getFirebaseToken);

  useEffect(() => {
    // Firebase auth state change listener
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (fetchStatus !== 'fetching') {
        if (!currentUser?._id && firebaseUser) {
          signOutFirebase();
          return;
        }
        const role = Helper.getUserRole() as ROLES;
        if (currentUser?._id && !firebaseUser) {
          const { firebaseToken } = await getFirebaseToken({});
          const { user } = await signInWithCustomToken(firebaseToken);
          queryClient.setQueryData<{ firebaseUserId?: string }>(
            ['currentUser', role],
            (prevCurrentUser) =>
              prevCurrentUser
                ? {
                    ...prevCurrentUser,
                    firebaseUserId: user.uid,
                  }
                : undefined,
          );
          return;
        }

        if (
          currentUser?._id &&
          !currentUser?.firebaseUserId &&
          firebaseUser?.uid
        ) {
          queryClient.setQueryData<{ firebaseUserId?: string }>(
            ['currentUser', role],
            (prevCurrentUser) =>
              prevCurrentUser
                ? {
                    ...prevCurrentUser,
                    firebaseUserId: firebaseUser.uid,
                  }
                : undefined,
          );
        }
      }
    });
    return () => {
      unsubscribe();
    };
  }, [
    currentUser?._id,
    currentUser?.firebaseUserId,
    fetchStatus,
    getFirebaseToken,
    signInWithCustomToken,
    signOutFirebase,
  ]);
};

export default useOnAuthStateChanged;

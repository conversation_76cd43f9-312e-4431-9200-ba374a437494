import type { InfiniteData } from '@tanstack/react-query';
import { useInfiniteQuery } from '@tanstack/react-query';
import { chatService } from 'features/chat/service';
import queryClient from 'utils/queryClient';

import type { IMessage } from '../types';

const useGetMessages = ({
  roomId,
  firebaseUserId,
}: {
  roomId?: string;
  firebaseUserId?: string;
}) => {
  const currentMessageData = queryClient.getQueryData<InfiniteData<IMessage[]>>(
    ['messages', roomId],
  );
  return useInfiniteQuery<IMessage[]>({
    queryKey: ['currentUser', 'messages', roomId],
    queryFn: (payload) =>
      chatService.getMessages({ ...payload, roomId: roomId || '' }),
    refetchOnReconnect: false,
    staleTime: Infinity,
    onSuccess: (data) => {
      const pages = data.pages || [];
      if ((pages[pages.length - 1] || []).length < 20) {
        queryClient.setQueryData<InfiniteData<IMessage[]>>(
          ['currentUser', 'messages', roomId],
          () => ({
            ...data,
            hasMore: false,
          }),
        );
      } else {
        queryClient.setQueryData<InfiniteData<IMessage[]>>(
          ['currentUser', 'messages', roomId],
          () => ({
            ...data,
            hasMore: true,
          }),
        );
      }
    },
    enabled: !!roomId && !currentMessageData && !!firebaseUserId,
    getNextPageParam: (lp) => {
      return lp.length !== 0 ? lp[lp.length - 1]?.createdAt : null;
    },
  });
};

export default useGetMessages;

import type {
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import type { Auth, AuthError, UserCredential } from 'firebase/auth';
import { signInWithCustomToken } from 'firebase/auth';

export default function useAuthSignInWithCustomToken(
  auth: Auth,
  useMutationOptions?: UseMutationOptions<UserCredential, AuthError, string>,
): UseMutationResult<UserCredential, AuthError, string> {
  return useMutation<UserCredential, AuthError, string>((customToken) => {
    return signInWithCustomToken(auth, customToken);
  }, useMutationOptions);
}

import type { UseInfiniteQueryOptions } from '@tanstack/react-query';
import { useInfiniteQuery } from '@tanstack/react-query';
import { chatService } from 'features/chat/service';

import type { IRoom } from '../types';

const useGetRooms = ({
  firebaseUserId,
  ...options
}: { firebaseUserId?: string } & UseInfiniteQueryOptions<
  IRoom[],
  unknown,
  IRoom[],
  IRoom[],
  string[]
>) => {
  return useInfiniteQuery({
    queryKey: ['currentUser', 'rooms'],
    queryFn: chatService.getRoomList,
    refetchOnReconnect: false,
    enabled: !!firebaseUserId,
    staleTime: Infinity,
    getNextPageParam: (lp) => {
      return lp.length && lp.length !== 0
        ? lp[lp.length - 1]?.lastMessage?.updatedAt
        : null;
    },
    ...options,
  });
};

export default useGetRooms;

import dayjs from 'dayjs';
import type { ROLES } from 'utils/constants';
import { MomentFormat } from 'utils/constants';

import {
  FILE_CONTENT_TYPE,
  IMAGE_CONTENT_TYPE,
  VIDEO_CONTENT_TYPE,
} from './const';
import type { IAttachmentItem } from './types';
import { FileType } from './types';

export const MAX_DESKTOP_WIDTH = 400;
export const MAX_DESKTOP_HEIGHT = 200;
export const MAX_MOBILE_WIDTH = 200;
export const MAX_MOBILE_HEIGHT = 280;

const Utils = {
  getRenderedSize: (width: number = 0, height: number = 0) => {
    const widthPercent = MAX_DESKTOP_WIDTH / width;
    const heightPercent = MAX_DESKTOP_HEIGHT / height;
    const mobileWidthPercent = MAX_MOBILE_WIDTH / width;
    const mobileHeightPercent = MAX_MOBILE_HEIGHT / height;
    const smallestPercent = Math.min(widthPercent, heightPercent);
    const mobileSmallestPercent = Math.min(
      mobileWidthPercent,
      mobileHeightPercent,
    );
    const renderedWidth = width * smallestPercent;
    const renderedHeight = height * smallestPercent;
    const mobileRenderedWidth = width * mobileSmallestPercent;
    const mobileRenderedHeight = height * mobileSmallestPercent;
    return {
      width: renderedWidth,
      height: renderedHeight,
      mobileRenderedWidth,
      mobileRenderedHeight,
    };
  },
  convertSecondsToTimeString: (second: number) => {
    return dayjs.utc(second * 1000).format('mm:ss');
  },
  getLastMessageFromAttachment: (attachment: IAttachmentItem) => {
    let lastMessageContent = '';
    if (attachment.contentType.startsWith('image')) {
      lastMessageContent = `[画像]`;
    } else if (attachment.contentType.startsWith('video')) {
      lastMessageContent = `[動画]`;
    } else lastMessageContent = `[ファイル] ${attachment.name}`;
    return lastMessageContent;
  },
  checkBeforeUpload: (
    files: File[],
    uploadType: 'image/video' | 'file',
    selectedFiles: any[],
  ) => {
    let message: string = '';
    if (files.length > 6 - selectedFiles.length) {
      if (uploadType === 'file') return 'ファイルは最大6つまで登録できます';
      return '動画または画像は最大6つまで登録できます';
    }
    files.some((file) => {
      if (
        uploadType === 'image/video' &&
        IMAGE_CONTENT_TYPE.includes(file.type) &&
        file.size > 5 * 1024 * 1024
      ) {
        message = '画像サイズは5MB以内にしてください。';
      }
      if (
        uploadType === 'image/video' &&
        VIDEO_CONTENT_TYPE.includes(file.type) &&
        file.size > 50 * 1024 * 1024
      ) {
        message = '動画サイズは50MB以内にしてください。';
      }
      if (
        uploadType === 'file' &&
        FILE_CONTENT_TYPE.includes(file.type) &&
        file.size > 50 * 1024 * 1024
      ) {
        message = 'ファイルサイズは50MB以内にしてください。';
      }
      if (
        uploadType === 'image/video' &&
        ![...IMAGE_CONTENT_TYPE, ...VIDEO_CONTENT_TYPE].includes(file.type)
      ) {
        message =
          '指定できないファイル形式です。添付ファイルはJPG、JPEG、PNG、WEBP、SVG、MP4、MOV形式でアップロードしてください。';
      }
      if (uploadType === 'file' && !FILE_CONTENT_TYPE.includes(file.type)) {
        message =
          '指定できないファイル形式です。添付ファイルはPDF、DOC、DOCX、XLSX、PPTX、PPT、CSV、TXT、XLS形式でアップロードしてください。';
      }
      return !!message;
    });
    return message;
  },
  getFileTypeFromContentType: (contentType: string): FileType => {
    if (IMAGE_CONTENT_TYPE.includes(contentType)) {
      return FileType.IMAGE;
    }
    if (VIDEO_CONTENT_TYPE.includes(contentType)) {
      return FileType.VIDEO;
    }
    return FileType.FILE;
  },
  downloadURI: (url: string, name: string) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = name;
        link.click();
      });
  },
  formatMessageTime: (thisTime?: string, lastTime?: string) => {
    if (thisTime) {
      const newMessageTime = dayjs(thisTime);
      const today = dayjs();
      if (lastTime) {
        const oldMessageTime = dayjs(lastTime);
        const timeDif = newMessageTime.diff(oldMessageTime, 'minutes');
        if (timeDif >= 15) {
          if (newMessageTime.isSame(today, 'day')) {
            return newMessageTime.format('HH:mm');
          }

          return newMessageTime.format(
            `${MomentFormat.JP_YEAR_MONTH_DATE_HOUR}`,
          );
        }
      } else {
        if (newMessageTime.isSame(today, 'day')) {
          return newMessageTime.format('HH:mm');
        }

        return newMessageTime.format(`${MomentFormat.JP_YEAR_MONTH_DATE_HOUR}`);
      }
    }
    return null;
  },
  formatLastMessageTime: (time: string) => {
    const today = dayjs();
    const lastMessageTime = dayjs(time);
    if (lastMessageTime.isSame(today, 'day')) {
      return lastMessageTime.format('HH:mm');
    }
    return lastMessageTime.format(`${MomentFormat.JP_YEAR_MONTH_DATE}`);
  },
  getRoomPath: ({
    receiverRole,
    roomId,
  }: {
    receiverRole: ROLES | 'CONSUMER';
    roomId: string;
  }) => {
    if (receiverRole === 'CONSUMER') {
      return `/customer/messages/${roomId}`;
    }
    return `/${receiverRole.toLocaleLowerCase()}/messages/${roomId}`;
  },
};

export default Utils;

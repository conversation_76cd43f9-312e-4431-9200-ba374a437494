import type { SxProps, Theme } from '@mui/material';

const styles = {
  cellWrapper: {
    gap: '8px',
  },
  messageContentWrapper: {
    borderRadius: '8px',
    width: 'fit-content',
    '.message-link': {
      color: 'heading',
      textDecoration: 'underline',
    },
  },
  messageText: {
    whiteSpace: 'pre-wrap',
  },
  avatar: {
    borderRadius: '50%',
    width: '32px',
    height: '32px',
    '@media (max-width: 768px)': {
      width: '24px',
      height: '24px',
    },
    // [theme.fn.smallerThan('sm')]: {
    //   width: 24,
    //   height: 24,
    // },
  },
  videoWrapper: {
    video: {
      objectFit: 'cover',
      borderRadius: '8px',
    },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

import { Avatar, Box, Typography } from '@mui/material';
import type { IAttachmentItem } from 'features/chat/types';
import { motion } from 'framer-motion';
import Linkify from 'linkify-react';
import type { IntermediateRepresentation, OptFn } from 'linkifyjs';
import Link from 'next/link';
import { memo } from 'react';
import { isIOS } from 'react-device-detect';

import FileGrid from './FileGrid';
import styles from './styles';

const renderLink: OptFn<(ir: IntermediateRepresentation) => any> = ({
  attributes,
  content,
}) => {
  const { href, ...props } = attributes;
  return (
    <Link className="message-link" href={href} target="_blank" {...props}>
      {content}
    </Link>
  );
};

const Message = ({
  data,
  currentUserId,
  isStartMessage,
  displayTime,
  isOnlyOne,
}: {
  data: {
    id: string;
    value: string;
    senderId: string;
    profileImage?: string;
    attachments: IAttachmentItem[];
    type: 'TEXT' | 'IMAGE' | 'VIDEO' | 'FILE';
    status?: 'PENDING';
  };
  currentUserId?: string;
  isStartMessage?: boolean;
  isOnlyOne?: boolean;
  isEndMessage?: boolean;
  displayTime?: string | null;
}) => {
  const isRightSide = data.senderId === currentUserId;
  return (
    <>
      <Box
        sx={styles.cellWrapper}
        alignSelf={isRightSide ? 'flex-end' : 'flex-start'}
        component={motion.div}
        display="flex"
        key={data.id}
        {...(!isIOS
          ? {
              animate: 'animate',
              initial: 'initial',
              transition: { damping: 100 },
              variants: {
                initial: { y: 16, opacity: 0 },
                animate: { y: 0, opacity: 1 },
              },
            }
          : {})}
        ml={
          !isRightSide && !isStartMessage && !isOnlyOne
            ? { xs: '32px', tablet: '40px' }
            : 0
        }
      >
        {!isRightSide && (isStartMessage || isOnlyOne) && (
          <Avatar
            alt=""
            sx={styles.avatar}
            src={data.profileImage || '/images/default-avatar.svg'}
          />
        )}
        <Box
          display="flex"
          alignItems={isRightSide ? 'flex-end' : 'flex-start'}
          flexDirection="column"
          gap="4px"
        >
          {data.attachments && data.attachments.length > 0 && (
            <FileGrid
              attachments={data.attachments}
              isRightSide={isRightSide}
              isLoading={data.status === 'PENDING'}
            />
          )}
          {data.value && (
            <Box
              sx={styles.messageContentWrapper}
              maxWidth={{ xs: 256, tablet: 480 }}
              padding="12px"
              bgcolor={!isRightSide ? '#EDF1F3' : '#FAD59E'}
            >
              <Linkify options={{ render: renderLink }}>
                <Typography
                  fontSize={14}
                  sx={styles.messageText}
                  color="heading"
                >
                  {data.value}
                </Typography>
              </Linkify>
            </Box>
          )}
        </Box>
      </Box>
      {displayTime && (
        <Typography
          textAlign="center"
          color="icon"
          fontSize={12}
          marginBottom="12px"
          lineHeight="24px"
          marginTop="12px"
        >
          {displayTime}
        </Typography>
      )}
    </>
  );
};

export default memo(
  Message,
  (prevProps, nextProps) =>
    JSON.stringify(prevProps) === JSON.stringify(nextProps),
);

import type { SxProps, Theme } from '@mui/material';

const styles = {
  playButton: {
    position: 'absolute',
    width: 'fit-content',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 1,
    svg: {
      width: { xs: 24, tablet: 40 },
      height: { xs: 24, tablet: 40 },
    },
  },
  timeWrapper: {
    backgroundColor: '#101211',
    opacity: '0.6',
    fontSize: '12px',
    fontWeight: '500',
    color: '#fff',
    padding: '0px 2px',
    borderRadius: '2px',
    position: 'absolute',
    left: '8px',
    bottom: '8px',
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

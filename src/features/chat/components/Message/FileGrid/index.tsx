import { Avatar, Box } from '@mui/material';
import LoadingOverlay from 'components/LoadingOverlay';
import type { IAttachmentItem } from 'features/chat/types';
import { FileType } from 'features/chat/types';
import Utils from 'features/chat/utils';
import { PlayVideoIcon } from 'icons';
import { useState } from 'react';
import ReactPlayer from 'react-player';

import FileLightBox from '../FileLightBox';
import FileMessage from '../FileMessage';
import ImageMessage from '../ImageMessage';
import VideoMessage from '../VideoMessage';
import styles from './styles';

const FileGrid = ({
  attachments,
  isLoading,
  isRightSide,
}: {
  attachments: IAttachmentItem[];
  isLoading?: boolean;
  isRightSide?: boolean;
}) => {
  const imageAndVideoAttachments = attachments.filter(
    (attachment) => attachment.type !== FileType.FILE,
  );
  const fileAttachments = attachments.filter(
    (attachment) => attachment.type === FileType.FILE,
  );
  const [index, setIndex] = useState(-1);
  if (attachments.length === 1) {
    if (attachments[0]?.type === 'IMAGE') {
      return (
        <Box
          display="flex"
          justifyContent={!isRightSide ? 'flex-start' : 'flex-end'}
          key={attachments[0].id}
        >
          <ImageMessage attachment={attachments[0]} />
        </Box>
      );
    }
    if (attachments[0]?.type === 'VIDEO') {
      return <VideoMessage attachment={attachments[0]} />;
    }
  }
  return (
    <Box
      display="flex"
      flexDirection="column"
      gap={{ xs: '2px', tablet: '4px' }}
      alignItems={!isRightSide ? 'flex-start' : 'flex-end'}
      position="relative"
    >
      <Box
        sx={{ position: 'relative' }}
        display="flex"
        gap={{ xs: '2px', tablet: '4px' }}
        flexWrap="wrap"
        justifyContent={!isRightSide ? 'flex-start' : 'flex-end'}
        flexDirection={'row'}
        maxWidth={{
          xs: 262,
          tablet: imageAndVideoAttachments.length === 2 ? 404 : 368,
        }}
      >
        {imageAndVideoAttachments.map((file, idx) => {
          if (file.type === 'IMAGE') {
            return (
              <Avatar
                alt=""
                key={file.id}
                sx={{
                  width:
                    imageAndVideoAttachments.length > 2
                      ? { xs: 86, tablet: 120 }
                      : { xs: 120, tablet: 200 },
                  height:
                    imageAndVideoAttachments.length > 2
                      ? { xs: 86, tablet: 120 }
                      : { xs: 120, tablet: 200 },
                  borderRadius: '8px',
                }}
                onClick={() => setIndex(idx)}
                src={file.url}
                style={{ cursor: 'pointer' }}
              />
            );
          }
          if (file.type === 'VIDEO') {
            return (
              <Box
                key={file.id}
                sx={{
                  width:
                    imageAndVideoAttachments.length > 2
                      ? { xs: 86, tablet: 120 }
                      : { xs: 120, tablet: 200 },
                  height:
                    imageAndVideoAttachments.length > 2
                      ? { xs: 86, tablet: 120 }
                      : { xs: 120, tablet: 200 },
                  borderRadius: '8px',
                  video: {
                    objectFit: 'cover',
                  },
                }}
                onClick={() => setIndex(idx)}
                overflow="hidden"
                position="relative"
              >
                <ReactPlayer
                  height="100%"
                  key={file.url}
                  url={file.url}
                  width="100%"
                />
                {!isLoading && (
                  <Box sx={styles.playButton}>
                    <PlayVideoIcon />
                  </Box>
                )}
                {file?.duration && (
                  <Box sx={styles.timeWrapper} position="absolute">
                    {Utils.convertSecondsToTimeString(file?.duration || 0)}
                  </Box>
                )}
              </Box>
            );
          }
          return null;
        })}
      </Box>
      {fileAttachments.length > 0 && (
        <Box
          maxWidth={256}
          sx={{ position: 'relative' }}
          display="flex"
          gap={{ xs: '2px', tablet: '4px' }}
          flexWrap="wrap"
          alignItems={!isRightSide ? 'flex-start' : 'flex-end'}
          flexDirection="column"
        >
          {fileAttachments.map((file, fileIdx) => {
            return (
              <FileMessage
                onClick={() =>
                  setIndex(imageAndVideoAttachments.length + fileIdx)
                }
                key={file.id}
                attachment={file}
              />
            );
          })}
        </Box>
      )}
      <LoadingOverlay visible={!!isLoading} />
      <FileLightBox
        onClose={() => setIndex(-1)}
        open={index > -1}
        index={index}
        attachments={[...imageAndVideoAttachments, ...fileAttachments]}
      />
    </Box>
  );
};

export default FileGrid;

import { AppBar, Avatar, Box, Dialog, Typography } from '@mui/material';
import LoadingOverlay from 'components/LoadingOverlay';
import type { IAttachmentItem } from 'features/chat/types';
import utils from 'features/chat/utils';
import { CloseIcon } from 'icons';
import { useState } from 'react';

const ImageMessage = ({
  attachment,
  isLoading,
}: {
  attachment: IAttachmentItem;
  isLoading?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const { width, height, mobileRenderedWidth, mobileRenderedHeight } =
    utils.getRenderedSize(attachment.width, attachment.height);
  return (
    <Box
      maxHeight={{ xs: 280, tablet: 200 }}
      maxWidth={{ xs: 200, tablet: 400 }}
      sx={{ position: 'relative' }}
    >
      <Avatar
        alt=""
        sx={{
          height: { xs: mobileRenderedHeight, tablet: height },
          width: { xs: mobileRenderedWidth, tablet: width },
          maxHeight: { xs: 280, tablet: 200 },
          maxWidth: { xs: 200, tablet: 400 },
          borderRadius: '8px',
        }}
        onClick={() => setOpen(true)}
        src={attachment.url}
        style={{ cursor: 'pointer' }}
      />
      <LoadingOverlay visible={!!isLoading} />
      <Dialog
        fullScreen
        open={open}
        onClose={() => setOpen(false)}
        PaperProps={{ sx: { borderRadius: 0 } }}
      >
        <AppBar sx={{ position: 'relative' }}>
          <Box bgcolor="#101211" p="14px">
            <Typography
              textAlign="center"
              fontSize={{ xs: 12, tablet: 14 }}
              lineHeight="20px"
              className="line-clamp"
              maxWidth="calc(100% - 112px)"
              margin="0 auto"
            >
              {attachment.name}
            </Typography>
          </Box>
          <Box
            onClick={() => setOpen(false)}
            position="absolute"
            width="40px"
            height="40px"
            top="4px"
            right="16px"
            display="flex"
            justifyContent="center"
            alignItems="center"
            className="pointer"
          >
            <CloseIcon />
          </Box>
        </AppBar>
        <Box
          height="calc(100% - 48px)"
          display="flex"
          justifyContent="center"
          alignItems="center"
          bgcolor="#4A504E"
        >
          <img
            alt=""
            src={attachment.url}
            style={{
              maxHeight: '100%',
              maxWidth: '100%',
              backgroundColor: '#4A504E',
            }}
          />
        </Box>
      </Dialog>
    </Box>
  );
};

export default ImageMessage;

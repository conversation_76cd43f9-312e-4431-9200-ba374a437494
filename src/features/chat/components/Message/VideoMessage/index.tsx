import { AppBar, Box, Dialog, Typography } from '@mui/material';
import LoadingOverlay from 'components/LoadingOverlay';
import type { IAttachmentItem } from 'features/chat/types';
import utils from 'features/chat/utils';
import { CloseIcon, PlayVideoIcon } from 'icons';
import { useState } from 'react';
import ReactPlayer from 'react-player';

import styles from './styles';

const VideoMessage = ({
  attachment,
  isLoading,
}: {
  attachment: IAttachmentItem;
  isLoading?: boolean;
}) => {
  const [open, setOpen] = useState(false);

  const handleClose = () => {
    setOpen(false);
  };
  const { width, height, mobileRenderedWidth, mobileRenderedHeight } =
    utils.getRenderedSize(attachment.width, attachment.height);
  const [videoDuration, setVideoDuration] = useState<number | null>(null);
  return (
    <div>
      <Box
        sx={styles.videoWrapper}
        onClick={() => {
          if (!isLoading) {
            setOpen(true);
          }
        }}
      >
        <Box
          height={{ xs: mobileRenderedHeight, tablet: height }}
          maxHeight={{ xs: 280, tablet: 200 }}
          maxWidth={{ xs: 200, tablet: 400 }}
          width={{ xs: mobileRenderedWidth, tablet: width }}
        >
          <ReactPlayer
            height="100%"
            key={attachment.url}
            onDuration={(duration) => setVideoDuration(duration)}
            url={attachment.url}
            width="100%"
          />
        </Box>
        {!isLoading && (
          <Box sx={styles.playButton}>
            <PlayVideoIcon />
          </Box>
        )}
        {videoDuration && (
          <Box sx={styles.timeWrapper} position="absolute">
            {utils.convertSecondsToTimeString(videoDuration)}
          </Box>
        )}
        <LoadingOverlay visible={!!isLoading} />
      </Box>
      <Dialog
        fullScreen
        open={open}
        onClose={handleClose}
        PaperProps={{ sx: { borderRadius: 0 } }}
      >
        <AppBar sx={{ position: 'relative' }}>
          <Box bgcolor="#101211" p="14px">
            <Typography
              textAlign="center"
              fontSize={{ xs: 12, tablet: 14 }}
              lineHeight="20px"
              className="line-clamp"
              maxWidth="calc(100% - 112px)"
              margin="0 auto"
            >
              {attachment.name}
            </Typography>
          </Box>
          <Box
            onClick={handleClose}
            position="absolute"
            width="40px"
            height="40px"
            top="4px"
            right="16px"
            display="flex"
            justifyContent="center"
            alignItems="center"
            className="pointer"
          >
            <CloseIcon />
          </Box>
        </AppBar>
        <ReactPlayer
          controls
          height="100%"
          style={{
            maxHeight: 'calc(99% - 48px)',
            maxWidth: '100%',
            backgroundColor: '#4A504E',
          }}
          url={attachment.url}
          width="100%"
        />
      </Dialog>
    </div>
  );
};

export default VideoMessage;

import type { SxProps, Theme } from '@mui/system';

const styles = {
  videoWrapper: {
    position: 'relative',
    cursor: 'pointer',
    borderRadius: '8px',
    overflow: 'hidden',
    video: {
      objectFit: 'cover',
      borderRadius: '8',
      display: 'block',
    },
  },
  timeWrapper: {
    backgroundColor: '#101211',
    opacity: '0.6',
    fontSize: '12px',
    fontWeight: '500',
    color: '#fff',
    padding: '0px 2px',
    borderRadius: '2px',
    position: 'absolute',
    left: '8px',
    bottom: '8px',
  },
  playButton: {
    position: 'absolute',
    width: 'fit-content',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 1,
  },
  modalContent: {
    height: '100%',
    backgroundColor: 'transparent',
    '@media (max-width: 768px)': {
      borderRadius: '0px',
    },
  },
  modalBody: {
    height: 'calc(100% - 48px)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '0px',
    backgroundColor: 'rgb(49, 54, 61, 09)',
  },
  modalHeader: {
    padding: '4px 16px',
    backgroundColor: 'rgba(16, 18, 17, 1)',
  },
  modalTitle: {
    color: '#fff',
    display: 'flex',
    fontSize: '14px',
    justifyContent: 'center',
    width: 'calc(100% + 40px)',
    lineHeight: 'normal',
  },
  // modalClose: {
  //   color: '#fff',
  //   &:hover {
  //     background-color: 'transparent',
  //   }
  // }
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

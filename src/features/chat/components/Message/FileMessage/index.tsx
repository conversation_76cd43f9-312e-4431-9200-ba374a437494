import { Box, Typography } from '@mui/material';
import LoadingOverlay from 'components/LoadingOverlay';
import type { IAttachmentItem } from 'features/chat/types';
import { FileIcon } from 'icons';

const FileMessage = ({
  attachment,
  isLoading,
  onClick,
}: {
  attachment: IAttachmentItem;
  isLoading?: boolean;
  onClick: () => void;
}) => {
  const fileExtension = attachment.name.split('.').pop();
  return (
    <div>
      <Box
        onClick={onClick}
        bgcolor="#E4EAED"
        display="flex"
        maxWidth="256px"
        padding="8px 34px 8px 8px"
        position="relative"
        className={isLoading ? '' : 'pointer'}
        sx={{
          borderRadius: '8px',
          gap: '8px',
          alignItems: 'center',
        }}
      >
        <Box>
          <Box
            bgcolor="white"
            height="48px"
            padding="12px"
            sx={{ borderRadius: '6px' }}
          >
            <FileIcon />
          </Box>
        </Box>
        <Box>
          <Box display="flex" color="#262626">
            <Typography fontSize={14} className="line-clamp" sx={{ flex: 1 }}>
              {attachment.name.replace(/\.[^/.]+$/, '')}
            </Typography>
            <Typography component="span" fontSize={14} width="fit-content ">
              .{fileExtension}
            </Typography>
          </Box>
          <Typography color="#A8B0B4" fontSize={12} marginTop={'3px'}>
            {(attachment.size / (1024 * 1024)).toFixed(2)} MB
          </Typography>
        </Box>
        <LoadingOverlay visible={isLoading} />
      </Box>
    </div>
  );
};

export default FileMessage;

import <PERSON><PERSON>ie<PERSON>, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import { AppBar, Box, Dialog, IconButton, Typography } from '@mui/material';
import type { IAttachmentItem } from 'features/chat/types';
import { ArrowLeftIcon, ArrowRightIcon, CloseIcon } from 'icons';
import { useEffect, useState } from 'react';
import ReactPlayer from 'react-player';

import styles from './styles';

const FileLightBox = ({
  attachments,
  index,
  open,
  onClose,
}: {
  attachments: IAttachmentItem[];
  index?: number;
  open: boolean;
  onClose: () => void;
}) => {
  const [activeIndex, setActiveIndex] = useState(index || 0);

  useEffect(() => {
    if (index !== undefined && index > -1) {
      setActiveIndex(index);
    }
  }, [index]);

  const handleBack = () => {
    setActiveIndex((prevIndex) => {
      if (prevIndex === 0) {
        return attachments.length - 1;
      }
      return prevIndex - 1;
    });
  };
  const handleNext = () => {
    setActiveIndex((prevIndex) => {
      if (prevIndex === attachments.length - 1) {
        return 0;
      }
      return prevIndex + 1;
    });
  };
  return (
    <Dialog
      fullScreen
      open={open}
      onClose={onClose}
      PaperProps={{ sx: { borderRadius: 0 } }}
    >
      <AppBar sx={{ position: 'relative' }}>
        <Box bgcolor="#101211" p="14px">
          <Typography
            textAlign="center"
            fontSize={{ xs: 12, tablet: 14 }}
            lineHeight="20px"
            className="line-clamp"
            maxWidth="calc(100% - 112px)"
            margin="0 auto"
          >
            {attachments[activeIndex]?.name}
          </Typography>
        </Box>
        <Box
          onClick={onClose}
          position="absolute"
          width="40px"
          height="40px"
          top="4px"
          right="16px"
          display="flex"
          justifyContent="center"
          alignItems="center"
          className="pointer"
        >
          <CloseIcon />
        </Box>
      </AppBar>
      <Box
        height="calc(100% - 48px)"
        display="flex"
        justifyContent="center"
        alignItems="center"
        bgcolor="#4A504E"
      >
        <IconButton onClick={handleBack} sx={styles.leftButton}>
          <Box color="white" display="inline-flex">
            <ArrowLeftIcon />
          </Box>
        </IconButton>
        <IconButton onClick={handleNext} sx={styles.nextButton}>
          <Box color="white" display="inline-flex">
            <ArrowRightIcon />
          </Box>
        </IconButton>
        {attachments[activeIndex]?.type === 'IMAGE' && (
          <img
            alt=""
            src={attachments[activeIndex]?.url}
            style={{
              maxHeight: '100%',
              maxWidth: '100%',
              backgroundColor: '#4A504E',
            }}
          />
        )}
        {attachments[activeIndex]?.type === 'VIDEO' && (
          <ReactPlayer
            controls
            height="100%"
            style={{
              maxHeight: 'calc(99% - 48px)',
              maxWidth: '100%',
              backgroundColor: '#4A504E',
            }}
            url={attachments[activeIndex]?.url}
            width="100%"
          />
        )}
        {attachments[activeIndex]?.type === 'FILE' && (
          <DocViewer
            config={{ header: { disableHeader: true } }}
            documents={[{ uri: attachments[activeIndex]?.url || '' }]}
            pluginRenderers={DocViewerRenderers}
            prefetchMethod="GET"
            style={{ width: '100%', whiteSpace: 'pre-line' }}
          />
        )}
      </Box>
    </Dialog>
  );
};

export default FileLightBox;

import type { SxProps, Theme } from '@mui/material';

const styles = {
  leftButton: {
    position: 'absolute',
    left: '24px',
    bgcolor: '#10121180',
    padding: 2,
    zIndex: 2,
    '@media (max-width: 768px)': {
      bgcolor: 'unset',
      padding: 1,
      left: 8,
    },
  },
  nextButton: {
    position: 'absolute',
    right: '24px',
    bgcolor: '#10121180',
    padding: 2,
    zIndex: 2,
    '@media (max-width: 768px)': {
      bgcolor: 'unset',
      padding: 1,
      right: 8,
    },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

import {
  Avatar,
  Box,
  IconButton,
  Input,
  Popover,
  Typography,
} from '@mui/material';
import { onlineManager } from '@tanstack/react-query';
import {
  FILE_CONTENT_TYPE,
  IMAGE_CONTENT_TYPE,
  VIDEO_CONTENT_TYPE,
} from 'features/chat/const';
import type { FileType } from 'features/chat/types';
import Utils from 'features/chat/utils';
import useGlobalState from 'hooks/useGlobalState';
import {
  AttachFileIcon,
  FolderIcon,
  ImageIcon,
  PlayVideoIcon,
  RemoveItemIcon,
  SendIcon,
} from 'icons';
import { debounce, set } from 'lodash';
import type { ChangeEvent } from 'react';
import React, { useRef, useState } from 'react';
import { isIOS, isMobile } from 'react-device-detect';
import ReactPlayer from 'react-player';
import Helper from 'utils/helpers';

import FilePreview from './FilePreview';
import styles from './styles';

const MessageInput = ({
  onSubmit,
}: {
  onSubmit: ({
    message,
    attachments,
  }: {
    message: string;
    attachments: {
      url: string;
      name: string;
      contentType: string;
      file: File;
      id: string;
      width?: number;
      height?: number;
      size: number;
    }[];
  }) => void;
}) => {
  const { setConfirmModal } = useGlobalState();
  const isComposing = useRef(false);
  const [typingMessage, setTypingMessage] = useState('');
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null,
  );
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // const [openPopover, setOpenPopover] = useState(false);
  const [previewFiles, setPreviewFiles] = useState<
    {
      url: string;
      name: string;
      contentType: string;
      file: File;
      id: string;
      width?: number;
      height?: number;
      size: number;
      type: FileType;
    }[]
  >([]);
  const handleSendMessage = debounce(() => {
    if (!onlineManager.isOnline()) {
      Helper.toast('インターネット接続がありません。', { type: 'error' });
      return;
    }
    if (
      (typingMessage || previewFiles.length > 0) &&
      typingMessage.length <= 1000
    ) {
      onSubmit({ attachments: previewFiles, message: typingMessage.trim() });
      setTypingMessage('');
      setPreviewFiles([]);
    }
  }, 0);

  const setMessage = debounce((event) => {
    const { selectionStart, selectionEnd } = event.target;
    setTypingMessage(
      (oldText) =>
        `${oldText.substring(0, selectionStart)}\n${oldText.substring(
          selectionEnd,
        )}`,
    );
    setTimeout(() => {
      set(event, 'target.selectionStart', selectionStart + 1);
      set(event, 'target.selectionEnd', selectionStart + 1);
    }, 1);
  }, 0);

  const handleOnEnter = async (
    event: React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    if (isMobile) {
      return;
    }
    if (
      event.key === 'Enter' &&
      (event.ctrlKey || event.shiftKey) &&
      isComposing.current === false
    ) {
      event.preventDefault();
      if (event.target instanceof HTMLTextAreaElement) {
        setMessage(event);
      }
      return;
    }
    if (event.key === 'Enter' && isComposing.current === false) {
      event.preventDefault();
      handleSendMessage();
      inputRef.current?.focus();
    }
  };

  const handleSelectFiles = async (
    e: ChangeEvent<HTMLInputElement>,
    uploadType: 'image/video' | 'file',
  ) => {
    if (e.target.files && e.target.files.length > 0 && e.target.files[0]) {
      const { files } = e.target;
      const errorMessage = Utils.checkBeforeUpload(
        Array.from(files),
        uploadType,
        previewFiles,
      );
      if (errorMessage) {
        setConfirmModal({
          title:
            uploadType === 'file'
              ? 'ファイルの添付に失敗しました'
              : '動画または画像の添付に失敗しました',
          content: errorMessage,
          hideCancelButton: true,
        });
        set(e, 'target.value', null);
        return;
      }
      setPreviewFiles((prev) =>
        prev.concat(
          Array.from(files).map((fileItem) => ({
            url: URL.createObjectURL(fileItem),
            name: fileItem.name,
            contentType: fileItem.type,
            file: fileItem,
            id: `${fileItem.name}_${new Date().toISOString()}`,
            size: fileItem.size,
            type: Utils.getFileTypeFromContentType(fileItem.type),
          })),
        ),
      );
      // }
    }
    inputRef.current?.focus();
    set(e, 'target.value', null);
  };

  const renderPreviewFiles = () => {
    return (
      <Box display="flex">
        {previewFiles.length > 0 && (
          <Box
            mb={1}
            p={{ xs: '12px 12px 0px', tablet: '12px 16px 0px 16px' }}
            position="relative"
            sx={{ zIndex: 1, overflowY: 'auto', scrollbarWidth: 'thin' }}
            display="flex"
            gap={1}
          >
            {previewFiles.map((file, index) => (
              <Box key={index} position="relative">
                {file.type === 'IMAGE' && (
                  <Avatar
                    sx={{ ...styles.previewFiles, ...styles.previewImage }}
                    onLoad={(e) =>
                      setPreviewFiles((prevValue) =>
                        prevValue.map((img, prevIndex) =>
                          prevIndex !== index
                            ? img
                            : {
                                ...img,
                                width: (e.target as unknown as HTMLImageElement)
                                  .naturalWidth,
                                height: (
                                  e.target as unknown as HTMLImageElement
                                ).naturalHeight,
                              },
                        ),
                      )
                    }
                    src={file.url}
                  />
                )}
                {file.type === 'VIDEO' && (
                  <Box
                    position="relative"
                    sx={{ ...styles.previewFiles, ...styles.previewVideo }}
                  >
                    <ReactPlayer
                      url={file.url}
                      onReady={(e) => {
                        const duration = e.getDuration();
                        const width = e.getInternalPlayer().videoWidth;
                        const height = e.getInternalPlayer().videoHeight;
                        setPreviewFiles((prevValue) =>
                          prevValue.map((img, idx) =>
                            idx === index
                              ? {
                                  ...img,
                                  duration,
                                  width,
                                  height,
                                }
                              : img,
                          ),
                        );
                      }}
                    />
                    <Box sx={styles.playButton}>
                      <PlayVideoIcon />
                    </Box>
                  </Box>
                )}
                {file.type === 'FILE' && (
                  <FilePreview name={file.name} size={file.size} />
                )}
                <Box
                  position="absolute"
                  sx={styles.removeButton}
                  right={file.type === 'FILE' ? 8 : 4}
                  top={file.type === 'FILE' ? 8 : 4}
                  onClick={() =>
                    setPreviewFiles((prev) =>
                      prev.filter((_, prevIndex) => prevIndex !== index),
                    )
                  }
                >
                  <RemoveItemIcon />
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </Box>
    );
  };
  return (
    <Box bgcolor="white">
      <Box display={{ tablet: 'none' }}>{renderPreviewFiles()}</Box>
      <Box
        display="flex"
        alignItems="flex-end"
        gap={{ tablet: '8px' }}
        padding={{ xs: '10px', tablet: '16px' }}
        bgcolor="white"
      >
        <IconButton
          sx={styles.actionButton}
          disableRipple
          onClick={handleClick}
          color="primary"
          disabled={previewFiles.length === 6}
        >
          <AttachFileIcon />
        </IconButton>
        <Popover
          id={id}
          open={open}
          anchorEl={anchorEl}
          PaperProps={{
            sx: {
              padding: '8px',
              boxShadow: '0px 8px 16px 0px rgba(210, 220, 225, 1)',
              minWidth: '180px',
            },
          }}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
        >
          <Box
            component="label"
            htmlFor="image/video"
            sx={styles.uploadMenuItem}
            mb="4px"
            className="pointer"
            onClick={handleClose}
          >
            <ImageIcon />
            <Typography fontSize={14}>画像または動画</Typography>
          </Box>
          <Box
            component="label"
            htmlFor="document"
            sx={styles.uploadMenuItem}
            className="pointer"
            onClick={handleClose}
          >
            <FolderIcon />
            <Typography fontSize={14}>ファイルを選択</Typography>
          </Box>
        </Popover>
        <Box sx={{ display: 'none' }}>
          <input
            accept={[...IMAGE_CONTENT_TYPE, ...VIDEO_CONTENT_TYPE].join(', ')}
            id="image/video"
            onChange={(e) => handleSelectFiles(e, 'image/video')}
            type="file"
            multiple
          />
          <input
            accept={FILE_CONTENT_TYPE.join(', ')}
            id="document"
            onChange={(e) => handleSelectFiles(e, 'file')}
            type="file"
            multiple
          />
        </Box>
        <Box sx={styles.inputWrapper} width="100%">
          <Box display={{ xs: 'none', tablet: 'block' }}>
            {renderPreviewFiles()}
          </Box>
          <Input
            sx={styles.messageInput}
            disableUnderline
            maxRows={4}
            minRows={1}
            onChange={(event) => {
              const value = event.target.value || '';
              setTypingMessage(value);
            }}
            onCompositionEnd={() => {
              isComposing.current = false;
            }}
            onCompositionStart={() => {
              isComposing.current = true;
            }}
            onKeyDown={handleOnEnter}
            placeholder="メッセージを入力"
            inputRef={inputRef}
            value={typingMessage}
            multiline
          />
          {typingMessage.length > 1000 && (
            <Box sx={styles.limitBox}>{typingMessage.length}/1000</Box>
          )}
        </Box>
        <IconButton
          sx={styles.actionButton}
          color="primary"
          disableRipple
          disabled={
            (typingMessage.length === 0 && previewFiles.length === 0) ||
            typingMessage.length > 1000
          }
          onClick={() => {
            handleSendMessage();
            setTypingMessage('');
            if (!isIOS) {
              inputRef.current?.focus();
            }
          }}
        >
          <SendIcon />
        </IconButton>
      </Box>
    </Box>
  );
};

export default MessageInput;

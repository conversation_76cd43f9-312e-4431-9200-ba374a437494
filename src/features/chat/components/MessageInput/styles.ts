const styles = {
  actionButton: {
    p: 0,
    width: '48px',
    height: '48px',
    borderRadius: 1,
    '&:hover': { backgroundColor: '#EDF1F3' },
    '&[disabled]': {
      color: '#D9D9D9',
    },
    '@media (max-width: 768px)': {
      width: '32px',
      height: '32px',
      svg: {
        width: '24px',
        height: '24px',
      },
    },
  },
  previewFiles: {
    border: '1px solid #E4EAED',
    display: 'block',
    borderRadius: '8px',
    overflow: 'hidden',
  },
  removeButton: {
    cursor: 'pointer',
  },
  inputWrapper: {
    backgroundColor: { tablet: '#F6F8F9' },
    borderRadius: '8px',
    position: 'relative',
    overflow: 'hidden',
    '.MuiInput-root': {
      width: '100%',
      padding: '14px 16px',
    },
    input: {
      fontSize: 16,
      padding: 0,
    },
    '@media (max-width: 768px)': {
      '.MuiInput-root': {
        padding: '4px 0px',
      },
    },
  },
  messageInput: {
    flex: 1,
  },
  playButton: {
    position: 'absolute',
    width: 'fit-content',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    display: 'inline-flex',
    zIndex: 1,
    svg: {
      width: 24,
      height: 24,
    },
  },
  previewVideo: {
    video: {
      objectFit: 'cover',
    },
    width: '64px !important',
    height: '64px !important',
    '@media (max-width: 768px)': {
      width: '56px !important',
      height: '56px !important',
    },
  },
  previewImage: {
    width: '64px',
    height: '64px',
    '@media (max-width: 768px)': {
      width: '56px',
      height: '56px',
      minWidth: '56px',
      minHeight: '56px',
    },
  },
  limitBox: {
    backgroundColor: '#D10017',
    borderRadius: '4px',
    padding: ' 2px 4px',
    position: 'absolute',
    bottom: '32px',
    color: '#fff',
    right: '8px',
    fontSize: '12px',
  },
  uploadMenuItem: {
    display: 'flex',
    transition: 'all 0.175s ease-out',
    gap: 1,
    padding: '8px',
    borderRadius: '6px',
    alignItems: 'center',
    '&:hover': {
      backgroundColor: '#F6F8F9',
    },
  },
};

export default styles;

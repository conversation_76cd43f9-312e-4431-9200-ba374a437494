import { Box, Typography } from '@mui/material';
import LoadingOverlay from 'components/LoadingOverlay';
import { FileIcon } from 'icons';

const FilePreview = ({
  name,
  size,
  loading = false,
  downloadable,
}: {
  name: string;
  size: number;
  loading?: boolean;
  downloadable?: boolean;
}) => {
  const fileExtension = name.split('.').pop();
  return (
    <Box
      bgcolor="#E4EAED"
      display="flex"
      maxWidth={'256px'}
      padding="8px 34px 8px 8px"
      position="relative"
      width="max-content"
      sx={{
        borderRadius: '8px',
        gap: '8px',
        alignItems: 'center',
        cursor: downloadable ? 'pointer' : 'unset',
      }}
    >
      <Box>
        <Box
          bgcolor="white"
          height="48px"
          padding="12px"
          sx={{ borderRadius: '6px' }}
        >
          <FileIcon />
        </Box>
      </Box>
      <Box>
        <Box display="flex" color="#262626">
          <Typography fontSize={14} className="line-clamp" sx={{ flex: 1 }}>
            {name.replace(/\.[^/.]+$/, '')}
          </Typography>
          <Typography component="span" fontSize={14} width="fit-content ">
            .{fileExtension}
          </Typography>
        </Box>
        <Typography color="#A8B0B4" fontSize={12} marginTop="3px">
          {(size / (1024 * 1024)).toFixed(2)} MB
        </Typography>
      </Box>
      <LoadingOverlay visible={loading} />
    </Box>
  );
};

export default FilePreview;

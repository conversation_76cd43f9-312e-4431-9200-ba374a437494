import { Box, But<PERSON>, Container, <PERSON>, Typography } from '@mui/material';
import { ArrowRightIcon, EmptyChat } from 'icons';
import React from 'react';

const EmptyPage = () => {
  return (
    <Container
      maxWidth="tablet"
      disableGutters
      sx={{
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        flexDirection: 'column',
        '@media (max-width: 768px)': {
          justifyContent: 'start',
        },
      }}
    >
      <Box
        p="15px 16px"
        display={{ tablet: 'none' }}
        bgcolor="white"
        marginTop={1}
      >
        <Typography fontSize={18} color="heading" fontWeight={700}>
          チャットルーム
        </Typography>
      </Box>
      <Box
        sx={{
          mt: { xs: 1, tablet: 4 },
          bgcolor: 'white',
          borderRadius: { xs: 0, md: 2 },
          p: '64px 24px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          '@media (max-width: 768px)': {
            display: 'flex',
          },
        }}
      >
        <EmptyChat />
        <Typography
          fontWeight={700}
          fontSize={16}
          marginTop={2}
          color="heading"
        >
          データが存在しません
        </Typography>
        <Typography fontSize={14} mt={1}>
          表示するチャットが存在しません
        </Typography>
        <Button
          variant="contained"
          className="shadow tabletStyle"
          size="large"
          color="yellow"
          fullWidth
          sx={{
            maxWidth: 358,
            mt: { xs: 4, tablet: 2 },
            '&.shadow.tabletStyle': {
              fontSize: { xs: 18, tablet: 20 },
              height: { xs: 48, tablet: 59 },
            },
          }}
          LinkComponent={Link}
          href="/"
          endIcon={<ArrowRightIcon width={32} height={32} color="white" />}
        >
          ホームへ戻る
        </Button>
      </Box>
    </Container>
  );
};

export default EmptyPage;

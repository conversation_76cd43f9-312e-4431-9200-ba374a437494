import { Box, Typography } from '@mui/material';
import Utils from 'features/chat/utils';
import Image from 'next/image';
import Link from 'next/link';
import { memo } from 'react';

import type { IRoom, IUserDoc } from '../../types';
import styles from './styles';

const Room = ({
  data,
  isActive,
  otherMemberData,
  isRead,
  replace,
}: {
  data: IRoom;
  isActive: boolean;
  otherMemberData?: IUserDoc;
  onClick?: (data: IRoom) => void;
  isRead?: boolean;
  replace?: boolean;
  firebaseUserId?: string;
}) => {
  return (
    <Box
      sx={styles.container}
      component={Link}
      data-active={isActive}
      href={{ query: { roomId: [data.id] } }}
      id={`room-${data.id}`}
      p={{ xs: '12px 16px', tablet: '8px' }}
      replace={replace}
      shallow
    >
      <Image
        alt=""
        className="shrink-0"
        height={56}
        quality={100}
        src={otherMemberData?.avatar || '/images/default-avatar.png'}
        style={{
          borderRadius: '50%',
          border: otherMemberData?.avatar ? 'unset' : '1px solid #DBE3E7',
        }}
        width={56}
      />
      <Box sx={{ flex: 1 }}>
        <Box
          display="flex"
          alignItems="center"
          gap={'8px'}
          justifyContent="space-between"
        >
          <Typography
            fontWeight={isRead ? 500 : 700}
            color="heading"
            fontSize={14}
            flex={1}
            className="line-clamp"
          >
            {otherMemberData?.fullName}
          </Typography>
          <Typography className="shrink-0" color="icon" fontSize={12}>
            {data.lastMessage?.updatedAt &&
              Utils.formatLastMessageTime(data.lastMessage.updatedAt)}
          </Typography>
        </Box>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          marginTop="4px"
        >
          <Typography
            sx={styles.messageText}
            color={isRead ? 'text.primary' : 'heading'}
            fontWeight={isRead ? 400 : 700}
            fontSize={12}
            className="line-clamp"
          >
            {data.lastMessage?.value}
            {!data.lastMessage?.value &&
              data.lastMessage.attachments &&
              data.lastMessage.attachments[0] &&
              Utils.getLastMessageFromAttachment(
                data.lastMessage.attachments[0],
              )}
          </Typography>
          {!isRead && (
            <Box
              bgcolor="#D10017"
              className="shrink-0"
              height="10px"
              sx={{ borderRadius: '50%' }}
              width="10px"
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default memo(
  Room,
  (prevProps, nextProps) =>
    JSON.stringify(prevProps) === JSON.stringify(nextProps),
);

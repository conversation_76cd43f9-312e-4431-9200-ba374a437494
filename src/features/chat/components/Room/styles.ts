const styles = {
  container: {
    display: 'flex',
    gap: '12px',
    backgroundColor: '#fff',
    borderRadius: '8px',
    transition: 'all 0.175s ease',
    cursor: 'pointer',
    alignItems: 'center',
    '&:hover': {
      backgroundColor: '#F6F8F9',
    },
    '&[data-active=true]': {
      backgroundColor: '#FDEED8',
    },
    '@media (max-width: 768px)': {
      borderRadius: 0,
      img: {
        width: '48px',
        height: '48px',
      },
    },
  },
  messageText: {
    flex: 1,
    whiteSpace: 'pre-wrap',
  },
};

export default styles;

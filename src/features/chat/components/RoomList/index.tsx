import { Box, CircularProgress, Typography } from '@mui/material';
import Room from 'features/chat/components/Room';
import type { IRoom, IUserDoc } from 'features/chat/types';
import React, { Fragment, memo } from 'react';

import styles from './styles';

const RoomList = ({
  isLoading,
  roomId,
  data,
  firebaseUserId,
  roomUsers,
}: {
  isLoading: boolean;
  roomId?: string;
  data: IRoom[];
  firebaseUserId?: string;
  roomUsers?: Record<string, IUserDoc>;
}) => {
  const isDetailPage = !!roomId;

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" mt={56}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      display={{ xs: isDetailPage ? 'none' : 'flex', tablet: 'flex' }}
      sx={{ flexDirection: 'column' }}
      width={{ xs: '100%', tablet: 344 }}
    >
      <Box sx={styles.title}>
        <Typography
          fontWeight={700}
          fontSize={{ xs: 18, tablet: 20 }}
          color="heading"
        >
          チャットルーム
        </Typography>
      </Box>
      <Box sx={styles.card} display="flex" p={{ xs: 0, tablet: 1 }}>
        {isLoading && (
          <Box display="flex" justifyContent="center" p={2}>
            <CircularProgress />
          </Box>
        )}
        {!isLoading && data.length === 0 && (
          <Box
            display="flex"
            alignItems="center"
            flexDirection="column"
            height="100%"
            justifyContent="center"
            padding={2}
          >
            <Typography>表示するデータが存在しません</Typography>
          </Box>
        )}
        {!isLoading &&
          data.length > 0 &&
          data.map((room) => {
            const isRead =
              room.numberOfUnreadMessages &&
              !!firebaseUserId &&
              (room.numberOfUnreadMessages[firebaseUserId] || 0) === 0;
            const otherMemberId = room.currentMembers.filter(
              (id) => id !== firebaseUserId,
            )[0];
            const otherMemberData = otherMemberId
              ? (roomUsers || {})[otherMemberId]
              : undefined;
            return (
              <Fragment key={room.id}>
                <Room
                  data={room}
                  firebaseUserId={firebaseUserId}
                  isActive={roomId === room.id}
                  isRead={isRead || roomId === room.id}
                  otherMemberData={otherMemberData}
                  replace={!!isDetailPage}
                />
              </Fragment>
            );
          })}
      </Box>
    </Box>
  );
};

export default memo(
  RoomList,
  (prevProps, nextProps) =>
    JSON.stringify(prevProps) === JSON.stringify(nextProps),
);

const styles = {
  title: {
    marginBottom: { xs: 1, tablet: 2 },
    marginTop: { xs: 1, tablet: 0 },
    padding: 2,
    gap: 2,
    alignItems: 'center',
    borderRadius: 2,
    backgroundColor: '#fff',
    '@media (max-width: 768px)': {
      borderRadius: 0,
      gap: '4px',
      overflow: 'unset',
      padding: '15px 16px',
    },
  },
  card: {
    flex: 1,
    flexDirection: 'column',
    borderRadius: 2,
    overflowY: 'auto',
    gap: 1,
    backgroundColor: 'white',
    '@media (max-width: 768px)': {
      flex: 0,
      overflow: 'unset',
      borderRadius: 0,
      marginBottom: 1,
      backgroundColor: 'unset',
      gap: '1px',
    },
  },
};

export default styles;

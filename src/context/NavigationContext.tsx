// src/context/NavigationContext.tsx

'use client';

// Since we're using client-side features

import type { ReactNode } from 'react';
import { createContext, useContext, useState } from 'react';

// Define the shape of the state with TypeScript
interface NavigationState {
  [key: string]: any; // Flexible state, can be customized
}

interface NavigationContextType {
  state: NavigationState;
  setState: (newState: NavigationState) => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(
  undefined,
);

export const NavigationProvider = ({ children }: { children: ReactNode }) => {
  const [state, setState] = useState<NavigationState>({});

  return (
    <NavigationContext.Provider value={{ state, setState }}>
      {children}
    </NavigationContext.Provider>
  );
};

// Custom hook to access the context
export const useNavigationState = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error(
      'useNavigationState must be used within a NavigationProvider',
    );
  }
  return context;
};

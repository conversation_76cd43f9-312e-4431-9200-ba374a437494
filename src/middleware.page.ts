// middleware.ts
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import getRedirecttUrl from 'utils/getRedirectUrl';

export function middleware(req: NextRequest) {
  const webCookie = JSON.parse(
    (req.cookies.get('lawyer-web-cookie')?.value || null) as unknown as string,
  );
  const role = webCookie?.role;
  const nextUrl = req.nextUrl.pathname;
  const redirectUrl = getRedirecttUrl({ role, nextUrl });
  if (redirectUrl) {
    return NextResponse.redirect(new URL(redirectUrl, req.url));
  }
  return NextResponse.next();
}

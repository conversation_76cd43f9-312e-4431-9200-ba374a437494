import {
  Box,
  Button,
  Container,
  Divider,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import ActionSection from 'components/Article/ActionSection';
import ArticleDetail from 'components/Article/ArticleDetail';
import OtherArticleList from 'components/Article/ArticleList/OtherArticleList';
import Breadcrumbs from 'components/Breadcrumbs';
import LawyerInfoCard from 'components/LawyerDetail/LawyerInfoCard';
import Layout from 'components/Layout';
import { useFetchDetail, useFetchList } from 'hooks';
import type { IArticleDetail, IPickupArticleListItem } from 'hooks/types';
import { t } from 'i18n';
import { ArrowRightIcon } from 'icons';
import { isEmpty } from 'lodash';
import articleQuery from 'models/article/query';
import type { GetServerSideProps } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import type { ReactElement } from 'react';
import { ARTICLE_SORT_ORDER } from 'utils/constants';
import Helper from 'utils/helpers';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const ArticleDetailPage = () => {
  const { query } = useRouter();

  const articleId = query.articleId as string;
  // Why do I call it again on client: There are some fields that QC and maybe partner wants it update at once (freeConsultationMenu)
  const fetchArticleDetail = useFetchDetail(
    articleQuery.articleDetail(articleId),
  );

  const fetchArticles = useFetchList({
    ...articleQuery.articleList,
    customParams: {
      sort: ARTICLE_SORT_ORDER,
    },
  });
  const articleDetail = fetchArticleDetail.detail as IArticleDetail;
  const articleList = (fetchArticles.list || []) as IPickupArticleListItem[];
  const otherArticles = articleList.filter(
    (article) => article._id !== articleId,
  );

  const { isLoading } = fetchArticles;

  return (
    <Box>
      <NextSeo {...seoConfig.articleDetail(articleDetail)} />
      <Container
        maxWidth="ex"
        disableGutters
        sx={{
          mt: 2,
          mb: { xs: 1, tablet: 4 },
          minHeight: 'calc(100vh - 375px)',
        }}
      >
        <Box px={{ xs: 2, ex: 0 }}>
          <Breadcrumbs
            omitIndexList={[2]}
            transformLabel={{
              '/articles/[articleId]': articleDetail.title || (
                <Skeleton variant="text" width={100} />
              ),
            }}
          />
        </Box>
        <Stack
          direction={{ sl: 'row' }}
          mt={{ xs: 2, tablet: 4 }}
          gap={{ xs: 1, tablet: 2 }}
        >
          <Box flex={1}>
            <Box
              flex={1}
              borderRadius={{ sl: 2 }}
              p={{ xs: 2, tablet: '32px 32px 48px' }}
              bgcolor="white"
            >
              <ArticleDetail data={articleDetail} />
            </Box>
          </Box>
          <Box sx={styles.rightSection}>
            {articleDetail.provider ? (
              <LawyerInfoCard data={articleDetail.provider} />
            ) : null}
            {(!isEmpty(otherArticles) || isLoading) && (
              <Box
                sx={styles.articleList}
                pb={otherArticles.length > 4 ? '12px' : 3}
              >
                <Typography
                  fontWeight="bold"
                  mb={{ xs: 2, tablet: 4 }}
                  color="heading"
                >
                  {t('articleList.otherArticleList')}
                </Typography>
                <OtherArticleList
                  loading={isLoading}
                  data={otherArticles.slice(0, 3)}
                  cellStyle={{ alignItems: 'start' }}
                  pathname="/articles/[articleId]"
                />
                {otherArticles.length > 4 && (
                  <>
                    <Divider sx={{ mt: { xs: 2, tablet: 3 } }} />
                    <Box
                      display="flex"
                      justifyContent="center"
                      mt={{ xs: '4px', tablet: '12px' }}
                      mb={{ xs: '-12px', tablet: -1 }}
                    >
                      <Link
                        passHref
                        href={{
                          pathname: '/articles',
                        }}
                        legacyBehavior
                      >
                        <Button endIcon={<ArrowRightIcon />}>
                          {t('global.seeMore')}
                        </Button>
                      </Link>
                    </Box>
                  </>
                )}
              </Box>
            )}
          </Box>
        </Stack>
      </Container>
      <ActionSection />
    </Box>
  );
};

ArticleDetailPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  try {
    const articleId = query.articleId as string;
    const qClient = new QueryClient();
    await Helper.prefetchDetail(articleQuery.articleDetail(articleId), qClient);
    return {
      props: {
        dehydratedState: dehydrate(qClient),
      },
    };
  } catch {
    return { notFound: true };
  }
};

export default ArticleDetailPage;

import { Box } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import ArticlesSection from 'components/NewHome/Articles';
import CTASection from 'components/NewHome/CTA';
import ExpertsSection from 'components/NewHome/Experts';
import ExternalArticlesSection from 'components/NewHome/ExternalArticles';
import FeaturesSection from 'components/NewHome/Features';
import HeadlinesSection from 'components/NewHome/Headlines';
import HeroSection from 'components/NewHome/Hero';
import HowToSection from 'components/NewHome/HowTo';
import NewLayout from 'components/NewHome/Layout';
import NewsSection from 'components/NewHome/News';
import PricingSection from 'components/NewHome/Pricing';
import SearchSection from 'components/NewHome/SearchSection';
import ThemesSection from 'components/NewHome/Themes';
import type { ReactElement } from 'react';
// const HeroSection = dynamic(() => import('components/NewHome/Hero'), {
//   ssr: false,
// });

// const SearchSection = dynamic(
//   () => import('components/NewHome/SearchSection'),
//   {
//     ssr: false,
//   },
// );

// const ExpertsSection = dynamic(() => import('components/NewHome/Experts'), {
//   ssr: false,
// });

// const FeaturesSection = dynamic(() => import('components/NewHome/Features'), {
//   ssr: false,
// });

// const ThemesSection = dynamic(() => import('components/NewHome/Themes'), {
//   ssr: false,
// });

// const CTASection = dynamic(() => import('components/NewHome/CTA'), {
//   ssr: false,
// });

// const HowToSection = dynamic(() => import('components/NewHome/HowTo'), {
//   ssr: false,
// });

// const PricingSection = dynamic(() => import('components/NewHome/Pricing'), {
//   ssr: false,
// });

// const ArticlesSection = dynamic(() => import('components/NewHome/Articles'), {
//   ssr: false,
// });

// const NewsSection = dynamic(() => import('components/NewHome/News'), {
//   ssr: false,
// });

// const HeadlinesSection = dynamic(() => import('components/NewHome/Headlines'), {
//   ssr: false,
// });

// const NewLayout = dynamic(() => import('components/NewHome/Layout'), {
//   ssr: false,
// });

const Index = () => {
  return (
    <Box>
      <Box component="section">
        <HeroSection />
      </Box>
      <Box component="section">
        <SearchSection />
      </Box>
      <Box
        component="section"
        id="experts-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <ExpertsSection />
      </Box>
      <Box
        component="section"
        id="features-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <FeaturesSection />
      </Box>
      <Box component="section">
        <CTASection />
      </Box>
      <Box
        component="section"
        id="themes-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <ThemesSection />
      </Box>
      <Box
        component="section"
        id="howto-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <HowToSection />
      </Box>
      <Box component="section">
        <CTASection />
      </Box>
      <Box
        component="section"
        id="pricing-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <PricingSection />
      </Box>
      <Box component="section">
        <CTASection />
      </Box>
      <Box
        component="section"
        id="articles-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <ArticlesSection />
      </Box>
      <Box
        component="section"
        id="news-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <NewsSection />
      </Box>
      <Box component="section" display={{ xs: 'none', md: 'block' }}>
        <ExternalArticlesSection />
      </Box>
      <Box component="section">
        <HeadlinesSection />
      </Box>
      <Box component="section">
        <CTASection />
      </Box>
    </Box>
  );
};

export async function getStaticProps() {
  const qClient = new QueryClient();

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
    revalidate: Number(process.env.HOME_REVALIDATE || 10),
  };
}

Index.getLayout = function getLayout(page: ReactElement) {
  return <NewLayout>{page}</NewLayout>;
};

export default Index;

import { dehydrate, QueryClient } from '@tanstack/react-query';
import Layout from 'components/Layout';
import providerQuery from 'models/provider/query';
import type { GetServerSideProps } from 'next';
import type { ReactElement } from 'react';
import ProviderList from 'shared/provider/providers';
import { PROVIDER_LIMIT, ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

const LawyerListPage = () => {
  return <ProviderList providerType={ProviderType.LAWYER} />;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const qClient = new QueryClient();
  if (req.url?.startsWith('/_next')) {
    return {
      props: {
        enabled: true,
      },
    };
  }
  await Helper.prefetchList(
    providerQuery.providerList(ProviderType.LAWYER),
    qClient,
    {
      ...query,
      providerType: [ProviderType.LAWYER],
      limit: PROVIDER_LIMIT,
    },
    undefined,
    { ...Helper.getTokenConfig(req, res) },
  );

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

LawyerListPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};
export default LawyerListPage;

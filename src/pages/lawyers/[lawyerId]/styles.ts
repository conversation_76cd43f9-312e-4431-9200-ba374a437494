import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  infoContainer: {
    bgcolor: 'white',
    p: { xs: 2, tablet: 4 },
    borderRadius: { xs: 0, tablet: 2 },
    mt: { xs: 2, tablet: 4 },
  },
  topArticle: {
    img: {
      borderRadius: '12px',
      width: '100%',
      height: 'auto',
    },
  },
  uploadVideo: {
    mt: { xs: 3, tablet: 4 },
    img: {
      width: '100%',
      height: 'auto',
      borderRadius: '12px',
      objectFit: 'cover',
    },
  },
  playIcon: {
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    svg: {
      width: { xs: 48, tablet: 88 },
      height: { xs: 48, tablet: 88 },
    },
  },
  seeMore: {
    display: 'flex',
    justifyContent: 'center',
    mt: { xs: '2px', tablet: '12px' },
    mb: -2,
  },
  solutionList: {
    p: { xs: 2, md: 4 },
    bgcolor: 'white',
    borderRadius: {
      md: 2,
      xs: 0,
    },
    mt: { xs: 1, tablet: 2 },
  },
  cellStyle: {
    alignItems: {
      md: 'center',
      xs: 'start',
    },
  },
  actions: {
    p: 4,
    display: 'flex',
    justifyContent: 'center',
    bgcolor: 'white',
    mt: { xs: 1, tablet: 4 },
  },
  actionWrapper: {
    flexDirection: { tablet: 'row' },
    justifyContent: 'space-between',
    maxWidth: { tablet: '600px' },
    width: 1,
  },
  actionText: {
    fontSize: { xs: 14, tablet: 16 },
    fontWeight: 500,
    color: 'heading',
  },
  secondButton: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    mt: { xs: 4, tablet: 0 },
  },
  actionButton: {
    mt: 2,
    width: { xs: 311, tablet: 240 },
    '.MuiButton-startIcon': {
      mr: { xs: 9, tablet: 2 },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import { dehydrate, QueryClient } from '@tanstack/react-query';
import Layout from 'components/Layout';
import articleQuery from 'models/article/query';
import providerQuery from 'models/provider/query';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import ProviderDetailPage from 'shared/provider/providers/[providerId]';
import api from 'utils/api';
import { ARTICLE_SORT_ORDER, ProviderType, ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

const LawyerDetailPage = () => {
  const { query } = useRouter();
  const lawyerId = query.lawyerId as string;

  return (
    <ProviderDetailPage
      providerId={lawyerId}
      providerType={ProviderType.LAWYER}
    />
  );
};

LawyerDetailPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export async function getStaticPaths() {
  const { data } = await api.get(
    providerQuery.providerList(ProviderType.LAWYER).apiUrl,
    {
      params: { limit: 9999, providerType: [ProviderType.LAWYER] },
    },
  );
  return {
    paths: data.docs.map((doc: { _id: string }) => ({
      params: { lawyerId: doc._id },
    })),
    fallback: 'blocking',
  };
}
export async function getStaticProps({ params }: any) {
  const providerId = params.lawyerId as string;
  const qClient = new QueryClient();
  const promises = [
    Helper.prefetchDetail(
      providerQuery.providerDetailByGuest(providerId),
      qClient,
      undefined,
      {
        params: {
          providerType: ROLES.LAWYER,
        },
      },
    ),
    Helper.prefetchList(providerQuery.providerSolutions(providerId), qClient, {
      providerId,
    }),
    Helper.prefetchList(
      providerQuery.providerArticleList(providerId),
      qClient,
      {
        providerId,
        sort: 'updatedAt.desc',
      },
    ),
    Helper.prefetchList(articleQuery.pickupArticleList, qClient, {
      providerId,
      sort: ARTICLE_SORT_ORDER,
    }),
    Helper.prefetchList(
      providerQuery.providerConsultationsByGuest(providerId),
      qClient,
      {
        providerId,
      },
    ),
  ];
  await Promise.all(promises);
  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
    revalidate: 1,
  };
}
export default LawyerDetailPage;

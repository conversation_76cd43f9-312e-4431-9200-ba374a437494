import { Box, Container, Divider, Skeleton, Stack } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import ListPagination from 'components/CustomPagination/ListPagination';
import LawyerInfoCard from 'components/LawyerDetail/LawyerInfoCard';
import SolutionList from 'components/LawyerDetail/SolutionList';
import Layout from 'components/Layout';
import BorderLabel from 'components/UI/BorderLabel';
import { useFetchDetail, useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ILawyerProfile, ISolution } from 'hooks/types';
import i18n from 'i18n';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';

import styles from '../styles';

const SolutionCases = () => {
  const { t } = i18n;
  const { query } = useRouter();
  const lawyerId = query.lawyerId as string;

  const fetchSolution = useFetchList({
    ...apiQuery.lawyerSolutions(lawyerId),
    customParams: {
      sort: 'updatedAt.desc',
    },
    omitKeys: ['providerId', 'solutionId'],
  });
  const fetchLawyerDetail = useFetchDetail(apiQuery.lawyerDetail(lawyerId));
  const lawyerDetail = fetchLawyerDetail.detail as ILawyerProfile;
  const solutionList = (fetchSolution.list || []) as ISolution[];
  const { total, isLoading } = fetchSolution;

  return (
    <Box>
      <Container disableGutters sx={{ mt: 2, mb: { xs: 1, tablet: 4 } }}>
        <Box px={{ xs: 2, lg: 0 }}>
          <Breadcrumbs
            transformLabel={{
              '/lawyers/[lawyerId]': lawyerDetail.fullName || (
                <Skeleton variant="text" width={100} />
              ),
            }}
          />
        </Box>
        <Stack
          direction={{ xs: 'column', md: 'row' }}
          mt={{ xs: 2, tablet: 4 }}
          gap={2}
        >
          <Stack spacing={2} flex={1}>
            <Box
              borderRadius={{ tablet: 2 }}
              p={{ xs: 2, tablet: 4 }}
              bgcolor="white"
            >
              <BorderLabel>{t('lawyerProfile.solution')}</BorderLabel>
              <SolutionList
                loading={isLoading}
                data={solutionList}
                lawyerId={lawyerId}
                sx={{ mt: { xs: '22px', tablet: 4 } }}
                spacing={{ xs: 2, tablet: 3 }}
                page={query.page as string}
                limit={query.limit as string}
                cellStyle={styles.cellStyle}
                divider={
                  <Box>
                    <Divider sx={{ ml: { xs: 6, tablet: 8 } }} />
                  </Box>
                }
              />
            </Box>
            {!isEmpty(solutionList) && (
              <Box display="flex" justifyContent="center" mt={5}>
                <ListPagination total={total || 0} />
              </Box>
            )}
          </Stack>
          <Box maxWidth={{ md: 384 }} width={1}>
            <LawyerInfoCard data={lawyerDetail} />
          </Box>
        </Stack>
      </Container>
    </Box>
  );
};

SolutionCases.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export default SolutionCases;

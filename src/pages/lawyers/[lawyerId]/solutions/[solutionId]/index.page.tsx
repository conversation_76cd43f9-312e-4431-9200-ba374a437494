import {
  <PERSON>,
  Button,
  Chip,
  Container,
  Divider,
  Grid,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import LawyerInfoCard from 'components/LawyerDetail/LawyerInfoCard';
import SolutionList from 'components/LawyerDetail/SolutionList';
import Layout from 'components/Layout';
import { useFetchDetail, useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ILawyerProfile, ISolution } from 'hooks/types';
import i18n from 'i18n';
import { ArrowRightIcon } from 'icons';
import { isEmpty } from 'lodash';
import type { GetServerSideProps } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { Gender, RadioOptions } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

const SolutionDetail = () => {
  const { t } = i18n;
  const { query } = useRouter();
  const lawyerId = query.lawyerId as string;
  const solutionId = query.solutionId as string;

  const fetchSolution = useFetchList({
    ...apiQuery.lawyerSolutions(lawyerId),
    customParams: {
      sort: 'updatedAt.desc',
    },
    omitKeys: ['providerId', 'solutionId'],
  });
  const fetchSolutionDetail = useFetchDetail({
    ...apiQuery.lawyerSolutionDetail(solutionId),
    enabled: false,
  });
  const fetchLawyerDetail = useFetchDetail({
    ...apiQuery.lawyerDetail(lawyerId),
    enabled: false,
  });
  const { isFetching: solutionListFetching } = fetchSolution;
  const solutionList = (fetchSolution.list || []) as ISolution[];
  const solutionDetail = fetchSolutionDetail.detail as ISolution;
  const lawyerDetail = fetchLawyerDetail.detail as ILawyerProfile;
  const otherSolutions = solutionList.filter(
    (solution) => solution._id !== solutionId,
  );
  return (
    <Box>
      <Container disableGutters sx={{ mt: 2, mb: 4 }}>
        <Box px={{ xs: 2, lg: 0 }}>
          <Breadcrumbs
            omitIndexList={[2]}
            transformLabel={{
              '/lawyers/[lawyerId]': lawyerDetail.fullName || (
                <Skeleton variant="text" width={100} />
              ),
              '/lawyers/[lawyerId]/solutions/[solutionId]':
                solutionDetail.title || <Skeleton variant="text" width={100} />,
            }}
          />
        </Box>
        <Stack
          direction={{ xs: 'column', md: 'row' }}
          mt={{ xs: 2, tablet: 4 }}
          gap={{ xs: 1, tablet: 2 }}
        >
          <Box flex={1}>
            <Box
              borderRadius={{ tablet: 2 }}
              p={{ xs: 2, tablet: '32px 32px 48px' }}
              bgcolor="white"
            >
              <Typography
                fontSize={{ xs: 20, tablet: 28 }}
                fontWeight="bold"
                color="heading"
              >
                {solutionDetail.title}
              </Typography>
              <Stack
                direction="row"
                gap={{ xs: '4px', tablet: 1 }}
                mt={{ xs: 1, tablet: '14px' }}
                flexWrap="wrap"
              >
                {solutionDetail.consultationField.map((field) => (
                  <Chip
                    className="tabletStyle"
                    label={field.value}
                    key={field._id}
                  />
                ))}
              </Stack>
              <Stack divider={<Divider />} sx={styles.customerInfo}>
                <Grid container py={{ xs: 1, tablet: 2 }} color="heading">
                  <Grid item xs={4} fontWeight="bold">
                    {t('solutionCase.customer')}
                  </Grid>
                  <Grid item xs={4}>
                    {solutionDetail.customer.age}
                    {t('solutionCase.ageUnit')}
                  </Grid>
                  <Grid item xs={4}>
                    {Gender[solutionDetail.customer.gender]}
                  </Grid>
                </Grid>
                <Grid container py={{ xs: 1, tablet: 2 }} color="heading">
                  <Grid item xs={4} fontWeight="bold">
                    {t('solutionCase.partner')}
                  </Grid>
                  <Grid item xs={4}>
                    {solutionDetail.partner.age}
                    {t('solutionCase.ageUnit')}
                  </Grid>
                  <Grid item xs={4}>
                    {Gender[solutionDetail.partner.gender]}
                  </Grid>
                </Grid>
                <Grid container py={{ xs: 1, tablet: 2 }} color="heading">
                  <Grid item xs={4} fontWeight="bold">
                    {t('solutionCase.kids')}
                  </Grid>
                  <Grid item xs={4}>
                    {RadioOptions[solutionDetail.children]}
                  </Grid>
                </Grid>
              </Stack>
              <Box px={{ tablet: 4 }}>
                <Stack spacing={2} mt={{ xs: 2, tablet: 3 }}>
                  <div>
                    <Typography sx={styles.label}>
                      {t('solutionCase.background')}
                    </Typography>
                    <Typography sx={styles.content}>
                      {solutionDetail.background}
                    </Typography>
                  </div>
                  <div>
                    <Typography sx={styles.label}>
                      {t('solutionCase.contents')}
                    </Typography>
                    <Typography sx={styles.content}>
                      {solutionDetail.content}
                    </Typography>
                  </div>
                  <div>
                    <Typography sx={styles.label}>
                      {t('solutionCase.benefit')}
                    </Typography>
                    <Typography sx={styles.content}>
                      {solutionDetail.benefit}
                    </Typography>
                  </div>
                  <div>
                    <Typography sx={styles.label}>
                      {t('solutionCase.point')}
                    </Typography>
                    <Typography sx={styles.content}>
                      {solutionDetail.point}
                    </Typography>
                  </div>
                </Stack>
              </Box>
            </Box>
          </Box>
          <Box maxWidth={{ md: 384 }} width={1}>
            <LawyerInfoCard data={lawyerDetail} />
            {!isEmpty(otherSolutions) && (
              <Box
                sx={styles.solutionList}
                pb={otherSolutions.length > 4 ? '12px' : 3}
              >
                <Typography sx={styles.otherTitle}>
                  {t('solutionCase.otherSolution')}
                </Typography>
                <SolutionList
                  loading={solutionListFetching}
                  lawyerId={lawyerId}
                  data={otherSolutions.slice(0, 3)}
                  spacing={{ xs: '12px', tablet: 3 }}
                />
                {otherSolutions.length > 4 && (
                  <>
                    <Divider sx={{ mt: { xs: '22px', tablet: 3 } }} />
                    <Box
                      display="flex"
                      justifyContent="center"
                      mt={{ xs: '2px', tablet: '12px' }}
                      mb={{ xs: '-14px', tablet: -1 }}
                    >
                      <Link
                        href={{
                          pathname: '/lawyers/[lawyerId]/solutions',
                          query: {
                            lawyerId,
                          },
                        }}
                        legacyBehavior
                      >
                        <Button endIcon={<ArrowRightIcon />}>
                          {t('global.seeMore')}
                        </Button>
                      </Link>
                    </Box>
                  </>
                )}
              </Box>
            )}
          </Box>
        </Stack>
      </Container>
    </Box>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  const qClient = new QueryClient();
  const providerId = query.lawyerId as string;
  const solutionId = query.solutionId as string;

  const promises = [
    Helper.prefetchDetail(apiQuery.lawyerDetail(providerId), qClient),
    Helper.prefetchDetail(apiQuery.lawyerSolutionDetail(solutionId), qClient),
  ];
  await Promise.all(promises);
  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};
SolutionDetail.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export default SolutionDetail;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  solutionList: {
    borderRadius: { tablet: 2 },
    p: { xs: 2, tablet: '24px 32px' },
    bgcolor: 'white',
    mt: { xs: 1, tablet: 2 },
    '.solution-item': {
      '.solution-title': {
        fontSize: { xs: 14, tablet: 16 },
        fontWeight: 500,
      },
    },
  },
  customerInfo: {
    borderRadius: { xs: 1, tablet: '12px' },
    bgcolor: 'backgroundColor',
    mt: { xs: 2, tablet: 3 },
    px: { xs: 2, tablet: 4 },
    fontSize: { xs: 14, tablet: 16 },
  },
  label: {
    fontWeight: 'bold',
    color: 'heading',
    fontSize: { xs: 14, tablet: 16 },
  },
  content: {
    mt: '4px',
    fontSize: { xs: 14, tablet: 16 },
  },
  otherTitle: {
    fontSize: { xs: 14, tablet: 16 },
    mb: { xs: '22px', tablet: 3 },
    fontWeight: 'bold',
    color: 'heading',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import { Box, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import Di<PERSON><PERSON>Background from 'components/ConsultationForm/DivorceBackground';
import KidInformation from 'components/ConsultationForm/KidInformation';
import MarriageInformation from 'components/ConsultationForm/MarriageDate';
import PartnerInformation from 'components/ConsultationForm/PartnerInformation';
import PropertyInformation from 'components/ConsultationForm/PropertyInfomation';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import ViewEditBlock from 'components/UI/ViewEditBlock/index';
import type { Dayjs } from 'dayjs';
import { useFetchDetail } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import useConsulation from 'hooks/customer/useConsultationForm';
import type {
  ConsultationRecord,
  DivorceBackgroundPayload,
  KidsFormPayload,
  MarriageInformationPayload,
  PartnerInformationPayload,
  PropertyInformationPayload,
} from 'hooks/customer/useConsultationForm/types';
import useGlobalState from 'hooks/useGlobalState';
import { t } from 'i18n';
import { KidIcon } from 'icons';
import { isEmpty, isUndefined } from 'lodash';
import type { ReactElement } from 'react';
import { useCallback, useMemo, useState } from 'react';
import { scroller } from 'react-scroll';
import type { GenderType, ISchoolType } from 'utils/constants';
import { Gender, RadioOptions, SchoolTypeOptions } from 'utils/constants';
import Helper from 'utils/helpers';

import type { IBlock } from './blocks';
import {
  divorceBlock,
  marriageBlock,
  partnerBlock,
  propertyBlock,
} from './blocks';
import styles from './styles';

const CustomerMyPage = () => {
  const [showNoti, setShowNoti] = useState<string>();
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetail,
  });
  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;
  const { refetch: refetchConsultation } = fetchConsultationDetail;

  const {
    updateParterInformation,
    updateDivorceBackground,
    updateMarriageInformation,
    updatePropertyInformation,
    updateKidsInformation,
    isUpdatingDivorceBackground,
    isUpdatingPartner,
    isUpdatingMarriage,
    isUpdatingProperty,
    isUpdatingKidsInformation,
  } = useConsulation();
  const { setConfirmModal } = useGlobalState();
  const [editForm, setEditForm] = useState<string[]>([]);

  const closeForm = useCallback((formName: string) => {
    setEditForm((forms) => forms.filter((form) => form !== formName));
    scroller.scrollTo(formName, {
      smooth: true,
      offset: -132,
    });
  }, []);

  const handleAfterSubmit = useCallback(
    (formName: string) => {
      closeForm(formName);
      setShowNoti(formName);
      refetchConsultation();
      Helper.toast(t('validation.completed'));
    },
    [closeForm, refetchConsultation],
  );

  const handleCancelEdit = useCallback(
    (isDirty: boolean, formName: string) => {
      setShowNoti('');
      if (isDirty) {
        setConfirmModal({
          onConfirm: () => closeForm(formName),
          title: 'discardInput.title',
          content: 'discardInput.message',
        });
      } else {
        closeForm(formName);
      }
    },
    [closeForm, setConfirmModal],
  );

  const handleUpdatePartnerInformation = useCallback(
    (values: PartnerInformationPayload, isDirty: boolean) => {
      if (isDirty) {
        updateParterInformation(values, {
          onSuccess: () => {
            handleAfterSubmit('partner-form');
            Helper.setEventTracking({
              ID: 'partner_record_completed',
            });
          },
        });
      } else {
        closeForm('partner-form');
      }
    },
    [closeForm, handleAfterSubmit, updateParterInformation],
  );

  const handleUpdateDivorceBackground = useCallback(
    (values: DivorceBackgroundPayload, isDirty: boolean) => {
      if (isDirty) {
        updateDivorceBackground(values, {
          onSuccess: () => {
            handleAfterSubmit('divorce-form');
            Helper.setEventTracking({
              ID: 'background_record_completed',
            });
          },
        });
      } else {
        closeForm('divorce-form');
      }
    },
    [closeForm, handleAfterSubmit, updateDivorceBackground],
  );

  const handleUpdateMarriageInformation = useCallback(
    (values: MarriageInformationPayload, isDirty: boolean) => {
      if (isDirty) {
        updateMarriageInformation(values, {
          onSuccess: () => {
            handleAfterSubmit('marriage-form');
            Helper.setEventTracking({
              ID: 'marriage_separation_record_completed',
            });
          },
        });
      } else {
        closeForm('marriage-form');
      }
    },
    [closeForm, handleAfterSubmit, updateMarriageInformation],
  );

  const handleUpdatePropertyInformation = useCallback(
    (values: PropertyInformationPayload, isDirty: boolean) => {
      if (isDirty) {
        updatePropertyInformation(values, {
          onSuccess: () => handleAfterSubmit('property-form'),
        });
      } else {
        closeForm('property-form');
      }
    },
    [closeForm, handleAfterSubmit, updatePropertyInformation],
  );
  const handleUpdateKidsInformation = useCallback(
    (values: KidsFormPayload, isDirty: boolean) => {
      if (isDirty) {
        updateKidsInformation(values, {
          onSuccess: () => handleAfterSubmit('kid-form'),
        });
      } else {
        closeForm('kid-form');
      }
    },
    [closeForm, handleAfterSubmit, updateKidsInformation],
  );

  const kids = consultationDetail?.kids?.kids;

  const handleEditForm = useCallback((form: string) => {
    setEditForm((forms) => forms.concat(form));
  }, []);

  const childrenFields = useMemo(
    () =>
      (kids || []).map((_: unknown, index: number) => [
        { label: t('consultationRecord.kidOrder', { number: index + 1 }) },
        {
          label: t('consultationRecord.kidGender'),
          path: `kids.kids[${index}].gender`,
          renderValue: (value: GenderType) => Gender[value],
        },
        {
          label: t('consultationRecord.kidAge'),
          path: `kids.kids[${index}].age`,
          renderValue: (value: Dayjs) =>
            !isUndefined(value) &&
            t('consultationRecord.ageNumber', { number: value }),
        },
        {
          label: t('consultationRecord.schoolType'),
          path: `kids.kids[${index}].schoolType`,
          renderValue: (value: ISchoolType) =>
            value && SchoolTypeOptions[value],
        },
      ]),
    [kids],
  );

  const kidsFields: IBlock = {
    title: t('consultationRecord.kidInformation'),
    icon: <KidIcon />,
    fields: [
      {
        label: t('consultationRecord.kids'),
        path: 'kids.kids',
        renderValue: (value: unknown) =>
          value && (
            <>
              <Typography>
                {isEmpty(value) ? RadioOptions.no : RadioOptions.yes}
              </Typography>
              <Typography sx={styles.kidHelperText}>
                {t('consultationRecord.under18')}
              </Typography>
            </>
          ),
      },
    ].concat(childrenFields.flat() as never),
  };
  return (
    <Box>
      <Box sx={styles.breadcrumbWrapper}>
        <Breadcrumbs omitIndexList={[0]} />
        <Typography sx={styles.breadcrumbText}>
          {t('mypage.consultation')}
        </Typography>
      </Box>
      <ViewEditBlock
        editMode={editForm.includes('divorce-form')}
        onEdit={() => handleEditForm('divorce-form')}
        detail={consultationDetail}
        block={divorceBlock}
        formId="divorce-form"
        showNoti={showNoti === 'divorce-form'}
      >
        <DivorceBackground
          record={consultationDetail}
          loading={isUpdatingDivorceBackground}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'divorce-form')}
          onSubmit={(values, isDirty) =>
            handleUpdateDivorceBackground(values, isDirty)
          }
        />
      </ViewEditBlock>
      <ViewEditBlock
        editMode={editForm.includes('partner-form')}
        onEdit={() => handleEditForm('partner-form')}
        detail={consultationDetail}
        block={partnerBlock}
        formId="partner-form"
        showNoti={showNoti === 'partner-form'}
      >
        <PartnerInformation
          record={consultationDetail}
          loading={isUpdatingPartner}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'partner-form')}
          onSubmit={handleUpdatePartnerInformation}
        />
      </ViewEditBlock>
      <ViewEditBlock
        editMode={editForm.includes('marriage-form')}
        onEdit={() => handleEditForm('marriage-form')}
        detail={consultationDetail}
        block={marriageBlock}
        formId="marriage-form"
        showNoti={showNoti === 'marriage-form'}
      >
        <MarriageInformation
          record={consultationDetail}
          loading={isUpdatingMarriage}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'marriage-form')}
          onSubmit={handleUpdateMarriageInformation}
        />
      </ViewEditBlock>
      <ViewEditBlock
        editMode={editForm.includes('property-form')}
        onEdit={() => handleEditForm('property-form')}
        detail={consultationDetail}
        block={propertyBlock}
        formId="property-form"
        showNoti={showNoti === 'property-form'}
      >
        <PropertyInformation
          record={consultationDetail}
          loading={isUpdatingProperty}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'property-form')}
          onSubmit={handleUpdatePropertyInformation}
        />
      </ViewEditBlock>
      <ViewEditBlock
        editMode={editForm.includes('kid-form')}
        onEdit={() => handleEditForm('kid-form')}
        detail={consultationDetail}
        block={kidsFields}
        formId="kid-form"
        showNoti={showNoti === 'kid-form'}
      >
        <KidInformation
          loading={isUpdatingKidsInformation}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'kid-form')}
          onSubmit={handleUpdateKidsInformation}
          record={consultationDetail}
        />
      </ViewEditBlock>
    </Box>
  );
};

CustomerMyPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout>{page}</SideMenuLayout>
    </Layout>
  );
};

export default CustomerMyPage;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  breadcrumbContainer: {
    p: { xs: '8px 16px', tablet: '16px 32px' },
    borderRadius: { tablet: 2 },
    bgcolor: 'white',
  },
  title: {
    fontSize: { xs: 24, tablet: 32 },
    fontWeight: 'bold',
    mt: { xs: '4px', tablet: '12px' },
    color: 'heading',
  },
  container: {
    py: { xs: 1, tablet: 2 },
    '& .mypage-menu': {
      mt: { xs: 1, tablet: 2 },
    },
  },
  logoutButton: {
    mt: { xs: 1, tablet: 2 },
    p: { xs: 2, tablet: 4 },
    borderRadius: { tablet: 2 },
    bgcolor: 'white',
    color: 'heading',
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      bgcolor: 'rgba(0, 0, 0, 0.04)',
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

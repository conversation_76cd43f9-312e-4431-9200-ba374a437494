import { Box, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import ProfileFrom from 'components/ConsultationForm/Profile';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import type { IBlockField } from 'components/UI/ViewEditBlock/index';
import ViewEditBlock from 'components/UI/ViewEditBlock/index';
import { useAuth, useFetchUser } from 'hooks';
import type { IUpdateCustomerProfile } from 'hooks/useAuth/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import i18n from 'i18n';
import { ProfileIcon } from 'icons';
import { pick } from 'lodash';
import type { ReactElement, ReactNode } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import type { GenderType } from 'utils/constants';
import { Gender } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

const CustomerProfilePage = () => {
  const { setConfirmModal } = useGlobalState();
  const [showNoti, setShowNoti] = useState(false);
  const { data: currentUser, refetch } = useFetchUser<CustomerData>({
    enabled: false,
  });
  const { t } = i18n;
  const defaultValues = useMemo(
    () => ({
      ...pick(currentUser, [
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'annualIncome',
        'job',
        'phone',
      ]),
      currentAddress1: currentUser?.currentAddress?.address1._id,
      age: currentUser?.age?._id,
    }),
    [currentUser],
  );

  const [editable, setEditable] = useState(false);
  const { updateCustomerProfile, isCustomerUpdating } = useAuth();

  useEffect(() => {
    refetch();
  }, [refetch]);

  const handleEditMode = useCallback(() => {
    setShowNoti(false);
    setEditable(true);
  }, []);

  const handleCancelEdit = useCallback(
    (isDirty: boolean) => {
      if (isDirty) {
        setConfirmModal({
          onConfirm: () => setEditable(false),
          title: 'discardInput.title',
          content: 'discardInput.message',
        });
      } else setEditable(false);
    },
    [setConfirmModal],
  );

  const handleUpdateCustomerProfile = useCallback(
    (values: IUpdateCustomerProfile, isDirty: boolean) => {
      if (isDirty) {
        updateCustomerProfile(values, {
          onSuccess: () => {
            Helper.toast(t('validation.completed'));
            refetch();
            setEditable(false);
            setShowNoti(true);
          },
        });
      } else {
        setEditable(false);
      }
    },
    [refetch, t, updateCustomerProfile],
  );

  const profileBlock: {
    title: string;
    icon: ReactNode;
    fields: IBlockField[];
  } = {
    title: t('updateCustomerProfile.yourProfile'),
    icon: <ProfileIcon />,
    fields: [
      {
        label: t('updateCustomerProfile.fullName'),
        path: 'fullName',
      },
      {
        label: t('updateCustomerProfile.katakanaName'),
        path: 'katakanaName',
      },
      {
        label: t('updateCustomerProfile.email'),
        path: 'email',
      },
      {
        label: t('updateCustomerProfile.gender'),
        path: 'gender',
        renderValue: (value) => Gender[value as GenderType],
      },
      {
        label: t('updateCustomerProfile.phone'),
        path: 'phone',
      },
      {
        label: t('updateCustomerProfile.address'),
        path: 'currentAddress.address1.value',
      },
      {
        label: t('updateCustomerProfile.age'),
        path: 'age.value',
      },
      {
        label: t('updateCustomerProfile.job'),
        path: 'job',
      },
      {
        label: t('updateCustomerProfile.annualIncome'),
        path: 'annualIncome',
      },
    ],
  };

  return (
    <Box>
      <Box sx={styles.breadcrumbWrapper}>
        <Breadcrumbs omitIndexList={[0]} />
        <Typography sx={styles.breadcrumbText}>
          {t('updateCustomerProfile.profileInformation')}
        </Typography>
      </Box>
      <ViewEditBlock
        editMode={editable}
        onEdit={handleEditMode}
        detail={currentUser}
        block={profileBlock}
        showNoti={showNoti}
        formId="profile-form"
      >
        <ProfileFrom
          currentUser={{
            email: currentUser?.email || '',
            isCompletedProfile: currentUser?.isCompletedProfile,
            phone: currentUser?.phone || '',
          }}
          onCancel={handleCancelEdit}
          onSubmit={handleUpdateCustomerProfile}
          defaultValues={defaultValues as IUpdateCustomerProfile}
          loading={isCustomerUpdating}
        />
      </ViewEditBlock>
    </Box>
  );
};

CustomerProfilePage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout>{page}</SideMenuLayout>
    </Layout>
  );
};

export default CustomerProfilePage;

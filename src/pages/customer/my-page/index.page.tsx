import { Box, Container, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import Layout from 'components/Layout';
import RecommendLawyer from 'components/RecommendLawyer';
import MyPageMenu from 'components/UI/MyPageMenu';
import { useFetchDetail, useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { IPrefecture } from 'hooks/types';
import useAuth from 'hooks/useAuth';
import useGlobalState from 'hooks/useGlobalState';
import { t } from 'i18n';
import {
  MyPageAppointmentIcon,
  MyPagePaymentIcon,
  MyPageProfileIcon,
  MyPageRecommendIcon,
  MyPageRecordIcon,
  SearchIcon,
} from 'icons';
import { isEmpty } from 'lodash';
import type { IRecommendListItem } from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import resourceQuery from 'models/resource/query';
import type { ReactElement } from 'react';
import { ProviderType } from 'utils/constants';

import styles from './styles';

const customerMenus = [
  {
    path: '/customer/my-page/cases',
    label: '面談情報',
    icon: <MyPageAppointmentIcon />,
  },
  {
    path: '/customer/my-page/consultation-record',
    label: 'カルテ',
    icon: <MyPageRecordIcon />,
  },
  {
    path: '/customer/my-page/preference',
    label: '希望する専門家',
    icon: <MyPageRecommendIcon />,
  },
  {
    path: '/customer/my-page/profile',
    label: 'プロフィール',
    icon: <MyPageProfileIcon />,
  },
  {
    path: '/customer/my-page/cards',
    label: 'お支払い方法',
    icon: <MyPagePaymentIcon />,
  },
];

const MyPage = () => {
  const { logOut } = useAuth();
  const { setConfirmModal } = useGlobalState();

  const handleLogout = () => {
    logOut();
  };
  const { ids: consultationIds } = useFetchList<IPrefecture>({
    ...resourceQuery.consultations,
    customParams: {
      ...resourceQuery.consultations.customParams,
      providerType: [ProviderType.LAWYER],
    },
  });
  const { ids: counselorConsultationIds } = useFetchList<IPrefecture>({
    ...resourceQuery.consultations,
    customParams: {
      ...resourceQuery.consultations.customParams,
      providerType: [ProviderType.COUNSELOR],
    },
  });
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetail,
    enabled: false,
  });

  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;

  const { list: recommendLawyerList, isLoading } =
    useFetchList<IRecommendListItem>({
      ...apiQuery.recommendLawyerList,
      enabled: !fetchConsultationDetail.isLoading,
      customParams: {
        gender: consultationDetail.expectLawyer?.gender,
        consultationField: consultationDetail.expectLawyer?.nothingSpecial
          ? consultationIds
          : consultationDetail.expectLawyer?.consultation?.map(
              (field) => field._id,
            ),
        minAge: consultationDetail.expectLawyer?.age?.min,
        maxAge: consultationDetail.expectLawyer?.age?.max,
      },
    });

  const { list: recommendCounselors } = useFetchList<IRecommendListItem>({
    ...providerQuery.recommendCounselorList,
    enabled: !fetchConsultationDetail.isLoading,
    customParams: {
      gender: consultationDetail.expectCounselor?.gender,
      consultationField: consultationDetail.expectCounselor?.nothingSpecial
        ? counselorConsultationIds
        : consultationDetail.expectCounselor?.consultation?.map(
            (field) => field._id,
          ),
      minAge: consultationDetail.expectCounselor?.age?.min,
      maxAge: consultationDetail.expectCounselor?.age?.max,
    },
  });

  return (
    <Container disableGutters maxWidth="lg" sx={styles.container}>
      <Box sx={styles.breadcrumbContainer}>
        <Breadcrumbs omitIndexList={[0]} />
        <Typography sx={styles.title}>{t('header.mypage')}</Typography>
      </Box>
      {(!isEmpty(recommendLawyerList) || isLoading) && (
        <Box mt={{ xs: 1, tablet: 2 }}>
          <RecommendLawyer
            tabletSlidesToShow={2.36}
            loading={isLoading}
            data={recommendLawyerList}
            recommendCounselors={recommendCounselors}
            showButton={false}
          />
        </Box>
      )}
      <MyPageMenu
        menu={[
          {
            path: '/lawyers',
            label: '弁護士を探す',
            icon: <SearchIcon />,
          },
          {
            path: '/counselors',
            label: 'カウンセラーを探す',
            icon: <SearchIcon />,
          },
        ]}
      />
      <MyPageMenu menu={customerMenus} />
      <Box
        sx={styles.logoutButton}
        className="pointer"
        onClick={() =>
          setConfirmModal({
            title: 'logout.title',
            onConfirm: handleLogout,
            content: 'logout.message',
          })
        }
      >
        <Typography fontWeight={500} fontSize={{ xs: 14, tablet: 16 }}>
          {t('global.logout')}
        </Typography>
      </Box>
    </Container>
  );
};

MyPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export default MyPage;

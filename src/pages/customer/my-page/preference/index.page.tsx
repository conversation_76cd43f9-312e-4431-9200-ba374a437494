import { Box, Chip, Stack, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import Preference from 'components/ConsultationForm/Preference';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import type { IBlockField } from 'components/UI/ViewEditBlock/index';
import ViewEditBlock from 'components/UI/ViewEditBlock/index';
import { useFetchDetail } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import useConsulation from 'hooks/customer/useConsultationForm';
import type {
  ConsultationRecord,
  ExpectLawyerPayload,
  PreferencePayload,
} from 'hooks/customer/useConsultationForm/types';
import type { IListItem } from 'hooks/types';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { RecommendIcon } from 'icons';
import { isEmpty } from 'lodash';
import recordQuery from 'models/record/query';
import type { ReactElement, ReactNode } from 'react';
import { useCallback, useState } from 'react';
import { scroller } from 'react-scroll';
import type { GenderType } from 'utils/constants';
import { Gender, ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

const CustomerExpectLawyer = () => {
  const { setConfirmModal } = useGlobalState();
  const [showNoti, setShowNoti] = useState<string>();
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetail,
  });
  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;
  const { refetch } = fetchConsultationDetail;

  const { updateExpectLawyer, isUpdatingExpectLawyer } = useConsulation();
  const {
    mutateAsync: updateExpectCounselor,
    isLoading: isUpdatingExpectCounselor,
  } = useMutate<PreferencePayload>(recordQuery.expectCounselor);

  const [editForm, setEditForm] = useState<string[]>([]);

  const handleEditForm = useCallback((form: string) => {
    setEditForm((forms) => forms.concat(form));
  }, []);

  const closeForm = useCallback((formName: string) => {
    setEditForm((forms) => forms.filter((form) => form !== formName));
    scroller.scrollTo(formName, {
      smooth: true,
      offset: -132,
    });
  }, []);

  const handleAfterSubmit = useCallback(
    (formName: string) => {
      closeForm(formName);
      setShowNoti(formName);
      refetch();
      Helper.toast(t('validation.completed'));
    },
    [closeForm, refetch],
  );

  const handleCancelEdit = useCallback(
    (isDirty: boolean, formName: string) => {
      setShowNoti('');
      if (isDirty) {
        setConfirmModal({
          onConfirm: () => closeForm(formName),
          title: 'discardInput.title',
          content: 'discardInput.message',
        });
      } else {
        closeForm(formName);
      }
    },
    [closeForm, setConfirmModal],
  );

  const handleUpdateExpectLawyer = useCallback(
    (values: ExpectLawyerPayload, isDirty: boolean) => {
      const nothingSpecial =
        values.consultation && values.consultation[0] === 'NOTHING_SPECIAL';
      const consultation = nothingSpecial ? [] : values.consultation;
      if (isDirty) {
        updateExpectLawyer(
          { ...values, consultation, nothingSpecial },
          {
            onSuccess: () => {
              handleAfterSubmit('expect-lawyer-form');
            },
          },
        );
      } else {
        closeForm('expect-lawyer-form');
      }
    },
    [closeForm, handleAfterSubmit, updateExpectLawyer],
  );

  const handleUpdateExpectCounselor = useCallback(
    (values: PreferencePayload, isDirty: boolean) => {
      if (isDirty) {
        const nothingSpecial =
          values.consultation && values.consultation[0] === 'NOTHING_SPECIAL';
        const consultation = nothingSpecial ? [] : values.consultation;
        updateExpectCounselor(
          { ...values, consultation, nothingSpecial },
          {
            onSuccess: () => {
              handleAfterSubmit('expect-counselor-form');
            },
          },
        );
      } else {
        closeForm('expect-counselor-form');
      }
    },
    [closeForm, handleAfterSubmit, updateExpectCounselor],
  );

  const expectLawyerBlock: {
    title: string;
    icon: ReactNode;
    fields: IBlockField<ConsultationRecord>[];
  } = {
    title: t('consultationRecord.expectLawyer'),
    icon: <RecommendIcon />,
    fields: [
      {
        label: t('consultationRecord.lawyerGender'),
        path: 'expectLawyer.gender',
        renderValue: (value: GenderType) => Gender[value],
      },
      {
        label: t('consultationRecord.lawyerAge'),
        path: 'expectLawyer.age.value',
      },
      {
        label: t('consultationRecord.consultation'),
        path: 'expectLawyer.consultation',
        renderValue: (value: IListItem[], data) => {
          if (data?.expectLawyer?.nothingSpecial) {
            return '特になし';
          }
          if (!isEmpty(value)) {
            return (
              <Stack gap="4px" direction="row" flexWrap="wrap">
                {value.map((field) => (
                  <Chip
                    className="tabletStyle"
                    key={field._id}
                    label={field.value}
                  />
                ))}
              </Stack>
            );
          }
          return null;
        },
      },
    ],
  };

  const expectCounselorBlock: {
    title: string;
    icon: ReactNode;
    fields: IBlockField<ConsultationRecord>[];
  } = {
    title: '希望するカウンセラー',
    icon: <RecommendIcon />,
    fields: [
      {
        label: 'カウンセラーの性別',
        path: 'expectCounselor.gender',
        renderValue: (value: GenderType) => Gender[value],
      },
      {
        label: 'カウンセラーの年齢',
        path: 'expectCounselor.age.value',
      },
      {
        label: '得意分野',
        path: 'expectCounselor.consultation',
        renderValue: (value: IListItem[], data) => {
          if (data?.expectCounselor?.nothingSpecial) {
            return '特になし';
          }
          if (!isEmpty(value)) {
            return (
              <Stack gap="4px" direction="row" flexWrap="wrap">
                {value.map((field) => (
                  <Chip
                    className="tabletStyle"
                    key={field._id}
                    label={field.value}
                  />
                ))}
              </Stack>
            );
          }
          return null;
        },
      },
    ],
  };

  return (
    <Box>
      <Box sx={styles.breadcrumbContainer}>
        <Breadcrumbs omitIndexList={[0]} />
        <Typography sx={styles.title}>
          {t('breadcrumb.myPageExpectLawyer')}
        </Typography>
      </Box>
      <ViewEditBlock
        editMode={editForm.includes('expect-lawyer-form')}
        onEdit={() => handleEditForm('expect-lawyer-form')}
        detail={consultationDetail}
        block={expectLawyerBlock}
        showNoti={showNoti === 'expect-lawyer-form'}
        formId="expect-lawyer-form"
      >
        <Preference
          onCancel={(isDirty) =>
            handleCancelEdit(isDirty, 'expect-lawyer-form')
          }
          onSubmit={handleUpdateExpectLawyer}
          record={consultationDetail}
          loading={isUpdatingExpectLawyer}
          providerType={ProviderType.LAWYER}
        />
      </ViewEditBlock>
      <ViewEditBlock
        editMode={editForm.includes('expect-counselor-form')}
        onEdit={() => handleEditForm('expect-counselor-form')}
        detail={consultationDetail}
        block={expectCounselorBlock}
        showNoti={showNoti === 'expect-counselor-form'}
        formId="expect-counselor-form"
      >
        <Preference
          onCancel={(isDirty) =>
            handleCancelEdit(isDirty, 'expect-counselor-form')
          }
          onSubmit={handleUpdateExpectCounselor}
          record={consultationDetail}
          loading={isUpdatingExpectCounselor}
          providerType={ProviderType.COUNSELOR}
        />
      </ViewEditBlock>
    </Box>
  );
};

CustomerExpectLawyer.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout>{page}</SideMenuLayout>
    </Layout>
  );
};

export default CustomerExpectLawyer;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  topAlert: {
    mt: { xs: 0, tablet: 2, desktop: 4 },
    letterSpacing: '-0.5px',
  },
  breadcrumbWrapper: {
    bgcolor: 'white',
    p: { xs: '8px 16px', tablet: '17px 17px 17px 32px' },
    borderRadius: { xs: 0, tablet: 2 },
    my: { xs: 1, tablet: 0 },
  },
  breadcrumbText: {
    fontSize: { xs: 24, tablet: 32 },
    fontWeight: 'bold',
    mt: { xs: '4px', tablet: '12px' },
    color: 'heading',
  },
  icon: (theme) => ({
    svg: { width: 32, height: 32, display: 'block' },
    [theme.breakpoints.down('tablet')]: {
      svg: {
        width: 28,
        height: 28,
        display: 'block',
      },
    },
  }),
  cardList: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  containerEmpty: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    mt: { xs: '200px', tablet: 10 },
    mb: { xs: '190px', tablet: '68px' },
  },
  iconEmpty: (theme) => ({
    svg: { width: 96, height: 96 },
    [theme.breakpoints.down('tablet')]: {
      svg: {
        width: 80,
        height: 80,
      },
    },
  }),
  containerCard: {
    mt: { xs: 0, tablet: 2 },
    borderRadius: { xs: 0, tablet: 2 },
    bgcolor: 'white',
    p: { xs: 2, tablet: 4 },
  },
  addButton: {
    maxWidth: { xs: 76, tablet: 92 },
    svg: { color: 'primary.main' },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

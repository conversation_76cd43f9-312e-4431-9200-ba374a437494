import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Container,
  Stack,
  Typography,
} from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import CardItem from 'components/Customer/CardItem';
import SkeletonList from 'components/Customer/CardItem/Skeleton';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import { LayoutGroup } from 'framer-motion';
import { useFetchList } from 'hooks';
import { t } from 'i18n';
import { AddIcon, LetterIcon, PaymentIcon } from 'icons';
import { isEmpty } from 'lodash';
import type { ICardsList } from 'models/card/interface';
import cardQuery from 'models/card/query';
import dynamic from 'next/dynamic';
import type { ReactElement } from 'react';
import { useState } from 'react';

import styles from './styles';

const AddCardModal = dynamic(() => import('components/Customer/AddCardModal'));

const CustomerCardsPage = () => {
  const [openAddCard, setOpenAddCard] = useState<boolean | ICardsList>(false);
  const {
    refetch,
    isLoading,
    list: cardsList,
  } = useFetchList<ICardsList>({
    ...cardQuery.cardsList,
  });

  const hasMissingHolderNameCard = cardsList.some((card) => {
    return !card.details.cardholderName;
  });

  return (
    <Box>
      <Box sx={styles.breadcrumbWrapper}>
        <Breadcrumbs omitIndexList={[0]} />
        <Typography sx={styles.breadcrumbText}>{t('mypage.card')}</Typography>
      </Box>
      <Container maxWidth="md" disableGutters>
        {hasMissingHolderNameCard && (
          <Alert sx={styles.topAlert} severity="warning">
            {t('otherMessage.OM02')}
          </Alert>
        )}
        <Box sx={styles.containerCard}>
          <Box sx={styles.cardList}>
            <Stack direction="row" spacing={1} alignItems="center">
              <Box sx={styles.icon}>
                <PaymentIcon />
              </Box>
              <Typography
                fontSize={{ xs: 18, tablet: 24 }}
                fontWeight="bold"
                color="heading"
              >
                {t('mypage.card')}
              </Typography>
            </Stack>
            <Button
              variant="tertiary"
              sx={styles.addButton}
              fullWidth
              startIcon={<AddIcon />}
              color="primary"
              className="tabletStyle"
              onClick={() => setOpenAddCard(true)}
            >
              {t('global.add')}
            </Button>
          </Box>
          {isLoading && isEmpty(cardsList) && (
            <Box mt={{ xs: '18px', tablet: '28px' }}>
              <SkeletonList />
            </Box>
          )}
          {!isLoading &&
            (isEmpty(cardsList) ? (
              <Box sx={styles.containerEmpty}>
                <Box sx={styles.iconEmpty}>
                  <LetterIcon />
                </Box>
                <Typography
                  textAlign="center"
                  component="div"
                  mt={{ xs: 1, tablet: 2 }}
                  fontSize={{ xs: '14px', tablet: '16px' }}
                >
                  {t('card.noData')}
                </Typography>
              </Box>
            ) : (
              <Box mt={{ xs: '18px', tablet: '28px' }}>
                <Stack gap={{ xs: 1, tablet: 2 }}>
                  <LayoutGroup>
                    {cardsList.map((item) => (
                      <CardItem
                        card={item}
                        key={item.id}
                        refetch={refetch}
                        isCheck={cardsList.length > 1}
                        editCallback={() => setOpenAddCard(item)}
                      />
                    ))}
                  </LayoutGroup>
                </Stack>
              </Box>
            ))}
        </Box>
        {openAddCard && (
          <AddCardModal
            open={!!openAddCard}
            onClose={() => setOpenAddCard(false)}
            onSuccess={() => {
              setOpenAddCard(false);
              refetch();
            }}
            editPayload={
              typeof openAddCard === 'object'
                ? { card: openAddCard, isEdit: true }
                : undefined
            }
          />
        )}
      </Container>
    </Box>
  );
};

CustomerCardsPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout>{page}</SideMenuLayout>
    </Layout>
  );
};

export default CustomerCardsPage;

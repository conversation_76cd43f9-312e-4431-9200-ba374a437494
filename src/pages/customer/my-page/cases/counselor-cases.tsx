import { Box, CircularProgress, Typography } from '@mui/material';
import CaseTag from 'components/Case/CaseTag';
import CaseListMobile from 'components/Customer/CaseList';
import CustomTable from 'components/CustomTable';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useFetchList } from 'hooks';
import useBreakpoint from 'hooks/useBreakpoint';
import i18n from 'i18n';
import type { MRT_ColumnDef } from 'material-react-table';
import type { ICaseListItem } from 'models/case/interface';
import caseQuery from 'models/case/query';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import type { CaseStatusType } from 'utils/constants';
import { MomentFormat, ProviderType } from 'utils/constants';

const CaseList = () => {
  const { push } = useRouter();
  const { t } = i18n;
  const {
    list = [],
    total,
    isFetching,
    isLoading,
  } = useFetchList<ICaseListItem>({
    ...caseQuery.customerCaseList,
    omitKeys: ['tab'],
    customParams: { providerType: ProviderType.COUNSELOR },
  });

  const isBreakpoint = useBreakpoint({});

  const columns: MRT_ColumnDef<ICaseListItem>[] = [
    {
      accessorKey: '_id',
      header: t('caseList.caseId'),
      enableSorting: false,
    },
    {
      accessorKey: 'provider.fullName',
      header: t('caseList.counselorName'),
      enableSorting: false,
      Cell: ({ cell }) => (
        <Typography fontSize={{ tablet: 14, lg: 16 }}>
          {cell.row.original.provider.nickname || cell.getValue<string>()}
        </Typography>
      ),
    },
    {
      accessorKey: 'finalizedDate',
      header: t('caseList.finalizedDate'),
      enableSorting: false,
      Cell: ({ cell }) =>
        cell.getValue() ? (
          <Typography fontSize={{ tablet: 14, lg: 16 }}>
            {`${dayjs(cell.getValue() as Dayjs).format(
              MomentFormat.JP_YEAR_MONTH_DATE_DAY,
            )}
              ${dayjs(cell.getValue() as Dayjs).format(
                MomentFormat.JP_HOUR_MINUTE,
              )}`}
          </Typography>
        ) : null,
    },
    {
      accessorKey: 'duration',
      header: 'オンライン面談時間',
      enableSorting: false,
      Cell: ({ cell }) => (
        <Typography fontSize={{ tablet: 14, lg: 16 }}>
          {cell.getValue<string>() && `${cell.getValue<string>()}分間`}
        </Typography>
      ),
    },
    {
      accessorKey: 'status',
      header: t('caseList.status'),
      enableSorting: false,
      Cell: ({ cell }) => <CaseTag status={cell.getValue<CaseStatusType>()} />,
    },
  ];

  const renderBookingList = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" mt={2}>
          <CircularProgress />
        </Box>
      );
    }
    if (isBreakpoint) {
      return <CaseListMobile data={list} total={total} />;
    }
    return (
      <CustomTable<ICaseListItem>
        total={total}
        isLoading={isLoading}
        isFetching={isFetching}
        data={list}
        onRowClick={(row) => push(`/customer/my-page/cases/${row.id}`)}
        columns={columns}
      />
    );
  };

  return <Box pb={2}>{renderBookingList()}</Box>;
};

CaseList.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout>{page}</SideMenuLayout>
    </Layout>
  );
};

export default CaseList;

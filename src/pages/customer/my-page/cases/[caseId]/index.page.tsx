import {
  Box,
  Button,
  CircularProgress,
  Grid,
  IconButton,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import CaseTag from 'components/Case/CaseTag';
import CustomerMeetingItem from 'components/Case/CustomerMeetingItem';
import SkeletonList from 'components/Case/CustomerMeetingItem/Skeleton';
import ReviewSection from 'components/Case/ReviewSection';
import type { ReviewFormValues } from 'components/Case/ReviewSection/ReviewModal/schema';
import ListPagination from 'components/CustomPagination/ListPagination';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import ButtonBack from 'components/UI/BackButton';
import Rating from 'components/UI/Rating';
import { useFetchDetail, useFetchList, useFetchUser } from 'hooks';
import useMutate from 'hooks/useMutate';
import i18n from 'i18n';
import {
  AddIcon,
  AppointmentIcon,
  ArrowRightIcon,
  BackIcon,
  ChatIcon,
  InfoIcon,
} from 'icons';
import { get, isEmpty } from 'lodash';
import type {
  ICaseDetail,
  IMeetingsItem,
  IReviewLawyer,
} from 'models/case/interface';
import caseQuery from 'models/case/query';
import chatQuery from 'models/chat';
import type { GetServerSideProps } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import api from 'utils/api';
import { CaseStatusType, ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from '../styles';

const closedStatus = [
  CaseStatusType.ASSIGNED,
  CaseStatusType.CANCELED_BY_CONSUMER,
  CaseStatusType.CANCELED_BY_PROVIDER,
  CaseStatusType.CLOSED,
];

const CaseDetailPage = () => {
  const { query, push } = useRouter();
  const { mutateAsync: createRoom, isLoading: isCreatingRoom } = useMutate<
    unknown,
    { roomId: string }
  >(chatQuery.createRoom);
  const { data: currentUser } = useFetchUser({ enabled: false });
  const caseId = query.caseId as string;
  const { t } = i18n;
  const {
    list: meetings = [],
    total = 0,
    isLoading,
    refetch: refetchMeetingList,
  } = useFetchList<IMeetingsItem>(caseQuery.customerMeetingList(caseId));
  const fetchDetail = useFetchDetail({
    ...caseQuery.customerCaseDetail(caseId),
    enabled: false,
  });
  const { mutateAsync: reviewProvider, isLoading: isReviewing } =
    useMutate<IReviewLawyer>(caseQuery.reviewProvider(caseId));
  const { mutateAsync: cancelReviewProvider } = useMutate(
    caseQuery.cancelReviewProvider(caseId),
  );

  const caseDetail = fetchDetail.detail as ICaseDetail;
  const { status, provider } = caseDetail;
  const isShowReviewPopup = caseDetail.reviewProvider?.isShowAgain;
  const isLawyer = provider.type === ProviderType.LAWYER;
  const handleCancelReview = () => {
    if (isShowReviewPopup !== false) {
      cancelReviewProvider(
        {
          caseId,
        },
        {
          onSuccess: () => fetchDetail.refetch(),
        },
      );
    }
  };

  const handleReviewCustomer = async (values: ReviewFormValues) => {
    await reviewProvider({
      reviewProvider: {
        ...values,
        isShowAgain: false,
      },
    });
    Helper.toast(t('reviewMeeting.reviewSuccess'));
    fetchDetail.refetch();
  };

  const handleCreateRoom = () => {
    if (caseDetail.roomInfo?.roomId) {
      setTimeout(() =>
        window.open(
          `/customer/messages/${caseDetail.roomInfo?.roomId}`,
          '_blank',
        ),
      );
    } else {
      createRoom(
        {
          type: 'ONE_BY_ONE_HAS_CASE',
          memberIds: [
            `CONSUMER_${currentUser?._id}`,
            `${caseDetail.provider.type}_${caseDetail.provider._id}`,
          ],
        },
        {
          onSuccess: ({ roomId }) =>
            setTimeout(() =>
              window.open(`/customer/messages/${roomId}`, '_blank'),
            ),
        },
      );
    }
  };

  return (
    <Box>
      <Box bgcolor="white" borderRadius={{ xs: 0, tablet: 2 }}>
        <Box
          sx={[
            styles.headerBooking as never,
            { pb: { xs: '13px', tablet: 2 } },
          ]}
        >
          <Breadcrumbs
            omitIndexList={[0]}
            transformLabel={{
              '/customer/my-page/cases/[caseId]': provider.nickname ||
                provider.fullName || (
                  <Skeleton sx={{ fontSize: 12 }} variant="text" width={100} />
                ),
            }}
          />
          <Box display="flex" mt={{ xs: '4px', tablet: '11px' }}>
            <Box m="5px 4px 0px -4px" display={{ xs: 'none', tablet: 'block' }}>
              <ButtonBack
                url="/customer/my-page/cases"
                icon={<BackIcon />}
                sx={styles.backButton}
              />
            </Box>
            <Stack
              direction="row"
              spacing={1}
              alignItems="center"
              mt={{ xs: '5px', tablet: 0 }}
            >
              <Typography
                fontSize={{ xs: 24, tablet: 32 }}
                fontWeight="bold"
                color="heading"
              >
                {provider.nickname || provider.fullName}
              </Typography>
              <CaseTag status={status} />
            </Stack>
          </Box>
        </Box>
      </Box>

      <Grid container columnGap={2} mt={{ xs: 1, tablet: 2 }} columns={29}>
        <Grid item xs={29} md={19}>
          <Box sx={styles.containerCase}>
            <Box sx={styles.caseInfo}>
              <Stack direction="row" spacing={1}>
                <Box sx={{ svg: { display: 'block' } }}>
                  <AppointmentIcon />
                </Box>
                <Typography sx={styles.title}>
                  {t('caseDetail.title')}
                </Typography>
              </Stack>
              <Box>
                <Button
                  variant="tertiary"
                  sx={{
                    maxWidth: { xs: 76, tablet: 96 },
                    svg: { color: 'primary.main' },
                  }}
                  fullWidth
                  startIcon={<AddIcon />}
                  color="primary"
                  className="tabletStyle"
                  disabled={closedStatus.includes(status)}
                  onClick={() => {
                    push({
                      pathname: isLawyer
                        ? '/customer/booking/[lawyerId]'
                        : '/customer/counselor-booking/[counselorId]',
                      query: isLawyer
                        ? { lawyerId: provider._id }
                        : { counselorId: provider._id },
                    });
                  }}
                >
                  {t('global.add')}
                </Button>
              </Box>
            </Box>
            <Typography fontSize={{ xs: 10, tablet: 12 }}>
              {t('caseDetail.caseId', {
                caseId,
              })}
            </Typography>
            {caseDetail.hasPaymentError && (
              <Box sx={styles.alert}>
                <Box display="flex" justifyContent="center" alignItems="center">
                  <Box sx={styles.icon}>
                    <InfoIcon />
                  </Box>
                  <Typography
                    fontWeight={500}
                    component="span"
                    color="neutral6"
                    fontSize={14}
                  >
                    <Typography
                      color="primary.main"
                      fontSize={14}
                      fontWeight={500}
                      component="span"
                    >
                      「お支払い方法変更」
                    </Typography>
                    {'からクレジットカードを変更してください'}
                  </Typography>
                </Box>
              </Box>
            )}
            {isLoading && isEmpty(meetings) && <SkeletonList />}
            <Box mt={{ xs: 2, tablet: 3 }}>
              {meetings.map((meeting) => (
                <CustomerMeetingItem
                  meeting={meeting}
                  key={meeting._id}
                  refetchMeetingList={refetchMeetingList}
                  isLawyer={isLawyer}
                  caseStatus={status}
                  refetchCase={fetchDetail.refetch}
                  caseId={caseId}
                />
              ))}
            </Box>
          </Box>
          <Box mt={{ xs: 2, tablet: 4 }} display="flex" justifyContent="center">
            <ListPagination total={total} />
          </Box>
        </Grid>
        <Grid item xs md mt={{ xs: 2, md: 0 }}>
          <ReviewSection
            caseStatus={status}
            title={
              !isLawyer
                ? t('reviewMeeting.reviewCounselorTitle', {
                    counselorName: provider.nickname || provider.fullName,
                  })
                : t('reviewMeeting.reviewLawyerTitle', {
                    lawyerName: provider.nickname || provider.fullName,
                  })
            }
            placeholder={
              isLawyer
                ? t('reviewMeeting.reviewLawyerPlaceholder')
                : t('reviewMeeting.reviewCounselorPlaceholder')
            }
            avatar={
              caseDetail.provider.images[0]?.originUrl ||
              '/images/default-avatar.png'
            }
            isLoading={isReviewing}
            onSubmit={handleReviewCustomer}
            onCancel={handleCancelReview}
            reviewData={caseDetail.reviewProvider}
          />
          <Box sx={styles.lawyerInfo}>
            <Stack direction="row" spacing={1}>
              <Stack direction="row" spacing={2} width="100%">
                <Box
                  width={80}
                  height={80}
                  borderRadius="50%"
                  overflow="hidden"
                >
                  <Image
                    src={get(
                      provider,
                      'images[0].originUrl',
                      '/images/default-avatar.png',
                    )}
                    style={{ width: '100%', height: 'auto' }}
                    className="pointer"
                    width={1}
                    height={1}
                    sizes="100vw"
                    alt=""
                  />
                </Box>
                <Box flex={1}>
                  <Typography
                    fontSize={20}
                    fontWeight="bold"
                    color="heading"
                    className="pointer"
                  >
                    {provider.nickname || provider.fullName}
                  </Typography>
                  {!provider.nickname && (
                    <Typography fontSize={14}>
                      {provider.katakanaName}
                    </Typography>
                  )}

                  {!isLawyer && (
                    <Rating
                      rate={{
                        avgRating: provider.avgRating,
                        totalReview: provider.totalReview,
                      }}
                      sx={{ marginTop: '4px' }}
                      size="lg"
                    />
                  )}

                  <Link
                    href={{
                      pathname: isLawyer
                        ? `/lawyers/[lawyerId]`
                        : '/counselors/[counselorId]',
                      query: isLawyer
                        ? {
                            lawyerId: provider._id,
                          }
                        : { counselorId: provider._id },
                    }}
                    legacyBehavior
                  >
                    <Button endIcon={<ArrowRightIcon />} sx={styles.button}>
                      {t('global.seeMore')}
                    </Button>
                  </Link>
                </Box>
              </Stack>
              <div>
                <IconButton
                  sx={{
                    width: '40px',
                    height: '40px',
                    bgcolor: '#FDF3D8',
                    '&:hover': {
                      bgcolor: '#FDF3D8',
                    },
                  }}
                  disabled={isCreatingRoom}
                  onClick={handleCreateRoom}
                >
                  {isCreatingRoom ? (
                    <CircularProgress size={24} />
                  ) : (
                    <ChatIcon />
                  )}
                </IconButton>
              </div>
            </Stack>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

CaseDetailPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout>{page}</SideMenuLayout>
    </Layout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const qClient = new QueryClient();
  const caseId = query.caseId as string;
  await qClient.prefetchQuery(
    caseQuery.customerCaseDetail(caseId).queryKey,
    async () => {
      const { data } = await api.get(
        caseQuery.customerCaseDetail(caseId).apiUrl,
        Helper.getTokenConfig(req, res),
      );
      return data;
    },
  );

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

export default CaseDetailPage;

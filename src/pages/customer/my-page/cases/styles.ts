import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  headerBooking: (theme: Theme) => ({
    p: '16px 16px 0px 32px',
    [theme.breakpoints.down('tablet')]: {
      borderRadius: 0,
      mt: 1,
      p: '8px 16px 0px 16px',
    },
  }),
  lawyerInfo: (theme: Theme) => ({
    p: '32px 32px 24px',
    bgcolor: 'white',
    flex: 1,
    borderRadius: 2,
    [theme.breakpoints.down('tablet')]: {
      p: 2,
      borderRadius: 0,
      mb: 1,
    },
  }),
  button: {
    ml: -2,
    fontSize: 14,
  },
  backButton: {
    p: '4px',
    svg: {
      width: { xs: 28, tablet: 32 },
      height: { xs: 28, tablet: 32 },
    },
  },
  containerCase: {
    p: { xs: 2, tablet: 4 },
    bgcolor: 'white',
    borderRadius: { xs: 0, tablet: 2 },
  },
  caseInfo: {
    display: 'flex',
    justifyContent: 'space-between',
    mb: { xs: '4px', tablet: 1 },
  },
  title: {
    fontSize: { xs: 20, tablet: 24 },
    fontWeight: 'bold',
    lineHeight: { xs: '28px', tablet: '35px' },
    color: 'heading',
  },
  textHeader: {
    fontSize: { xs: 24, tablet: 32 },
    fontWeight: 'bold',
    mt: { xs: '5px', tablet: '12px' },
    mb: 1,
    color: 'heading',
  },
  tab: (theme) => ({
    fontSize: { xs: 12, tablet: 14 },
    [theme.breakpoints.down('tablet')]: {
      flex: 1,
      maxWidth: 'unset',
    },
  }),
  alert: {
    mt: { xs: 2, tablet: 3 },
    mb: { xs: -1, tablet: 3 },
    bgcolor: 'divine',
    borderRadius: '6px',
    pt: '9px',
    pb: '7px',
    px: { xs: 2, tablet: 0 },
  },
  icon: {
    mr: 1,
    display: 'flex',
    alignItems: 'center',
    svg: {
      color: 'icon',
      width: 20,
      height: 20,
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

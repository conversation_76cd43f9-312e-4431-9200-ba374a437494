import { TabContext, TabPanel } from '@mui/lab';
import { Box, Tab, Tabs, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import i18n from 'i18n';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';

import CounselorCases from './counselor-cases';
import LawyerCases from './lawyer-cases';
import styles from './styles';

const CaseList = () => {
  const { query, replace, pathname } = useRouter();
  const { tab = 'lawyer' } = query;
  const { t } = i18n;

  return (
    <Box>
      <TabContext value={tab as string}>
        <Box bgcolor="white" borderRadius={{ xs: 0, tablet: 2 }}>
          <Box sx={styles.headerBooking}>
            <Breadcrumbs omitIndexList={[0]} />
            <Typography sx={styles.textHeader}>{t('mypage.cases')}</Typography>
          </Box>
          <Box p={{ xs: 0, tablet: '0px 16px 0px 32px' }}>
            <Tabs
              sx={{ ml: { xs: 0, tablet: -2 } }}
              variant="standard"
              value={tab}
              onChange={(_, newValue) =>
                replace({ pathname, query: { tab: newValue } }, undefined, {
                  shallow: true,
                })
              }
            >
              <Tab label="弁護士" value="lawyer" sx={styles.tab} />
              <Tab label="カウンセラー" value="counselor" sx={styles.tab} />
            </Tabs>
          </Box>
        </Box>
        <TabPanel value="lawyer" sx={{ p: 0 }}>
          <LawyerCases />
        </TabPanel>
        <TabPanel value="counselor" sx={{ p: 0 }}>
          <CounselorCases />
        </TabPanel>
      </TabContext>
    </Box>
  );
};

CaseList.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout>{page}</SideMenuLayout>
    </Layout>
  );
};

export default CaseList;

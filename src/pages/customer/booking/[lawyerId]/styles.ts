import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  topAlert: {
    mb: { xs: 1, tablet: 2, desktop: 4 },
    letterSpacing: '-0.5px',
  },
  container: {
    pt: 2,
    pb: { xs: 0, tablet: '64px' },
  },
  containerForm: {
    mt: { xs: 2, tablet: 4 },
    display: { sl: 'flex' },
  },
  boxDescription: {
    p: 2,
    borderRadius: { xs: 0, tablet: 2 },
    bgcolor: 'white',
  },
  bookingHeader: {
    textAlign: 'center',
    fontSize: { xs: 14, tablet: 16 },
  },
  icon: {
    svg: { width: 32, height: 32, display: 'block' },
  },
  sectionTitle: {
    fontSize: { xs: 14, tablet: 16 },
    mb: { xs: 1, tablet: 2 },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

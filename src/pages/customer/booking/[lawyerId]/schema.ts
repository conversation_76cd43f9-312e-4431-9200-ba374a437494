import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { t } from 'i18n';
import { get, isNaN } from 'lodash';
import { MeetingType } from 'utils/constants';
import type { InferType } from 'yup';
import { array, boolean, mixed, number, object, string } from 'yup';

dayjs.extend(isSameOrAfter);

const schema = object({
  type: mixed<MeetingType>().oneOf(Object.values(MeetingType)).required(),
  duration: number()
    .required()
    .transform((value) => (isNaN(value) ? undefined : value)),

  consumerNote: string().max(500, t('validation.maxLength', { number: 500 })),
  hasCard: boolean(),
  cardNumber: string().when(['type', 'hasCard'], {
    is: (type: MeetingType, hasCard: boolean) =>
      type === MeetingType.ONLINE && hasCard,
    then: string().required(),
  }),
  firstChoice: string()
    .nullable()
    .required()
    .test('validDay', t('validation.invalidField'), (value) => {
      return value ? dayjs(value).isSameOrAfter(dayjs(), 'day') : true;
    }),
  secondChoice: string()
    .nullable()
    .required()
    .test('validDay', t('validation.invalidField'), (value) => {
      return value ? dayjs(value).isSameOrAfter(dayjs(), 'day') : true;
    }),
  thirdChoice: string()
    .nullable()
    .required()
    .test('validDay', t('validation.invalidField'), (value) => {
      return value ? dayjs(value).isSameOrAfter(dayjs(), 'day') : true;
    }),
  firstChoiceTime: array()
    .of(string().required())
    .nullable()
    .required()
    .min(2, t('validation.requiredField'))
    .test(
      'smallerThanEndTime',
      '開始時刻は終了時刻より前に設定してください。',
      (value) => {
        if (value && value[0] && value[1]) {
          return value[0] !== value[1];
        }
        return true;
      },
    )
    .test('invalidTIme', t('validation.invalidField'), (value, ctx) => {
      if (value && value[0] && value[1]) {
        const date = get(ctx, 'from[0].value.firstChoice');
        const selectedDay = dayjs(date)
          .set('hour', new Date(value[0]).getHours())
          .set('minute', new Date(value[0]).getMinutes());
        return selectedDay.isAfter(dayjs(), 'minute');
      }
      return true;
    }),
  secondChoiceTime: array()
    .of(string().required())
    .nullable()
    .required()
    .min(2, t('validation.requiredField'))
    .test(
      'smallerThanEndTime',
      '開始時刻は終了時刻より前に設定してください。',
      (value) => {
        if (value && value[0] && value[1]) {
          return value[0] !== value[1];
        }
        return true;
      },
    )
    .test('invalidTIme', t('validation.invalidField'), (value, ctx) => {
      if (value && value[0] && value[1]) {
        const date = get(ctx, 'from[0].value.secondChoice');
        const selectedDay = dayjs(date)
          .set('hour', new Date(value[0]).getHours())
          .set('minute', new Date(value[0]).getMinutes());
        return selectedDay.isAfter(dayjs(), 'minute');
      }
      return true;
    }),
  thirdChoiceTime: array()
    .of(string().required())
    .nullable()
    .required()
    .min(2, t('validation.requiredField'))
    .test(
      'smallerThanEndTime',
      '開始時刻は終了時刻より前に設定してください。',
      (value) => {
        if (value && value[0] && value[1]) {
          return value[0] !== value[1];
        }
        return true;
      },
    )
    .test('invalidTIme', t('validation.invalidField'), (value, ctx) => {
      if (value && value[0] && value[1]) {
        const date = get(ctx, 'from[0].value.thirdChoice');
        const selectedDay = dayjs(date)
          .set('hour', new Date(value[0]).getHours())
          .set('minute', new Date(value[0]).getMinutes());
        return selectedDay.isAfter(dayjs(), 'minute');
      }
      return true;
    }),
});

export type CreateMeetingValues = InferType<typeof schema>;

export default schema;

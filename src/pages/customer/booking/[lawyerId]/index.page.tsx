import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  <PERSON><PERSON>,
  Box,
  Button,
  Container,
  Divider,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import ConfirmMeetingModal from 'components/Case/ConfirmMeetingModal';
import CouponModal from 'components/Case/CouponModal';
import CreateMeeting from 'components/Case/CreateMeeting';
import AddCardModal from 'components/Customer/AddCardModal';
import { Select } from 'components/Form';
import HelperText from 'components/Form/HelperText';
import Layout from 'components/Layout';
import IconLabel from 'components/UI/IconLabel';
import TermPolicy from 'components/UI/TermPolicy';
import dayjs from 'dayjs';
import { useFetchDetail, useFetchList, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { ILawyerProfile } from 'hooks/types';
import useGlobalState from 'hooks/useGlobalState';
import useHookForm from 'hooks/useHookForm';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { AddIcon, PaymentIcon } from 'icons';
import { get, isEmpty } from 'lodash';
import type {
  ICreateBooking,
  IExtraTdsPayload,
  IRequest3DsRes,
} from 'models/booking/interface';
import bookingQuery from 'models/booking/query';
import type { ICardsList } from 'models/card/interface';
import cardQuery from 'models/card/query';
import type { IConsultationItem } from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import couponQuery from 'models/coupon';
import type { ICoupon } from 'models/coupon/interface';
import type { GetServerSideProps, NextApiRequest, NextApiResponse } from 'next';
import { useRouter } from 'next/router';
import Script from 'next/script';
import type { ReactElement } from 'react';
import { useEffect, useMemo, useState } from 'react';
import { useWatch } from 'react-hook-form';
import api from 'utils/api';
import CaseUtil from 'utils/caseUtil';
import { CardIcon, MeetingType, ProviderType } from 'utils/constants';
import errors from 'utils/errors';
import Helper from 'utils/helpers';
import localStorageHelper from 'utils/localStorageHelper';

import type { CreateMeetingValues } from './schema';
import schema from './schema';
import styles from './styles';

const BookingPage = () => {
  const {
    query: { lawyerId, tryInitDataLS },
    push,
  } = useRouter();
  const { setConfirmModal } = useGlobalState();
  const [agree, setAgree] = useState(false);
  const [openAddCard, setOpenAddCard] = useState<
    boolean | ICardsList | string
  >();
  const [openConfirmMeeting, setOpenConfirmMeeting] = useState(false);
  const [openCouponModal, setOpenCouponModal] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState<ICoupon | null>(null);

  const { data: currentUser } = useFetchUser({ enabled: true });

  const { list: menuList } = useFetchList<IConsultationItem>({
    ...consultationQuery.list(lawyerId as string, currentUser?._id),
  });
  const { detail: consultationDetail } = useFetchDetail<ConsultationRecord>({
    ...apiQuery.consultationDetail,
    enabled: false,
  });
  const { detail: lawyerDetail } = useFetchDetail<ILawyerProfile>({
    ...apiQuery.lawyerDetail(lawyerId as string),
  });
  const { list, refetch: refetchCardList } = useFetchList<ICardsList>(
    cardQuery.cardsList,
  );

  const {
    mutateAsync: checkCoupon,
    isLoading: isCheckingCoupon,
    // variables: couponPayload,
  } = useMutate<
    {
      code: string;
      totalPrice: number;
    },
    ICoupon
  >(couponQuery.checkCoupon);

  // const { list: couponList, isLoading: isCouponLoading } =
  //   useFetchList<ICoupon>({
  //     ...couponQuery.list,
  //   });

  const { mutateAsync: createBooking3ds, isLoading } = useMutate<
    ICreateBooking,
    IRequest3DsRes
  >({
    ...bookingQuery.create3Ds,
    onError: (e) => {
      const errorMessage = get(e, 'error');
      if (errorMessage === errors.INVALID_BOOKING_UNITPRICE)
        setConfirmModal({
          hideCancelButton: true,
          content: errorMessage,
          icon: 'warning',
        });
    },
  });

  const { mutateAsync: createNormalBooking, isLoading: isNormalLoading } =
    useMutate<ICreateBooking>({
      ...bookingQuery.create,
      onError: (e) => {
        const errorMessage = get(e, 'error');
        if (errorMessage === errors.INVALID_BOOKING_UNITPRICE)
          setConfirmModal({
            hideCancelButton: true,
            content: errorMessage,
            icon: 'warning',
          });
      },
    });

  const unitPrices = useMemo(
    () => menuList.find((menu) => menu.unitPrices)?.unitPrices || [],
    [menuList],
  );
  const unitPrice = unitPrices[0]?.price || 0;
  const { reset, control, handleSubmit, getValues, setValue, trigger, watch } =
    useHookForm<CreateMeetingValues>({
      mode: 'all',
      resolver: yupResolver(schema),
      defaultValues: {
        type: MeetingType.IN_PERSON,
      },
    });
  const watchDuration = useWatch({ name: 'duration', control });
  const watchMeetingType = useWatch({ name: 'type', control });

  const { partner, propertyInformation, kids } = consultationDetail;
  const cardList = useMemo(() => {
    return list.map((card) => ({
      _id: card.id,
      value: Helper.formatCardNumberText(card.details.lastNumber),
      icon: CardIcon[card.details.brand],
      no3Ds: !card.details.cardholderName,
    }));
  }, [list]);

  const primaryCardId = useMemo(
    () => list.find((card) => card?.details?.default)?.id,
    [list],
  );
  const basicPrice = Helper.getBasicPrice(unitPrice, watchDuration);
  const { usedCouponPrice } = CaseUtil.getCouponPrice({
    basicPrice,
    couponAmount: selectedCoupon?.couponAmount || 0,
  });

  useEffect(() => {
    if (!watchDuration) {
      setSelectedCoupon(null);
    }
  }, [watchDuration]);

  useEffect(() => {
    if (primaryCardId) {
      setValue('hasCard', true);
    }
    setValue('cardNumber', primaryCardId);
  }, [primaryCardId, reset, setValue]);

  useEffect(() => {
    let resetValue = {} as CreateMeetingValues;
    if (primaryCardId) {
      resetValue.hasCard = true;
      resetValue.cardNumber = primaryCardId;
    }

    if (tryInitDataLS) {
      resetValue = {
        ...resetValue,
        ...(localStorageHelper.getItem<CreateMeetingValues>(
          'bookingLawyerFormVal',
        ) || {}),
      };
    }
    reset(resetValue);
  }, [primaryCardId, reset, tryInitDataLS]);

  const handleCreateMeeting = (values: CreateMeetingValues) => {
    setOpenConfirmMeeting(false);
    const {
      firstChoice,
      firstChoiceTime,
      secondChoice,
      secondChoiceTime,
      thirdChoice,
      thirdChoiceTime,
      consumerNote,
      duration,
      type,
      cardNumber,
    } = values;
    const expectDate = [
      {
        start: dayjs(firstChoice)
          .set('hour', dayjs(firstChoiceTime[0]).hour())
          .set('minute', dayjs(firstChoiceTime[0]).minute())
          .toISOString(),
        end: dayjs(firstChoice)
          .set('hour', dayjs(firstChoiceTime[1]).hour())
          .set('minute', dayjs(firstChoiceTime[1]).minute())
          .toISOString(),
      },
      {
        start: dayjs(secondChoice)
          .set('hour', dayjs(secondChoiceTime[0]).hour())
          .set('minute', dayjs(secondChoiceTime[0]).minute())
          .toISOString(),
        end: dayjs(secondChoice)
          .set('hour', dayjs(secondChoiceTime[1]).hour())
          .set('minute', dayjs(secondChoiceTime[1]).minute())
          .toISOString(),
      },
      {
        start: dayjs(thirdChoice)
          .set('hour', dayjs(thirdChoiceTime[0]).hour())
          .set('minute', dayjs(thirdChoiceTime[0]).minute())
          .toISOString(),
        end: dayjs(thirdChoice)
          .set('hour', dayjs(thirdChoiceTime[1]).hour())
          .set('minute', dayjs(thirdChoiceTime[1]).minute())
          .toISOString(),
      },
    ];

    const submitValue = {
      expectDate,
      consumerNote,
      provider: lawyerId as string,
      type,
      paymentInfo: {
        duration,
        unitPrice: unitPrices[0]?.price || 0,
        card: type === MeetingType.ONLINE ? cardNumber : undefined,
        couponCode: selectedCoupon?.code,
      },
      extraPayload: {
        lawyerId,
        needFillForm:
          !partner || Helper.checkIsEmptyObject(propertyInformation) || !kids,
      } as IExtraTdsPayload | undefined,
    };

    if (watchMeetingType !== MeetingType.ONLINE) {
      submitValue.extraPayload = undefined;
      createNormalBooking(submitValue, {
        onSuccess: () => {
          if (
            partner &&
            !Helper.checkIsEmptyObject(propertyInformation) &&
            kids
          ) {
            setConfirmModal({
              icon: 'success',
              content: (
                <>
                  {t('booking.thank')}

                  <img
                    src="https://step.lme.jp/p/135442/xnnmsZPq"
                    id="get_image"
                    alt=""
                    style={{
                      width: '0px',
                      height: '0px',
                      visibility: 'hidden',
                    }}
                  />
                  <br />
                  <Script
                    src="https://step.lme.jp/js/conversion/get_url_target_page.js"
                    strategy="lazyOnload"
                  />
                </>
              ),
              confirmText: t('booking.seeList'),
              cancelText: t('global.backToHome'),
              onCancel: () => push('/'),
              onConfirm: () => push('/customer/my-page/cases'),
              buttonLayout: 'vertical',
              dialogContentTextProps: {
                sx: {
                  fontSize: 16,
                  whiteSpace: { xs: 'pre-line', tablet: 'normal' },
                },
              },
            });
          } else {
            setConfirmModal({
              icon: 'success',
              content: (
                <>
                  {t('booking.longThank')}
                  <img
                    src="https://step.lme.jp/p/135442/xnnmsZPq"
                    id="get_image"
                    alt=""
                    style={{
                      width: '0px',
                      height: '0px',
                      visibility: 'hidden',
                    }}
                  />
                  <br />
                  <Script
                    src="https://step.lme.jp/js/conversion/get_url_target_page.js"
                    strategy="lazyOnload"
                  />
                </>
              ),
              confirmText: t('booking.enterForm'),
              hideCancelButton: true,
              onConfirm: () => push('/customer/my-page/consultation-record'),
              dialogContentTextProps: { sx: { fontSize: 16 } },
            });
          }
        },
      });
    } else if (watchMeetingType === MeetingType.ONLINE) {
      createBooking3ds(submitValue, {
        onSuccess: (res) => {
          const { redirectUrl } = res;
          if (redirectUrl) {
            localStorageHelper.setItem('bookingLawyerFormVal', values);
            reset();
            setTimeout(() => {
              window.location.href = redirectUrl;
            }, 0);
          }
        },
      });
    }
  };

  const handleConfirmMeeting = (values: CreateMeetingValues) => {
    if (isEmpty(list) && values.type === MeetingType.ONLINE) {
      setOpenAddCard('open_when_submit');
    } else {
      const firstTimeStart = values.firstChoiceTime[0];
      const firstTimeEnd = values.firstChoiceTime[1];
      const secondTimeStart = values.secondChoiceTime[0];
      const secondTimeEnd = values.secondChoiceTime[1];
      const thirdTimeStart = values.thirdChoiceTime[0];
      const thirdTimeEnd = values.thirdChoiceTime[1];
      if (
        firstTimeStart &&
        firstTimeEnd &&
        secondTimeStart &&
        secondTimeEnd &&
        thirdTimeStart &&
        thirdTimeEnd
      ) {
        const firstDayStart = dayjs(values.firstChoice)
          .set('hour', new Date(firstTimeStart).getHours())
          .set('minutes', new Date(firstTimeStart).getMinutes());
        const firstDayEnd = dayjs(values.firstChoice)
          .set('hour', new Date(firstTimeEnd).getHours())
          .set('minutes', new Date(firstTimeEnd).getMinutes());
        const secondDayStart = dayjs(values.secondChoice)
          .set('hours', new Date(secondTimeStart).getHours())
          .set('minutes', new Date(secondTimeStart).getMinutes());
        const secondDayEnd = dayjs(values.secondChoice)
          .set('hours', new Date(secondTimeEnd).getHours())
          .set('minutes', new Date(secondTimeEnd).getMinutes());
        const thirdDayStart = dayjs(values.thirdChoice)
          .set('hours', new Date(thirdTimeStart).getHours())
          .set('minutes', new Date(thirdTimeStart).getMinutes());
        const thirdDayEnd = dayjs(values.thirdChoice)
          .set('hours', new Date(thirdTimeEnd).getHours())
          .set('minutes', new Date(thirdTimeEnd).getMinutes());
        if (
          Helper.checkOverlapTimeRange(
            firstDayStart,
            firstDayEnd,
            secondDayStart,
            secondDayEnd,
          ) ||
          Helper.checkOverlapTimeRange(
            firstDayStart,
            firstDayEnd,
            thirdDayStart,
            thirdDayEnd,
          ) ||
          Helper.checkOverlapTimeRange(
            secondDayStart,
            secondDayEnd,
            thirdDayStart,
            thirdDayEnd,
          )
        ) {
          setConfirmModal({
            icon: 'warning',
            content:
              '開始日時と終了日時は、同じまたは重複しないように設定してください。',
            confirmText: 'OK',
            hideCancelButton: true,
          });
        } else {
          setOpenConfirmMeeting(true);
        }
      }
    }
  };

  const handleCheckCoupon = (code: string) => {
    checkCoupon(
      { code, totalPrice: basicPrice },
      {
        onSuccess: (data) => {
          setOpenCouponModal(false);
          setSelectedCoupon(data);
        },
      },
    );
  };

  const handleSelectCoupon = (coupon: ICoupon) => {
    checkCoupon(
      { code: coupon.code, totalPrice: basicPrice },
      {
        onSuccess: (data) => {
          setOpenCouponModal(false);
          setSelectedCoupon(data);
        },
      },
    );
  };

  const selectedCard = watch('cardNumber');
  const selectedCardInfo = list.find((card) => card.id === selectedCard);
  const hasMissingHolderNameCard =
    watchMeetingType === MeetingType.ONLINE &&
    selectedCardInfo &&
    !selectedCardInfo?.details.cardholderName;

  return (
    <Container maxWidth="lg" disableGutters sx={styles.container}>
      <Box px={{ xs: 2, tablet: 0 }}>
        <Breadcrumbs
          omitIndexList={[0]}
          transformLabel={{
            '/customer/booking/[lawyerId]': lawyerDetail.fullName || (
              <Skeleton
                sx={{ fontSize: { xs: 10, tablet: 12 } }}
                variant="text"
                width={100}
              />
            ),
          }}
        />
      </Box>
      <form
        id="create-meeting-form"
        onSubmit={handleSubmit(handleConfirmMeeting)}
      >
        <Container maxWidth="ex" disableGutters sx={styles.containerForm}>
          <Container maxWidth="sl" disableGutters>
            {hasMissingHolderNameCard && (
              <Alert sx={styles.topAlert} severity="warning">
                {t('otherMessage.OM02')}
              </Alert>
            )}
            <Box sx={styles.boxDescription}>
              <Typography
                sx={styles.bookingHeader}
                fontWeight={500}
                color="heading"
              >
                ご希望の面談方法と面談日時をお知らせください
              </Typography>
            </Box>

            <CreateMeeting
              menuList={menuList}
              control={control}
              loading={isLoading || isNormalLoading}
              trigger={trigger}
              hasOnlineSupport={lawyerDetail?.hasOnlineSupport}
              getDisableSubmitBtnState={({ agree: _agree }) =>
                !_agree || hasMissingHolderNameCard
              }
            />
          </Container>
          <Box
            mt={{ xs: 1, sl: 0 }}
            maxWidth={{ xs: 1032, sl: 384 }}
            width={1}
            ml={{ sl: 2 }}
          >
            <Box borderRadius={{ sl: 2 }} bgcolor="white" p={{ xs: 2, sl: 4 }}>
              {/* Only show Payment method when meeting type is ONLINE */}
              {watchMeetingType === MeetingType.ONLINE && (
                <>
                  <IconLabel
                    icon={PaymentIcon}
                    containerProps={{ mb: { xs: 2, tablet: 4 } }}
                  >
                    {t('booking.paymentMethod')}
                  </IconLabel>

                  <Select
                    name="cardNumber"
                    labelCol={12}
                    control={control}
                    data={cardList}
                    placeholder={t('booking.placeholderSelectCard')}
                  />
                  {hasMissingHolderNameCard && (
                    <HelperText
                      error={t('inlineMessage.GIM18')}
                      onClick={() => {
                        setOpenAddCard(selectedCardInfo);
                      }}
                    />
                  )}
                  <Button
                    size="small"
                    onClick={() => setOpenAddCard('open_normal')}
                    sx={{
                      mt: '12px',
                      ml: '-4px',
                      svg: {
                        width: 20,
                        height: 20,
                      },
                      '& .MuiButton-startIcon': {
                        mr: '4px',
                      },
                    }}
                    className="tabletStyle"
                    startIcon={<AddIcon />}
                    color="primary"
                  >
                    新しいカードを追加
                  </Button>
                  <Typography
                    fontSize={{ xs: 12, tablet: 14 }}
                    color="hint"
                    mt="12px"
                  >
                    {t('booking.description')}
                  </Typography>
                  <Divider
                    sx={{
                      mt: { xs: '12px', tablet: '16px' },
                      mb: { xs: 1, tablet: '20px' },
                    }}
                  />
                </>
              )}
              {/* Hide for v1.6.0 */}
              {/* <Divider
                sx={{
                  mt: { xs: '12px', tablet: '16px' },
                  mb: { xs: 1, tablet: '16px' },
                }}
              />
              {!selectedCoupon ? (
                <>
                  <Button
                    size="small"
                    onClick={() => setOpenCouponModal(true)}
                    sx={{
                      ml: '-4px',
                      svg: {
                        width: 20,
                        height: 20,
                      },
                      '& .MuiButton-startIcon': {
                        mr: '4px',
                      },
                      '&.Mui-disabled': {
                        color: '#A8B0B4',
                        backgroundColor: 'unset',
                        svg: {
                          color: '#A8B0B4',
                        },
                      },
                    }}
                    className="tabletStyle"
                    startIcon={<AddIcon />}
                    color="primary"
                    disabled={!watchDuration}
                  >
                    クーポンを使う
                  </Button>
                  <Typography
                    mt="4px"
                    mb={{ xs: 2, tablet: '20px' }}
                    fontSize={{ xs: 12, tablet: 14 }}
                    color="#9E9B92"
                  >
                    クーポンご利用前に面談時間を選択してください
                  </Typography>
                </>
              ) : (
                <Box
                  display="flex"
                  gap={1}
                  justifyContent="space-between"
                  py="4px"
                  flexWrap="wrap"
                >
                  <Box display="flex" alignItems="center">
                    <Typography
                      fontSize={{ xs: 14, tablet: 16 }}
                      fontWeight={700}
                      mr={1}
                      color="heading"
                    >
                      クーポン値引
                    </Typography>
                    <CouponTag
                      code={selectedCoupon.code}
                      onDelete={() => setSelectedCoupon(null)}
                    />
                  </Box>
                  <Typography fontWeight={700} color="neutral7">
                    {selectedCoupon.couponAmount > 0 && '-'}
                    {Helper.addComma(selectedCoupon.couponAmount || 0)}円
                  </Typography>
                </Box>
              )} */}

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="flex-end"
              >
                <Stack flexDirection="row">
                  <Typography
                    fontSize={{ xs: 14, tablet: 16 }}
                    fontWeight="bold"
                    color="heading"
                    mr="6px"
                  >
                    {t('booking.basicPrice')}
                  </Typography>
                </Stack>
                <Stack direction="row" spacing="2px" alignItems="flex-end">
                  <Typography
                    fontSize={{ xs: 20, tablet: 24 }}
                    fontWeight="bold"
                    color="neutral7"
                  >
                    {Helper.addComma(usedCouponPrice)}
                  </Typography>
                  <Typography
                    fontSize={{ xs: 14, tablet: 16 }}
                    fontWeight="bold"
                    color="neutral7"
                    lineHeight={{ xs: 1.71, tablet: '30px' }}
                  >
                    円
                  </Typography>
                </Stack>
              </Stack>
              {watchMeetingType === MeetingType.ONLINE ? (
                <Stack>
                  <Typography
                    fontSize={{ xs: 12, tablet: 14 }}
                    color="error"
                    mt={1}
                  >
                    {t('booking.descriptionForOnlineMeeting1')}
                  </Typography>
                  <Typography
                    fontSize={{ xs: 12, tablet: 14 }}
                    color="hint"
                    mt={2}
                  >
                    {t('booking.descriptionForOnlineMeeting2')}
                  </Typography>
                </Stack>
              ) : (
                <Typography
                  fontSize={{ xs: 12, tablet: 14 }}
                  color="hint"
                  mt={1}
                >
                  {t('booking.description2')}
                </Typography>
              )}
            </Box>
          </Box>
          <Box display={{ xs: 'block', sl: 'none' }} p="8px 16px 24px">
            <Box
              display="flex"
              justifyContent={{ xs: 'flex-start', tablet: 'center' }}
            >
              <TermPolicy checked={agree} onChange={(data) => setAgree(data)} />
            </Box>
            <Box display="flex" justifyContent="center" mt={1}>
              <LoadingButton
                color="secondary"
                type="submit"
                variant="contained"
                size="large"
                className="tabletStyle shadow"
                fullWidth
                loading={isLoading}
                disabled={!agree || hasMissingHolderNameCard}
                sx={{
                  maxWidth: 368,
                }}
              >
                内容を確認する
              </LoadingButton>
            </Box>
          </Box>
        </Container>
      </form>
      {openAddCard && (
        <AddCardModal
          open={!!openAddCard}
          onClose={() => setOpenAddCard('')}
          onSuccess={() => {
            setOpenAddCard('');
            refetchCardList();
            if (openAddCard === 'open_when_submit') {
              setOpenConfirmMeeting(true);
            }
          }}
          editPayload={
            typeof openAddCard === 'object'
              ? { card: openAddCard, isEdit: true }
              : undefined
          }
        />
      )}
      <ConfirmMeetingModal
        onConfirm={handleSubmit(handleCreateMeeting)}
        onClose={() => setOpenConfirmMeeting(false)}
        open={openConfirmMeeting}
        control={control}
        providerType={ProviderType.LAWYER}
        cardList={cardList.filter((card) => !card.no3Ds)}
        data={{
          avatar: get(
            lawyerDetail,
            'images[0].originUrl',
            '/images/default-avatar.png',
          ),
          fullName: lawyerDetail?.fullName,
          katakanaName: lawyerDetail?.katakanaName,
          consultationField: lawyerDetail?.consultationField,
          unitPrice,
          coupon: selectedCoupon,
          ...getValues(),
        }}
      />
      <CouponModal
        onClose={() => setOpenCouponModal(false)}
        open={openCouponModal}
        // couponList={couponList}
        // isLoading={isCouponLoading}
        // total={couponList.length}
        checkCoupon={handleCheckCoupon}
        isCheckingCoupon={isCheckingCoupon}
        onSelect={handleSelectCoupon}
        // couponPayload={couponPayload}
      />
    </Container>
  );
};

BookingPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  try {
    const cookies = Helper.getWebCookie(
      req as NextApiRequest,
      res as NextApiResponse,
    );
    const { lawyerId } = query;
    const { token } = cookies;
    const config = {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    const queryClient = new QueryClient();
    const consultation = await queryClient.fetchQuery(
      apiQuery.consultationDetail.queryKey,
      async () => {
        const { data } = await api.get(
          apiQuery.consultationDetail.apiUrl,
          config,
        );
        return data;
      },
      { staleTime: Infinity },
    );
    const { backgroundOfDivorce, marriageInformation, consumer } = consultation;

    if (
      !(
        consumer?.isCompletedProfile &&
        !!consumer?.phone &&
        backgroundOfDivorce &&
        marriageInformation
      )
    ) {
      return {
        redirect: {
          destination: `/customer/consultation-form?lawyerId=${lawyerId}`,
          permanent: true,
        },
      };
    }
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
      },
    };
  } catch (error) {
    return {
      redirect: {
        destination: '/',
        permanent: true,
      },
    };
  }
};

export default BookingPage;

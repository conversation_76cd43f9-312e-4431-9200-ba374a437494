import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Container, Stack, Typography } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import DivorceBackgroundFields from 'components/ConsultationForm/DivorceBackground/fields';
import ExpectLawyerFields from 'components/ConsultationForm/ExpectLawyer/fields';
import MarriageDateFields from 'components/ConsultationForm/MarriageDate/fields';
import PreferenceFields from 'components/ConsultationForm/Preference/fields';
import ProfileFields from 'components/ConsultationForm/Profile/fields';
import Layout from 'components/Layout';
import { useFetchDetail, useFetchList, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import useConsultationForm from 'hooks/customer/useConsultationForm';
import type {
  ConsultationRecord,
  IBasicConsultationForm,
  IBasicConsultationPayload,
} from 'hooks/customer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import { DivorceIcon, ProfileIcon, RecommendIcon, RingIcon } from 'icons';
import { pick } from 'lodash';
import type { ICause } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import type { GetServerSideProps, NextApiRequest, NextApiResponse } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useEffect, useMemo } from 'react';
import api from 'utils/api';
import { ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

import schema from './schema';
import styles from './styles';

const ConsultationFormPage = () => {
  const { query, push } = useRouter();
  const { lawyerId, counselorId } = query;
  const { list: causeList } = useFetchList<ICause>(resourceQuery.divorceCauses);

  const { data: currentUser, refetch: refetchCurrentUser } =
    useFetchUser<CustomerData>({
      enabled: false,
    });
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetail,
    enabled: false,
  });
  const { refetch } = fetchConsultationDetail;
  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;

  const { updateBasicConsultation, isUpdatingBasicConsultation } =
    useConsultationForm();

  const hasExtraId = (causeList || []).find((cause) => cause.hasExtra)?._id;
  const backgroundReason = consultationDetail.backgroundOfDivorce?.reason;

  const { backgroundOfDivorce, marriageInformation, expectLawyer } =
    consultationDetail;

  const reason = useMemo(
    () => ({
      value: (backgroundReason || []).map(
        (item: { value?: string; extraData?: string }) => item.value,
      ),
      extraData: backgroundReason?.find((item) => item.extraData)?.extraData,
    }),
    [backgroundReason],
  );

  const defaultValues = useMemo(
    () => ({
      ...pick(currentUser, [
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'phone',
      ]),
      currentAddress1: currentUser?.currentAddress?.address1._id,
      ...pick(backgroundOfDivorce, [
        'youDivorce',
        'partnerDivorce',
        'additionalTopics',
      ]),
      ...pick(marriageInformation, [
        'marriedDate',
        'isSeparated',
        'separationDate',
      ]),
      lawyerGender: expectLawyer?.gender,
      lawyerAge: expectLawyer?.age?._id,
      lawyerConsultation: expectLawyer?.consultation?.map((field) => field._id),
      reason,
    }),
    [
      backgroundOfDivorce,
      currentUser,
      expectLawyer,
      marriageInformation,
      reason,
    ],
  );

  const { control, handleSubmit, clearErrors, setValue, reset, trigger } =
    useHookForm<IBasicConsultationForm>({
      resolver: yupResolver(schema(hasExtraId as string)),
      mode: 'onTouched',
      defaultValues,
    });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  useEffect(() => {
    if (currentUser?.isCompletedProfile && !currentUser?.phone) {
      setValue('phone', '', { shouldTouch: true, shouldValidate: true });
    }
  }, [currentUser?.isCompletedProfile, currentUser?.phone, setValue, trigger]);

  const handleUpdateBasicConsultation = (values: IBasicConsultationForm) => {
    const divorceReason = values.reason.value.map((item) =>
      item === hasExtraId
        ? { value: item, extraData: values.reason.extraData }
        : { value: item },
    );
    const payload: IBasicConsultationPayload = {
      profile: pick(values, [
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'phone',
        'currentAddress1',
      ]),
      backgroundOfDivorce: {
        ...pick(values, ['youDivorce', 'partnerDivorce', 'additionalTopics']),
        reason: divorceReason,
      },
      marriageInformation: pick(values, [
        'marriedDate',
        'isSeparated',
        'separationDate',
      ]),
      expectLawyer: {
        age: values.lawyerAge,
        gender: values.lawyerGender,
        consultation: values.lawyerConsultation,
      },
      expectCounselor: {
        age: values.preferenceAge,
        gender: values.preferenceGender,
        consultation: values.preferenceConsultation,
      },
    };
    Helper.setEventTracking({
      ID: 'reservation_record_input',
    });

    updateBasicConsultation(payload, {
      onSuccess: () => {
        refetch();
        refetchCurrentUser();
        if (lawyerId) {
          push({
            pathname: '/customer/booking/[lawyerId]',
            query,
          });
        } else if (counselorId) {
          // Filter out undefined values from query
          const cleanQuery = Object.entries(query).reduce(
            (acc, [key, value]) => {
              if (
                value !== undefined &&
                value !== 'undefined' &&
                value !== ''
              ) {
                acc[key] = value;
              }
              return acc;
            },
            {} as Record<string, string | string[]>,
          );

          push({
            pathname: '/customer/counselor-booking/[counselorId]',
            query: cleanQuery,
          });
        } else {
          push('/customer/recommend');
        }
      },
    });
  };

  return (
    <Container
      maxWidth="md"
      sx={{ pt: { xs: 1, tablet: 4 }, pb: { xs: 2, tablet: 4 } }}
      disableGutters
    >
      <form onSubmit={handleSubmit(handleUpdateBasicConsultation)}>
        <Stack spacing={{ xs: 1, tablet: 2 }}>
          <Box sx={styles.titleContainer}>
            <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
              <ProfileIcon />
              <Typography sx={styles.title}>
                {t('updateCustomerProfile.yourProfile')}
              </Typography>
            </Stack>
            <ProfileFields
              firstBooking
              control={control as never}
              currentUser={{ email: currentUser?.email || '' }}
            />
          </Box>
          <Box sx={styles.titleContainer}>
            <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
              <DivorceIcon />
              <Typography sx={styles.title}>
                {t('consultationRecord.divorceBackground')}
              </Typography>
            </Stack>
            <DivorceBackgroundFields
              control={control as never}
              setValue={setValue as never}
            />
          </Box>
          <Box sx={styles.titleContainer}>
            <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
              <RingIcon />
              <Typography sx={styles.title}>
                {t('consultationRecord.marriageInformation')}
              </Typography>
            </Stack>
            <MarriageDateFields
              clearErrors={clearErrors as never}
              control={control as never}
              setValue={setValue as never}
            />
          </Box>
          {!lawyerId && !counselorId && (
            <Box sx={styles.titleContainer}>
              <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
                <RecommendIcon />
                <Typography sx={styles.title}>
                  {t('consultationRecord.expectLawyer')}
                </Typography>
              </Stack>
              <ExpectLawyerFields control={control as never} />
            </Box>
          )}
          {!lawyerId && !counselorId && (
            <Box sx={styles.titleContainer}>
              <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
                <RecommendIcon />
                <Typography sx={styles.title}>希望するカウンセラー</Typography>
              </Stack>
              <PreferenceFields
                providerType={ProviderType.COUNSELOR}
                control={control as never}
              />
            </Box>
          )}
        </Stack>
        <Box sx={styles.buttonContainer}>
          <LoadingButton
            size="large"
            color="secondary"
            variant="contained"
            type="submit"
            fullWidth
            className="tabletStyle"
            loading={isUpdatingBasicConsultation}
            sx={{ maxWidth: '368px' }}
          >
            {t('global.next')}
          </LoadingButton>
        </Box>
      </form>
    </Container>
  );
};

ConsultationFormPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  try {
    const cookies = Helper.getWebCookie(
      req as NextApiRequest,
      res as NextApiResponse,
    );
    const { lawyerId, counselorId, duration, meetingType, startSlot } = query;
    const { token } = cookies;
    const config = {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    const queryClient = new QueryClient();
    const consultation = await queryClient.fetchQuery(
      apiQuery.consultationDetail.queryKey,
      async () => {
        const { data } = await api.get(
          apiQuery.consultationDetail.apiUrl,
          config,
        );
        return data;
      },
      { staleTime: Infinity },
    );
    const {
      backgroundOfDivorce,
      marriageInformation,
      consumer,
      expectLawyer,
      expectCounselor,
    } = consultation;
    const hasExpectLawyer = !Helper.checkIsEmptyObject(expectLawyer);
    const hasExpectCounselor = !Helper.checkIsEmptyObject(expectCounselor);

    if (
      consumer?.isCompletedProfile &&
      consumer?.phone &&
      backgroundOfDivorce &&
      marriageInformation &&
      (hasExpectLawyer || hasExpectCounselor) &&
      !lawyerId &&
      !counselorId
    ) {
      return {
        redirect: {
          destination: '/customer/my-page',
          permanent: true,
        },
      };
    }
    if (
      consumer?.isCompletedProfile &&
      consumer?.phone &&
      backgroundOfDivorce &&
      marriageInformation &&
      (lawyerId || counselorId)
    ) {
      return {
        redirect: {
          destination: lawyerId
            ? `/customer/booking/${lawyerId}`
            : (() => {
                const params = new URLSearchParams();
                if (duration && duration !== 'undefined')
                  params.append('duration', String(duration));
                if (meetingType && meetingType !== 'undefined')
                  params.append('meetingType', String(meetingType));
                if (startSlot && startSlot !== 'undefined')
                  params.append('startSlot', String(startSlot));
                const queryString = params.toString();
                return `/customer/counselor-booking/${counselorId}${
                  queryString ? `?${queryString}` : ''
                }`;
              })(),
          permanent: true,
        },
      };
    }
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
      },
    };
  } catch (error) {
    return {
      redirect: {
        destination: '/',
        permanent: true,
      },
    };
  }
};

export default ConsultationFormPage;

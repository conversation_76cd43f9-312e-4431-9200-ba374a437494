import { LoadingButton } from '@mui/lab';
import { Box, Divider } from '@mui/material';
import Layout from 'components/Layout';
import OperatorContact from 'components/UI/OperatorContact';
import useGlobalState from 'hooks/useGlobalState';
import { t } from 'i18n';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useEffect } from 'react';
import api from 'utils/api';

const VerifyEmail = () => {
  const { setConfirmModal, toggleConfirmModal } = useGlobalState();
  const { replace } = useRouter();

  useEffect(() => {
    setConfirmModal({
      icon: 'success',
      content: (
        <>
          {t('register.customerSuccess_first')}
          <LoadingButton
            fullWidth
            variant="contained"
            color="secondary"
            size="large"
            className="tabletStyle"
            sx={{ marginTop: 3 }}
            onClick={() => toggleConfirmModal(false)}
          >
            OK
          </LoadingButton>
          <Divider
            sx={{ borderBottomWidth: 2, borderColor: '#dbe3e7', marginTop: 3 }}
          />
          <Box marginTop={3}>
            <OperatorContact lineUrlType="verify-email" />
          </Box>
        </>
      ),
      hideActions: true,
      closeOnNavigate: false,
    });
    replace('/');
  }, [replace, setConfirmModal, toggleConfirmModal]);

  return null;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  try {
    const { token } = query;
    await api.post('/account/consumer/verifiedEmail', { token });
    return {
      props: { token },
    };
  } catch (error) {
    return {
      redirect: {
        destination: '/',
        permanent: true,
      },
    };
  }
};

VerifyEmail.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export default VerifyEmail;

import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { t } from 'i18n';
import { IMeetingType } from 'models/consultation/interface';
import { MeetingType } from 'utils/constants';
import type { InferType } from 'yup';
import { boolean, mixed, object, string } from 'yup';

dayjs.extend(isSameOrAfter);

const schema = object({
  type: mixed<MeetingType>().oneOf(Object.values(MeetingType)).required(),
  meetingType: mixed<IMeetingType>().oneOf(Object.values(IMeetingType)),
  menuId: string().required(),
  expectDate: string()
    .nullable()
    .required()
    .test('validDay', t('validation.invalidField'), (value) => {
      if (!dayjs(value).isSameOrAfter(dayjs(), 'minute')) {
        return false;
      }
      if (value && !['00', '30'].includes(dayjs(value).format('mm'))) {
        return false;
      }
      return true;
    }),

  consumerNote: string().max(500, t('validation.maxLength', { number: 500 })),
  hasCard: boolean(),
  cardNumber: string().when(['type', 'hasCard'], {
    is: (type: MeetingType, hasCard: boolean) =>
      type === MeetingType.ONLINE && hasCard,
    then: string().required(),
  }),
});

export type CreateCounselorMeetingValues = InferType<typeof schema>;

export default schema;

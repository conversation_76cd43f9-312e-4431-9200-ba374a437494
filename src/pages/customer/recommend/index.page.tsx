import { Box, Container, Typography } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Layout from 'components/Layout';
import RecommendLawyer from 'components/RecommendLawyer';
import { useFetchDetail, useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { IPrefecture } from 'hooks/types';
import i18n from 'i18n';
import type { IRecommendListItem } from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import resourceQuery from 'models/resource/query';
import type { GetServerSideProps, NextApiRequest, NextApiResponse } from 'next';
import type { ReactElement } from 'react';
import api from 'utils/api';
import { ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

const RecommendPage = () => {
  const { ids: consultationIds, isFetched } = useFetchList<IPrefecture>({
    ...resourceQuery.consultations,
    customParams: {
      ...resourceQuery.consultations.customParams,
      providerType: [ProviderType.LAWYER],
    },
  });
  const {
    ids: counselorConsultationIds,
    isFetched: isFetchedCounselorConsultation,
  } = useFetchList<IPrefecture>({
    ...resourceQuery.consultations,
    customParams: {
      ...resourceQuery.consultations.customParams,
      providerType: [ProviderType.COUNSELOR],
    },
  });
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetail,
    enabled: false,
  });
  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;

  const { list: recommendLawyers, isLoading } =
    useFetchList<IRecommendListItem>({
      ...apiQuery.recommendLawyerList,
      enabled: !fetchConsultationDetail.isLoading && isFetched,
      customParams: {
        gender: consultationDetail.expectLawyer?.gender,
        consultationField: consultationDetail.expectLawyer?.nothingSpecial
          ? consultationIds
          : consultationDetail.expectLawyer?.consultation?.map(
              (field) => field._id,
            ),
        minAge: consultationDetail.expectLawyer?.age?.min,
        maxAge: consultationDetail.expectLawyer?.age?.max,
      },
    });

  const { list: recommendCounselors } = useFetchList<IRecommendListItem>({
    ...providerQuery.recommendCounselorList,
    enabled:
      !fetchConsultationDetail.isLoading && isFetchedCounselorConsultation,
    customParams: {
      gender: consultationDetail.expectCounselor?.gender,
      consultationField: consultationDetail.expectCounselor?.nothingSpecial
        ? counselorConsultationIds
        : consultationDetail.expectCounselor?.consultation?.map(
            (field) => field._id,
          ),
      minAge: consultationDetail.expectCounselor?.age?.min,
      maxAge: consultationDetail.expectCounselor?.age?.max,
    },
  });

  const { t } = i18n;

  return (
    <Container
      maxWidth="lg"
      disableGutters
      sx={{
        p: { xs: '8px 0px 16px', tablet: '16px 0px' },
      }}
    >
      <Box
        bgcolor="white"
        p={2}
        borderRadius={{ xs: 0, tablet: 2 }}
        mb={{ xs: 1, tablet: 2 }}
      >
        <Typography textAlign="center" fontSize={{ xs: 14, tablet: 16 }}>
          {t('recommend.text')}
        </Typography>
      </Box>
      <Box mt={{ xs: 1, tablet: 2 }}>
        <RecommendLawyer
          displayRows={2}
          data={recommendLawyers}
          loading={isLoading}
          recommendCounselors={recommendCounselors}
          showButton
        />
      </Box>
    </Container>
  );
};

RecommendPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  try {
    const cookies = Helper.getWebCookie(
      req as NextApiRequest,
      res as NextApiResponse,
    );
    const { token } = cookies;
    const config = {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    const queryClient = new QueryClient();
    const consultation = await queryClient.fetchQuery(
      apiQuery.consultationDetail.queryKey,
      async () => {
        const { data } = await api.get(
          apiQuery.consultationDetail.apiUrl,
          config,
        );
        return data;
      },
      { staleTime: Infinity },
    );
    const { backgroundOfDivorce, marriageInformation, consumer } = consultation;

    if (
      !(
        consumer?.isCompletedProfile &&
        backgroundOfDivorce &&
        marriageInformation
      )
    ) {
      return {
        redirect: {
          destination: `/customer/consultation-form`,
          permanent: true,
        },
      };
    }
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
      },
    };
  } catch (error) {
    return {
      redirect: {
        destination: '/',
        permanent: true,
      },
    };
  }
};
export default RecommendPage;

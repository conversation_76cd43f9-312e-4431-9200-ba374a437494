import {
  Box,
  Container,
  Divider,
  Grid,
  Stack,
  Typography,
} from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import Layout from 'components/Layout';
import { t } from 'i18next';
import { NextSeo } from 'next-seo';
import type { ReactElement } from 'react';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const CommercialTransactionsLawPage = () => {
  const commercialTransactionsLaw = [
    {
      label: '販売業社の名称',
      value: (
        <Typography fontSize="inherit">株式会社リライフテクノロジー</Typography>
      ),
    },
    {
      label: '所在地',
      value: (
        <>
          {/* <Typography fontSize="inherit">100-0005</Typography>
          <Typography fontSize="inherit">
            {' '}
            東京都千代田区丸の内2-2-1{' '}
          </Typography>
          <Typography fontSize="inherit">岸本ビルヂング5階</Typography> */}
          <Typography fontSize="inherit">
            {t('companyInfo.location')}
          </Typography>
        </>
      ),
    },
    {
      label: '連絡先',
      value: (
        <>
          <a href={`tel:07040754201`} target="_blank" rel="noreferrer">
            <Typography
              fontSize="inherit"
              color="primary.main"
              marginBottom="4px"
            >
              070-4075-4201
            </Typography>
          </a>{' '}
          <Typography fontSize="inherit">
            受付時間 10:00-18:00（土日祝を除く）
          </Typography>
        </>
      ),
    },
    {
      label: 'メールアドレス',
      value: (
        <>
          <a href="mailto:<EMAIL>">
            <Typography color="primary.main" fontSize="inherit">
              <EMAIL>
            </Typography>
          </a>
        </>
      ),
    },
    {
      label: '運営統括責任者',
      value: '小林弘典',
    },

    {
      label: '追加手数料等の追加料金',
      value: (
        <>
          <Typography marginBottom="4px" fontSize="inherit">
            ・サイトの閲覧時等に､携帯通信料又はパケット通信料がかかります｡
          </Typography>
          <Typography fontSize="inherit">
            詳しくはご利用の携帯電話会社もしくは契約プロバイダーにお問い合わせください｡
          </Typography>
        </>
      ),
    },

    {
      label: '返金ポリシー',
      value: (
        <>
          <Typography marginBottom="16px" fontSize="inherit">
            ＜お客様都合のキャンセルの場合＞
            <br />
            サービス提供前：前日24時までにウェブサイトのキャンセルボタンを押すことで予約のキャンセルが可能です。
            <br />
            サービス提供後：サービス提供後については、返金はお受けしておりません。
          </Typography>
          <Typography fontSize="inherit">
            ＜サービスに不備がある場合＞
            <br />
            当社の規定に基づき返金もしくは代替えサービスを提供いたします。まずはお客様サポートセンターまでご連絡ください。
          </Typography>
        </>
      ),
    },
    {
      label: '引渡時期',
      value: '提供サービスの提供が完了された時点',
    },

    {
      label: '受け付け可能な決済手段',
      value: 'クレジットカード決済',
    },
    {
      label: '決済期間',
      value: 'クレジットカード決済の場合はただちに処理されます。',
    },
    {
      label: '販売価格',
      value: '提供サービスの各ページに記載しております。',
    },
  ];

  return (
    <Container maxWidth="lg" disableGutters sx={styles.container}>
      <NextSeo {...seoConfig.commercial} />
      <Box px={{ xs: 2, lg: 0 }}>
        <Breadcrumbs
          transformLabel={{
            '/commercial-transactions-law': '特定商取引法に基づく表記',
          }}
        />
      </Box>
      <Container maxWidth="md" disableGutters sx={styles.containerInfo}>
        <Typography sx={styles.title}>特定商取引法に基づく表記</Typography>
        <Box sx={styles.content}>
          <Stack rowGap={2} divider={<Divider />}>
            {commercialTransactionsLaw.map((data) => {
              return (
                <Grid
                  container
                  key={data.label}
                  columns={23}
                  columnSpacing={{ xs: 2, tablet: 7 }}
                >
                  <Grid
                    item
                    xs={8}
                    tablet={7}
                    fontWeight="bold"
                    fontSize={{ xs: 14, tablet: 16 }}
                  >
                    {data.label}
                  </Grid>
                  <Grid
                    item
                    xs
                    tablet
                    fontSize={{ xs: 14, tablet: 16 }}
                    whiteSpace={{ xs: 'pre-line', tablet: 'normal' }}
                  >
                    {data.value}
                  </Grid>
                </Grid>
              );
            })}
          </Stack>
        </Box>
      </Container>
    </Container>
  );
};

CommercialTransactionsLawPage.getLayout = function getLayout(
  page: ReactElement,
) {
  return <Layout showMedia>{page}</Layout>;
};

export default CommercialTransactionsLawPage;

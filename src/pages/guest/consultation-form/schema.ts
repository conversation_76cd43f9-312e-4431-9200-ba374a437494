import dayjs from 'dayjs';
import { t } from 'i18n';
import { Regex } from 'utils/constants';
import type { AnyObjectSchema } from 'yup';
import { array, object, string } from 'yup';

const schema = (hasExtraId: string): AnyObjectSchema =>
  object().shape({
    firstName: string()
      .required(t('validation.requiredField'))
      .max(10, t('validation.maxLength', { number: 10 }))
      .trim(t('validation.notAllowedWhiteSpace')),
    lastName: string()
      .max(10, t('validation.maxLength', { number: 10 }))
      .required(t('validation.requiredField'))
      .trim(t('validation.notAllowedWhiteSpace')),
    firstKatakanaName: string()
      .max(10, t('validation.maxLength', { number: 10 }))
      .required(t('validation.requiredField'))
      .trim(t('validation.notAllowedWhiteSpace'))
      .matches(Regex.KATAKANA, t('validation.invalidField')),
    lastKatakanaName: string()
      .max(10, t('validation.maxLength', { number: 10 }))
      .required(t('validation.requiredField'))
      .trim(t('validation.notAllowedWhiteSpace'))
      .matches(Regex.KATAKANA, t('validation.invalidField')),
    gender: string().required(t('validation.requiredField')),
    youDivorce: string().required(t('validation.requiredField')),
    partnerDivorce: string().required(t('validation.requiredField')),
    reason: object({
      value: array()
        .min(1, t('validation.requiredField'))
        .required(t('validation.requiredField')),
    }),
    extraData: string().when('value', (reasonValue, reasonSchema) => {
      if ((reasonValue || []).includes(hasExtraId)) {
        return string().required(t('validation.requiredField'));
      }
      return reasonSchema;
    }),
    additionalTopics: string().max(
      500,
      t('validation.maxLength', { number: 500 }),
    ),
    phone: string()
      .matches(Regex.PHONE, {
        message: t('validation.invalidPhoneNumber'),
        excludeEmptyString: true,
      })
      .required(),
    currentAddress1: string().required(),
    marriedDate: string()
      .nullable()
      .required(t('validation.requiredField'))
      .test('validDay', t('validation.invalidMarriedDate'), (value) => {
        if (!dayjs(value).isValid()) {
          return false;
        }
        return true;
      }),
    isSeparated: string().required(t('validation.requiredField')),
    separationDate: string()
      .nullable()
      .when('isSeparated', {
        is: 'yes',
        then: string()
          .nullable()
          .required(t('validation.requiredField'))
          .test('validDay', t('validation.invalidSeparationDate'), (value) => {
            if (!dayjs(value).isValid()) {
              return false;
            }
            return true;
          }),
      }),
    email: string()
      .required()
      .trim()
      .matches(Regex.EMAIL, t('validation.invalidField')),
  });

export default schema;

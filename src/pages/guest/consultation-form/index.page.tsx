import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Container, Stack, Typography } from '@mui/material';
import DivorceBackgroundFields from 'components/ConsultationForm/DivorceBackground/fields';
import ExpectLawyerFields from 'components/ConsultationForm/ExpectLawyer/fields';
import MarriageDateFields from 'components/ConsultationForm/MarriageDate/fields';
import PreferenceFields from 'components/ConsultationForm/Preference/fields';
import ProfileFields from 'components/GuestForm/Profile/fields';
import Layout from 'components/Layout';
import ChipList from 'components/UI/ChipList';
import { useNavigationState } from 'context/NavigationContext';
import { useFetchDetail, useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type {
  ConsultationRecord,
  IBasicConsultationForm,
  IBasicConsultationPayload,
} from 'hooks/customer/useConsultationForm/types';
import useGuestForm from 'hooks/guest';
import type { ICause } from 'hooks/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18next';
import {
  ArrowRightIcon,
  DivorceIcon,
  ProfileIcon,
  RecommendIcon,
  RingIcon,
} from 'icons';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import omit from 'lodash/omit';
import pick from 'lodash/pick';
import type { ICounselorItem } from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import resourceQuery from 'models/resource/query';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { type ReactElement, useEffect, useMemo } from 'react';
import { ProviderType, ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

import schema from './schema';
import styles from './styles';

type TFieldsOfForm = IBasicConsultationForm & {
  email: string;
};

type TBasicConsultation = IBasicConsultationPayload & {
  profile: IBasicConsultationPayload['profile'] & {
    email: string;
  };
};

const ConsultationFormPage = () => {
  const { state: counselorDetail } = useNavigationState();
  const router = useRouter();
  const { query, push, replace } = router;
  const { counselorId } = query;
  const { list: causeList } = useFetchList<ICause>(resourceQuery.divorceCauses);
  const webCookie = Helper.getWebCookieByGuest();
  const webCookieByRealUser = Helper.getWebCookie();
  const { detail: currentUser, refetch: refetchCurrentUser } =
    useFetchDetail<CustomerData>(
      apiQuery.currentGuestUser(webCookie?.otpId as string),
    );
  const { detail: counselorDetailData } = useFetchDetail<ICounselorItem>({
    ...providerQuery.providerDetailByGuest(counselorId as string),
  });
  const hasExtraId = (causeList || []).find((cause) => cause.hasExtra)?._id;
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetailByGuest(webCookie?.otpId as string),
    enabled: !!webCookie?.otpId,
  });
  const { refetch } = fetchConsultationDetail;
  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;

  const { updateBasicGuestConsultation, isUpdatingBasicGuestConsultation } =
    useGuestForm();
  const backgroundReason = consultationDetail.backgroundOfDivorce?.reason;
  const { backgroundOfDivorce, marriageInformation, expectLawyer } =
    consultationDetail;

  const reason = useMemo(
    () => ({
      value: (backgroundReason || []).map(
        (item: { value?: string; extraData?: string }) => item.value,
      ),
      extraData: backgroundReason?.find((item) => item.extraData)?.extraData,
    }),
    [backgroundReason],
  );
  const initialValues = useMemo(
    () => ({
      ...pick(currentUser, [
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'phone',
      ]),
      ...pick(currentUser, ['email']),
      currentAddress1: currentUser?.currentAddress?.address1._id,
      ...pick(backgroundOfDivorce, [
        'youDivorce',
        'partnerDivorce',
        'additionalTopics',
      ]),
      ...pick(marriageInformation, [
        'marriedDate',
        'isSeparated',
        'separationDate',
      ]),
      lawyerGender: expectLawyer?.gender,
      lawyerAge: expectLawyer?.age?._id,
      lawyerConsultation: expectLawyer?.consultation?.map((field) => field._id),
      reason,
    }),
    [
      backgroundOfDivorce,
      currentUser,
      expectLawyer,
      marriageInformation,
      reason,
    ],
  );
  const {
    control,
    handleSubmit,
    clearErrors,
    setValue,
    trigger,
    formState: { errors },
    reset,
  } = useHookForm<TFieldsOfForm>({
    resolver: yupResolver(schema(hasExtraId as string)),
    mode: 'onTouched',
    values: initialValues as TFieldsOfForm,
  });

  useEffect(() => {
    if (currentUser?.isCompletedProfile && !currentUser?.phone) {
      setValue('phone', '', { shouldTouch: true, shouldValidate: true });
    }
  }, [currentUser?.isCompletedProfile, currentUser?.phone, setValue, trigger]);

  // Disabled alert of browser
  useEffect(() => {
    const disableBeforeUnload = (event: BeforeUnloadEvent) => {
      event.stopImmediatePropagation();
    };

    window.addEventListener('beforeunload', disableBeforeUnload, {
      capture: true,
    });
    return () =>
      window.removeEventListener('beforeunload', disableBeforeUnload, {
        capture: true,
      });
  }, []);

  const handleUpdateBasicConsultation = (
    values: TFieldsOfForm & {
      guestOtp?: {
        guestExpiredAt: string;
        otpExpiredAt?: string | null;
        otpId: string;
        otpValue: string;
        otpVerify: boolean;
      };
    },
  ) => {
    // Check if email is verified (otpId exists)
    if (
      !webCookie?.otpId ||
      webCookie.otpId === 'undefined' ||
      webCookie.otpId === ''
    ) {
      Helper.toast('メールアドレスを認証してください。', { type: 'error' });
      return;
    }

    const divorceReason = values.reason.value.map((item) =>
      item === hasExtraId
        ? { value: item, extraData: values.reason.extraData }
        : { value: item },
    );
    const payload: TBasicConsultation & {
      isVerifiedEmail?: boolean;
    } = {
      profile: pick(values, [
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'phone',
        'email',
        'currentAddress1',
      ]),
      backgroundOfDivorce: {
        ...pick(values, ['youDivorce', 'partnerDivorce', 'additionalTopics']),
        reason: divorceReason,
      },
      marriageInformation: pick(values, [
        'marriedDate',
        'isSeparated',
        'separationDate',
      ]),
      expectLawyer: {
        age: values.lawyerAge,
        gender: values.lawyerGender,
        consultation: values.lawyerConsultation,
      },
      expectCounselor: {
        age: values.preferenceAge,
        gender: values.preferenceGender,
        consultation: values.preferenceConsultation,
      },
      guestOtp: get(currentUser, 'guestOtp'),
      isVerifiedEmail: get(currentUser, 'isVerifiedEmail', false),
    };
    refetch();
    refetchCurrentUser();
    reset();
    updateBasicGuestConsultation(payload, {
      onSuccess: () => {
        setTimeout(() => {
          if (counselorId) {
            push({
              pathname: '/guest/counselor-booking/[counselorId]',
              query,
            });
          }
        }, 0);
      },
      onError: (error) => {
        const {
          code,
        }: Partial<
          | {
              code: string;
            }
          | undefined
        > = error || {};
        // Error is already handled in the hook with toast message
        console.error('Failed to update consultation form:', error);
        if (code === 'OBJECT_NOT_FOUND') {
          router.reload();
        }
      },
    });
  };

  useEffect(() => {
    if (
      omit(ROLES, ROLES.GUEST)?.[
        `${webCookieByRealUser?.role as Exclude<ROLES, ROLES.GUEST>}`
      ]
    ) {
      replace({
        pathname: '/customer/consultation-form',
        query: {
          ...query,
        },
      });
    }
  }, [query, replace, webCookieByRealUser]);

  const currentCounselorDetail = useMemo(() => {
    return counselorDetailData ?? counselorDetail;
  }, [counselorDetail, counselorDetailData]);

  return (
    <Container
      maxWidth="md"
      sx={{ pt: { xs: 1, tablet: 4 }, pb: { xs: 2, tablet: 4 } }}
      disableGutters
    >
      <form onSubmit={handleSubmit(handleUpdateBasicConsultation)}>
        <Stack spacing={{ xs: 1, tablet: 2 }}>
          {currentCounselorDetail && (
            <Box sx={styles.titleContainer}>
              <Stack direction="row" gap={{ xs: '12px', md: 2 }}>
                <Box
                  sx={{
                    width: { xs: '80px', md: '84px' },
                    height: { xs: '80px', md: '84px' },
                    borderRadius: '100%',
                    overflow: 'hidden',
                    flexShrink: 0,
                  }}
                >
                  {currentCounselorDetail?.images?.[0]?.originUrl ? (
                    <Image
                      style={{ width: '100%', height: '100%' }}
                      src={currentCounselorDetail?.images?.[0]?.originUrl}
                      width={200}
                      height={200}
                      alt={currentCounselorDetail.fullName}
                    />
                  ) : null}
                </Box>
                <Box>
                  <Typography
                    sx={{
                      fontSize: {
                        xs: 10,
                        md: 12,
                      },
                    }}
                    fontWeight={500}
                    color="heading"
                  >
                    カウンセラー
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: {
                        xs: 18,
                        md: 20,
                      },
                    }}
                    color="heading"
                    fontWeight={700}
                  >
                    {currentCounselorDetail.fullName}
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: {
                        xs: 12,
                        md: 14,
                      },
                    }}
                    color="#887F70"
                    fontWeight={500}
                  >
                    {currentCounselorDetail.katakanaName}
                  </Typography>
                  {!isEmpty(currentCounselorDetail.consultationField) && (
                    <Box>
                      <ChipList
                        data={currentCounselorDetail.consultationField || []}
                        maxLines={5}
                        labelPath="value"
                        stackProps={{ gap: '4px', mt: 1 }}
                      />
                    </Box>
                  )}
                </Box>
              </Stack>
            </Box>
          )}
          <Box sx={styles.titleContainer}>
            <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
              <ProfileIcon />
              <Typography sx={styles.title}>
                {t('updateCustomerProfile.yourProfile')}
              </Typography>
            </Stack>

            <ProfileFields
              firstBooking
              control={control as never}
              currentUser={{ email: currentUser?.email || '' }}
              {...{
                errors,
                setValue,
              }}
            />
          </Box>

          <Box sx={styles.titleContainer}>
            <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
              <DivorceIcon />
              <Typography sx={styles.title}>
                {t('consultationRecord.divorceBackground')}
              </Typography>
            </Stack>
            <DivorceBackgroundFields
              control={control as never}
              setValue={setValue as never}
            />
          </Box>

          <Box sx={styles.titleContainer}>
            <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
              <RingIcon />
              <Typography sx={styles.title}>
                {t('consultationRecord.marriageInformation')}
              </Typography>
            </Stack>
            <MarriageDateFields
              clearErrors={clearErrors as never}
              control={control as never}
              setValue={setValue as never}
            />
          </Box>

          {!counselorId && (
            <Box sx={styles.titleContainer}>
              <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
                <RecommendIcon />
                <Typography sx={styles.title}>
                  {t('consultationRecord.expectLawyer')}
                </Typography>
              </Stack>
              <ExpectLawyerFields control={control as never} />
            </Box>
          )}
          {!counselorId && (
            <Box sx={styles.titleContainer}>
              <Stack direction="row" spacing={1} sx={styles.titleWrapper}>
                <RecommendIcon />
                <Typography sx={styles.title}>希望するカウンセラー</Typography>
              </Stack>
              <PreferenceFields
                providerType={ProviderType.COUNSELOR}
                control={control as never}
              />
            </Box>
          )}
        </Stack>

        <Box sx={styles.buttonContainer}>
          <LoadingButton
            size="large"
            color="secondary"
            variant="contained"
            type="submit"
            fullWidth
            className="shadow tabletStyle"
            endIcon={<ArrowRightIcon width={32} height={32} color="white" />}
            loading={isUpdatingBasicGuestConsultation}
            sx={{ maxWidth: '368px' }}
          >
            {t('global.next')}
          </LoadingButton>
        </Box>
        <Typography sx={styles.textDescription}>
          この情報はマイページに保存されます。
          <br />
          このボタンを押しても専門家には送信されません
        </Typography>
      </form>
    </Container>
  );
};

ConsultationFormPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export default ConsultationFormPage;

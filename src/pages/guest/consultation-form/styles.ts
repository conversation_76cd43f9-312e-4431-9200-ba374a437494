const styles = {
  breadcrumbWrapper: {
    bgcolor: 'white',
    p: { xs: '8px 16px', tablet: '16px 16px 16px 32px' },
    borderRadius: { xs: 0, tablet: 2 },
    my: { xs: 1, tablet: 0 },
  },
  breadcrumbText: {
    fontSize: { xs: 24, tablet: 32 },
    fontWeight: 'bold',
    mt: { xs: '4px', tablet: '12px' },
    color: 'heading',
  },
  titleContainer: {
    p: { xs: 2, tablet: 4 },
    bgcolor: 'white',
    borderRadius: { xs: 0, tablet: 2 },
  },
  titleWrapper: {
    mb: { xs: 2, tablet: 4 },
    alignItems: 'center',
    svg: {
      width: 28,
      height: 28,
    },
  },
  title: {
    fontSize: { xs: 18, tablet: 24 },
    fontWeight: 'bold',
    color: 'heading',
  },
  submitText: {
    fontSize: 12,
    textAlign: 'center',
    mt: 2,
    px: { xs: 2, tablet: 0 },
    whiteSpace: { xs: 'pre-line', tablet: 'normal' },
  },
  buttonContainer: {
    display: 'flex',
    justifyContent: 'center',
    mt: { xs: 3.5, tablet: 7 },
    px: 2,
  },
  textDescription: {
    fontSize: 12,
    textAlign: 'center',
    color: 'text.primary',
    mt: 1,
  },
} as const;

export default styles;

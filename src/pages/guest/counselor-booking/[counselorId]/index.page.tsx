/* eslint-disable react-hooks/exhaustive-deps */
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  Al<PERSON>,
  Box,
  Button,
  Container,
  Divider,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material';
import { dehydrate, QueryClient, useQueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import ConfirmMeetingModal from 'components/Case/ConfirmMeetingModal';
import CounselorCreateMeeting from 'components/Case/CounselorCreateMeeting';
import CouponModal from 'components/Case/CouponModal';
import CouponTag from 'components/Case/CouponTag';
import PaymentTooltipButton from 'components/Case/PaymentTooltipButton';
import AddCardModal from 'components/Customer/AddCardModal';
import { Select } from 'components/Form';
import HelperText from 'components/Form/HelperText';
import Layout from 'components/Layout';
import ChipList from 'components/UI/ChipList';
import IconLabel from 'components/UI/IconLabel';
import TermPolicy from 'components/UI/TermPolicy';
import { setCookie } from 'cookies-next';
import dayjs from 'dayjs';
import { useFetchDetail, useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import useGlobalState from 'hooks/useGlobalState';
import useHookForm from 'hooks/useHookForm';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { AddIcon, PaymentIcon } from 'icons';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import omit from 'lodash/omit';
import type {
  IExtraTdsPayload,
  IRequest3DsRes,
} from 'models/booking/interface';
import type { ICardsList } from 'models/card/interface';
import cardQuery from 'models/card/query';
import type { CounselorCreateMeetingPayload } from 'models/case/interface';
import caseQuery from 'models/case/query';
import {
  type ICounselorConsultationMenu,
  IMeetingType,
} from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import couponQuery from 'models/coupon';
import type { ICoupon } from 'models/coupon/interface';
import type { ICounselorItem } from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import type { GetServerSideProps, NextApiRequest, NextApiResponse } from 'next';
import Image from 'next/image';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useWatch } from 'react-hook-form';
import api from 'utils/api';
import CaseUtil from 'utils/caseUtil';
import { CardIcon, MeetingType, ProviderType, ROLES } from 'utils/constants';
import Helper from 'utils/helpers';
import localStorageHelper from 'utils/localStorageHelper';

import type { CreateCounselorMeetingValues } from './schema';
import schema from './schema';
import styles from './styles';

const BookingPage = () => {
  const {
    query: {
      counselorId,
      tryInitDataLS,
      isFailCallback,
      meetingType,
      startSlot,
      isFilterCounselor,
      date,
    },
    push,
    replace,
  } = useRouter();
  const { setConfirmModal } = useGlobalState();
  const webCookie = Helper.getWebCookieByGuest();
  const webCookieByRealUser = Helper.getWebCookie();
  const { query } = useRouter();
  const { list: menuList, isLoading: isLoadingMenu } =
    useFetchList<ICounselorConsultationMenu>({
      ...consultationQuery.listByGuest(counselorId as string),
    });
  const formattedMenuList = useMemo(() => {
    return menuList.map((menu) => ({
      ...menu,
      value: `${menu.unitPrices[0].duration}分 - ${menu.title}`,
    }));
  }, [menuList]);

  const { queryMenuId, queryMeetingType } = useMemo(() => {
    let currentMenuId = '';

    // Check if URL has valid duration and meetingType (not empty strings)
    const durationParam = Array.isArray(query?.duration)
      ? query.duration[0]
      : query?.duration;
    const meetingTypeParam = Array.isArray(query?.meetingType)
      ? query.meetingType[0]
      : query?.meetingType;
    const menuIdParam = Array.isArray(query?.menuId)
      ? query.menuId[0]
      : query?.menuId;

    const urlDuration =
      durationParam && durationParam.trim() !== ''
        ? Number(durationParam)
        : null;
    const urlMeetingType =
      meetingTypeParam && meetingTypeParam.trim() !== ''
        ? meetingTypeParam
        : null;

    if (urlDuration && urlMeetingType && !Number.isNaN(urlDuration)) {
      // Find menu that matches URL parameters
      const currentMenu = formattedMenuList?.find(
        (menu) =>
          menu.meetingType === urlMeetingType &&
          menu?.unitPrices?.[0]?.duration === urlDuration,
      );
      if (currentMenu?._id) currentMenuId = currentMenu?._id;
    }

    // If no match from URL parameters or URL parameters are empty, use default logic
    if (!currentMenuId && formattedMenuList && formattedMenuList.length > 0) {
      const defaultMenuId = Helper.getDefaultMenuId(formattedMenuList);
      if (defaultMenuId) currentMenuId = defaultMenuId;
    }

    // Determine default meeting type
    let defaultMeetingType = IMeetingType.SINGLE;

    // If no URL meeting type is specified, check if we should auto-select PARTNER
    if (!urlMeetingType && formattedMenuList.length > 0) {
      const hasSingleMenus = formattedMenuList.some(
        (menu) => menu.meetingType === IMeetingType.SINGLE,
      );
      const hasPartnerMenus = formattedMenuList.some(
        (menu) => menu.meetingType === IMeetingType.PARTNER,
      );

      // Auto-select PARTNER if no SINGLE menus exist but PARTNER menus do
      if (!hasSingleMenus && hasPartnerMenus) {
        defaultMeetingType = IMeetingType.PARTNER;
      }
    }

    return {
      queryMenuId: (menuIdParam && menuIdParam.trim() !== ''
        ? menuIdParam
        : currentMenuId) as string,
      queryMeetingType: (urlMeetingType as IMeetingType) || defaultMeetingType,
    };
  }, [formattedMenuList, query?.duration, query?.meetingType, query?.menuId]);

  const queryCurrentStartSlot = useMemo(() => {
    let currentExpectDate = '';
    if (query?.startSlot) {
      const currentStartSlot = String(query?.startSlot);
      currentExpectDate = dayjs(currentStartSlot).utc().toISOString();
    }
    return currentExpectDate;
  }, [query?.startSlot]);

  const [agree, setAgree] = useState(false);
  const [openAddCard, setOpenAddCard] = useState<
    boolean | ICardsList | string
  >();
  const [openCouponModal, setOpenCouponModal] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState<ICoupon | null>(null);
  const [openConfirmMeeting, setOpenConfirmMeeting] = useState(false);
  const [cardInitialized, setCardInitialized] = useState(false);
  const queryClient = useQueryClient();

  const { detail: consultationDetail } = useFetchDetail<ConsultationRecord>({
    ...apiQuery.consultationDetailByGuest(webCookie?.otpId as string),
    enabled: !!webCookie?.otpId,
  });
  const { detail: counselorDetail } = useFetchDetail<ICounselorItem>({
    ...providerQuery.providerDetailByGuest(counselorId as string),
  });
  const {
    list,
    refetch: refetchCardList,
    isLoading: isLoadingCards,
  } = useFetchList<ICardsList>(
    cardQuery.cardsListByGuest(webCookie?.otpId as string),
  );

  const { mutateAsync: createMeetingNormal, isLoading: isLoadingNormal } =
    useMutate<CounselorCreateMeetingPayload>(
      caseQuery.counselorCreateMeetingByGuest(webCookie?.otpId as string),
    );
  const { mutateAsync: createMeeting3D, isLoading: isLoading3D } = useMutate<
    CounselorCreateMeetingPayload,
    IRequest3DsRes
  >(caseQuery.counselorCreateMeeting3DsByGuest(webCookie?.otpId as string));

  const { reset, control, handleSubmit, getValues, setValue, watch } =
    useHookForm<CreateCounselorMeetingValues>({
      mode: 'all',
      resolver: yupResolver(schema),
      values: {
        type: MeetingType.ONLINE,
        menuId: queryMenuId,
        meetingType: queryMeetingType,
        expectDate: queryCurrentStartSlot,
        consumerNote: undefined,
        hasCard: undefined,
        cardNumber: undefined,
      },
    });

  const watchMenuId = useWatch({ name: 'menuId', control });
  const watchMeetingType = useWatch({ name: 'meetingType', control });

  const watchMenu = useMemo(
    () => formattedMenuList.find((menu) => menu._id === watchMenuId),
    [formattedMenuList, watchMenuId],
  );

  // Filter menu list based on selected meeting type
  const filteredMenuList = useMemo(() => {
    if (!watchMeetingType) return formattedMenuList;

    const meetingTypeParam = Array.isArray(query?.meetingType)
      ? query.meetingType[0]
      : query?.meetingType;
    const urlMeetingType =
      meetingTypeParam && meetingTypeParam.trim() !== ''
        ? meetingTypeParam
        : null;

    return formattedMenuList.filter((menu) => {
      // Always filter by meeting type
      if (menu.meetingType !== watchMeetingType) return false;

      // Apply duration filter based on URL meetingType parameter
      if (urlMeetingType === 'SINGLE' && watchMeetingType === 'SINGLE') {
        // URL has SINGLE and user selected SINGLE: apply duration filter
        return (
          (menu.unitPrices[0]?.duration || 0) >= Number(query?.duration || 0)
        );
      }
      if (urlMeetingType === 'PARTNER' && watchMeetingType === 'PARTNER') {
        // URL has PARTNER and user selected PARTNER: apply duration filter for 90/120 only
        const urlDuration = Number(query?.duration || 0);
        if (urlDuration === 90 || urlDuration === 120) {
          const menuDuration = menu.unitPrices[0]?.duration || 0;
          return menuDuration === 90 || menuDuration === 120;
        }
        // For duration=60 or other values, no filtering
        return true;
      }

      // For all other cases, no duration filter
      return true;
    });
  }, [
    formattedMenuList,
    watchMeetingType,
    query?.duration,
    query?.meetingType,
  ]);

  const duration = useMemo(
    () =>
      watchMenu?.unitPrices[0].duration || menuList[0]?.unitPrices[0].duration,
    [menuList, watchMenu?.unitPrices],
  );

  const { partner, propertyInformation, kids } = consultationDetail;
  const cardList = useMemo(() => {
    return list.map((card) => ({
      _id: card.id,
      value: Helper.formatCardNumberText(card.details.lastNumber),
      icon: CardIcon[card.details.brand],
      no3Ds: !card.details.cardholderName,
    }));
  }, [list]);

  const basicPrice = useMemo(
    () => watchMenu?.unitPrices[0].price || 0,
    [watchMenu?.unitPrices],
  );

  const { mutateAsync: checkCoupon, isLoading: isCheckingCoupon } = useMutate<
    {
      code: string;
      totalPrice: number;
    },
    ICoupon
  >(couponQuery.checkCouponByGuest(webCookie?.otpId as string));

  // Recalculate coupon amount based on current basic price
  const recalculatedCouponAmount = useMemo(() => {
    if (!selectedCoupon || basicPrice <= 0) return 0;

    return CaseUtil.calculateFinalCouponAmount({
      totalPrice: basicPrice,
      issuedCoupon: selectedCoupon,
    });
  }, [selectedCoupon, basicPrice]);

  const { usedCouponPrice } = CaseUtil.getCouponPrice({
    basicPrice,
    couponAmount: recalculatedCouponAmount,
  });

  const hasMissingHolderNameCard = useMemo(
    () =>
      list.find((card) => card.id === watch('cardNumber')) &&
      !list.find((card) => card.id === watch('cardNumber'))?.details
        .cardholderName,
    [list, watch('cardNumber')],
  );

  // Create a custom refetch function
  const refetchCards = useCallback(() => {
    setCardInitialized(false);
    return refetchCardList();
  }, [refetchCardList]);

  useEffect(() => {
    let resetValue = {} as CreateCounselorMeetingValues;
    if (menuList.length > 0) {
      // Use priority-based default menu selection
      const defaultMenuId = Helper.getDefaultMenuId(menuList);
      if (defaultMenuId) {
        resetValue.menuId = defaultMenuId;
      }
    }

    if (tryInitDataLS) {
      resetValue = {
        ...resetValue,
        ...(localStorageHelper.getItem<CreateCounselorMeetingValues>(
          'bookingCounselorFormVal',
        ) || {}),
      };
    }
    reset(resetValue);
  }, [isLoadingMenu, menuList, reset, tryInitDataLS]);

  // Add separate useEffect for card loading - enhanced for reload scenarios
  useEffect(() => {
    // Reset the initialization state when cards finish loading
    if (
      !isLoadingCards &&
      Array.isArray(list) &&
      list.length > 0 &&
      cardInitialized
    ) {
      const currentCard = watch('cardNumber');
      // If we have cards but no card is selected, reset the initialization
      if (!currentCard) {
        setCardInitialized(false);
      }
    }

    // Always force card initialization when the component first mounts or after a page reload
    const shouldInitialize =
      (!cardInitialized || watch('cardNumber') === undefined) &&
      !isLoadingCards &&
      Array.isArray(list);

    if (shouldInitialize) {
      // Initialize variables
      let foundCard = false;

      // Try to find a default card
      const defaultCard = list.find(
        (card) =>
          card &&
          typeof card.id === 'string' &&
          card.details &&
          card.details.default,
      );

      if (defaultCard && typeof defaultCard.id === 'string') {
        // Found a default card - set it with a timeout to avoid React batching
        const cardId = defaultCard.id;
        setTimeout(() => {
          setValue('hasCard', true);
          setValue('cardNumber', cardId);
          setCardInitialized(true);
        }, 0);
        foundCard = true;
      }

      // If no default card was found, try using the first card
      if (!foundCard && list.length > 0) {
        const firstCard = list[0];
        if (firstCard && typeof firstCard.id === 'string') {
          const cardId = firstCard.id;
          setTimeout(() => {
            setValue('hasCard', true);
            setValue('cardNumber', cardId);
            setCardInitialized(true);
          }, 0);
          foundCard = true;
        }
      }

      // If no cards were suitable, just mark as initialized
      if (!foundCard) {
        setCardInitialized(true);
      }
    }
  }, [cardInitialized, isLoadingCards, list, setValue, watch]);

  useEffect(() => {
    if (isEmpty(watchMenuId)) {
      setSelectedCoupon(null);
    }
  }, [setValue, watchMenuId]);

  // Auto-select menu when meeting type changes and current menu is not available
  useEffect(() => {
    if (watchMeetingType) {
      if (filteredMenuList.length > 0) {
        // Check if current selected menu is still in the filtered list
        const currentMenuExists = filteredMenuList.some(
          (menu) => menu._id === watchMenuId,
        );

        if (!currentMenuExists) {
          let selectedMenuId: string | undefined;

          // Get URL parameters for priority-based selection
          const durationParam = Array.isArray(query?.duration)
            ? query.duration[0]
            : query?.duration;
          const meetingTypeParam = Array.isArray(query?.meetingType)
            ? query.meetingType[0]
            : query?.meetingType;

          const urlDuration =
            durationParam && durationParam.trim() !== ''
              ? Number(durationParam)
              : null;
          const urlMeetingType =
            meetingTypeParam && meetingTypeParam.trim() !== ''
              ? meetingTypeParam
              : null;

          // Priority 1: If URL has valid duration and meetingType, find matching menu
          if (urlDuration && urlMeetingType && !Number.isNaN(urlDuration)) {
            const matchingMenu = filteredMenuList.find(
              (menu) =>
                menu.meetingType === urlMeetingType &&
                menu?.unitPrices?.[0]?.duration === urlDuration,
            );
            selectedMenuId = matchingMenu?._id;
          }

          // Priority 2: If no URL match, use default selection logic
          if (!selectedMenuId) {
            selectedMenuId =
              Helper.getDefaultMenuId(filteredMenuList) || undefined;
          }

          // Priority 3: Fallback to first menu in filtered list
          if (!selectedMenuId && filteredMenuList.length > 0) {
            selectedMenuId = filteredMenuList[0]?._id;
          }

          if (selectedMenuId) {
            setValue('menuId', selectedMenuId);
          }
        }
      } else if (watchMenuId) {
        // If no menus are available for the selected meeting type, clear the selected menu
        setValue('menuId', '');
      }
    }
  }, [
    filteredMenuList,
    watchMeetingType,
    watchMenuId,
    setValue,
    query?.duration,
    query?.meetingType,
  ]);

  // Clear expectDate when meetingType changes
  useEffect(() => {
    // Only clear if not initial load (watchMeetingType exists and differs from initial)
    if (watchMeetingType && watchMeetingType !== queryMeetingType) {
      setValue('expectDate', '');
    }
  }, [watchMeetingType, setValue, queryMeetingType]);

  // Check if URL parameters specify PARTNER + duration < 90 but no matching menu exists
  useEffect(() => {
    if (formattedMenuList.length === 0) return; // Wait for menu list to load

    const durationParam = Array.isArray(query?.duration)
      ? query.duration[0]
      : query?.duration;
    const meetingTypeParam = Array.isArray(query?.meetingType)
      ? query.meetingType[0]
      : query?.meetingType;

    const urlDuration =
      durationParam && durationParam.trim() !== ''
        ? Number(durationParam)
        : null;
    const urlMeetingType =
      meetingTypeParam && meetingTypeParam.trim() !== ''
        ? meetingTypeParam
        : null;

    // Check if URL specifies PARTNER meeting with duration < 90
    if (urlDuration && urlMeetingType === 'PARTNER' && urlDuration < 90) {
      // Check if any menu exists with PARTNER + duration < 90
      const hasPartnerMenuUnder90 = formattedMenuList.some(
        (menu) =>
          menu.meetingType === 'PARTNER' && menu.unitPrices?.[0]?.duration < 90,
      );

      // If no such menu exists, show toast message
      if (!hasPartnerMenuUnder90) {
        Helper.toast(
          'ご指定のカウンセラーへのご相談が初めての方は、夫婦カウンセリングは90分からご利用いただけます',
          { type: 'error' },
        );
      }
    }
  }, [formattedMenuList, query?.duration, query?.meetingType]);

  useEffect(() => {
    if (
      omit(ROLES, ROLES.GUEST)?.[
        `${webCookieByRealUser?.role as Exclude<ROLES, ROLES.GUEST>}`
      ]
    ) {
      replace({
        pathname: `/customer/counselor-booking/${query?.counselorId}`,
        query: {
          ...query,
        },
      });
    }
  }, [query, replace, webCookieByRealUser]);

  const handleChangeMenu = (e: React.ChangeEvent<HTMLButtonElement>) => {
    const target = e.target as HTMLButtonElement | null;

    // Ensure the target exists and has a dataset or specific attribute
    const value = target?.value;
    if (value) {
      setValue('expectDate', '');
    }
  };

  const handleCreateMeeting = (values: CreateCounselorMeetingValues) => {
    setOpenConfirmMeeting(false);
    const { expectDate, consumerNote, cardNumber, menuId } = values;
    const selectedMenu = menuList.find(
      (menu) => menu._id === menuId,
    ) as ICounselorConsultationMenu;

    const submitValue = {
      expectDate: [{ start: expectDate, end: expectDate }] as [
        { start: string; end: string },
      ],
      startSlot: expectDate,
      consumerNote,
      provider: counselorId as string,
      paymentInfo: {
        duration: selectedMenu.unitPrices[0].duration,
        basicPrice: selectedMenu.unitPrices[0].price,
        card: cardNumber,
        couponCode: selectedCoupon?.code,
      },
      consultationMenu: selectedMenu,
      extraPayload: {
        counselorId: counselorId as string,
        needFillForm:
          !partner || Helper.checkIsEmptyObject(propertyInformation) || !kids,
      } as IExtraTdsPayload | undefined,
    };
    reset();
    if (usedCouponPrice === 0) {
      submitValue.extraPayload = undefined;
      reset();
      createMeetingNormal(submitValue, {
        onSuccess: () => {
          setConfirmModal({
            icon: 'success',
            content: t('booking.thankGuest'),
            confirmText: t('booking.enterFormGuest'),
            hideCancelButton: true,
            onConfirm: () => {
              Helper.setToken({
                token: webCookie?.guest_token as string,
                email: webCookie?.email as string,
                role: ROLES.CUSTOMER,
              });
              Helper.removeWebCookieByGuest();
              setTimeout(() => {
                push('/');
              }, 50);
            },
            dialogContentTextProps: { sx: { fontSize: 16 } },
          });
        },
        onError: (error) => {
          if (get(error, 'code') === 'MEETNG_HAS_SLOT_IS_CLOSED') {
            setValue('expectDate', '');
            queryClient.refetchQueries({
              queryKey: ['currentUser', 'consumer', 'calendars'],
            });
          }
        },
      });
    } else {
      reset();
      createMeeting3D(submitValue, {
        onSuccess: (res) => {
          const { redirectUrl } = res;
          setCookie(
            'lawyer-web-booking-url',
            `${window?.location?.pathname}${window?.location?.search}`,
          );
          if (redirectUrl) {
            localStorageHelper.setItem('bookingCounselorFormVal', values);

            setTimeout(() => {
              window.location.href = redirectUrl;
            }, 0);
          }
        },
        onError: (error) => {
          if (get(error, 'code') === 'MEETNG_HAS_SLOT_IS_CLOSED') {
            setValue('expectDate', '');
            queryClient.refetchQueries({
              queryKey: ['currentUser', 'consumer', 'calendars'],
            });
          }
        },
      });
    }
  };

  const handleConfirmMeeting = (values: CreateCounselorMeetingValues) => {
    if (isEmpty(list) && values.type === MeetingType.ONLINE) {
      setOpenAddCard('open_when_submit');
    } else setOpenConfirmMeeting(true);
  };

  const handleCheckCoupon = (code: string) => {
    checkCoupon(
      { code, totalPrice: basicPrice },
      {
        onSuccess: (data) => {
          setOpenCouponModal(false);
          setSelectedCoupon(data);
        },
      },
    );
  };

  const handleSelectCoupon = (coupon: ICoupon) => {
    checkCoupon(
      { code: coupon.code, totalPrice: basicPrice },
      {
        onSuccess: (data) => {
          setOpenCouponModal(false);
          setSelectedCoupon(data);
        },
      },
    );
  };
  const selectedCard = watch('cardNumber');
  const selectedCardInfo = useMemo(
    () => list.find((card) => card.id === selectedCard),
    [list, selectedCard],
  );

  useEffect(() => {
    if (isFailCallback) {
      setConfirmModal({
        icon: 'error',
        content:
          '認証コードの有効期限が切れています。認証コードを再送信してください。',
        confirmText: t('global.ok'),
        hideCancelButton: true,
        onConfirm: () => {
          setTimeout(() => Helper.removeWebCookieByGuest(), 50);
          // Build URL with only defined parameters
          const params = new URLSearchParams();
          if (counselorId) params.append('counselorId', String(counselorId));
          if (duration && Number.isFinite(duration))
            params.append('duration', String(duration));
          if (meetingType && meetingType !== 'undefined')
            params.append('meetingType', String(meetingType));
          if (startSlot && startSlot !== 'undefined')
            params.append('startSlot', String(startSlot));
          if (isFilterCounselor && isFilterCounselor !== 'undefined')
            params.append('isFilterCounselor', String(isFilterCounselor));
          if (date && date !== 'undefined') params.append('date', String(date));
          const queryString = params.toString();

          push(
            `/guest/consultation-form${queryString ? `?${queryString}` : ''}`,
          );
        },
        dialogContentTextProps: { sx: { fontSize: 16 } },
      });
    }
  }, [
    counselorId,
    duration,
    isFailCallback,
    isFilterCounselor,
    meetingType,
    push,
    startSlot,
    setConfirmModal,
    date,
  ]);

  return (
    <Container maxWidth="lg" disableGutters sx={styles.container}>
      <Box px={{ xs: 2, tablet: 0 }}>
        <Breadcrumbs
          omitIndexList={[0]}
          transformLabel={{
            '/guest/counselor-booking/[counselorId]':
              counselorDetail.nickname || counselorDetail.fullName || (
                <Skeleton
                  sx={{ fontSize: { xs: 10, tablet: 12 } }}
                  variant="text"
                  width={100}
                />
              ),
          }}
        />
      </Box>
      <form
        id="create-meeting-form"
        onSubmit={handleSubmit(handleConfirmMeeting)}
      >
        <Container maxWidth="ex" disableGutters sx={styles.containerForm}>
          <Container
            maxWidth="se"
            disableGutters
            sx={{ maxWidth: { xs: 'unset', sl: 'se' } }}
          >
            {counselorDetail && (
              <Box sx={styles.boxCol}>
                <Stack direction="row" gap={{ xs: '12px', md: 2 }}>
                  <Box
                    sx={{
                      width: { xs: '80px', md: '84px' },
                      height: { xs: '80px', md: '84px' },
                      borderRadius: '100%',
                      overflow: 'hidden',
                      flexShrink: 0,
                    }}
                  >
                    {counselorDetail?.images?.[0]?.originUrl ? (
                      <Image
                        style={{ width: '100%', height: '100%' }}
                        src={counselorDetail?.images?.[0]?.originUrl}
                        width={200}
                        height={200}
                        alt={counselorDetail.fullName}
                      />
                    ) : null}
                  </Box>
                  <Box>
                    <Typography
                      sx={{
                        fontSize: {
                          xs: 10,
                          md: 12,
                        },
                      }}
                      fontWeight={500}
                      color="heading"
                    >
                      カウンセラー
                    </Typography>
                    <Typography
                      sx={{
                        fontSize: {
                          xs: 18,
                          md: 20,
                        },
                      }}
                      color="heading"
                      fontWeight={700}
                    >
                      {counselorDetail.fullName}
                    </Typography>
                    <Typography
                      sx={{
                        fontSize: {
                          xs: 12,
                          md: 14,
                        },
                      }}
                      color="#887F70"
                      fontWeight={500}
                    >
                      {counselorDetail.katakanaName}
                    </Typography>
                    {!isEmpty(counselorDetail.consultationField) && (
                      <Box>
                        <ChipList
                          data={counselorDetail.consultationField || []}
                          maxLines={5}
                          labelPath="value"
                          stackProps={{ gap: '4px', mt: 1 }}
                        />
                      </Box>
                    )}
                  </Box>
                </Stack>
              </Box>
            )}

            {hasMissingHolderNameCard && (
              <Alert sx={styles.topAlert} severity="warning">
                {t('otherMessage.OM02')}
              </Alert>
            )}
            <CounselorCreateMeeting
              menuList={filteredMenuList}
              control={control}
              loading={isLoading3D || isLoadingNormal}
              duration={duration as number}
              counselorId={counselorId as string}
              getDisableSubmitBtnState={({ agree: _agree }) =>
                !_agree || hasMissingHolderNameCard
              }
              {...{
                onChangeMenu: handleChangeMenu,
              }}
            />
          </Container>
          <Box
            mt={{ xs: 1, sl: 0 }}
            maxWidth={{ xs: 1032, sl: 384 }}
            width={1}
            ml={{ sl: 2 }}
          >
            <Box borderRadius={{ sl: 2 }} bgcolor="white" p={{ xs: 2, sl: 4 }}>
              <IconLabel
                icon={PaymentIcon}
                containerProps={{ mb: { xs: 2, tablet: 4 } }}
              >
                {t('booking.paymentMethod')}
              </IconLabel>
              <Select
                name="cardNumber"
                labelCol={12}
                control={control}
                data={cardList}
                placeholder={t('booking.placeholderSelectCard')}
              />
              {hasMissingHolderNameCard && (
                <HelperText
                  error={t('inlineMessage.GIM18')}
                  onClick={() => {
                    setOpenAddCard(selectedCardInfo);
                  }}
                />
              )}
              <Button
                size="small"
                onClick={() => setOpenAddCard('open_normal')}
                sx={{
                  mt: '12px',
                  ml: '-4px',
                  svg: {
                    width: 20,
                    height: 20,
                  },
                  '& .MuiButton-startIcon': {
                    mr: '4px',
                  },
                  '.Mui-disabled': {
                    backgroundColor: 'unset',
                  },
                }}
                className="tabletStyle"
                startIcon={<AddIcon />}
                color="primary"
              >
                新しいカードを追加
              </Button>
              <Divider
                sx={{
                  mt: { xs: '12px', tablet: '16px' },
                  mb: { xs: 1, tablet: '16px' },
                }}
              />
              {!selectedCoupon ? (
                <>
                  <Button
                    size="small"
                    onClick={() => setOpenCouponModal(true)}
                    sx={{
                      ml: '-4px',
                      svg: {
                        width: 20,
                        height: 20,
                      },
                      '& .MuiButton-startIcon': {
                        mr: '4px',
                      },
                    }}
                    className="tabletStyle"
                    startIcon={<AddIcon />}
                    color="primary"
                    disabled={!watchMenu}
                  >
                    クーポンを使う
                  </Button>
                  <Typography mt="4px" mb="20px" fontSize={14} color="#9E9B92">
                    クーポンご利用前にメニューを選択してください
                  </Typography>
                </>
              ) : (
                <Box
                  display="flex"
                  gap={1}
                  justifyContent="space-between"
                  py="4px"
                  flexWrap="wrap"
                >
                  <Box display="flex" alignItems="center">
                    <Typography
                      fontSize={{ xs: 14, tablet: 16 }}
                      fontWeight={700}
                      mr={1}
                      color="heading"
                    >
                      クーポン値引
                    </Typography>
                    <CouponTag
                      code={selectedCoupon.code}
                      onDelete={() => setSelectedCoupon(null)}
                    />
                  </Box>
                  <Typography fontWeight={700} color="neutral7">
                    {recalculatedCouponAmount > 0 && '-'}{' '}
                    {Helper.addComma(recalculatedCouponAmount || 0)}円
                  </Typography>
                </Box>
              )}
              <Divider
                sx={{
                  mt: { xs: '12px', tablet: '16px' },
                  mb: { xs: 1, tablet: '20px' },
                }}
              />
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="flex-end"
              >
                <Stack flexDirection="row">
                  <Typography
                    fontSize={{ xs: 14, tablet: 16 }}
                    fontWeight="bold"
                    color="heading"
                    mr="6px"
                  >
                    {t('booking.basicPrice')}
                  </Typography>
                  <PaymentTooltipButton
                    title="お支払い予定金額に関して"
                    content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
                  />
                </Stack>
                <Stack direction="row" spacing="2px" alignItems="flex-end">
                  <Typography
                    fontSize={{ xs: 20, tablet: 24 }}
                    fontWeight="bold"
                    color="neutral7"
                  >
                    {Helper.addComma(usedCouponPrice)}
                  </Typography>
                  <Typography
                    fontSize={{ xs: 14, tablet: 16 }}
                    fontWeight="bold"
                    color="neutral7"
                    lineHeight={{ xs: 1.71, tablet: '30px' }}
                  >
                    円
                  </Typography>
                </Stack>
              </Stack>
              <Stack>
                <Typography
                  fontSize={{ xs: 12, tablet: 14 }}
                  color="neutral7"
                  fontWeight="bold"
                  mt={1}
                >
                  {t('booking.descriptionForCounselor1')}
                </Typography>
                <Typography fontSize={{ xs: 12, tablet: 14 }} color="hint">
                  {t('booking.descriptionForCounselor2')}
                </Typography>
              </Stack>
            </Box>
          </Box>
          <Box display={{ xs: 'block', sl: 'none' }} p="8px 16px 24px">
            <Box
              display="flex"
              justifyContent={{ xs: 'flex-start', tablet: 'center' }}
            >
              <TermPolicy checked={agree} onChange={(data) => setAgree(data)} />
            </Box>
            <Box display="flex" justifyContent="center" mt={1}>
              <LoadingButton
                color="secondary"
                type="submit"
                variant="contained"
                size="large"
                className="tabletStyle shadow"
                fullWidth
                loading={isLoading3D || isLoadingNormal}
                disabled={!agree || hasMissingHolderNameCard}
                sx={{
                  maxWidth: 368,
                }}
              >
                {t('booking.submit')}
              </LoadingButton>
            </Box>
          </Box>
        </Container>
      </form>
      {openAddCard && (
        <AddCardModal
          open={!!openAddCard}
          onClose={() => setOpenAddCard('')}
          onSuccess={() => {
            setOpenAddCard('');
            refetchCards();
            if (openAddCard === 'open_when_submit') {
              setOpenConfirmMeeting(true);
            }
          }}
          editPayload={
            typeof openAddCard === 'object'
              ? { card: openAddCard, isEdit: true }
              : undefined
          }
        />
      )}
      <ConfirmMeetingModal
        onConfirm={handleSubmit(handleCreateMeeting)}
        onClose={() => setOpenConfirmMeeting(false)}
        open={openConfirmMeeting}
        control={control}
        providerType={ProviderType.COUNSELOR}
        cardList={cardList.filter((card) => !card.no3Ds)}
        data={{
          avatar: get(
            counselorDetail,
            'images[0].originUrl',
            '/images/default-avatar.png',
          ),
          nickName: counselorDetail?.nickname,
          fullName: counselorDetail?.fullName,
          katakanaName: counselorDetail?.katakanaName,
          consultationField: counselorDetail?.consultationField,
          unitPrice: watchMenu?.unitPrices[0].price || 0,
          duration: watchMenu?.unitPrices[0].duration || 0,
          menuTitle: watchMenu?.value,
          coupon: selectedCoupon,
          ...getValues(),
          meetingType: watchMenu?.meetingType,
        }}
      />
      <CouponModal
        onClose={() => setOpenCouponModal(false)}
        open={openCouponModal}
        // couponList={couponList}
        // isLoading={isCouponLoading}
        // total={couponList.length}
        checkCoupon={handleCheckCoupon}
        isCheckingCoupon={isCheckingCoupon}
        onSelect={handleSelectCoupon}
      />
    </Container>
  );
};

BookingPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const {
    counselorId,
    // duration,
    // meetingType,
    // startSlot,
    // isFilterCounselor,
    // date,
  } = query;
  try {
    const cookies = Helper.getWebCookieByGuest(
      req as NextApiRequest,
      res as NextApiResponse,
    );
    const { otpId } = cookies;
    const queryClient = new QueryClient();
    const { data: consultation } = await api.get(
      apiQuery.consultationDetailByGuest(otpId as string).apiUrl,
      // config,
    );
    const { backgroundOfDivorce, marriageInformation, consumer } = consultation;

    const isValidProfile =
      consumer?.isCompletedProfile &&
      consumer?.phone &&
      backgroundOfDivorce &&
      marriageInformation;
    if (!isValidProfile) {
      return {
        redirect: {
          destination: `/guest/consultation-form?counselorId=${counselorId}`,
          permanent: false,
        },
      };
    }
    // Pre-fetch data for client-side
    await queryClient.prefetchQuery({
      queryKey: ['consultation', otpId],
      queryFn: () => consultation,
    });

    return {
      props: {
        dehydratedState: dehydrate(queryClient),
      },
    };
  } catch (error) {
    // return {
    //   redirect: {
    //     destination: `/guest/counselor-booking/${counselorId}?duration=${duration}&meetingType=${meetingType}&startSlot=${startSlot}&isFilterCounselor=${isFilterCounselor}&date=${date}&isFailCallback=${true}`,
    //     permanent: false,
    //   },
    // };
    return {
      props: {
        dehydratedState: null,
      },
    };
  }
};

export default BookingPage;

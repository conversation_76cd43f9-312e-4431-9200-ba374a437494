import { Box } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import CTASection from 'components/NewHome/CTA';
import ExpertsSection from 'components/NewHome/Experts';
import HowToSection from 'components/NewHome/HowTo';
import NewLayout from 'components/NewHome/Layout';
import PricingSection from 'components/NewHome/Pricing';
import SearchSection from 'components/NewHome/SearchSection';
import ThemeBanner from 'components/NewHome/ThemeBanner';
import ThemeDetailSection from 'components/NewHome/ThemeDetailSection';
import ThemesSection from 'components/NewHome/Themes';
import { useFetchList } from 'hooks';
import type { IListItem } from 'hooks/types';
import resourceQuery from 'models/resource/query';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useEffect, useMemo } from 'react';
import { ProviderType } from 'utils/constants';

// Define the mapping type
type ThemeConsultationMap = {
  [key: string]: string[];
};

// Theme to lawyer consultation field names mapping type
type ThemeLawyerConsultationMap = {
  [key: string]: string[];
};

const ThemePage = () => {
  const router = useRouter();
  const { id: themeId } = router.query;

  // Scroll to top when theme ID changes
  useEffect(() => {
    if (router.isReady && themeId) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [themeId, router.isReady]);

  // Define direct IDs for consultations based on theme (counselors)
  const themeConsultationMapping = useMemo<ThemeConsultationMap>(() => {
    return {
      '1': ['id24'], // 夫婦カウンセリング
      '2': ['id23'], // DV／モラハラ
      '3': ['id22'], // 修復
      '4': ['id24'], // 夫婦カウンセリング
      '5': ['id24'], // 夫婦カウンセリング
      '6': ['id20'], // 離婚
      '7': ['id21'], // 浮気
      '8': ['id25'], // その他 (離婚後の子育て、共同養育等)
    };
  }, []);

  // Theme to lawyer consultation field names mapping (from SearchSection)
  const themeLawyerConsultationMapping =
    useMemo<ThemeLawyerConsultationMap>(() => {
      return {
        '1': ['モラハラ', '男性弁護', '女性弁護'],
        '2': [
          '離婚手続き',
          '協議離婚',
          '離婚調停',
          '裁判離婚',
          '慰謝料',
          'DV',
          'モラハラ',
          '男性弁護',
          '女性弁護',
        ],
        '3': ['離婚準備', '不貞行為', 'モラハラ'],
        '4': [],
        '5': [
          '離婚準備',
          '離婚手続き',
          '協議離婚',
          '離婚調停',
          '裁判離婚',
          '慰謝料',
          '婚姻費用',
          '財産分与',
          '年金分割',
          '借金',
          '男性弁護',
          '女性弁護',
        ],
        '6': [
          '離婚準備',
          '離婚手続き',
          '協議離婚',
          '離婚調停',
          '裁判離婚',
          '婚姻費用',
        ],
        '7': [
          '離婚手続き',
          '協議離婚',
          '離婚調停',
          '裁判離婚',
          '不貞行為',
          '慰謝料',
          '男性弁護',
          '女性弁護',
        ],
        '8': [
          '離婚手続き',
          '協議離婚',
          '離婚調停',
          '裁判離婚',
          '親権',
          '面会交流',
          '養育費',
          '養育費回収',
          '男性弁護',
          '女性弁護',
        ],
      };
    }, []);

  // Fetch lawyer consultation data
  const { ids: consultationIds, entities: consultationEntities } =
    useFetchList<IListItem>({
      ...resourceQuery.consultations,
      customParams: {
        ...resourceQuery.consultations.customParams,
        providerType: [ProviderType.LAWYER],
      },
    });

  // Get counselor consultation IDs based on theme ID
  const counselorConsultationIds = useMemo(() => {
    if (!themeId) return [];
    return themeConsultationMapping[themeId as string] || [];
  }, [themeId, themeConsultationMapping]);

  // Get lawyer consultation IDs based on theme ID
  const lawyerConsultationIds = useMemo(() => {
    if (!themeId || !consultationIds || consultationIds.length === 0) return [];

    const consultationFieldNames =
      themeLawyerConsultationMapping[themeId as string];
    if (!consultationFieldNames) return [];

    // Map consultation field names to entity IDs (same logic as SearchSection)
    const entityIds = consultationFieldNames
      .map((fieldName) =>
        consultationIds.find(
          (consultationId) =>
            consultationEntities[consultationId]?.value?.trim() ===
            fieldName.trim(),
        ),
      )
      .filter((entityId): entityId is string => entityId !== undefined);

    return entityIds;
  }, [
    themeId,
    themeLawyerConsultationMapping,
    consultationIds,
    consultationEntities,
  ]);

  return (
    <Box>
      <Box component="section">
        <ThemeBanner key={`banner-${themeId}`} />
      </Box>
      <Box component="section">
        <ThemeDetailSection key={`detail-${themeId}`} />
      </Box>
      <Box component="section">
        <SearchSection />
      </Box>
      <Box
        component="section"
        id="experts-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <ExpertsSection
          counselorConsultationIds={counselorConsultationIds}
          lawyerConsultationIds={lawyerConsultationIds}
        />
      </Box>
      <Box
        component="section"
        id="howto-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <HowToSection />
      </Box>
      <Box
        component="section"
        id="themes-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <ThemesSection />
      </Box>
      <Box component="section">
        <CTASection />
      </Box>
      <Box
        component="section"
        id="pricing-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <PricingSection />
      </Box>
      <Box component="section">
        <CTASection />
      </Box>
    </Box>
  );
};

export const getServerSideProps: GetServerSideProps = async () => {
  const qClient = new QueryClient();

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

ThemePage.getLayout = function getLayout(page: ReactElement) {
  return <NewLayout>{page}</NewLayout>;
};

export default ThemePage;

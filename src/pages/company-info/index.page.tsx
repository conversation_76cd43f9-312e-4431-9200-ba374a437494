import {
  Box,
  Container,
  Divider,
  Grid,
  Stack,
  Typography,
} from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import Layout from 'components/Layout';
import { t } from 'i18n';
import { NextSeo } from 'next-seo';
import type { ReactElement } from 'react';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const CompanyInfo = () => {
  const companyInfo = [
    {
      label: t('companyField.name'),
      value: (
        <>
          <Typography fontSize={{ xs: 14, tablet: 16 }}>
            {t('companyInfo.name')}
          </Typography>
          <a
            href="https://relife-technology.co.jp"
            target="_blank"
            rel="noreferrer"
          >
            <Typography
              color="primary.main"
              fontSize={{ xs: 14, tablet: 16 }}
              marginTop="6px"
            >
              https://relife-technology.co.jp
            </Typography>
          </a>
        </>
      ),
    },
    {
      label: t('companyField.representative'),
      value: t('companyInfo.representative'),
    },
    {
      label: t('companyField.dateOfEstablishment'),
      value: t('companyInfo.dateOfEstablishment'),
    },
    {
      label: t('companyField.email'),
      value: t('companyInfo.email'),
      emailTo: true,
    },
    {
      label: t('companyField.location'),
      value: t('companyInfo.location'),
    },
    {
      label: t('companyField.businessContent'),
      value: t('companyInfo.businessContent'),
    },
    {
      label: '顧問',
      value: (
        <>
          <Typography fontSize={{ xs: 14, tablet: 16 }}>
            弁護士法人Authense法律事務所
          </Typography>
          <a href="https://www.authense.jp" target="_blank" rel="noreferrer">
            <Typography
              color="primary.main"
              fontSize={{ xs: 14, tablet: 16 }}
              marginTop="6px"
            >
              https://www.authense.jp
            </Typography>
          </a>
        </>
      ),
    },
  ];
  return (
    <Container maxWidth="lg" disableGutters sx={styles.container}>
      <NextSeo {...seoConfig.companyInfo} />
      <Box px={{ xs: 2, lg: 0 }}>
        <Breadcrumbs />
      </Box>
      <Container maxWidth="md" disableGutters sx={styles.containerInfo}>
        <Typography sx={styles.companyHeader}>
          {t('breadcrumb.companyInfo')}
        </Typography>
        <Box sx={styles.content}>
          <Stack rowGap={2} divider={<Divider />}>
            {companyInfo.map((data) => {
              return (
                <Grid
                  container
                  key={data.label}
                  columns={23}
                  columnSpacing={{ tablet: 7 }}
                >
                  <Grid
                    item
                    xs={8}
                    tablet={7}
                    fontWeight="bold"
                    fontSize={{ xs: 14, tablet: 16 }}
                  >
                    {data.label}
                  </Grid>
                  <Grid
                    item
                    xs
                    tablet
                    fontSize={{ xs: 14, tablet: 16 }}
                    whiteSpace={{ xs: 'pre-line', tablet: 'normal' }}
                  >
                    {data.emailTo ? (
                      <a href="mailto:<EMAIL>">
                        <Typography
                          color="primary.main"
                          fontSize={{ xs: 14, tablet: 16 }}
                        >
                          {data.value}
                        </Typography>
                      </a>
                    ) : (
                      data.value
                    )}
                  </Grid>
                </Grid>
              );
            })}
          </Stack>
        </Box>
      </Container>
    </Container>
  );
};

CompanyInfo.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export default CompanyInfo;

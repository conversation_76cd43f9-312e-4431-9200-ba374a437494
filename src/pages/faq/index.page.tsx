import { Box, Container, Stack, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import Layout from 'components/Layout';
import CollapseLayout from 'components/Layout/CollapseLayout';
import MuiLink from 'components/Link';
import { t } from 'i18n';
import { NextSeo } from 'next-seo';
import type { ReactElement } from 'react';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const Faq = () => {
  return (
    <Container maxWidth="lg" disableGutters sx={styles.container}>
      <NextSeo {...seoConfig.qAndA} />
      <Box px={{ xs: 2, lg: 0 }}>
        <Breadcrumbs />
      </Box>
      <Container maxWidth="md" disableGutters sx={styles.containerInfo}>
        <Typography sx={styles.faqHeader}>{t('breadcrumb.faq')}</Typography>
        <Stack spacing={{ xs: 1, tablet: 2 }}>
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer1')}
              </Typography>
            }
            label={t('faq.question1')}
            number="1."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer1a')}
              </Typography>
            }
            label={t('faq.question1a')}
            number="2."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer2')}
              </Typography>
            }
            label={t('faq.question2')}
            number="3."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer3')}
              </Typography>
            }
            label={t('faq.question3')}
            number="4."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer4')}
              </Typography>
            }
            label={t('faq.question4')}
            number="5."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer4a')}
              </Typography>
            }
            label={t('faq.question4a')}
            number="6."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer5')}
              </Typography>
            }
            label={t('faq.question5')}
            number="7."
          />
          <CollapseLayout
            content={
              <Stack spacing="12px">
                <Stack>
                  <Stack direction="row">
                    <Box maxWidth="32px" width="100%" textAlign="center">
                      <Typography sx={styles.textHeader}>{'•'}</Typography>
                    </Box>
                    <Typography sx={styles.textHeader}>
                      {t('faq.answer6Title1')}
                    </Typography>
                  </Stack>
                  <Typography
                    color="heading"
                    fontSize={{ xs: 14, tablet: 16 }}
                    pl={4}
                  >
                    <Stack
                      direction="row"
                      display="inherit"
                      whiteSpace="pre-wrap"
                    >
                      →docomo、au、softbankなど各キャリアのセキュリティ設定のためユーザー受信拒否と認識されているか、お客様が迷惑メール対策等で、ドメイン指定受信を設定されている場合に、メー
                      ルが正しく届かない場合があります。『
                      <a
                        href="mailto:@ricokatsu.jp"
                        style={{ color: '#FCAB28' }}
                      >
                        @ricokatsu.jp
                      </a>
                      』のドメインを受信できるように設定してください。
                    </Stack>
                  </Typography>
                </Stack>
                <Stack>
                  <Stack direction="row">
                    <Box maxWidth="32px" width="100%" textAlign="center">
                      <Typography sx={styles.textHeader}>{'•'}</Typography>
                    </Box>
                    <Typography sx={styles.textHeader}>
                      {t('faq.answer6Title2')}
                    </Typography>
                  </Stack>
                  <Typography
                    color="heading"
                    fontSize={{ xs: 14, tablet: 16 }}
                    pl={4}
                  >
                    {t('faq.answer6Content2')}
                  </Typography>
                </Stack>
              </Stack>
            }
            label={t('faq.question6')}
            number="8."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer7')}
              </Typography>
            }
            label={t('faq.question7')}
            number="9."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer8')}
              </Typography>
            }
            label={t('faq.question8')}
            number="10."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer9')}
              </Typography>
            }
            label={t('faq.question9')}
            number="11."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer10')}
              </Typography>
            }
            label={t('faq.question10')}
            number="12."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer11')}
              </Typography>
            }
            label={t('faq.question11')}
            number="13."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer12')}
              </Typography>
            }
            label={t('faq.question12')}
            number="14."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer13')}
              </Typography>
            }
            label={t('faq.question13')}
            number="15."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer14')}
              </Typography>
            }
            label={t('faq.question14')}
            number="16."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer15')}
              </Typography>
            }
            label={t('faq.question15')}
            number="17."
          />
          <CollapseLayout
            content={
              <>
                <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                  {t('faq.answer16')}
                </Typography>
                <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                  URL：
                  <MuiLink href="/contact-us" underline="hover">
                    {`${process.env.NEXT_PUBLIC_DOMAIN}/contact-us`}
                  </MuiLink>
                </Typography>
              </>
            }
            label={t('faq.question16')}
            number="18."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer17')}
              </Typography>
            }
            label={t('faq.question17')}
            number="19."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer18')}
              </Typography>
            }
            label={t('faq.question18')}
            number="20."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer19')}
              </Typography>
            }
            label={t('faq.question19')}
            number="21."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer20')}
              </Typography>
            }
            label={t('faq.question20')}
            number="22."
          />
          <CollapseLayout
            content={
              <Typography color="heading" fontSize={{ xs: 14, tablet: 16 }}>
                {t('faq.answer21')}
              </Typography>
            }
            label={t('faq.question21')}
            number="23."
          />
        </Stack>
      </Container>
    </Container>
  );
};

Faq.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export default Faq;

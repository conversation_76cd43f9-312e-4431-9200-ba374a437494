/* eslint-disable react-hooks/exhaustive-deps */
import Layout from 'components/Layout';
import LoadingOverlay from 'components/LoadingOverlay';
import { deleteCookie, getCookie } from 'cookies-next';
import useGlobalState from 'hooks/useGlobalState';
import { t } from 'i18next';
import type { ActionTds, IExtraTdsPayload } from 'models/booking/interface';
import { useRouter } from 'next/router';
import { type ReactElement, useCallback, useEffect } from 'react';
import { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

const Payment3DsCallback = () => {
  const { query, push, isReady } = useRouter();
  const webCookie = Helper.getWebCookieByGuest();
  const lawyerWebBookingUrl = getCookie('lawyer-web-booking-url');
  const { setConfirmModal } = useGlobalState();

  const { code, action, message, cep } = query as {
    code: string;
    action: ActionTds;
    meeting_id: string;
    message: string;
    cep: string;
  };

  const handleUpdateToken = useCallback(
    (url: string) => {
      if (webCookie?.guest_token) {
        Helper.setToken({
          token: webCookie?.guest_token as string,
          email: webCookie?.email as string,
          role: ROLES.CUSTOMER,
        });
        Helper.removeWebCookieByGuest();
        deleteCookie('lawyer-web-booking-url', { path: '/' });
      }

      push(url);
    },
    [push, webCookie?.email, webCookie?.guest_token],
  );

  useEffect(() => {
    if (!isReady) return;

    const extraPayload = JSON.parse(cep || '{}') as IExtraTdsPayload;

    // Error case: code 500
    if (code === '500') {
      let redirectDestination = '/';
      if (extraPayload.lawyerId && action === 'create_meeting_lawyer_3ds') {
        redirectDestination = `/customer/booking/${extraPayload.lawyerId}`;
        redirectDestination += '?tryInitDataLS=1';
      } else if (
        extraPayload.counselorId &&
        action === 'create_meeting_counselor_3ds'
      ) {
        redirectDestination = `/customer/counselor-booking/${extraPayload.counselorId}`;
        redirectDestination += '?tryInitDataLS=1';
      } else if (action === 'update_card_of_meeting_need_3ds') {
        redirectDestination = `/customer/my-page/cases/${
          extraPayload.caseId || ''
        }`;
      }

      setConfirmModal({
        icon: 'error',
        content: message || 'エラーが発生しました',
        confirmText: t('global.ok'),
        hideCancelButton: true,
        onConfirm: () =>
          push((lawyerWebBookingUrl || redirectDestination) as string),
        dialogContentTextProps: { sx: { fontSize: 16 } },
      });

      return;
    }

    // Code 200: case update_card_of_meeting_need_3ds
    if (action === 'update_card_of_meeting_need_3ds') {
      push(`/customer/my-page/cases/${extraPayload.caseId || ''}`);
      return;
    }

    // Code 200: case create_meeting_counselor_3ds | create_meeting_lawyer_3ds
    if (webCookie) {
      setConfirmModal({
        icon: 'success',
        content: t('booking.thankGuest'),
        confirmText: t('booking.enterFormGuest'),
        hideCancelButton: true,
        onConfirm: () => {
          setTimeout(() => {
            handleUpdateToken('/');
          }, 50);
        },
        dialogContentTextProps: { sx: { fontSize: 16 } },
      });
    } else if (!extraPayload.needFillForm) {
      setConfirmModal({
        icon: 'success',
        content: t('booking.thank'),
        confirmText: t('booking.seeList'),
        cancelText: t('global.backToHome'),
        onCancel: () => push('/'),
        onConfirm: () => {
          setTimeout(() => {
            handleUpdateToken(
              `/customer/my-page/cases${
                action === 'create_meeting_counselor_3ds'
                  ? '?tab=counselor'
                  : ''
              }`,
            );
          }, 50);
        },
        buttonLayout: 'vertical',
        dialogContentTextProps: {
          sx: {
            fontSize: 16,
            whiteSpace: { xs: 'pre-line', tablet: 'normal' },
          },
        },
      });
    } else {
      setConfirmModal({
        icon: 'success',
        content: t('booking.longThank'),
        confirmText: t('booking.enterForm'),
        hideCancelButton: true,
        onConfirm: () => {
          setTimeout(() => {
            handleUpdateToken('/customer/my-page/consultation-record');
          }, 50);
        },
        dialogContentTextProps: { sx: { fontSize: 16 } },
      });
    }
  }, [
    action,
    cep,
    code,
    isReady,
    lawyerWebBookingUrl,
    message,
    push,
    setConfirmModal,
    handleUpdateToken,
  ]);

  return (
    <>
      <LoadingOverlay visible={true}></LoadingOverlay>
    </>
  );
};

Payment3DsCallback.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export default Payment3DsCallback;

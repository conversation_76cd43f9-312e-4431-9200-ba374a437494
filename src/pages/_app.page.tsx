import 'react-toastify/dist/ReactToastify.css';
import '../styles/global.css';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import '../utils/yup.config';

import type { EmotionCache } from '@emotion/react';
import { CacheProvider } from '@emotion/react';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from '@mui/material/styles';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import type { DehydratedState } from '@tanstack/react-query';
import { Hydrate, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import ConfirmModal from 'components/ConfirmModal';
import DataProvider from 'components/DataProvider';
import FloatingLineButton from 'components/FloatingLineButton';
import LoginModal from 'components/LoginModal';
import NextNProgress from 'components/ProgressBar';
import { NavigationProvider } from 'context/NavigationContext';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import type { NextPage } from 'next';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { DefaultSeo } from 'next-seo';
import type { ReactElement, ReactNode } from 'react';
import { useState } from 'react';
import { ToastContainer } from 'react-toastify';
import createEmotionCache from 'styles/createEmotionCache';
import theme from 'styles/theme';
import queryClient from 'utils/queryClient';
import seoConfig from 'utils/seo.config';

dayjs.extend(utc);
dayjs.extend(timezone);

type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
  auth?: {
    roles: string[];
  };
};

type AppPropsWithLayout<P = {}> = {
  emotionCache?: EmotionCache;
  Component: NextPageWithLayout<P>;
} & AppProps<P>;

// Client-side cache, shared for the whole session of the user in the browser.
const clientSideEmotionCache = createEmotionCache();

// const variants = {
//   inactive: {
//     opacity: 1,
//     transition: {
//       duration: 0.5,
//       ease: 'easeInOut',
//     },
//   },
//   out: {
//     opacity: 0,
//     transition: {
//       duration: 0.5,
//       ease: 'easeInOut',
//     },
//   },
//   in: {
//     opacity: 0,
//     transition: {
//       duration: 0.5,
//       ease: 'easeInOut',
//     },
//   },
// };

function MyApp(
  props: AppPropsWithLayout<{ dehydratedState: DehydratedState }>,
) {
  const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;
  const getLayout = Component.getLayout ?? ((page) => page);

  const [qClient] = useState(queryClient);
  return (
    <CacheProvider value={emotionCache}>
      <Head>
        <meta name="viewport" content="initial-scale=1, width=device-width" />
      </Head>
      <DefaultSeo
        dangerouslySetAllPagesToNoIndex={
          process.env.NEXT_PUBLIC_DEPLOY_ENV !== 'production'
        }
        dangerouslySetAllPagesToNoFollow={
          process.env.NEXT_PUBLIC_DEPLOY_ENV !== 'production'
        }
        {...seoConfig.default}
      />
      <ThemeProvider theme={theme}>
        {/* Same as */}
        <QueryClientProvider client={qClient}>
          <Hydrate state={pageProps.dehydratedState}>
            {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
            <CssBaseline />
            <ToastContainer
              position="bottom-right"
              autoClose={3000}
              hideProgressBar
              theme="colored"
              closeButton={false}
            />
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              {/* Wrap with NavigationProvider */}
              <NavigationProvider>
                <DataProvider>
                  <NextNProgress />
                  {/* <AnimatePresence mode="wait"> */}
                  {getLayout(
                    // <motion.div
                    //   key={router.route}
                    //   initial="in"
                    //   animate="inactive"
                    //   exit="out"
                    //   variants={variants}
                    // >
                    <Component {...pageProps} />,
                    // </motion.div>,
                  )}
                  {/* </AnimatePresence> */}
                  <LoginModal />
                  <ConfirmModal />
                </DataProvider>
              </NavigationProvider>
            </LocalizationProvider>
          </Hydrate>
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </ThemeProvider>
      <FloatingLineButton />
    </CacheProvider>
  );
}

export default MyApp;

import {
  Box,
  Button,
  Container,
  Divider,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import ActionSection from 'components/Article/ActionSection';
import ArticleDetail from 'components/Article/ArticleDetail';
import OtherArticleList from 'components/Article/ArticleList/OtherArticleList';
import Breadcrumbs from 'components/Breadcrumbs';
import LawyerInfoCard from 'components/LawyerDetail/LawyerInfoCard';
import Layout from 'components/Layout';
import { useFetchDetail, useFetchList } from 'hooks';
import type { IPickupArticleDetail, IPickupArticleListItem } from 'hooks/types';
import i18n from 'i18n';
import { ArrowRightIcon } from 'icons';
import { isEmpty } from 'lodash';
import articleQuery from 'models/article/query';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import type { ReactElement } from 'react';
import api from 'utils/api';
import { ARTICLE_SORT_ORDER } from 'utils/constants';
import Helper from 'utils/helpers';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const PickupArticleDetail = () => {
  const { t } = i18n;
  const { query } = useRouter();

  const articleId = query.articleId as string;
  const fetchPickupArticleDetail = useFetchDetail(
    articleQuery.pickupArticleDetail(articleId),
  );
  const fetchPickupArticles = useFetchList({
    ...articleQuery.pickupArticleList,
    customParams: {
      sort: ARTICLE_SORT_ORDER,
    },
  });
  const pickupArticleDetail =
    fetchPickupArticleDetail.detail as IPickupArticleDetail;
  const pickupArticleList = (fetchPickupArticles.list ||
    []) as IPickupArticleListItem[];
  const otherPickupArticles = pickupArticleList.filter(
    (pickupArticle) => pickupArticle._id !== articleId,
  );
  const { isLoading } = fetchPickupArticles;

  return (
    <Box>
      <NextSeo {...seoConfig.pickupArticleDetail(pickupArticleDetail)} />
      <Container
        maxWidth="ex"
        disableGutters
        sx={{
          mt: 2,
          mb: { xs: 1, tablet: 4 },
          minHeight: 'calc(100vh - 375px)',
        }}
      >
        <Box px={{ xs: 2, ex: 0 }}>
          <Breadcrumbs
            omitIndexList={[2]}
            transformLabel={{
              '/pickup-articles/[articleId]': pickupArticleDetail.title || (
                <Skeleton variant="text" width={100} />
              ),
            }}
          />
        </Box>
        <Stack
          direction={{ sl: 'row' }}
          mt={{ xs: 2, tablet: 4 }}
          gap={{ xs: 1, tablet: 2 }}
        >
          <Box flex={1}>
            <Box
              flex={1}
              borderRadius={{ sl: 2 }}
              p={{ xs: 2, tablet: 4 }}
              bgcolor="white"
            >
              <ArticleDetail data={pickupArticleDetail} />
            </Box>
          </Box>
          <Box sx={styles.rightSection}>
            {pickupArticleDetail.providers?.length >= 1 ? (
              <LawyerInfoCard data={pickupArticleDetail.providers} />
            ) : null}
            {(!isEmpty(otherPickupArticles) || isLoading) && (
              <Box
                sx={styles.pickupArticleList}
                pb={otherPickupArticles?.length > 4 ? '12px' : 3}
              >
                <Typography
                  fontWeight="bold"
                  mb={{ xs: 2, tablet: 4 }}
                  color="heading"
                >
                  {t('articleList.otherArticleList')}
                </Typography>
                <OtherArticleList
                  loading={isLoading}
                  data={otherPickupArticles.slice(0, 3)}
                  cellStyle={{ alignItems: 'start' }}
                  pathname="/pickup-articles/[articleId]"
                />
                {otherPickupArticles?.length > 4 && (
                  <>
                    <Divider sx={{ mt: { xs: 2, tablet: 3 } }} />
                    <Box
                      display="flex"
                      justifyContent="center"
                      mt={{ xs: '4px', tablet: '12px' }}
                      mb={{ xs: '-12px', tablet: -1 }}
                    >
                      <Link
                        passHref
                        href={{
                          pathname: '/pickup-articles',
                        }}
                        legacyBehavior
                      >
                        <Button endIcon={<ArrowRightIcon />}>
                          {t('global.seeMore')}
                        </Button>
                      </Link>
                    </Box>
                  </>
                )}
              </Box>
            )}
          </Box>
        </Stack>
      </Container>

      <ActionSection />
    </Box>
  );
};

PickupArticleDetail.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export async function getStaticPaths() {
  const { data } = await api.get(articleQuery.pickupArticleList.apiUrl, {
    params: { limit: 9999, sort: ARTICLE_SORT_ORDER },
  });
  return {
    paths: data.docs.map((doc: { _id: string }) => ({
      params: { articleId: doc._id },
    })),
    fallback: 'blocking',
  };
}

export async function getStaticProps({ params }: any) {
  try {
    const qClient = new QueryClient();
    const articleId = params.articleId as string;
    await Helper.prefetchDetail(
      articleQuery.pickupArticleDetail(articleId),
      qClient,
    );

    return {
      props: {
        dehydratedState: dehydrate(qClient),
      },
      revalidate: 1,
    };
  } catch {
    return { notFound: true, revalidate: 1 };
  }
}

export default PickupArticleDetail;

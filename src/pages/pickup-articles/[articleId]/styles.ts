import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  pickupArticleList: {
    borderRadius: { sl: 2 },
    p: { xs: 2, tablet: '24px 32px' },
    bgcolor: 'white',
    '.article-item': {
      '.article-title': {
        fontSize: { xs: 14, tablet: 16 },
        fontWeight: 500,
      },
    },
  },
  rightSection: {
    maxWidth: { sl: 384 },
    width: 1,
    '.lawyer-info-card': {
      borderRadius: { xs: 0, sl: 2 },
      mb: { xs: 1, tablet: 2 },
    },
  },
  actions: {
    p: 4,
    display: 'flex',
    justifyContent: 'center',
    bgcolor: 'white',
  },
  actionText: {
    fontSize: { xs: 14, tablet: 16 },
    fontWeight: 500,
    color: 'heading',
  },
  actionButton: {
    mt: 2,
    width: { xs: 311, tablet: 240 },
    '.MuiButton-startIcon': {
      mr: { xs: 9, tablet: 2 },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

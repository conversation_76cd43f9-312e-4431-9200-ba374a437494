import { Box, Divider } from '@mui/material';
import ArticleList from 'components/Article/ArticleList';
import TopArticleList from 'components/Article/ArticleList/TopArticleList';
import ListPagination from 'components/CustomPagination/ListPagination';
import Layout from 'components/Layout';
import ArticleListLayout from 'components/Layout/ArticleListLayout';
import BorderLabel from 'components/UI/BorderLabel';
import { useFetchList } from 'hooks';
import i18n from 'i18n';
import type { IArticleListItem } from 'models/article/interface';
import articleQuery from 'models/article/query';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useMemo } from 'react';
import { ARTICLE_SORT_ORDER } from 'utils/constants';

const PickupArticleListPage = () => {
  const { t } = i18n;
  const { query, isReady } = useRouter();

  const { page } = query;
  const {
    total,
    isLoading,
    list: articleList,
  } = useFetchList<IArticleListItem>({
    ...articleQuery.pickupArticleList,
    customParams: { sort: ARTICLE_SORT_ORDER },
    keepPreviousData: false,
  });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const { list: firstPageArticleList } = useFetchList<IArticleListItem>({
    ...articleQuery.pickupArticleList,
    staleTime: Infinity,
    customParams: { page: 1, limit: 10, sort: ARTICLE_SORT_ORDER },
  });

  const displayArticles = useMemo(() => {
    if (isReady) {
      if (page) {
        return articleList;
      }
      if (firstPageArticleList.length < 4) {
        return firstPageArticleList.slice(1, 4);
      }
      return firstPageArticleList.slice(4, 10);
    }
    return [];
  }, [articleList, firstPageArticleList, isReady, page]);

  const topArticles = useMemo(() => {
    if (firstPageArticleList.length >= 4) {
      return firstPageArticleList.slice(0, 4);
    }
    return firstPageArticleList.slice(0, 1);
  }, [firstPageArticleList]);

  return (
    <Box>
      <Box
        p={{ xs: 2, tablet: 4 }}
        mt={{ xs: 1, tablet: 2 }}
        borderRadius={{ xs: 0, md: 2 }}
        bgcolor="white"
      >
        <BorderLabel>{t('home.pickupArticle')}</BorderLabel>
        <Box mt={{ xs: 2, tablet: 4 }}>
          <TopArticleList
            showLawyer={false}
            pathname="/pickup-articles/[articleId]"
            data={topArticles}
            loading={isLoading}
          />
        </Box>
        {(isLoading || ![0, 1, 4].includes(firstPageArticleList.length)) && (
          <Divider sx={{ m: { xs: '20px 0px 16px', tablet: '24px 0px' } }} />
        )}
        <ArticleList
          divider
          spacing={{ xs: 2, tablet: 3 }}
          pathname="/pickup-articles/[articleId]"
          data={displayArticles}
          loading={isLoading}
        />
      </Box>
      {total > 0 && (
        <Box display="flex" justifyContent="center" mt={{ xs: 2, tablet: 5 }}>
          <ListPagination total={total} />
        </Box>
      )}
    </Box>
  );
};

PickupArticleListPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout showMedia>
      <ArticleListLayout>{page}</ArticleListLayout>
    </Layout>
  );
};
export default PickupArticleListPage;

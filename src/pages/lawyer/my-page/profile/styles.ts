import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  changeContainer: {
    bgcolor: '#edf1f3',
    py: 1,
    mx: 4,
    display: 'flex',
    justifyContent: { xs: ' flex-start', tablet: 'center' },
    borderRadius: { xs: '4px', tablet: '6px' },
    alignItems: 'center',
    svg: {
      color: 'icon',
      width: { xs: 16, tablet: 20 },
      height: { xs: 16, tablet: 20 },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

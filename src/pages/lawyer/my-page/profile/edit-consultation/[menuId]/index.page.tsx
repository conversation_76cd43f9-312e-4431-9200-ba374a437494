import { Box, Stack, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import ConsultationMenuForm from 'components/Lawyer/ConsultationMenuForm';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import BackButton from 'components/UI/BackButton';
import apiQuery from 'hooks/apiQuery';
import useFetchDetail from 'hooks/useFetchDetail';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import type {
  IConsultationItem,
  ICreateConsultation,
  IUpdateConsultation,
} from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { ROLES } from 'utils/constants';

const EditConsultation = () => {
  const { back, query } = useRouter();
  const menuId = query.menuId as string;

  const { mutateAsync: updateConsultation, isLoading } =
    useMutate<IUpdateConsultation>(consultationQuery.update(menuId));
  const fetchDetail = useFetchDetail(apiQuery.consultationMenuDetail(menuId));
  const detail = fetchDetail.detail as IConsultationItem;

  const handleCancelEdit = () => {
    back();
  };

  const handleUpdateConsultation = (
    values: ICreateConsultation,
    isDirty: boolean,
  ) => {
    if (isDirty) {
      updateConsultation(
        { ...values, _id: menuId as string },
        {
          onSuccess: () => {
            back();
            fetchDetail.refetch();
          },
        },
      );
    } else {
      back();
    }
  };

  return (
    <div>
      <Box p="16px 32px" borderRadius={2} bgcolor="white">
        <Breadcrumbs omitIndexList={[0, 3]} />
        <Stack direction="row" mt="11px">
          <Box m="5px 4px 0px -4px">
            <BackButton />
          </Box>
          <Typography fontSize={32} fontWeight="bold" color="heading">
            {t('breadcrumb.consultationMenu')}
          </Typography>
        </Stack>
      </Box>
      <ConsultationMenuForm
        loading={isLoading}
        defaultValues={detail}
        onCancel={handleCancelEdit}
        onSubmit={handleUpdateConsultation}
      />
    </div>
  );
};

EditConsultation.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.LAWYER}>{page}</SideMenuLayout>
    </Layout>
  );
};
export default EditConsultation;

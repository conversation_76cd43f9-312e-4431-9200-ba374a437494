import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Divider,
  <PERSON><PERSON><PERSON><PERSON>on,
  <PERSON>ack,
  Typography,
} from '@mui/material';
import type { IUnitPriceForm } from 'components/Case/UnitPriceForm';
import UnitPriceForm from 'components/Case/UnitPriceForm';
import CustomTable from 'components/CustomTable';
import TruncateText from 'components/TruncateText';
import EmptyComponent from 'components/UI/EmptyComponent';
import { useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { AddIcon, EditIcon, InfoIcon, SheetIcon, TrashIcon } from 'icons';
import type { MRT_ColumnDef } from 'material-react-table';
import type {
  IAddUnitPrice,
  IConsultationItem,
  IDeleteConsultation,
} from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import Link from 'next/link';
import React, { useEffect, useMemo, useState } from 'react';

import styles from './styles';

const ConsultationList = () => {
  const { setConfirmModal } = useGlobalState();
  const [editPriceMode, setEditPriceMode] = useState(false);
  const { mutateAsync: deleteConsultation } = useMutate<IDeleteConsultation>(
    consultationQuery.delete,
  );
  const { mutateAsync: addUnitPrice } = useMutate<IAddUnitPrice>(
    consultationQuery.addUnitPrice,
  );

  const {
    list = [],
    total,
    isFetching,
    isLoading,
    refetch,
  } = useFetchList({ ...apiQuery.consultationMenu, omitKeys: ['tab'] });

  useEffect(() => {
    if (total === 0) {
      setEditPriceMode(false);
    }
  }, [total]);

  const consultationList = list as IConsultationItem[];
  const unitPrices = useMemo(
    () => consultationList.find((item) => item.unitPrices)?.unitPrices || [],
    [consultationList],
  );

  const handleAddUnitPrice = async (values: IUnitPriceForm) => {
    await addUnitPrice(
      {
        unitPrices: [{ price: values.price, duration: 30 }],
      },
      {
        onSuccess: async () => {
          await refetch();
          setEditPriceMode(false);
        },
      },
    );
  };

  const columns = [
    {
      accessorKey: 'title',
      header: t('consultationMenu.title'),
      enableSorting: false,
      enableResizing: true,
      Cell: ({ cell }) => {
        return (
          <TruncateText
            text={cell.getValue() as string}
            textProps={{
              sx: { fontWeight: 500, color: 'heading' },
            }}
          />
        );
      },
    },
    {
      accessorKey: 'content',
      header: t('consultationMenu.content'),
      enableSorting: false,
      Cell: ({ cell }) => {
        return <TruncateText text={cell.getValue() as string} />;
      },
    },
    {
      accessorKey: 'action',
      header: '',
      maxSize: 100,
      columnDefType: 'display',
      Cell: ({ row }) => {
        return (
          <Stack direction="row" justifyContent="flex-end" mr={-1}>
            <Link
              href={`/lawyer/my-page/profile/edit-consultation/${row.id}`}
              legacyBehavior
            >
              <IconButton sx={{ svg: { color: 'primary.main' }, mr: -1 }}>
                <EditIcon />
              </IconButton>
            </Link>
            <IconButton
              sx={{ svg: { color: 'neutral7' } }}
              onClick={() =>
                setConfirmModal({
                  onConfirm: () => {
                    deleteConsultation(
                      { _id: row.id as string },
                      {
                        onSuccess: () => refetch(),
                      },
                    );
                  },
                  title: t('consultationMenu.deleteTitle'),
                  content: t('consultationMenu.deleteContent'),
                })
              }
            >
              <TrashIcon />
            </IconButton>
          </Stack>
        );
      },
    },
  ] as MRT_ColumnDef<{ title: string; content?: string }>[];

  return (
    <div>
      <Container maxWidth="md" disableGutters>
        <UnitPriceForm
          total={total}
          onSubmit={handleAddUnitPrice}
          defaultValues={unitPrices[0]}
          editPriceMode={editPriceMode}
          setEditPriceMode={(mode) => setEditPriceMode(mode)}
        />
        <Box bgcolor="white" borderRadius={2}>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            p="32px 32px 16px"
            mt={2}
          >
            <Stack direction="row" spacing={1} alignItems="center">
              <SheetIcon />
              <Typography fontSize={24} fontWeight={500} color="heading">
                {t('breadcrumb.consultationMenu')}
              </Typography>
            </Stack>
            <Link
              href="/lawyer/my-page/profile/add-consultation"
              legacyBehavior
            >
              <Button
                variant="tertiary"
                sx={{ maxWidth: 96 }}
                fullWidth
                startIcon={<AddIcon />}
                color="primary"
                disabled={total === 10}
              >
                {t('global.add')}
              </Button>
            </Link>
          </Box>
          {total === 10 && (
            <Box sx={styles.changeContainer}>
              <Stack direction="row" spacing={1}>
                <InfoIcon />
                <Typography
                  color="neutral6"
                  fontWeight={500}
                  fontSize={{ xs: 12, tablet: 14 }}
                  flex={1}
                >
                  {t('consultationMenu.warning')}
                </Typography>
              </Stack>
            </Box>
          )}
          {total > 0 && <Divider sx={{ mt: total === 10 ? 3 : 2, mb: -2 }} />}
          {(isLoading || total > 0) && (
            <CustomTable
              total={total}
              isFetching={isFetching}
              isLoading={isLoading}
              data={list as {}[]}
              showPagination={false}
              columns={columns as MRT_ColumnDef<Record<string, any>>[]}
            />
          )}
          {!isLoading && total === 0 && (
            <EmptyComponent text={t('consultationMenu.empty')} />
          )}
        </Box>
      </Container>
    </div>
  );
};

export default ConsultationList;

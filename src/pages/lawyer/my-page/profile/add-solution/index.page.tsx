import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import type { ReactElement } from 'react';
import ProviderAddSolution from 'shared/provider/my-page/add-solution';
import { ROLES } from 'utils/constants';

const AddSolutionPage = () => {
  return <ProviderAddSolution />;
};

AddSolutionPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.LAWYER}>{page}</SideMenuLayout>
    </Layout>
  );
};

export default AddSolutionPage;

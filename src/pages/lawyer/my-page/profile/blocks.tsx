import { Chip, Stack } from '@mui/material';
import type { IBlockField } from 'components/UI/ViewEditBlock';
import type { IListItem } from 'hooks/types';
import { t } from 'i18n';
import { BuildingIcon, RingIcon } from 'icons';
import { isEmpty } from 'lodash';
import type { ReactNode } from 'react';
import type { RadioOptionsType } from 'utils/constants';
import { RadioOptions } from 'utils/constants';

export interface IBlock {
  title: string;
  icon: ReactNode;
  fields: IBlockField[];
}
export const profileBlock: {
  title: string;
  icon: ReactNode;
  fields: IBlockField[];
} = {
  title: t('lawyerProfile.officeInfo'),
  icon: <BuildingIcon />,
  fields: [
    {
      label: t('lawyerProfile.officeName'),
      path: 'office.fullName',
    },
    {
      label: t('lawyerProfile.association'),
      path: 'barAssociation',
    },
    {
      label: t('lawyerProfile.focusArea'),
      path: 'consultationField',
      renderValue: (value: IListItem[]) => {
        if (isEmpty(value)) {
          return null;
        }
        return (
          <Stack direction="row" gap={1} flexWrap="wrap">
            {value.map((i) => (
              <Chip key={i._id} label={i.value} />
            ))}
          </Stack>
        );
      },
    },
    {
      label: t('lawyerProfile.attribute'),
      path: 'attribute',
      renderValue: (value: IListItem[]) => {
        if (isEmpty(value)) {
          return null;
        }
        return (
          <Stack direction="row" gap={1} flexWrap="wrap">
            {value.map((i) => (
              <Chip key={i._id} label={i.value} />
            ))}
          </Stack>
        );
      },
    },
  ],
};

export const divorceBlock: IBlock = {
  title: t('lawyerProfile.divorceInfo'),
  icon: <RingIcon />,
  fields: [
    {
      label: 'lawyerProfile.marriageHistory',
      path: 'marriageHistory',
      renderValue: (value: RadioOptionsType) => RadioOptions[value],
    },
    {
      label: 'lawyerProfile.divorceHistory',
      path: 'divorceHistory',
      renderValue: (value: RadioOptionsType) => RadioOptions[value],
    },
    {
      label: 'lawyerProfile.children',
      path: 'children',
      renderValue: (value: RadioOptionsType) => RadioOptions[value],
    },
  ],
};

import { Box, Button, Stack, Typography } from '@mui/material';
import ListPagination from 'components/CustomPagination/ListPagination';
import SkeletonList from 'components/SolutionCases/SkeletonList';
import SolutionCaseItem from 'components/SolutionCases/SolutionCaseItem';
import EmptyComponent from 'components/UI/EmptyComponent';
import { LayoutGroup, motion } from 'framer-motion';
import apiQuery from 'hooks/apiQuery';
import useFetchList from 'hooks/useFetchList';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { AddIcon, BookmarkIcon } from 'icons';
import { get, isEmpty } from 'lodash';
import type { IDeleteSolution, ISolutionItem } from 'models/solution/interface';
import solutionQuery from 'models/solution/query';
import { useRouter } from 'next/router';
import { useCallback } from 'react';

const SolutionList = () => {
  const { push, replace } = useRouter();
  const { setConfirmModal } = useGlobalState();
  const { mutateAsync: deleteSolution } = useMutate<IDeleteSolution>(
    solutionQuery.delete,
  );
  const fetchSolutionList = useFetchList({
    ...apiQuery.solutionList,
    omitKeys: ['tab'],
    keepPreviousData: false,
  });
  const {
    refetch: refetchList,
    isLoading: fetchSolutionLoading,
    total,
  } = fetchSolutionList;
  const list = fetchSolutionList.list as ISolutionItem[];

  const handleDeleteSolutionCase = useCallback(
    (_id: string) => {
      setConfirmModal({
        onConfirm: () => {
          deleteSolution(
            { _id },
            {
              onSuccess: async () => {
                const result = await refetchList();
                if (isEmpty(get(result, 'data.docs'))) {
                  replace('/lawyer/my-page/profile?tab=solutions');
                }
              },
            },
          );
        },
        title: 'deleteSolution.title',
        content: 'deleteSolution.message',
      });
    },
    [deleteSolution, refetchList, replace, setConfirmModal],
  );

  const handleEditSolution = useCallback(
    (_id: string) => {
      push(`/lawyer/my-page/profile/edit-solution/${_id}`);
    },
    [push],
  );

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p="16px 32px"
        bgcolor="white"
        borderRadius={2}
        mt={2}
        mb={2}
      >
        <Stack direction="row" spacing={1} alignItems="center">
          <BookmarkIcon />
          <Typography fontSize={24} fontWeight={500} color="heading">
            {t('lawyerProfile.solution')}
          </Typography>
        </Stack>
        <Button
          variant="tertiary"
          onClick={() => push('/lawyer/my-page/profile/add-solution')}
          sx={{ maxWidth: 96, svg: { color: 'primary.main' } }}
          fullWidth
          startIcon={<AddIcon />}
          color="primary"
        >
          {t('global.add')}
        </Button>
      </Box>
      {fetchSolutionLoading && <SkeletonList />}
      {total === 0 && !fetchSolutionLoading && (
        <EmptyComponent text={t('solutionCase.noData')} />
      )}
      <LayoutGroup>
        {!fetchSolutionLoading && !isEmpty(list) && (
          <Stack spacing={2}>
            {(list || []).map((solution) => (
              <motion.div layout key={solution._id}>
                <SolutionCaseItem
                  onDelete={handleDeleteSolutionCase}
                  onEdit={handleEditSolution}
                  data={solution}
                />
              </motion.div>
            ))}
          </Stack>
        )}

        {total > 0 && (
          <motion.div layout key="pagination">
            <Box display="flex" justifyContent="center" mt={4}>
              <ListPagination total={total} limit={5} />
            </Box>
          </motion.div>
        )}
      </LayoutGroup>
    </Box>
  );
};

export default SolutionList;

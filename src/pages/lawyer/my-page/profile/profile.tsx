import { Box, Typography } from '@mui/material';
import VisibleMotion from 'components/Animation/VisibleMotion';
import DivorceInfoForm from 'components/Lawyer/DivorceInfoForm';
import OfficeInfoForm from 'components/Lawyer/OfficeInfoForm';
import ProfileDetail from 'components/Provider/Profile/BasicProfileView';
import LawyerProfileForm from 'components/Provider/Profile/LawyerProfileForm';
import type { LawyerProfileFormValues } from 'components/Provider/Profile/LawyerProfileForm/schema';
import ViewEditBlock from 'components/UI/ViewEditBlock';
import dayjs from 'dayjs';
import { useFetchUser } from 'hooks';
import type { DivorcePayload, JobPayload } from 'hooks/lawyer/useProfile/types';
import type { LawyerData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { get, pick } from 'lodash';
import providerQuery from 'models/provider/query';
import { useCallback, useMemo, useState } from 'react';
import { RadioOptionsType } from 'utils/constants';
import Helper from 'utils/helpers';

import { divorceBlock, profileBlock } from './blocks';

const BasicInfo = () => {
  const { data: currentUser, refetch } = useFetchUser<LawyerData>({
    enabled: true,
  });
  const attribute = get(currentUser, 'attribute', []);
  const consultationField = get(currentUser, 'consultationField', []);

  const [editForm, setEditForm] = useState<string[]>([]);
  const { mutateAsync: updateProfile, isLoading: isUpdatingProfile } =
    useMutate(providerQuery.updateProfile);
  const { mutateAsync: updateJob, isLoading: isUpdatingJob } = useMutate(
    providerQuery.updateJob,
  );
  const { mutateAsync: updateDivorce, isLoading: isUpdatingDivorce } =
    useMutate(providerQuery.updateDivorce);

  const handleEditForm = useCallback((form: string) => {
    setEditForm((forms) => forms.concat(form));
  }, []);

  const { setConfirmModal } = useGlobalState();

  const closeForm = useCallback((formName: string) => {
    setEditForm((forms) => forms.filter((form) => form !== formName));
  }, []);

  const handleCancelEdit = useCallback(
    (isDirty: boolean, formName: string) => {
      if (isDirty) {
        setConfirmModal({
          onConfirm: () => closeForm(formName),
          title: 'discardInput.title',
          content: 'discardInput.message',
        });
      } else {
        closeForm(formName);
      }
    },
    [closeForm, setConfirmModal],
  );

  const handleUpdateProfile = (
    values: LawyerProfileFormValues,
    isDirty: boolean,
  ) => {
    if (isDirty) {
      const params = {
        ...values,
        hasOnlineSupport: values.hasOnlineSupport === 'yes',
        images: values.images.map((image) => image.key),
        birthday: dayjs(values.birthday).format('YYYY-MM-DDTHH:mm:ss.sss[Z]'),
      };
      updateProfile(params, {
        onSuccess: () => {
          refetch();
          closeForm('profile-form');
        },
      });
    } else {
      closeForm('profile-form');
    }
  };

  const handleUpdateJob = (values: JobPayload, isDirty: boolean) => {
    if (isDirty) {
      updateJob(values, {
        onSuccess: () => {
          refetch();
          closeForm('job-form');
        },
      });
    } else {
      closeForm('job-form');
    }
  };

  const handleUpdateDivorce = (values: DivorcePayload, isDirty: boolean) => {
    if (isDirty) {
      updateDivorce(values, {
        onSuccess: () => {
          refetch();
          closeForm('divorce-form');
        },
      });
    } else {
      closeForm('divorce-form');
    }
  };

  const profileDefaultValues = useMemo(
    () => ({
      ...pick(currentUser, [
        'images',
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'birthday',
        'catchphrase',
        'introduction',
      ]),
      hasOnlineSupport: currentUser?.hasOnlineSupport
        ? RadioOptionsType.yes
        : RadioOptionsType.no,
      videoUrl: currentUser?.videoUrl?.value,
    }),
    [currentUser],
  );

  const divorceDefaultValues = useMemo(
    () => pick(currentUser, ['children', 'marriageHistory', 'divorceHistory']),
    [currentUser],
  );

  const jobDefaultValues = useMemo(
    () => ({
      ...pick(currentUser, ['barAssociation']),
      consultationField: consultationField.map((field) => field._id),
      attribute: attribute.map((field) => field._id),
    }),
    [attribute, consultationField, currentUser],
  ) as JobPayload;

  return (
    <Box>
      <Box maxWidth="md" p={4} bgcolor="white" borderRadius={2} mt={2}>
        {!editForm.includes('profile-form') ? (
          <VisibleMotion key="profile-detail">
            <ProfileDetail
              data={currentUser}
              onEdit={() => handleEditForm('profile-form')}
            />
          </VisibleMotion>
        ) : (
          <VisibleMotion key="profile-form">
            <LawyerProfileForm
              data={currentUser}
              onSubmit={(values, isDirty) =>
                handleUpdateProfile(values, isDirty)
              }
              loading={isUpdatingProfile}
              defaultValues={profileDefaultValues}
              onCancel={(isDirty) => handleCancelEdit(isDirty, 'profile-form')}
            />
          </VisibleMotion>
        )}
      </Box>
      <Box>
        <ViewEditBlock
          editMode={editForm.includes('job-form')}
          onEdit={() => handleEditForm('job-form')}
          detail={currentUser}
          block={profileBlock}
          formId="job-form"
        >
          <OfficeInfoForm
            loading={isUpdatingJob}
            defaultValues={jobDefaultValues}
            onSubmit={handleUpdateJob}
            detail={{ fullName: currentUser?.office.fullName || '' }}
            onCancel={(isDirty) => handleCancelEdit(isDirty, 'job-form')}
          />
        </ViewEditBlock>
        <ViewEditBlock
          editMode={editForm.includes('divorce-form')}
          onEdit={() => handleEditForm('divorce-form')}
          detail={currentUser}
          block={divorceBlock}
          formId="divorce-form"
          detailRender={
            Helper.checkIsEmptyObject(
              pick(currentUser, [
                'children',
                'divorceHistory',
                'marriageHistory',
              ]),
            ) ? (
              <Typography color="hint">{t('global.hasNoInfo')}</Typography>
            ) : null
          }
        >
          <DivorceInfoForm
            loading={isUpdatingDivorce}
            defaultValues={divorceDefaultValues}
            onSubmit={handleUpdateDivorce}
            onCancel={(isDirty) => handleCancelEdit(isDirty, 'divorce-form')}
          />
        </ViewEditBlock>
      </Box>
    </Box>
  );
};

export default BasicInfo;

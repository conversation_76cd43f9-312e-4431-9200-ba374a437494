import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import type { ReactElement } from 'react';
import React from 'react';
import ProviderCaseList from 'shared/provider/my-page/cases';
import { ProviderType, ROLES } from 'utils/constants';

const CaseList = () => {
  return <ProviderCaseList providerType={ProviderType.LAWYER} />;
};

CaseList.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.LAWYER}>{page}</SideMenuLayout>
    </Layout>
  );
};

export default CaseList;

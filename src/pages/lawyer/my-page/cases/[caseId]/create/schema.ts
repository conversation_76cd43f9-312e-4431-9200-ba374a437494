import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { t } from 'i18n';
import { isNaN } from 'lodash';
import { MeetingType, Regex } from 'utils/constants';
import type { InferType } from 'yup';
import { mixed, number, object, string } from 'yup';

dayjs.extend(isSameOrAfter);

const schema = object({
  type: mixed<MeetingType>().oneOf(Object.values(MeetingType)).required(),
  duration: number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .required(),
  finalizedDate: string()
    .nullable()
    .required()
    .test('validDay', t('validation.invalidField'), (value) => {
      if (!dayjs(value).isSameOrAfter(dayjs(), 'minute')) {
        return false;
      }
      return true;
    }),
  discount: number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .min(0, t('validation.invalidField')),
  method: string().required(),
  meetingUrl: string().when(['type'], {
    is: (type: MeetingType) => type === MeetingType.ONLINE,
    then: string()
      .required()
      .test('validUrl', t('validation.invalidUrl'), (value) => {
        if (value) {
          return Regex.URL.test(value);
        }
        return true;
      }),
  }),
});

export const updatePaymentMethodSchema = object({
  method: string().required(),
});
export const updateHasFinalDateMeetingSchema = ({
  finalizedDate,
}: {
  finalizedDate: string;
}) =>
  schema.shape({
    finalizedDate: string()
      .nullable()
      .required()
      .test('validDay', t('validation.invalidField'), (value) => {
        if (
          dayjs(finalizedDate) < dayjs() &&
          dayjs(value).isSameOrAfter(dayjs(finalizedDate), 'minute')
        ) {
          return true;
        }
        if (
          dayjs(finalizedDate) >= dayjs() &&
          dayjs(value).isSameOrAfter(dayjs(), 'minute')
        ) {
          return true;
        }
        return false;
      }),
  });

export type CreateMeetingValues = InferType<typeof schema>;
export type UpdatePaymentMethodValues = InferType<typeof schema>;
export default schema;

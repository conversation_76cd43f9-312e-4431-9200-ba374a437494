import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Divider, Grid, Stack, Typography } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import CouponTag from 'components/Case/CouponTag';
import EditFinishedMeeting from 'components/Case/EditFInishedMeeting';
import PaymentTooltipButton from 'components/Case/PaymentTooltipButton';
import { DateTimePicker, Radio, Select, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import NumberField from 'components/Form/NumberField';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import ButtonBack from 'components/UI/BackButton';
import dayjs from 'dayjs';
import { AnimatePresence, motion } from 'framer-motion';
import {
  useDeepCompareEffect,
  useFetchDetail,
  useFetchList,
  useFetchUser,
} from 'hooks';
import type { LawyerData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import useHookForm from 'hooks/useHookForm';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { BackIcon, PaymentIcon, SheetIcon } from 'icons';
import { get } from 'lodash';
import type {
  ICreateMeeting,
  IMeetingsItem,
  IProviderCaseDetail,
} from 'models/case/interface';
import caseQuery from 'models/case/query';
import type { IConsultationItem } from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useEffect, useMemo } from 'react';
import { useWatch } from 'react-hook-form';
import {
  CaseStatusType,
  MEETING_DURATION,
  MeetingStatusType,
  MeetingType,
  PAYMENT_METHOD_OPTIONS,
  PaymentStatusType,
  ROLES,
} from 'utils/constants';
import errors from 'utils/errors';
import Helper from 'utils/helpers';

import type { CreateMeetingValues } from './schema';
import schema, {
  updateHasFinalDateMeetingSchema,
  updatePaymentMethodSchema,
} from './schema';
import styles from './styles';

const CreateMeetingPage = () => {
  const { setConfirmModal } = useGlobalState();
  const { query, push, replace } = useRouter();
  const caseId = query.caseId as string;
  const meetingId = query.meetingId as string;

  const { mutateAsync: createMeeting, isLoading } = useMutate<ICreateMeeting>({
    ...caseQuery.createMeeting,
    onError: (e) => {
      const errorMessage = get(e, 'error');
      if (errorMessage === errors.LAWYER_INVALID_BOOKING_UNITPRICE)
        setConfirmModal({
          hideCancelButton: true,
          content: errorMessage,
          icon: 'warning',
          onConfirm: () => push('/lawyer/my-page/profile?tab=consultations'),
        });
    },
  });
  const { mutateAsync: updateMeeting, isLoading: isLoadingUpdate } =
    useMutate<ICreateMeeting>(caseQuery.updateMeeting(meetingId));
  const { mutateAsync: updateMethod, isLoading: isUpdatingMethod } = useMutate<{
    paymentInfo: { method: string };
  }>(caseQuery.updatePaymentMethod(meetingId));

  const fetchCaseDetail = useFetchDetail({
    ...caseQuery.providerCaseDetail(caseId),
    enabled: false,
  });
  const fetchMeetingDetail = useFetchDetail({
    ...caseQuery.getMeetingDetail(meetingId),
    enabled: false,
  });

  const { data: currentUser } = useFetchUser<LawyerData>({ enabled: true });
  const lawyerId = currentUser?._id;
  const userRole = Helper.getUserRole();
  const checkCustomerId =
    userRole === ROLES.CUSTOMER ? currentUser?._id : undefined;

  const { list: menuList } = useFetchList<IConsultationItem>({
    ...consultationQuery.list(lawyerId as string, checkCustomerId),
    enabled: !!lawyerId, // Only fetch when lawyerId is available
  });
  const unitPrices = useMemo(
    () => menuList.find((menu) => menu.unitPrices)?.unitPrices || [],
    [menuList],
  );

  const caseDetail = fetchCaseDetail.detail as IProviderCaseDetail;
  const meetingDetail = fetchMeetingDetail.detail as IMeetingsItem;
  const couponInfo = meetingDetail.paymentInfo?.couponInfo;
  const couponAmount = couponInfo?.couponAmount || 0;
  const unitPrice = meetingId
    ? meetingDetail.paymentInfo.unitPrice
    : unitPrices[0]?.price || 0;

  const isFinishedMeeting =
    meetingDetail?.status === MeetingStatusType.FINISHED &&
    meetingDetail?.paymentInfo.status === PaymentStatusType.ERROR;

  const defaultValues = useMemo(
    () => ({
      finalizedDate: meetingDetail.finalizedDate,
      meetingUrl: meetingDetail.meetingUrl,
      type: meetingDetail.type,
      discount: meetingDetail.paymentInfo?.discount,
      duration: meetingDetail.paymentInfo?.duration || 30,
      method: meetingDetail.paymentInfo?.method,
    }),
    [meetingDetail],
  );

  const formSchema = useMemo(() => {
    if (isFinishedMeeting) {
      return updatePaymentMethodSchema;
    }
    if (meetingDetail?.finalizedDate) {
      return updateHasFinalDateMeetingSchema({
        finalizedDate: meetingDetail.finalizedDate,
      });
    }
    return schema;
  }, [isFinishedMeeting, meetingDetail?.finalizedDate]);

  const {
    control,
    handleSubmit,
    clearErrors,
    setValue,
    reset,
    formState: { dirtyFields },
  } = useHookForm<CreateMeetingValues>({
    mode: 'all',
    resolver: yupResolver(formSchema),
    defaultValues,
  });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const watchDuration = useWatch({ name: 'duration', control });
  const watchDiscount = useWatch({ name: 'discount', control }) || 0;
  const watchType = useWatch({ name: 'type', control });

  useEffect(() => {
    if (watchType !== MeetingType.ONLINE) {
      setValue('meetingUrl', '', { shouldValidate: true });
    }
  }, [clearErrors, setValue, watchType]);

  const basicPrice = Helper.getBasicPrice(unitPrice, watchDuration);

  const handleSubmitForm = (values: CreateMeetingValues) => {
    const { finalizedDate, meetingUrl, duration, type, discount, method } =
      values;
    const payload: ICreateMeeting = {
      caseId,
      meetingUrl,
      type,
      paymentInfo: {
        discount: discount || 0,
        duration,
        method,
      },
    };
    if (dirtyFields.finalizedDate) {
      payload.finalizedDate = dayjs(finalizedDate).toISOString();
    }
    if (!meetingId) {
      payload.paymentInfo.unitPrice = unitPrice;
    }

    const onSuccess = () => {
      replace({
        pathname: '/lawyer/my-page/cases/[caseId]',
        query: { caseId },
      });
    };

    if (meetingId) {
      if (isFinishedMeeting) {
        updateMethod({ paymentInfo: { method } }, { onSuccess });
      } else
        updateMeeting(payload, {
          onSuccess,
          onError: (e) => {
            if (
              get(e, 'code') ===
              'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT'
            ) {
              Helper.toast(get(e, 'error'), { type: 'error' });
            }
          },
        });
    } else {
      createMeeting(payload, { onSuccess });
    }
  };

  if (meetingId && fetchMeetingDetail.isLoading) {
    return null;
  }
  return (
    <Box>
      <form id="create-meeting-form" onSubmit={handleSubmit(handleSubmitForm)}>
        <Box sx={styles.headerCreateMeeting}>
          <Box>
            <Breadcrumbs
              omitIndexList={[0]}
              transformLabel={{
                '/lawyer/my-page/cases/[caseId]': caseDetail.consumer.fullName,
              }}
            />
            <Box display="flex" mt={{ xs: '4px', tablet: '11px' }}>
              <Box m="5px 4px 0px -4px">
                <ButtonBack icon={<BackIcon />} sx={styles.backButton} />
              </Box>
              <Typography
                fontSize={{ xs: 24, tablet: 32 }}
                fontWeight="bold"
                color="heading"
              >
                {t('caseDetail.createMeeting')}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Grid
          container
          columnGap={2}
          mt={{ xs: 1, tablet: 2 }}
          columns={29}
          minHeight="calc(100vh - 390px)"
        >
          <Grid item xs={29} md={19}>
            <Box sx={styles.confirmMeeting}>
              <Stack spacing={1} direction="row" alignItems="center" mb={3}>
                <SheetIcon />
                <Typography sx={styles.textHeader}>
                  {t('confirmMeeting.meetingTitle')}
                </Typography>
              </Stack>
              {isFinishedMeeting ? (
                <EditFinishedMeeting data={meetingDetail} />
              ) : (
                <Stack spacing={{ xs: '20px', tablet: 2 }}>
                  <DateTimePicker
                    label={t('confirmMeeting.meetingDate')}
                    placeholder="yyyy年mm月dd日 hh時mm分"
                    name="finalizedDate"
                    control={control}
                    minDate={dayjs()}
                    required
                    labelCol={3}
                    columns={13}
                  />
                  <Grid container columns={15}>
                    <Label
                      labelCol={3}
                      label={t('confirmMeeting.meetingType')}
                      paddingTop={3}
                      required
                    />
                    <Grid item xs={12} columnSpacing={4}>
                      <Box sx={styles.typeMeeting}>
                        <Radio
                          labelCol={0}
                          control={control}
                          name="type"
                          row={false}
                          data={[
                            {
                              _id: MeetingType.IN_PERSON,
                              value: '対面での面談',
                            },
                            {
                              _id: MeetingType.ONLINE,
                              value: 'オンライン面談',
                            },
                          ]}
                        />
                        <AnimatePresence initial={false}>
                          {watchType === MeetingType.ONLINE && (
                            <motion.div
                              initial="collapsed"
                              animate="open"
                              exit="collapsed"
                              variants={{
                                open: { opacity: 1, height: 'auto' },
                                collapsed: { opacity: 0, height: 0 },
                              }}
                              transition={{
                                duration: 0.3,
                              }}
                            >
                              <TextField
                                labelCol={12}
                                name="meetingUrl"
                                control={control}
                                placeholder={t(
                                  'confirmMeeting.placeholderMeetingUrl',
                                )}
                              />
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </Box>
                    </Grid>
                  </Grid>
                  <Select
                    labelCol={3}
                    columns={13}
                    name="duration"
                    label={t('confirmMeeting.duration')}
                    placeholder="面談時間"
                    data={Helper.convertObjectToOptions(MEETING_DURATION)}
                    control={control}
                    required
                    handleChange={() => {
                      setValue('discount', '' as unknown as number);
                    }}
                  />
                </Stack>
              )}
            </Box>
          </Grid>
          <Grid item xs md>
            <Box borderRadius={2} bgcolor="white" p={4}>
              <Stack spacing={1} direction="row" alignItems="center" mb={4}>
                <Box sx={styles.icon}>
                  <PaymentIcon />
                </Box>
                <Typography sx={styles.textHeader}>決済方法 </Typography>
              </Stack>
              <Select
                name="method"
                labelCol={12}
                control={control}
                data={PAYMENT_METHOD_OPTIONS}
                placeholder={t('confirmMeeting.placeholderMethod')}
                required
              />
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="flex-end"
                mt={2}
              >
                <Typography fontWeight={700}>
                  {t('confirmMeeting.basicPrice')}
                </Typography>
                <Stack direction="row" spacing="2px" alignItems="flex-end">
                  <Typography fontWeight={700}>
                    {Helper.addComma(
                      Helper.getBasicPrice(unitPrice, watchDuration),
                    )}
                  </Typography>
                  <Typography fontWeight={700}>円</Typography>
                </Stack>
              </Stack>
              <Typography fontSize={14} color="hint" mt={1} mb={2}>
                {t('confirmMeeting.description')}
              </Typography>
              {couponInfo && (
                <Stack
                  direction="row"
                  my={2}
                  alignItems="center"
                  justifyContent="space-between"
                  flexWrap="wrap"
                >
                  <Stack direction="row" alignItems="center">
                    <Typography fontWeight={700} mr={1}>
                      クーポン値引
                    </Typography>
                    <CouponTag
                      wrapperProps={{ mr: '6px' }}
                      code={couponInfo.coupon.code}
                    />
                    <PaymentTooltipButton />
                  </Stack>
                  <Typography fontWeight={700}>
                    - {Helper.addComma(couponInfo.couponAmount || 0)}円
                  </Typography>
                </Stack>
              )}
              {isFinishedMeeting ? (
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  alignItems="flex-end"
                  mt={2}
                >
                  <Typography fontWeight={500} color="heading">
                    割引金額
                  </Typography>
                  <Stack direction="row" spacing="2px" alignItems="flex-end">
                    <Typography fontWeight={500} color="heading">
                      {Helper.addComma(meetingDetail.paymentInfo.discount || 0)}
                    </Typography>
                    <Typography fontWeight={500} color="heading">
                      円
                    </Typography>
                  </Stack>
                </Stack>
              ) : (
                <Box>
                  <NumberField
                    label={t('confirmMeeting.discount')}
                    placeholder="割引金額を入力"
                    name="discount"
                    control={control}
                    labelCol={5}
                    columns={18}
                    adornment={
                      <Typography ml={1} color="neutral7">
                        円
                      </Typography>
                    }
                    min={0}
                    max={Math.max(basicPrice - couponAmount, 0)}
                  />
                </Box>
              )}
              <Divider sx={{ mt: 3, mb: 2 }} />
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="flex-end"
              >
                <Stack direction="row" alignItems="center">
                  <Typography fontWeight="bold" color="heading" mr="6px">
                    {t('confirmMeeting.finalPrice')}
                  </Typography>
                  <PaymentTooltipButton
                    title="お支払い予定金額に関して"
                    content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
                  />
                </Stack>
                <Stack direction="row" spacing="2px" alignItems="flex-end">
                  <Typography fontSize={24} fontWeight="bold" color="neutral7">
                    {watchDiscount <=
                    Helper.getBasicPrice(unitPrice, watchDuration) -
                      couponAmount
                      ? Helper.addComma(
                          Helper.getBasicPrice(unitPrice, watchDuration) -
                            watchDiscount -
                            couponAmount,
                        )
                      : 0}
                  </Typography>
                  <Typography
                    fontWeight="bold"
                    color="neutral7"
                    lineHeight="30px"
                  >
                    円
                  </Typography>
                </Stack>
              </Stack>
            </Box>
          </Grid>
        </Grid>
        <Box sx={styles.confirmWrapper}>
          <Box flex={1}>
            <Box display="flex" justifyContent="right">
              <Stack spacing={1} direction="row" alignItems="center">
                <Button
                  variant="outlined"
                  sx={{ maxHeight: 40, minWidth: 112 }}
                  onClick={() => {
                    push({
                      pathname: '/lawyer/my-page/cases/[caseId]',
                      query: { caseId },
                    });
                  }}
                >
                  {t('global.cancel')}
                </Button>
                <LoadingButton
                  color="secondary"
                  variant="contained"
                  type="submit"
                  fullWidth
                  loading={isLoading || isLoadingUpdate || isUpdatingMethod}
                  sx={{ fontWeight: 'bold', maxHeight: 40, minWidth: 112 }}
                >
                  {t('global.settle')}
                </LoadingButton>
              </Stack>
            </Box>
          </Box>
        </Box>
      </form>
    </Box>
  );
};

CreateMeetingPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.LAWYER}>{page}</SideMenuLayout>
    </Layout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const qClient = new QueryClient();
  const caseId = query.caseId as string;
  const meetingId = query.meetingId as string;
  const promises = [
    Helper.prefetchDetail(
      caseQuery.providerCaseDetail(caseId),
      qClient,
      undefined,
      Helper.getTokenConfig(req, res),
    ),
  ];
  if (meetingId) {
    promises.push(
      Helper.prefetchDetail(
        caseQuery.getMeetingDetail(meetingId),
        qClient,
        undefined,
        Helper.getTokenConfig(req, res),
      ),
    );
  }
  const [caseDetail, meetingDetail] = await Promise.all(promises);
  if (
    meetingDetail &&
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
    ].includes(meetingDetail.status)
  ) {
    return {
      redirect: {
        destination: `/lawyer/my-page/cases/${query.caseId}`,
        permanent: true,
      },
    };
  }
  if (caseDetail.status !== CaseStatusType.SCHEDULE_IS_ADJUSTED) {
    return {
      redirect: {
        destination: `/lawyer/my-page/cases/${query.caseId}/confirm`,
        permanent: true,
      },
    };
  }

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

export default CreateMeetingPage;

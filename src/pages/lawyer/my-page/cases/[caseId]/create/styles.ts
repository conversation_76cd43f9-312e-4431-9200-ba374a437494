import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  headerCreateMeeting: {
    p: '16px 30px 16px 35px',
    borderRadius: 2,
    bgcolor: 'white',
  },
  backButton: {
    p: '4px',
    svg: {
      width: { xs: 28, tablet: 32 },
      height: { xs: 28, tablet: 32 },
    },
  },
  confirmMeeting: {
    p: { xs: 2, tablet: 4 },
    bgcolor: 'white',
    borderRadius: { xs: 0, tablet: 2 },
  },
  textHeader: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'heading',
  },
  typeMeeting: {
    ml: 4,
    p: 3,
    border: (theme) => `solid 1px ${theme.palette.neutral4}`,
    borderRadius: '12px',
  },
  icon: {
    svg: { width: 32, height: 32, display: 'block' },
  },
  confirmWrapper: {
    position: 'sticky',
    bottom: 0,
    mb: -2,
    p: '24px 32px',
    display: 'flex',
    justifyContent: 'center',
    boxShadow: '0 -8px 16px -16px #d2dce1;',
    borderRadius: '16px 16px 0px 0px',
    bgcolor: 'white',
    alignItems: 'center',
    zIndex: 2,
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import { <PERSON>, Button, Grid, Stack, Typography } from '@mui/material';
import VisibleMotion from 'components/Animation/VisibleMotion';
import SkeletonList from 'components/Case/CustomerMeetingItem/Skeleton';
import LawyerMeetingItem from 'components/Case/LawyerMeetingItem';
import ReviewSection from 'components/Case/ReviewSection';
import type { ReviewFormValues } from 'components/Case/ReviewSection/ReviewModal/schema';
import ListPagination from 'components/CustomPagination/ListPagination';
import MemoForm from 'components/Lawyer/MemoForm';
import PrivateNoteForm from 'components/Lawyer/PrivateNoteForm';
import TruncateText from 'components/TruncateText';
import dayjs from 'dayjs';
import { useFetchDetail, useFetchList } from 'hooks';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { AddIcon, AppointmentIcon, EditIcon, InfoIcon, NoteIcon } from 'icons';
import { isEmpty } from 'lodash';
import type {
  IAddCaseMemo,
  IAddPrivateNote,
  IMeetingsItem,
  IProviderCaseDetail,
  IReviewCustomer,
} from 'models/case/interface';
import caseQuery from 'models/case/query';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { CaseStatusType, MomentFormat } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

const closedStatus = [
  CaseStatusType.ASSIGNED,
  CaseStatusType.CANCELED_BY_CONSUMER,
  CaseStatusType.CANCELED_BY_PROVIDER,
];

const CaseDetail = ({
  onAddCaseMemo,
  isAdding,
  onAddPrivateNote,
  isAddingPrivateNote,
}: {
  onAddCaseMemo: ({ note }: Omit<IAddCaseMemo, 'caseId'>) => void;
  onAddPrivateNote: ({ privateNote }: Omit<IAddPrivateNote, 'caseId'>) => void;
  isAdding: boolean;
  isAddingPrivateNote: boolean;
}) => {
  const { query, push } = useRouter();
  const caseId = query.caseId as string;
  const {
    list: meetings = [],
    total,
    isLoading,
    refetch,
  } = useFetchList<IMeetingsItem>(caseQuery.lawyerMeetingList(caseId));
  const { detail: caseDetail, refetch: refetchCaseDetail } =
    useFetchDetail<IProviderCaseDetail>({
      ...caseQuery.providerCaseDetail(caseId),
      enabled: false,
    });
  const { mutateAsync: reviewCustomer, isLoading: isReviewing } =
    useMutate<IReviewCustomer>(caseQuery.reviewCustomer(caseId));

  const { mutateAsync: cancelReviewCustomer } = useMutate(
    caseQuery.cancelReviewCustomer(caseId),
  );

  const { memo, status: caseStatus, privateMemo } = caseDetail;
  const [editMemo, setEditMemo] = useState(false);
  const [editPrivateNote, setEditPrivateNote] = useState(false);

  const handleUpdateCaseMemo = async (
    values: Omit<IAddCaseMemo, 'caseId'>,
    isDirty: boolean,
  ) => {
    if (isDirty) {
      await onAddCaseMemo(values);
      setEditMemo(false);
    } else setEditMemo(false);
  };

  const handleUpdatePrivateNote = async (
    values: Omit<IAddPrivateNote, 'caseId'>,
    isDirty: boolean,
  ) => {
    if (isDirty) {
      await onAddPrivateNote(values);
      setEditPrivateNote(false);
    } else setEditPrivateNote(false);
  };

  const isShowReviewPopup = caseDetail.reviewConsumer?.isShowAgain;

  const handleCancelReview = () => {
    if (isShowReviewPopup !== false) {
      cancelReviewCustomer(
        {
          caseId,
        },
        {
          onSuccess: () => refetchCaseDetail(),
        },
      );
    }
  };

  const handleReviewCustomer = async (values: ReviewFormValues) => {
    await reviewCustomer({
      reviewConsumer: {
        ...values,
        isShowAgain: false,
      },
    });
    Helper.toast(t('reviewMeeting.reviewSuccess'));
    refetchCaseDetail();
  };

  return (
    <Grid container columnGap={2} mt={2} columns={29}>
      <Grid item xs={29} md={19} height="fit-content">
        <Box sx={styles.containerCase}>
          <Box sx={styles.caseInfo}>
            <Stack direction="row" spacing={1}>
              <Box sx={styles.icon}>
                <AppointmentIcon />
              </Box>
              <Typography sx={styles.title}>{t('caseDetail.title')}</Typography>
            </Stack>
            {!closedStatus.includes(caseStatus) && (
              <Button
                variant="tertiary"
                sx={{
                  maxWidth: 96,
                  ':not(.Mui-disabled)': { svg: { color: 'primary.main' } },
                }}
                fullWidth
                startIcon={<AddIcon />}
                color="primary"
                onClick={() => {
                  push({
                    pathname: '/lawyer/my-page/cases/[caseId]/create',
                    query: { caseId },
                  });
                }}
              >
                {t('global.add')}
              </Button>
            )}
          </Box>
          <Typography fontSize={{ xs: 10, tablet: 12 }}>
            {t('caseDetail.caseId', {
              caseId,
            })}
          </Typography>
          {caseDetail.hasPaymentError && (
            <Box sx={styles.alert}>
              <Stack direction="row" spacing={1} sx={styles.checkError}>
                <InfoIcon />
                <Typography fontWeight={500} color="neutral6" fontSize={14}>
                  {t('caseDetail.alert')}
                </Typography>
              </Stack>
            </Box>
          )}
          {isLoading && isEmpty(meetings) && <SkeletonList />}
          {!isEmpty(meetings) && (
            <Box mt={3}>
              {meetings.map((meeting) => (
                <LawyerMeetingItem
                  meeting={meeting}
                  key={meeting._id}
                  refetch={refetch}
                  hasReview={!!caseDetail.reviewConsumer}
                  refetchCaseDetail={refetchCaseDetail}
                  refetchMeetingList={refetch}
                />
              ))}
            </Box>
          )}
        </Box>
        {total > 0 && (
          <Box mt={4} display="flex" justifyContent="center">
            <ListPagination total={total} />
          </Box>
        )}
      </Grid>
      <Grid item xs md>
        <ReviewSection
          caseStatus={caseStatus}
          title={t('reviewMeeting.reviewCustomerTitle', {
            customerName: caseDetail.consumer.fullName,
          })}
          placeholder={t('reviewMeeting.reviewCustomerPlaceholder')}
          content={t('reviewMeeting.reviewCustomerContent')}
          isLoading={isReviewing}
          onSubmit={handleReviewCustomer}
          onCancel={handleCancelReview}
          reviewData={caseDetail.reviewConsumer}
        />
        <Box p={4} bgcolor="white" flex={1} borderRadius={2}>
          <Box display="flex" justifyContent="space-between">
            <Stack direction="row" spacing={1} sx={{ svg: { mt: '2px' } }}>
              <NoteIcon />
              <Typography fontSize={24} fontWeight="bold" color="heading">
                {t('caseDetail.memo')}
              </Typography>
            </Stack>
            {!editMemo && !closedStatus.includes(caseStatus) && (
              <Button
                variant="contained"
                color="secondary"
                onClick={() => setEditMemo(true)}
                startIcon={<EditIcon />}
                sx={styles.editIcon}
              >
                {t('global.edit')}
              </Button>
            )}
          </Box>
          {memo?.updatedAt && !editMemo && (
            <Stack direction="row" spacing={1} sx={styles.updatedContainer}>
              <InfoIcon />
              <Typography
                fontSize={{ xs: 12, tablet: 14 }}
                fontWeight={500}
                color="primary"
              >
                {t('caseDetail.updateOn')}:{' '}
                {dayjs(memo.updatedAt).format(MomentFormat.JP_YEAR_MONTH_DATE)}
              </Typography>
            </Stack>
          )}
          {editMemo && (
            <VisibleMotion key="edit">
              <Box mt={4}>
                <MemoForm
                  defaultValues={memo}
                  onSubmit={handleUpdateCaseMemo}
                  onCancel={() => setEditMemo(false)}
                  loading={isAdding}
                />
              </Box>
            </VisibleMotion>
          )}
          {!editMemo && (
            <VisibleMotion key="view">
              <Box mt={memo?.updatedAt ? 2 : '27px'}>
                {memo?.note ? (
                  <TruncateText lines={5} text={memo?.note} />
                ) : (
                  <Typography color="hint">{t('caseDetail.noMemo')}</Typography>
                )}
              </Box>
            </VisibleMotion>
          )}
        </Box>
        <Box mt={2} p={4} bgcolor="white" flex={1} borderRadius={2}>
          <Box display="flex" justifyContent="space-between">
            <Stack direction="row" spacing={1} sx={{ svg: { mt: '2px' } }}>
              <NoteIcon />
              <Typography fontSize={24} fontWeight="bold" color="heading">
                特記事項
              </Typography>
            </Stack>
            {!editPrivateNote && !closedStatus.includes(caseStatus) && (
              <Button
                variant="contained"
                color="secondary"
                onClick={() => setEditPrivateNote(true)}
                startIcon={<EditIcon />}
                sx={styles.editIcon}
              >
                {t('global.edit')}
              </Button>
            )}
          </Box>
          {editPrivateNote && (
            <VisibleMotion key="edit">
              <Box mt={4}>
                <PrivateNoteForm
                  defaultValues={{ privateNote: privateMemo?.note || '' }}
                  onSubmit={handleUpdatePrivateNote}
                  onCancel={() => setEditPrivateNote(false)}
                  loading={isAddingPrivateNote}
                />
              </Box>
            </VisibleMotion>
          )}
          {!editPrivateNote && (
            <VisibleMotion key="view">
              <Box mt={privateMemo?.updatedAt ? 2 : '27px'}>
                {privateMemo?.note ? (
                  <TruncateText lines={5} text={privateMemo?.note} />
                ) : (
                  <Typography color="hint">{t('caseDetail.noMemo')}</Typography>
                )}
              </Box>
            </VisibleMotion>
          )}
        </Box>
      </Grid>
    </Grid>
  );
};

export default CaseDetail;

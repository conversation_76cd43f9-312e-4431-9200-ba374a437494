import { Box, Typography } from '@mui/material';
import DivorceBackground from 'components/ConsultationForm/DivorceBackground';
import KidInformation from 'components/ConsultationForm/KidInformation';
import MarriageInformation from 'components/ConsultationForm/MarriageDate';
import PartnerInformation from 'components/ConsultationForm/PartnerInformation';
import ProfileFrom from 'components/ConsultationForm/Profile';
import PropertyInformation from 'components/ConsultationForm/PropertyInfomation';
import ViewEditBlock from 'components/UI/ViewEditBlock/index';
import type { Dayjs } from 'dayjs';
import useConsulation from 'hooks/lawyer/useConsultationForm';
import type {
  ConsultationRecord,
  DivorceBackgroundPayload,
  KidsFormPayload,
  MarriageInformationPayload,
  PartnerInformationPayload,
  PropertyInformationPayload,
} from 'hooks/lawyer/useConsultationForm/types';
import type { IUpdateCustomerProfile } from 'hooks/useAuth/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import i18n from 'i18n';
import { KidIcon } from 'icons';
import { isEmpty, isUndefined, pick } from 'lodash';
import { useCallback, useMemo, useState } from 'react';
import { scroller } from 'react-scroll';
import type { GenderType, ISchoolType } from 'utils/constants';
import {
  CaseStatusType,
  Gender,
  RadioOptions,
  SchoolTypeOptions,
} from 'utils/constants';
import Helper from 'utils/helpers';

import type { IBlock } from './blocks';
import {
  divorceBlock,
  marriageBlock,
  partnerBlock,
  profileBlock,
  propertyBlock,
} from './blocks';

const closedStatus = [
  CaseStatusType.ASSIGNED,
  CaseStatusType.CANCELED_BY_CONSUMER,
  CaseStatusType.CANCELED_BY_PROVIDER,
];
export interface ICustomerConsultation {
  data: ConsultationRecord;
  customerProfile: CustomerData;
  refetchCustomerProfile: () => void;
  refetchConsultation: () => void;
  caseStatus: CaseStatusType;
}
const CustomerConsultation = ({
  data,
  customerProfile,
  refetchCustomerProfile,
  refetchConsultation,
  caseStatus,
}: ICustomerConsultation) => {
  const [showNoti, setShowNoti] = useState<string>();

  const {
    updateCustomer,
    updateParterInformation,
    updateDivorceBackground,
    updateMarriageInformation,
    updatePropertyInformation,
    updateKidsInformation,
    isUpdatingCustomer,
    isUpdatingDivorceBackground,
    isUpdatingPartner,
    isUpdatingMarriage,
    isUpdatingProperty,
    isUpdatingKidsInformation,
  } = useConsulation();
  const customerId = customerProfile?._id;
  const { setConfirmModal } = useGlobalState();
  const [editForm, setEditForm] = useState<string[]>([]);
  const { t } = i18n;

  const closeForm = useCallback((formName: string) => {
    setEditForm((forms) => forms.filter((form) => form !== formName));
    scroller.scrollTo(formName, {
      smooth: true,
      offset: -132,
    });
  }, []);

  const handleAfterSubmit = useCallback(
    (formName: string) => {
      closeForm(formName);
      setShowNoti(formName);
      if (formName === 'profile-form') {
        refetchCustomerProfile();
      } else refetchConsultation();
      Helper.toast(t('validation.completed'));
    },
    [closeForm, refetchConsultation, refetchCustomerProfile, t],
  );

  const handleCancelEdit = useCallback(
    (isDirty: boolean, formName: string) => {
      setShowNoti('');
      if (isDirty) {
        setConfirmModal({
          onConfirm: () => closeForm(formName),
          title: 'discardInput.title',
          content: 'discardInput.message',
        });
      } else {
        closeForm(formName);
      }
    },
    [closeForm, setConfirmModal],
  );

  const handleUpdatePartnerInformation = useCallback(
    (values: PartnerInformationPayload, isDirty: boolean) => {
      if (isDirty) {
        updateParterInformation(
          { ...values, customerId },
          {
            onSuccess: () => handleAfterSubmit('partner-form'),
          },
        );
      } else {
        closeForm('partner-form');
      }
    },
    [closeForm, customerId, handleAfterSubmit, updateParterInformation],
  );

  const handleUpdateDivorceBackground = useCallback(
    (values: DivorceBackgroundPayload, isDirty: boolean) => {
      if (isDirty) {
        updateDivorceBackground(
          { ...values, customerId },
          {
            onSuccess: () => handleAfterSubmit('divorce-form'),
          },
        );
      } else {
        closeForm('divorce-form');
      }
    },
    [closeForm, customerId, handleAfterSubmit, updateDivorceBackground],
  );

  const handleUpdateMarriageInformation = useCallback(
    (values: MarriageInformationPayload, isDirty: boolean) => {
      if (isDirty) {
        updateMarriageInformation(
          { ...values, customerId },
          {
            onSuccess: () => handleAfterSubmit('marriage-form'),
          },
        );
      } else {
        closeForm('marriage-form');
      }
    },
    [closeForm, customerId, handleAfterSubmit, updateMarriageInformation],
  );

  const handleUpdatePropertyInformation = useCallback(
    (values: PropertyInformationPayload, isDirty: boolean) => {
      if (isDirty) {
        updatePropertyInformation(
          { ...values, customerId },
          {
            onSuccess: () => handleAfterSubmit('property-form'),
          },
        );
      } else {
        closeForm('property-form');
      }
    },
    [closeForm, customerId, handleAfterSubmit, updatePropertyInformation],
  );
  const handleUpdateKidsInformation = useCallback(
    (values: KidsFormPayload, isDirty: boolean) => {
      if (isDirty) {
        updateKidsInformation(
          { ...values, customerId },
          {
            onSuccess: () => handleAfterSubmit('kid-form'),
          },
        );
      } else {
        closeForm('kid-form');
      }
    },
    [closeForm, customerId, handleAfterSubmit, updateKidsInformation],
  );

  const kids = data?.kids?.kids;

  const handleEditForm = useCallback((form: string) => {
    setEditForm((forms) => forms.concat(form));
  }, []);

  const childrenFields = useMemo(
    () =>
      (kids || []).map((_: unknown, index: number) => [
        { label: t('consultationRecord.kidOrder', { number: index + 1 }) },
        {
          label: t('consultationRecord.kidGender'),
          path: `kids.kids[${index}].gender`,
          renderValue: (value: GenderType) => Gender[value],
        },
        {
          label: t('consultationRecord.kidAge'),
          path: `kids.kids[${index}].age`,
          renderValue: (value: Dayjs) =>
            !isUndefined(value) &&
            t('consultationRecord.ageNumber', { number: value }),
        },
        {
          label: t('consultationRecord.schoolType'),
          path: `kids.kids[${index}].schoolType`,
          renderValue: (value: ISchoolType) =>
            value && SchoolTypeOptions[value],
        },
      ]),
    [kids, t],
  );

  const kidsFields: IBlock = {
    title: t('consultationRecord.kidInformation'),
    icon: <KidIcon />,
    fields: [
      {
        label: t('consultationRecord.kids'),
        path: 'kids.kids',
        renderValue: (value: unknown) =>
          value && (
            <>
              <Typography>
                {isEmpty(value) ? RadioOptions.no : RadioOptions.yes}
              </Typography>
              <Typography fontSize={14} mt={1} color="hint">
                {t('consultationRecord.under18')}
              </Typography>
            </>
          ),
      },
    ].concat(childrenFields.flat() as never),
  };

  const defaultValues = useMemo(
    () => ({
      ...pick(customerProfile, [
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'annualIncome',
        'job',
        'phone',
      ]),
      currentAddress1: customerProfile?.currentAddress?.address1._id,
      age: customerProfile?.age?._id,
    }),
    [customerProfile],
  );

  const handleUpdateCustomerProfile = useCallback(
    (values: IUpdateCustomerProfile, isDirty: boolean) => {
      if (isDirty) {
        updateCustomer(
          { ...values, customerId },
          {
            onSuccess: () => handleAfterSubmit('profile-form'),
          },
        );
      } else {
        closeForm('profile-form');
      }
    },
    [closeForm, customerId, handleAfterSubmit, updateCustomer],
  );

  return (
    <Box>
      <ViewEditBlock
        hideEditModeButton={closedStatus.includes(caseStatus)}
        editMode={editForm.includes('profile-form')}
        onEdit={() => handleEditForm('profile-form')}
        detail={customerProfile}
        block={profileBlock}
        showNoti={showNoti === 'profile-form'}
        formId="profile-form"
      >
        <ProfileFrom
          isLawyer
          currentUser={customerProfile}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'profile-form')}
          onSubmit={handleUpdateCustomerProfile}
          defaultValues={defaultValues as IUpdateCustomerProfile}
          loading={isUpdatingCustomer}
        />
      </ViewEditBlock>
      <ViewEditBlock
        hideEditModeButton={closedStatus.includes(caseStatus)}
        editMode={editForm.includes('divorce-form')}
        onEdit={() => handleEditForm('divorce-form')}
        detail={data}
        block={divorceBlock}
        formId="divorce-form"
        showNoti={showNoti === 'divorce-form'}
      >
        <DivorceBackground
          record={data}
          loading={isUpdatingDivorceBackground}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'divorce-form')}
          onSubmit={(values, isDirty) =>
            handleUpdateDivorceBackground(values, isDirty)
          }
        />
      </ViewEditBlock>
      <ViewEditBlock
        hideEditModeButton={closedStatus.includes(caseStatus)}
        editMode={editForm.includes('partner-form')}
        onEdit={() => handleEditForm('partner-form')}
        detail={data}
        block={partnerBlock}
        formId="partner-form"
        showNoti={showNoti === 'partner-form'}
      >
        <PartnerInformation
          record={data}
          loading={isUpdatingPartner}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'partner-form')}
          onSubmit={handleUpdatePartnerInformation}
        />
      </ViewEditBlock>
      <ViewEditBlock
        hideEditModeButton={closedStatus.includes(caseStatus)}
        editMode={editForm.includes('marriage-form')}
        onEdit={() => handleEditForm('marriage-form')}
        detail={data}
        block={marriageBlock}
        formId="marriage-form"
        showNoti={showNoti === 'marriage-form'}
      >
        <MarriageInformation
          record={data}
          loading={isUpdatingMarriage}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'marriage-form')}
          onSubmit={handleUpdateMarriageInformation}
        />
      </ViewEditBlock>
      <ViewEditBlock
        hideEditModeButton={closedStatus.includes(caseStatus)}
        editMode={editForm.includes('property-form')}
        onEdit={() => handleEditForm('property-form')}
        detail={data}
        block={propertyBlock}
        formId="property-form"
        showNoti={showNoti === 'property-form'}
      >
        <PropertyInformation
          record={data}
          loading={isUpdatingProperty}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'property-form')}
          onSubmit={handleUpdatePropertyInformation}
        />
      </ViewEditBlock>
      <ViewEditBlock
        hideEditModeButton={closedStatus.includes(caseStatus)}
        editMode={editForm.includes('kid-form')}
        onEdit={() => handleEditForm('kid-form')}
        detail={data}
        block={kidsFields}
        formId="kid-form"
        showNoti={showNoti === 'kid-form'}
      >
        <KidInformation
          loading={isUpdatingKidsInformation}
          onCancel={(isDirty) => handleCancelEdit(isDirty, 'kid-form')}
          onSubmit={handleUpdateKidsInformation}
          record={data}
        />
      </ViewEditBlock>
    </Box>
  );
};

export default CustomerConsultation;

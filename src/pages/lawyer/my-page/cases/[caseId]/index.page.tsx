import { LoadingButton } from '@mui/lab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { Box, Stack, Typography } from '@mui/material';
import Tab from '@mui/material/Tab';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import CancelCaseForm from 'components/Case/CancelCaseForm';
import CaseTag from 'components/Case/CaseTag';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import BackButton from 'components/UI/BackButton';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { ChatIcon } from 'icons';
import { get } from 'lodash';
import type {
  IAddCaseMemo,
  IAddPrivateNote,
  IProviderCaseDetail,
  IUpdateCaseStatus,
} from 'models/case/interface';
import caseQuery from 'models/case/query';
import chatQuery from 'models/chat';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useState } from 'react';
import api from 'utils/api';
import { CaseStatusType, ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

import CaseDetail from './case-detail';
import CustomerProfile from './customer-consultation';

const CaseDetailPage = () => {
  const { setConfirmModal } = useGlobalState();
  const { query, replace, pathname } = useRouter();
  const [loadingStatus, setLoadingStatus] = useState<
    CaseStatusType | undefined
  >();
  const { tab = 'case-detail' } = query;
  const caseId = query.caseId as string;

  const { data: currentUser } = useFetchUser({ enabled: false });
  const { mutateAsync: createRoom, isLoading: isCreatingRoom } = useMutate<
    unknown,
    { roomId: string }
  >(chatQuery.createRoom);
  const fetchCaseDetail = useFetchDetail({
    ...caseQuery.providerCaseDetail(caseId),
    enabled: false,
  });
  const { refetch: refetchCaseDetail } = fetchCaseDetail;
  const caseDetail = fetchCaseDetail.detail as IProviderCaseDetail;
  const isAdjustedCase =
    caseDetail.status === CaseStatusType.SCHEDULE_IS_ADJUSTED;
  const fetchCustomerProfile = useFetchDetail({
    ...apiQuery.lawyerCustomerProfile(caseDetail.consumer._id),
  });
  const { refetch: refetchCustomerProfile } = fetchCustomerProfile;
  const customerProfile = fetchCustomerProfile.detail as CustomerData;

  const fetchCustomerConsultation = useFetchDetail({
    ...apiQuery.lawyerCustomerConsultation(caseDetail.consumer._id),
  });
  const { refetch: refetchConsultation } = fetchCustomerConsultation;
  const customerConsultation =
    fetchCustomerConsultation.detail as ConsultationRecord;

  const { mutateAsync: addBookingMemo, isLoading: isAdding } =
    useMutate<IAddCaseMemo>(caseQuery.addMemo(caseId));
  const { mutateAsync: addPrivateNote, isLoading: isAddingPrivateNote } =
    useMutate<IAddPrivateNote>(caseQuery.addPrivateNote(caseId));
  const { mutateAsync: updateStatus } = useMutate<IUpdateCaseStatus>(
    caseQuery.updateCaseStatus(caseId),
  );

  const { mutateAsync: notAssign } = useMutate<IUpdateCaseStatus>({
    ...caseQuery.updateCaseStatus(caseId),
    successMessage: '受任なしで更新しました',
  });

  const handleUpdateCaseStatus = async (status: CaseStatusType) => {
    try {
      setLoadingStatus(status);
      if (
        [
          CaseStatusType.CANCELED_BY_CONSUMER,
          CaseStatusType.CANCELED_BY_PROVIDER,
        ].includes(status)
      ) {
        await notAssign(
          { status },
          {
            onSuccess: refetchCaseDetail,
          },
        );
      } else
        await updateStatus(
          { status },
          {
            onSuccess: refetchCaseDetail,
          },
        );
    } catch (e) {
      if (get(e, 'code') === 'CASE_CLOSED_ERROR') {
        setConfirmModal({
          icon: 'error',
          hideCancelButton: true,
          content:
            '未完了の面談があります\nすべての面談の決済を完了するか、面談をキャンセルしてください',
        });
      }
    } finally {
      setLoadingStatus(undefined);
    }
  };

  const handleAddCaseMemo = async (values: Omit<IAddCaseMemo, 'caseId'>) => {
    await addBookingMemo(
      {
        ...values,
        caseId,
      },
      {
        onSettled: () => refetchCaseDetail(),
      },
    );
  };

  const handleAddPrivateNote = async (
    values: Omit<IAddPrivateNote, 'caseId'>,
  ) => {
    await addPrivateNote(
      {
        ...values,
        caseId,
      },
      {
        onSettled: () => refetchCaseDetail(),
      },
    );
  };

  const handleOpenCancelCase = () => {
    setConfirmModal({
      title: '受任なし',
      content: (
        <CancelCaseForm
          customerName={caseDetail.consumer.fullName}
          onSubmit={({ status }) => handleUpdateCaseStatus(status)}
        />
      ),
      cancelText: t('global.cancel'),
      confirmText: t('global.settle'),
      contentAlign: 'left',
      confirmButtonProps: {
        form: 'cancel-form',
        type: 'submit',
      },
    });
  };

  const handleAssignCase = () => {
    setConfirmModal({
      title: '受任',
      confirmText: t('global.settle'),
      onConfirm: () => handleUpdateCaseStatus(CaseStatusType.ASSIGNED),
      content: (
        <Typography fontSize={14}>
          <Typography component="span" fontSize={14} fontWeight="bold">
            {caseDetail.consumer.fullName}
          </Typography>
          様のご相談を受任します
        </Typography>
      ),
    });
  };

  const handleCreateRoom = () => {
    if (caseDetail.roomInfo?.roomId) {
      setTimeout(() =>
        window.open(
          `/lawyer/messages/${caseDetail.roomInfo?.roomId}`,
          '_blank',
        ),
      );
    } else {
      createRoom(
        {
          type: 'ONE_BY_ONE_HAS_CASE',
          memberIds: [
            `CONSUMER_${caseDetail.consumer._id}`,
            `LAWYER_${currentUser?._id}`,
          ],
        },
        {
          onSuccess: ({ roomId }) =>
            setTimeout(() =>
              window.open(`/lawyer/messages/${roomId}`, '_blank'),
            ),
        },
      );
    }
  };

  return (
    <TabContext value={tab as string}>
      <Box borderRadius={2} p="16px 32px 0px 16px" bgcolor="white">
        <Breadcrumbs
          omitIndexList={[0]}
          transformLabel={{
            '/lawyer/my-page/cases/[caseId]': get(
              caseDetail,
              'consumer.fullName',
            ),
          }}
        />
        <Box display="flex" justifyContent="space-between" mt="12px" mb={1}>
          <Stack direction="row" flex={1}>
            <Box mt="4px" mr="3px" ml="11px">
              <BackButton url="/lawyer/my-page/cases" />
            </Box>
            <Box>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography fontSize={32} fontWeight="bold" color="heading">
                  {get(caseDetail, 'consumer.fullName')}
                </Typography>
                <Box>
                  <CaseTag status={caseDetail.status} />
                </Box>
              </Stack>
              <Typography fontSize={14} mt="1px">
                {get(caseDetail, 'consumer.katakanaName')} •{' '}
                {get(caseDetail, 'consumer.email')}
              </Typography>
            </Box>
          </Stack>
          <Box>
            <Box display="flex" justifyContent="right" mt="3px">
              <LoadingButton
                variant="tertiary"
                fullWidth
                startIcon={<ChatIcon />}
                color="primary"
                className="tabletStyle"
                loading={isCreatingRoom}
                onClick={handleCreateRoom}
                sx={{
                  mr: isAdjustedCase ? 1 : 0,
                  minWidth: '148px',
                }}
              >
                チャットする
              </LoadingButton>
              {isAdjustedCase && (
                <>
                  <LoadingButton
                    variant="outlined"
                    fullWidth
                    sx={{ minWidth: 112 }}
                    loading={
                      loadingStatus &&
                      [
                        CaseStatusType.CANCELED_BY_CONSUMER,
                        CaseStatusType.CANCELED_BY_PROVIDER,
                      ].includes(loadingStatus)
                    }
                    onClick={handleOpenCancelCase}
                  >
                    受任なし
                  </LoadingButton>
                  <LoadingButton
                    variant="contained"
                    color="secondary"
                    fullWidth
                    sx={{ minWidth: 112, ml: 1 }}
                    loading={
                      loadingStatus && loadingStatus === CaseStatusType.ASSIGNED
                    }
                    onClick={handleAssignCase}
                  >
                    受任
                  </LoadingButton>
                </>
              )}
            </Box>
          </Box>
        </Box>

        <TabList
          onChange={(_, newValue) =>
            replace({ pathname, query: { tab: newValue, caseId } }, undefined, {
              shallow: true,
            })
          }
        >
          <Tab label={t('caseDetail.tabTitle')} value="case-detail" />
          <Tab
            label={t('breadcrumb.myPageConsultation')}
            value="customer-detail"
          />
        </TabList>
      </Box>
      <TabPanel value="customer-detail" sx={{ p: 0 }}>
        <CustomerProfile
          data={customerConsultation}
          customerProfile={customerProfile}
          refetchCustomerProfile={refetchCustomerProfile}
          refetchConsultation={refetchConsultation}
          caseStatus={caseDetail.status}
        />
      </TabPanel>

      <TabPanel value="case-detail" sx={{ p: 0 }}>
        <CaseDetail
          onAddPrivateNote={handleAddPrivateNote}
          onAddCaseMemo={handleAddCaseMemo}
          isAdding={isAdding}
          isAddingPrivateNote={isAddingPrivateNote}
        />
      </TabPanel>
    </TabContext>
  );
};

CaseDetailPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.LAWYER}>{page}</SideMenuLayout>
    </Layout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const qClient = new QueryClient();
  const caseId = query.caseId as string;
  const caseDetail = await qClient.fetchQuery(
    caseQuery.providerCaseDetail(caseId).queryKey,
    async () => {
      const { data } = await api.get(
        caseQuery.providerCaseDetail(caseId).apiUrl,
        Helper.getTokenConfig(req, res),
      );
      return data;
    },
  );
  if (caseDetail.status === CaseStatusType.WAITING_ANWSER_FROM_PROVIDER) {
    return {
      redirect: {
        destination: `/lawyer/my-page/cases/${query.caseId}/confirm`,
        permanent: true,
      },
    };
  }

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

export default CaseDetailPage;

import { Box, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import AdditionalEmailForm from 'components/Lawyer/AdditionalEmail';
import type { AdditionalEmailFormValues } from 'components/Lawyer/AdditionalEmail/schema';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import LoadingOverlay from 'components/LoadingOverlay';
import type { IBlockField } from 'components/UI/ViewEditBlock';
import ViewEditBlock from 'components/UI/ViewEditBlock';
import { useFetchUser } from 'hooks';
import type { LawyerData } from 'hooks/useFetchUser/types';
import useMutate from 'hooks/useMutate';
import { EmailEmptyState } from 'icons';
import authQuery from 'models/auth/query';
import type { ReactElement } from 'react';
import { useState } from 'react';
import { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

export const emailBlock: {
  title: string;
  subTitle: string;
  fields?: IBlockField[];
} = {
  title: '面談関連メール送信先',
  subTitle: '面談関連のお知らせを追加で送信するメールアドレスを設定できます',
};

const MailSetting = () => {
  const [editable, setEditable] = useState(false);
  const {
    data: currentUser,
    refetch: refetchUser,
    isLoading,
  } = useFetchUser<LawyerData>({
    enabled: false,
  });
  const { mutateAsync: updateAdditionalEmail, isLoading: isUpdating } =
    useMutate(authQuery.additionalEmail);
  const additionalEmail = (
    currentUser?.additionalEmail && currentUser.additionalEmail.length > 0
      ? currentUser.additionalEmail
      : ['']
  ).map((mail) => ({ value: mail }));
  const email = currentUser?.email;

  const handleUpdateAdditionalEmail = (
    values: AdditionalEmailFormValues,
    isDirty: boolean,
  ) => {
    if (isDirty) {
      const submitAdditionalEmail = values.additionalEmail.reduce(
        (prev: string[], currentValue: { value?: string }) => {
          if (currentValue.value) {
            return prev.concat(currentValue.value);
          }
          return prev;
        },
        [],
      );
      if (
        submitAdditionalEmail.length !== new Set(submitAdditionalEmail).size
      ) {
        Helper.toast('すでに登録されているメールアドレスです', {
          type: 'error',
        });
      } else {
        updateAdditionalEmail(
          {
            additionalEmail: submitAdditionalEmail,
          },
          {
            onSuccess: async () => {
              await refetchUser();
              setEditable(false);
            },
          },
        );
      }
    } else setEditable(false);
  };

  return (
    <Box>
      <Box bgcolor="white" px={4} py={2} borderRadius={2}>
        <Breadcrumbs omitIndexList={[0]} />
        <Typography fontWeight="bold" fontSize={32} color="heading" mt="11px">
          メール送信先設定
        </Typography>
      </Box>
      <Box position="relative">
        <ViewEditBlock<{ additionalEmail: { value: string }[] }>
          editMode={editable}
          onEdit={() => setEditable(true)}
          detail={{
            additionalEmail,
          }}
          detailRender={
            !isLoading &&
            currentUser?.additionalEmail.length === 0 && (
              <Box mt="4px">
                <Box display="flex" justifyContent="center">
                  <EmailEmptyState />
                </Box>
                <Typography textAlign="center" mt={1}>
                  メールアドレスが設定されていません
                </Typography>
              </Box>
            )
          }
          block={{
            ...emailBlock,
            fields: (additionalEmail || []).map((_, index) => ({
              label: `メールアドレス (${index + 1})`,
              path: `additionalEmail.${index}.value`,
            })),
          }}
          formId="job-form"
        >
          <AdditionalEmailForm
            loading={isUpdating}
            onCancel={() => {
              setEditable(false);
            }}
            onSubmit={handleUpdateAdditionalEmail}
            defaultValues={{
              email,
              additionalEmail,
            }}
          />
        </ViewEditBlock>
        <LoadingOverlay visible={isLoading} />
      </Box>
    </Box>
  );
};

MailSetting.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.LAWYER}>{page}</SideMenuLayout>
    </Layout>
  );
};

export default MailSetting;

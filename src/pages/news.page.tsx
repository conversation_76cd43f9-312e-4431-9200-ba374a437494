import {
  Box,
  Container,
  Pagination,
  PaginationItem,
  Typography,
} from '@mui/material';
import NewLayout from 'components/NewHome/Layout';
import { useFetchList } from 'hooks';
import type { INewsItem } from 'models/news/interface';
import newsQuery from 'models/news/query';
import Link from 'next/link';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import React from 'react';

const NewsItem: React.FC<{
  date: string;
  title: string;
  id: string;
  processHtmlContent: (content: string) => string;
}> = ({ date, title, id, processHtmlContent }) => {
  const handleRowClick = (e: React.MouseEvent) => {
    // Don't navigate if user clicked on a link or any element inside a link
    const target = e.target as HTMLElement;
    if (target.tagName === 'A' || target.closest('a')) {
      return;
    }
    // Navigate to news detail page
    window.location.href = `/news/${id}`;
  };

  return (
    <Box
      onClick={handleRowClick}
      sx={{
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        alignItems: 'flex-start',
        width: '100%',
        gap: { xs: 1, md: '50px' },
        py: 2,
        borderBottom: '1px solid #FF5400',
        textDecoration: 'none',
        cursor: 'pointer',
        '&:hover': {
          bgcolor: 'rgba(255, 84, 0, 0.05)',
        },
      }}
    >
      <Typography
        sx={{
          fontSize: '16px',
          fontWeight: 500,
          color: '#262626',
          minWidth: '120px',
          flexShrink: 0,
          lineHeight: 1.5,
        }}
      >
        {date}
      </Typography>
      <Typography
        sx={{
          fontSize: '16px',
          fontWeight: 500,
          color: '#262626',
          flex: 1,
          lineHeight: 1.5,
          overflow: 'hidden',
          maxHeight: { xs: 'calc(1.5em * 3)', md: 'calc(1.5em * 2)' },
          // Normalize HTML elements to prevent height variations
          '& *': {
            fontSize: 'inherit !important',
            fontWeight: 'inherit !important',
            lineHeight: 'inherit !important',
            margin: '0 !important',
            padding: '0 !important',
            display: 'inline !important',
            verticalAlign: 'baseline !important',
          },
          '& a': {
            color: 'inherit',
            textDecoration: 'underline',
            cursor: 'pointer',
            position: 'relative',
            zIndex: 1,
            '&:hover': {
              opacity: 0.8,
            },
          },
        }}
        dangerouslySetInnerHTML={{ __html: processHtmlContent(title) }}
      />
    </Box>
  );
};

const NewsPage = () => {
  const router = useRouter();
  const page = Number(router.query.page || 1);

  // Process HTML content to fix incomplete URLs and normalize for consistent height
  const processHtmlContent = (htmlContent: string) => {
    if (!htmlContent) return '';

    // First decode HTML entities
    const textArea = document.createElement('textarea');
    textArea.innerHTML = htmlContent;
    let decodedContent = textArea.value;

    // Replace href attributes that don't start with http://, https://, mailto:, tel:, or #
    decodedContent = decodedContent.replace(
      /href="([^"]*?)"/g,
      (match, url) => {
        // Skip if URL already has protocol, is relative (starts with /), is anchor (starts with #), or is special protocol
        if (
          url.startsWith('http://') ||
          url.startsWith('https://') ||
          url.startsWith('mailto:') ||
          url.startsWith('tel:') ||
          url.startsWith('#') ||
          url.startsWith('/')
        ) {
          return match;
        }

        // Add https:// to incomplete URLs
        return `href="https://${url}"`;
      },
    );

    // Add target="_blank" and rel="noopener noreferrer" to external links for security
    decodedContent = decodedContent.replace(
      /<a\s+([^>]*?)href="([^"]*?)"([^>]*?)>/gi,
      (match, beforeHref, url, afterHref) => {
        // Check if it's an external link
        const isExternal =
          url.startsWith('http://') ||
          url.startsWith('https://') ||
          url.startsWith('mailto:') ||
          url.startsWith('tel:');

        if (isExternal) {
          // Add target and rel attributes if not already present
          let attributes = beforeHref + afterHref;
          if (!attributes.includes('target=')) {
            attributes += ' target="_blank"';
          }
          if (!attributes.includes('rel=')) {
            attributes += ' rel="noopener noreferrer"';
          }
          return `<a ${attributes} href="${url}">`;
        }

        return match;
      },
    );

    // Remove problematic block elements that can cause height issues
    decodedContent = decodedContent.replace(/<\/(div|p|h[1-6]|br)>/gi, ' ');
    decodedContent = decodedContent.replace(/<(div|p|h[1-6]|br)[^>]*>/gi, ' ');

    // Clean up multiple spaces
    decodedContent = decodedContent.replace(/\s+/g, ' ').trim();

    return decodedContent;
  };

  // Fetch news items with pagination
  const {
    list: newsList,
    isLoading,
    total,
  } = useFetchList<INewsItem>({
    ...newsQuery.newsListPaginated,
    customParams: {
      ...newsQuery.newsListPaginated.customParams,
      page,
    },
  });

  const pageCount = Math.ceil(total / 20);

  // Format date to JST time format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    // Add 9 hours for JST
    date.setHours(date.getHours() + 9);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}.${month}.${day}`;
  };

  const handlePageChange = (_: React.ChangeEvent<unknown>, value: number) => {
    router.push({
      pathname: '/news',
      query: { page: value },
    });
  };

  // Render news content based on loading/empty state
  const renderNewsContent = () => {
    if (isLoading) {
      return Array(10)
        .fill(0)
        .map((_, index) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              alignItems: 'flex-start',
              width: '100%',
              gap: { xs: 1, md: '50px' },
              py: 2,
              borderBottom: '1px solid #FF5400',
            }}
          >
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: 500,
                color: '#262626',
                minWidth: '120px',
                flexShrink: 0,
                bgcolor: '#eee',
                height: '24px',
                width: '80px',
                borderRadius: '4px',
              }}
            />
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: 500,
                color: '#262626',
                bgcolor: '#eee',
                height: '24px',
                width: '70%',
                borderRadius: '4px',
              }}
            />
          </Box>
        ));
    }

    if (newsList.length === 0) {
      return (
        <Typography sx={{ textAlign: 'center', py: 4 }}>
          現在お知らせはありません
        </Typography>
      );
    }

    return newsList.map((item) => (
      <NewsItem
        key={item._id}
        id={item._id}
        date={formatDate(item.createdAt)}
        title={item.title}
        processHtmlContent={processHtmlContent}
      />
    ));
  };

  return (
    <Box>
      <Box
        component="section"
        sx={{
          width: '100%',
          bgcolor: 'white',
          py: { xs: 6, md: 9 },
        }}
      >
        <Container maxWidth="lg">
          {/* Title */}
          <Typography
            variant="h1"
            sx={{
              fontSize: { xs: '28px', md: '36px' },
              fontWeight: 700,
              color: '#262626',
              textAlign: 'center',
              mb: { xs: 6, md: 8 },
            }}
          >
            NEWS
          </Typography>

          {/* News Items */}
          <Box
            sx={{
              width: '100%',
              maxWidth: '1098px',
              display: 'flex',
              flexDirection: 'column',
              mx: 'auto',
              mb: { xs: 4, md: 6 },
            }}
          >
            {renderNewsContent()}
          </Box>

          {/* Pagination */}
          {pageCount > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={pageCount}
                page={page}
                onChange={handlePageChange}
                color="primary"
                shape="rounded"
                sx={{
                  '.MuiPaginationItem-root': {
                    color: '#262626',
                  },
                  '.Mui-selected': {
                    bgcolor: '#FF5400 !important',
                    color: 'white !important',
                  },
                }}
                renderItem={(item) => (
                  <PaginationItem
                    component={Link}
                    href={`/news?page=${item.page}`}
                    {...item}
                  />
                )}
              />
            </Box>
          )}
        </Container>
      </Box>
    </Box>
  );
};

NewsPage.getLayout = function getLayout(page: ReactElement) {
  return <NewLayout>{page}</NewLayout>;
};

export default NewsPage;

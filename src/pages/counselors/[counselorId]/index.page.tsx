import { dehydrate, QueryClient } from '@tanstack/react-query';
import Layout from 'components/Layout';
import dayjs from 'dayjs';
import { useFetchDetail } from 'hooks';
import articleQuery from 'models/article/query';
import type { ICalendarDetail } from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import ProviderDetailPage from 'shared/provider/providers/[providerId]';
import api from 'utils/api';
import { ARTICLE_SORT_ORDER, ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

const CounselorDetailPage = () => {
  const { query } = useRouter();
  const counselorId = query.counselorId as string;
  const startTime = dayjs().startOf('d').toISOString();
  const { detail: calendarDetail } = useFetchDetail<ICalendarDetail>({
    ...providerQuery.getCalendarList(counselorId as string, startTime),
  });

  return (
    <ProviderDetailPage
      providerId={counselorId}
      providerType={ProviderType.COUNSELOR}
      calendarDetail={calendarDetail}
    />
  );
};

CounselorDetailPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export async function getStaticPaths() {
  const { data } = await api.get(
    providerQuery.providerList(ProviderType.COUNSELOR).apiUrl,
    {
      params: { limit: 9999, providerType: [ProviderType.COUNSELOR] },
    },
  );
  return {
    paths: data.docs.map((doc: { _id: string }) => ({
      params: { counselorId: doc._id },
    })),
    fallback: 'blocking',
  };
}
export async function getStaticProps({ params }: any) {
  const qClient = new QueryClient();
  const providerId = params.counselorId as string;

  const promises = [
    Helper.prefetchDetail(
      providerQuery.providerDetailByGuest(providerId),
      qClient,
    ),
    Helper.prefetchList(providerQuery.providerSolutions(providerId), qClient, {
      providerId,
    }),
    Helper.prefetchList(
      providerQuery.providerArticleList(providerId),
      qClient,
      {
        providerId,
        sort: 'updatedAt.desc',
      },
    ),
    Helper.prefetchList(articleQuery.pickupArticleList, qClient, {
      providerId,
      sort: ARTICLE_SORT_ORDER,
    }),
    Helper.prefetchList(
      providerQuery.providerConsultationsByGuest(providerId),
      qClient,
      {
        providerId,
      },
    ),
  ];
  await Promise.all(promises);
  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
    revalidate: 1,
  };
}
export default CounselorDetailPage;

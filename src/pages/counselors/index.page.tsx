import { dehydrate, QueryClient } from '@tanstack/react-query';
import Layout from 'components/Layout';
import providerQuery from 'models/provider/query';
import type { GetServerSideProps } from 'next';
import type { ReactElement } from 'react';
import ProviderList from 'shared/provider/providers';
import { PROVIDER_LIMIT, ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

const CounselorListPage = () => {
  return <ProviderList providerType={ProviderType.COUNSELOR} />;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
}) => {
  const qClient = new QueryClient();
  if (req.url?.startsWith('/_next')) {
    return {
      props: {
        enabled: true,
      },
    };
  }
  await Helper.prefetchList(
    providerQuery.providerList(ProviderType.COUNSELOR),
    qClient,
    { ...query, providerType: [ProviderType.COUNSELOR], limit: PROVIDER_LIMIT },
  );

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

CounselorListPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};
export default CounselorListPage;

import { Box, Container, Grid, Typography } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import {
  AboutLawyer,
  ArticleSection,
  BannerSection,
  ExternalArticleSection,
  // MiddleBanner,
  PickUpArticleSection,
} from 'components/Home';
import ExperienceSection from 'components/Home/ExperienceSection';
import ExplainSection from 'components/Home/ExplainSection';
import HowToUseSection from 'components/Home/HowToUseSection';
import NewProviderList from 'components/Home/ProviderList';
import Layout from 'components/Layout';
import { useFetchList, useFetchUser } from 'hooks';
import { t } from 'i18n';
import { isEmpty } from 'lodash';
import type {
  IArticleListItem,
  IExternalArticleListItem,
  IPickupArticleListItem,
} from 'models/article/interface';
import articleQuery from 'models/article/query';
import type { ICounselorItem, ILawyerItem } from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import dynamic from 'next/dynamic';
import type { ReactElement } from 'react';
import { Link as ScrollLink } from 'react-scroll';
import {
  ARTICLE_SORT_ORDER,
  PROVIDER_LIMIT,
  ProviderType,
} from 'utils/constants';
import Helper from 'utils/helpers';

import styles from '../styles';

const NewConsultationButton = dynamic(
  () => import('components/Home/HomeConsultationButton'),
  { ssr: false },
);

const OldHome = () => {
  const { isSuccess } = useFetchUser({ enabled: false });
  const { list: lawyerList, isLoading: isLawyerLoading } =
    useFetchList<ILawyerItem>({
      ...providerQuery.providerList(ProviderType.LAWYER),
      customParams: {
        providerType: [ProviderType.LAWYER],
        limit: PROVIDER_LIMIT,
      },
    });
  const { list: counselorList, isLoading: isCounselorLoading } =
    useFetchList<ICounselorItem>({
      ...providerQuery.providerList(ProviderType.COUNSELOR),
      customParams: {
        providerType: [ProviderType.COUNSELOR],
        limit: PROVIDER_LIMIT,
      },
    });
  const { list: pickupArticleList } = useFetchList<IPickupArticleListItem>({
    ...articleQuery.pickupArticleList,
    enabled: false,
    staleTime: 60 * 1000,
    customParams: { sort: ARTICLE_SORT_ORDER },
  });
  const { list: articleList } = useFetchList<IArticleListItem>({
    ...articleQuery.articleList,
    enabled: false,
    staleTime: 60 * 1000,
    customParams: { sort: ARTICLE_SORT_ORDER },
  });
  const { list: externalArticleList } = useFetchList<IExternalArticleListItem>({
    ...articleQuery.externalArticleList,
    // enabled: false,
    staleTime: 60 * 1000,
    customParams: { sort: 'updatedAt.desc' },
  });
  const navigationSection = [
    {
      label: t('home.lawyerSection'),
      href: 'lawyer-list',
    },
    {
      label: t('home.articleSection'),
      href: 'pickup-articles',
    },
    {
      label: t('home.externalArticleSection'),
      href: 'external-articles',
    },
    {
      label: t('home.aboutLawyerSection'),
      href: 'aboutLawyer',
    },
  ];

  return (
    <Box bgcolor="white">
      <Box
        bgcolor="orange"
        position="relative"
        zIndex={1}
        sx={{ display: { sm: 'block', xs: 'none' } }}
      >
        <Container maxWidth="lg" disableGutters>
          <Grid container>
            {navigationSection.map((section) => (
              <Grid item xs={3} p={1} key={section.href}>
                <ScrollLink href="#" to={section.href} smooth offset={-96}>
                  <Typography className="pointer" sx={styles.navigationText}>
                    {section.label}
                  </Typography>
                </ScrollLink>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
      <Box sx={styles.line}>
        <Container maxWidth="xl" disableGutters sx={styles.line} />
      </Box>
      <BannerSection />
      <Box
        p={
          isSuccess
            ? { xs: '48px 24px', tablet: '40px 16px 48px' }
            : { xs: '40px 24px', tablet: '48px 16px' }
        }
        display="flex"
        justifyContent="center"
        minHeight={{ xs: 178, tablet: 232 }}
      >
        <NewConsultationButton />
      </Box>
      <ExplainSection />
      <NewProviderList
        isLawyerLoading={isLawyerLoading}
        isCounselorLoading={isCounselorLoading}
        lawyers={lawyerList}
        counselors={counselorList}
      />
      <Box sx={{ bgcolor: '#cedae0', height: '4px' }} />
      {!isEmpty(pickupArticleList) && (
        <PickUpArticleSection data={pickupArticleList} />
      )}
      <Box
        p={
          isSuccess
            ? { xs: '40px 24px', tablet: '68px 16px' }
            : { xs: '48px 24px', tablet: '40px 16px 48px' }
        }
        display="flex"
        justifyContent="center"
        bgcolor="#fff5cc"
      >
        <NewConsultationButton />
      </Box>
      {!isEmpty(articleList) && <ArticleSection data={articleList} />}

      <Box sx={{ height: '4px', bgcolor: '#cedae0' }}></Box>
      <HowToUseSection />
      <Box sx={{ height: '4px', bgcolor: '#cedae0' }}></Box>
      <ExperienceSection />
      <Box
        p={
          isSuccess
            ? { xs: '40px 24px', tablet: '68px 16px' }
            : { xs: '48px 24px', tablet: '40px 16px 48px' }
        }
        display="flex"
        justifyContent="center"
        bgcolor="#fff5cc"
      >
        <NewConsultationButton />
      </Box>
      {!isEmpty(externalArticleList) && (
        <ExternalArticleSection data={externalArticleList} />
      )}
      <AboutLawyer />
    </Box>
  );
};

export async function getStaticProps() {
  const qClient = new QueryClient();

  const promises = [
    Helper.prefetchList(articleQuery.articleList, qClient, {
      sort: ARTICLE_SORT_ORDER,
    }),
    Helper.prefetchList(articleQuery.pickupArticleList, qClient, {
      sort: ARTICLE_SORT_ORDER,
    }),
    // Helper.prefetchList(articleQuery.externalArticleList, {
    //   sort: 'updatedAt.desc',
    // }),
  ];

  await Promise.all(promises);

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
    revalidate: Number(process.env.HOME_REVALIDATE || 10),
  };
}

OldHome.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export default OldHome;

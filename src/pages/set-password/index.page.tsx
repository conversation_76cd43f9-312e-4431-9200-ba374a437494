import { yupResolver } from '@hookform/resolvers/yup';
import { Loading<PERSON>utton } from '@mui/lab';
import { Box, Container, Stack, Typography } from '@mui/material';
import { TextField } from 'components/Form';
import Layout from 'components/Layout';
import useMutate from 'hooks/useMutate';
import i18n from 'i18n';
import type { ResetPasswordPayload } from 'models/auth/interface';
import authQuery from 'models/auth/query';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import api from 'utils/api';
import { Regex, ROLES } from 'utils/constants';
import type { AnyObjectSchema } from 'yup';
import { object, ref, string } from 'yup';

const schema = (t: (key: string) => string): AnyObjectSchema =>
  object().shape({
    password: string()
      .required()
      .test(
        'checkWhiteSpace',
        t('validation.notAllowedWhiteSpace'),
        (value) => {
          return !Regex.WHITESPACE.test(value as string);
        },
      )
      .min(8, t('validation.passwordRule'))
      .matches(Regex.PASSWORD, t('validation.invalidField')),
    confirmPassword: string()
      .required()
      .test(
        'checkWhiteSpace',
        t('validation.notAllowedWhiteSpace'),
        (value) => {
          return !Regex.WHITESPACE.test(value as string);
        },
      )
      .min(8, t('validation.passwordRule'))
      .matches(Regex.PASSWORD, t('validation.invalidField'))
      .oneOf([ref('password')], t('validation.notMatchedPasswords')),
  });

export type FormFields = {
  confirmPassword: string;
  password: string;
};

const SetPassword = ({ token }: { token: string }) => {
  const { replace, query } = useRouter();
  const { role } = query;
  const { t } = i18n;
  const { control, handleSubmit, trigger } = useForm<FormFields>({
    resolver: yupResolver(schema(t)),
    mode: 'onTouched',
    reValidateMode: 'onBlur',
  });
  const watchPassword = useWatch({ name: 'password', control });
  const watchConfirmPassword = useWatch({ name: 'confirmPassword', control });

  useEffect(() => {
    if (watchConfirmPassword && watchPassword) {
      trigger('confirmPassword', { shouldFocus: false });
    }
  }, [trigger, watchPassword, watchConfirmPassword]);

  const { mutateAsync: resetPassword, isLoading } =
    useMutate<ResetPasswordPayload>(authQuery.resetPassword(role as string));

  const handleResetPassword = (values: FormFields) => {
    resetPassword(
      { ...values, token },
      {
        onSuccess: () => {
          replace('/');
        },
      },
    );
  };

  return (
    <Container maxWidth="mw" disableGutters>
      <Box
        p={{ xs: 2, tablet: 4 }}
        borderRadius={{ xs: 0, tablet: 2 }}
        bgcolor="white"
        mt={{ xs: 1, tablet: 4 }}
      >
        <Typography
          fontSize={{ xs: 24, tablet: 32 }}
          fontWeight="bold"
          color="heading"
          textAlign="center"
        >
          {t('setting.changePassword')}
        </Typography>
        <form onSubmit={handleSubmit(handleResetPassword)}>
          <Stack spacing={{ xs: '20px', tablet: 3 }} mt={{ xs: 4, tablet: 5 }}>
            <TextField
              name="password"
              control={control}
              label={t('verifyEmail.newPassword')}
              placeholder={t('placeholder.newPassword')}
              type="password"
              labelCol={12}
            />
            <TextField
              name="confirmPassword"
              control={control}
              label={t('verifyEmail.confirmNewPassword')}
              placeholder={t('placeholder.newConfirmPassword')}
              type="password"
              labelCol={12}
            />
          </Stack>
          <Box display="flex" justifyContent="center" mt={{ xs: 4, tablet: 5 }}>
            <LoadingButton
              fullWidth
              type="submit"
              loading={isLoading}
              variant="contained"
              color="secondary"
              size="large"
              className="tabletStyle"
              sx={{ maxWidth: '388px' }}
            >
              {t('global.updateInfo')}
            </LoadingButton>
          </Box>
        </form>
      </Box>
    </Container>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  const { token, role } = query;
  await api.post(
    role === ROLES.LAWYER
      ? '/account/provider/verifyTokenForgotPassword'
      : '/account/consumer/verifyTokenForgotPassword',
    { token },
  );
  return {
    props: { token },
  };
};

SetPassword.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};
export default SetPassword;

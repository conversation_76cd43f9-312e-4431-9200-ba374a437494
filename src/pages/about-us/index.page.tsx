import { Box } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import { GuideSection, PickUpArticleSection } from 'components/Home';
import Layout from 'components/Layout';
import AboutUsSection from 'components/UI/AboutUsSection';
import { useFetchList } from 'hooks';
import type { IPickupArticleListItem } from 'hooks/types';
import { isEmpty } from 'lodash';
import articleQuery from 'models/article/query';
import { NextSeo } from 'next-seo';
import type { ReactElement } from 'react';
import { ARTICLE_SORT_ORDER } from 'utils/constants';
import Helper from 'utils/helpers';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const About = () => {
  const fetchPickupArticleList = useFetchList({
    ...articleQuery.pickupArticleList,
    enabled: false,
    customParams: { sort: ARTICLE_SORT_ORDER },
  });

  const pickupArticleList =
    fetchPickupArticleList.list as IPickupArticleListItem[];

  return (
    <Box bgcolor="white" sx={styles.page}>
      <NextSeo {...seoConfig.aboutUs} />
      <AboutUsSection />
      <GuideSection />
      {!isEmpty(pickupArticleList) && (
        <PickUpArticleSection data={pickupArticleList} />
      )}
    </Box>
  );
};

export async function getStaticProps() {
  const qClient = new QueryClient();
  await Helper.prefetchList(articleQuery.pickupArticleList, qClient, {
    sort: ARTICLE_SORT_ORDER,
  });

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
    revalidate: 1,
  };
}

About.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export default About;

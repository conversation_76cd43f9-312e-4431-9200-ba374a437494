import { Box } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import ConsultingBanner from 'components/NewHome/ConsultingBanner';
import CTASection from 'components/NewHome/CTA';
import ExpertsSection from 'components/NewHome/Experts';
import HowToSection from 'components/NewHome/HowTo';
import NewLayout from 'components/NewHome/Layout';
import PricingSection from 'components/NewHome/Pricing';
import SearchSection from 'components/NewHome/SearchSection';
import ThemesSection from 'components/NewHome/Themes';
import type { ReactElement } from 'react';

const ConsultingMarriageWorries = () => {
  return (
    <Box>
      <Box component="section">
        <ConsultingBanner />
      </Box>
      <Box component="section">
        <SearchSection />
      </Box>
      <Box
        component="section"
        id="experts-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <ExpertsSection />
      </Box>
      <Box
        component="section"
        id="themes-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <ThemesSection />
      </Box>
      <Box
        component="section"
        id="howto-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <HowToSection />
      </Box>
      <Box component="section">
        <CTASection />
      </Box>
      <Box
        component="section"
        id="pricing-section"
        sx={{ scrollMarginTop: '120px' }}
      >
        <PricingSection />
      </Box>
      <Box component="section">
        <CTASection />
      </Box>
    </Box>
  );
};

export async function getStaticProps() {
  const qClient = new QueryClient();

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
    revalidate: Number(process.env.HOME_REVALIDATE || 10),
  };
}

ConsultingMarriageWorries.getLayout = function getLayout(page: ReactElement) {
  return <NewLayout>{page}</NewLayout>;
};

export default ConsultingMarriageWorries;

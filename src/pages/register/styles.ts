import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  page: {
    p: { tablet: 4 },
  },
  container: {
    mt: {
      xs: 1,
      tablet: 0,
    },
    p: { xs: 2, tablet: '29px 32px 32px' },
    bgcolor: 'white',
    borderRadius: { tablet: 2 },
    '.term-policy-container': {
      mt: { xs: '22px', tablet: '20px' },
    },
  },
  storyContainer: {
    p: { xs: '40px 16px', tablet: '40px 32px 32px' },
    mt: {
      xs: 1,
      tablet: 2,
    },
    bgcolor: 'white',
    borderRadius: { tablet: 2 },
  },
  button: {
    maxWidth: '384px',
  },
  title: {
    fontSize: { xs: '24px', tablet: '32px' },
    fontWeight: 'bold',
    color: '#FCAB28',
    textAlign: 'center',
  },
  itemWrapper: {
    borderRadius: '12px',
    bgcolor: '#fff5cc',
    padding: { xs: '16px 20px', tablet: '24px 32px' },
    display: 'flex',
    gap: { xs: 3, tablet: 4 },
    svg: {
      width: { xs: 70, tablet: 101 },
      height: { xs: 70, tablet: 101 },
    },
  },
  contentText: {
    fontSize: { xs: 14, tablet: 20 },
    lineHeight: { xs: '21px', tablet: '35px' },
  },
  contactBtn: {
    maxWidth: 358,
    marginTop: '32px',
    backgroundColor: '#fff',
    '&:hover': { backgroundColor: '#fff' },
    '&.shadow': {
      '@media (max-width: 768px)': {
        borderRadius: 24,
        height: 48,
        fontSize: 14,
        fontWeight: 700,
        svg: {
          width: 24,
          height: 24,
        },
      },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

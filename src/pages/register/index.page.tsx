import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Container, Stack, Typography } from '@mui/material';
import { TextField } from 'components/Form';
import Layout from 'components/Layout';
import TermPolicy from 'components/UI/TermPolicy';
import TitleButton from 'components/UI/TitleButton';
import { useAuth } from 'hooks';
import useGlobalState from 'hooks/useGlobalState';
import i18n from 'i18n';
import {
  ArrowRightIcon,
  StoryIcon1,
  StoryIcon2,
  StoryIcon3,
  StoryIcon4,
} from 'icons';
import Link from 'next/link';
import { useRouter } from 'next/router';
import Script from 'next/script';
import type { ReactElement } from 'react';
import { useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { Regex } from 'utils/constants';
import Helper from 'utils/helpers';
import { object, ref, string } from 'yup';

import styles from './styles';

const experienceContents = [
  {
    icon: <StoryIcon1 />,
    content: (
      <Typography sx={styles.contentText} color="#464646" fontWeight={500}>
        <Typography
          sx={styles.contentText}
          component="span"
          color="#FCAB28"
          mr={{ xs: '2px', tablet: '4px' }}
          fontWeight={700}
        >
          カルテの入力
        </Typography>
        で{'\n'}
        自分が何で悩んでいるかを{'\n'}考える機会になりました
      </Typography>
    ),
  },
  {
    icon: <StoryIcon3 />,
    content: (
      <Typography sx={styles.contentText} color="#464646" fontWeight={500}>
        <Typography
          sx={styles.contentText}
          component="span"
          color="#FCAB28"
          mr={{ xs: '2px', tablet: '4px' }}
          fontWeight={700}
        >
          専門家のプロフに動画
        </Typography>
        もあり{'\n'}人柄までイメージしやすくて{'\n'}安心できました
      </Typography>
    ),
  },
  {
    icon: <StoryIcon2 />,
    content: (
      <Typography sx={styles.contentText} color="#464646" fontWeight={500}>
        <Typography
          sx={styles.contentText}
          component="span"
          color="#FCAB28"
          mr={{ xs: '2px', tablet: '4px' }}
          fontWeight={700}
        >
          相談したら離婚が加速する？
        </Typography>
        {'\n'}
        と思っていたが相談を通して{'\n'}冷静な整理ができました
      </Typography>
    ),
  },
  {
    icon: <StoryIcon4 />,
    content: (
      <Typography sx={styles.contentText} color="#464646" fontWeight={500}>
        <Typography
          sx={styles.contentText}
          component="span"
          color="#FCAB28"
          mr={{ xs: '2px', tablet: '4px' }}
          fontWeight={700}
        >
          弁護士とカウンセラー
        </Typography>
        {'\n'}
        両方に相談できて手続きも{'\n'}気持ちも助けられました
      </Typography>
    ),
  },
];
interface RegisterFormValues {
  email: string;
  confirmEmail: string;
  password: string;
  confirmPassword: string;
}

function ConfirmRegisterContent() {
  const { t } = i18n;

  useEffect(() => {
    Helper.setEventTracking({
      ID: 'temporary_registration_completed_popup',
    });
  }, []);

  return (
    <>
      <Typography>{t('register.verifyEmail')}</Typography>
      <img
        src="https://step.lme.jp/p/135442/L7MoCTHo"
        id="get_image"
        alt=""
        style={{
          width: 0,
          height: 0,
          visibility: 'hidden',
        }}
      />
      <br />
      <Script
        src="https://step.lme.jp/js/conversion/get_url_target_page.js"
        strategy="lazyOnload"
      />
    </>
  );
}

const RegisterPage = () => {
  const { setConfirmModal, setLoginModal } = useGlobalState();
  const { registerCustomer, isRegistering } = useAuth();
  const { push } = useRouter();
  const { t } = i18n;
  const [agree, setAgree] = useState(false);

  const schema = object().shape({
    email: string()
      .trim()
      .required()
      .matches(Regex.EMAIL, t('validation.invalidField')),
    confirmEmail: string()
      .trim()
      .required()
      .matches(Regex.EMAIL, t('validation.invalidField'))
      .oneOf([ref('email')], t('validation.notMatchedEmails')),
    password: string()
      .required()
      .test(
        'checkWhiteSpace',
        t('validation.notAllowedWhiteSpace'),
        (value) => {
          return !Regex.WHITESPACE.test(value as string);
        },
      )
      .min(8, t('validation.passwordRule'))
      .matches(Regex.PASSWORD, t('validation.invalidField')),
    confirmPassword: string()
      .required()
      .test(
        'checkWhiteSpace',
        t('validation.notAllowedWhiteSpace'),
        (value) => {
          return !Regex.WHITESPACE.test(value as string);
        },
      )
      .min(8, t('validation.passwordRule'))
      .matches(Regex.PASSWORD, t('validation.invalidField'))
      .oneOf([ref('password')], t('validation.notMatchedPasswords')),
  });

  const handleRegister = (values: RegisterFormValues) => {
    registerCustomer(values, {
      onSuccess: () =>
        setConfirmModal({
          title: 'register.temporaryRegistration',
          onConfirm: () => {
            Helper.setEventTracking({
              ID: 'temporary_registration_completed',
            });
            push('/');
          },
          content: <ConfirmRegisterContent />,
          hideCancelButton: true,
          contentAlign: 'left',
        }),
    });
  };

  const {
    control,
    handleSubmit,
    trigger,
    formState: { isValid },
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    reValidateMode: 'onBlur',
  });

  const watchPassword = useWatch({ name: 'password', control });
  const watchConfirmPassword = useWatch({ name: 'confirmPassword', control });

  useEffect(() => {
    if (watchConfirmPassword && watchPassword) {
      trigger('confirmPassword', { shouldFocus: false });
    }
  }, [trigger, watchPassword, watchConfirmPassword]);

  return (
    <div>
      <Box sx={styles.page}>
        <Container maxWidth="mw" sx={styles.container}>
          <form onSubmit={handleSubmit(handleRegister)}>
            <Typography
              fontSize={{ xs: 24, tablet: 32 }}
              fontWeight="bold"
              textAlign="center"
              color="orange"
              mb={{ xs: 3, tablet: 4 }}
            >
              無料登録
            </Typography>
            <Typography mb="4px" align="center" color="#464646">
              メールだけで簡単30秒{'\n'}登録後、
              <Typography component="span" color="orange">
                無料相談
              </Typography>
              をご体験ください
            </Typography>
            <Typography
              color="#464646"
              align="center"
              fontSize={{ xs: 10, tablet: 12 }}
              mb={4}
            >
              ※初回無料、無料時間は専門家により異なる場合があります
            </Typography>
            <Stack spacing={2}>
              <TextField
                name="email"
                control={control}
                label={t('login.email')}
                autoComplete="email"
                placeholder={t('placeholder.email')}
                labelCol={12}
              />
              <TextField
                name="confirmEmail"
                control={control}
                autoComplete="confirmEmail"
                label={t('register.confirmEmail')}
                placeholder={t('placeholder.confirmEmail')}
                labelCol={12}
              />
              <TextField
                name="password"
                autoComplete="password"
                control={control}
                label={t('login.password')}
                type="password"
                placeholder={t('placeholder.password')}
                labelCol={12}
              />
              <TextField
                name="confirmPassword"
                autoComplete="confirm-password"
                control={control}
                label={t('register.confirmPassword')}
                type="password"
                placeholder={t('placeholder.confirmPassword')}
                labelCol={12}
              />
            </Stack>
            <TermPolicy
              linkProps={{ color: 'orange' }}
              checked={agree}
              onChange={(data) => setAgree(data)}
            />
            <Box
              display="flex"
              justifyContent="center"
              mt={{ xs: 4, tablet: '18px' }}
            >
              <TitleButton
                title="無料相談を体験"
                text="無料登録"
                subText="［ 秘密厳守 ］"
                loading={isRegistering}
                disabled={!agree || !isValid}
                type="submit"
                showArrow={false}
              />
            </Box>
            <Typography
              color="orange"
              fontSize={14}
              mt={3}
              fontWeight={500}
              textAlign="center"
              className="pointer"
              onClick={() => setLoginModal(true)}
            >
              登録済みの方はこちら＞
            </Typography>
          </form>
        </Container>
        <Container maxWidth="mw" sx={styles.storyContainer}>
          <Typography sx={styles.title}>リコ活の体験談</Typography>
          <Box className="logo-divider" mt={{ xs: '10px', tablet: 2 }} />
          <Container
            maxWidth="hc"
            disableGutters
            sx={{ mt: { xs: 3, tablet: 4 } }}
          >
            <Box display="flex" flexDirection="column" gap={2}>
              {experienceContents.map((content, index) => (
                <Box key={index} sx={styles.itemWrapper}>
                  {content.icon}
                  <Box flex={1}>{content.content}</Box>
                </Box>
              ))}
            </Box>
          </Container>
        </Container>
        <Container maxWidth="mw" sx={styles.storyContainer}>
          <Typography
            fontSize={{ xs: 24, tablet: 28 }}
            color="orange"
            fontWeight={500}
            textAlign="center"
          >
            離婚、夫婦問題・修復も{'\n'}オンラインで無料相談
          </Typography>
          <Box
            display="flex"
            justifyContent="center"
            mt={4}
            pb={{ tablet: '20px' }}
          >
            <div>
              <TitleButton
                title="メールで簡単30秒"
                text="お悩みを無料相談"
                subText="［ 秘密厳守 ］"
                onClick={() => window.scrollTo(0, 0)}
              />
              <Typography
                color="heading"
                fontSize={{ xs: 12, tablet: 14 }}
                marginTop="13px"
                whiteSpace="pre-line"
                textAlign="center"
              >
                {`※初回無料相談の有無は専門家ごとに異なります。\n各専門家ページをご確認ください。`}
              </Typography>
              <Button
                href="/contact-us"
                variant="outlined"
                color="primary"
                type="submit"
                fullWidth
                size="large"
                sx={styles.contactBtn}
                className="shadow tabletStyle"
                LinkComponent={Link}
                endIcon={<ArrowRightIcon width={32} height={32} color="gold" />}
              >
                専門家の登録はこちら
              </Button>
            </div>
          </Box>
        </Container>
      </Box>
    </div>
  );
};

RegisterPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};
export default RegisterPage;

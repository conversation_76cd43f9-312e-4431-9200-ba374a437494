import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Container, Stack, Typography } from '@mui/material';
import { TextField } from 'components/Form';
import Layout from 'components/Layout';
import { useFetchUser } from 'hooks';
import useMutate from 'hooks/useMutate';
import i18n from 'i18n';
import type { LoginResponse } from 'models/auth/interface';
import authQuery from 'models/auth/query';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { setTimeout } from 'timers';
import api from 'utils/api';
import type { ProviderType } from 'utils/constants';
import { PROVIDER_MY_PAGE_PATH, Regex } from 'utils/constants';
import Helper from 'utils/helpers';
import queryClient from 'utils/queryClient';
import type { AnyObjectSchema } from 'yup';
import { object, ref, string } from 'yup';

const schema = (t: (key: string) => string): AnyObjectSchema =>
  object().shape({
    password: string()
      .required()
      .test(
        'checkWhiteSpace',
        t('validation.notAllowedWhiteSpace'),
        (value) => {
          return !Regex.WHITESPACE.test(value as string);
        },
      )
      .min(8, t('validation.passwordRule'))
      .matches(Regex.PASSWORD, t('validation.invalidField')),
    confirmPassword: string()
      .required()
      .test(
        'checkWhiteSpace',
        t('validation.notAllowedWhiteSpace'),
        (value) => {
          return !Regex.WHITESPACE.test(value as string);
        },
      )
      .min(8, t('validation.passwordRule'))
      .matches(Regex.PASSWORD, t('validation.invalidField'))
      .oneOf([ref('password')], t('validation.notMatchedPasswords')),
  });

export type FormFields = {
  confirmPassword: string;
  password: string;
};

const VerifyEmail = ({ token }: { token: string }) => {
  const { t } = i18n;
  const [enabledUser, setEnabledUser] = useState(false);
  const { replace } = useRouter();
  const { control, handleSubmit, trigger } = useForm<FormFields>({
    resolver: yupResolver(schema(t)),
    mode: 'onTouched',
    reValidateMode: 'onBlur',
  });
  const watchPassword = useWatch({ name: 'password', control });
  const watchConfirmPassword = useWatch({ name: 'confirmPassword', control });

  useEffect(() => {
    if (watchConfirmPassword && watchPassword) {
      trigger('confirmPassword', { shouldFocus: false });
    }
  }, [trigger, watchPassword, watchConfirmPassword]);

  useFetchUser({ enabled: enabledUser });
  const {
    mutateAsync: setLawyerFirstPassword,
    isLoading: isSettingLawyerFirstPassword,
  } = useMutate<{ token: string; password: string }, Required<LoginResponse>>(
    authQuery.setProviderFirstPassword,
  );

  const handleSetFirstPassword = (values: FormFields) => {
    setLawyerFirstPassword(
      { password: values.password, token },
      {
        onSuccess: (data) => {
          queryClient
            .getQueryCache()
            .findAll(['currentUser'])
            .forEach((query) => query.setData(undefined));
          Helper.setToken({
            token: data.token.token,
            role: data.provider.type,
          });
          setEnabledUser(true);
          setTimeout(() =>
            replace(
              `${
                PROVIDER_MY_PAGE_PATH[
                  data.provider.type as unknown as ProviderType
                ]
              }/my-page`,
            ),
          );
        },
      },
    );
  };

  return (
    <Container maxWidth="mw" disableGutters>
      <Box
        p={{ xs: 2, tablet: 4 }}
        borderRadius={{ xs: 0, tablet: 2 }}
        bgcolor="white"
        mt={{ xs: 1, tablet: 4 }}
      >
        <Typography
          fontSize={{ xs: 24, tablet: 32 }}
          fontWeight="bold"
          color="heading"
          textAlign="center"
        >
          {t('setting.changePassword')}
        </Typography>
        <form onSubmit={handleSubmit(handleSetFirstPassword)}>
          <Stack spacing={{ xs: '20px', tablet: 3 }} mt={{ xs: 4, tablet: 5 }}>
            <TextField
              name="password"
              control={control}
              label={t('verifyEmail.newPassword')}
              placeholder={t('placeholder.newPassword')}
              type="password"
              labelCol={12}
            />
            <TextField
              name="confirmPassword"
              control={control}
              label={t('verifyEmail.confirmNewPassword')}
              placeholder={t('placeholder.newConfirmPassword')}
              type="password"
              labelCol={12}
            />
          </Stack>
          <Box display="flex" justifyContent="center" mt={{ xs: 4, tablet: 5 }}>
            <LoadingButton
              fullWidth
              type="submit"
              loading={isSettingLawyerFirstPassword}
              variant="contained"
              color="secondary"
              size="large"
              className="tabletStyle"
              sx={{ maxWidth: '388px' }}
            >
              {t('global.updateInfo')}
            </LoadingButton>
          </Box>
        </form>
      </Box>
    </Container>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  const { token } = query;
  await api.post('account/provider/verifiedToken', { token });
  return {
    props: { token },
  };
};

VerifyEmail.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};
export default VerifyEmail;

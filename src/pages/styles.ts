import type { Theme } from '@mui/material';

const styles = {
  middleBanner: {
    width: '100%',
    height: 'auto',
    display: 'block',
  },
  navigationText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
    transition: '0.4s color',
    lineHeight: '24px',
    '&:hover': {
      color: 'neutral8',
    },
  },
  line: {
    height: { xs: '6px', tablet: '4px' },
    background: (theme: Theme) =>
      `linear-gradient(to right, ${theme.palette.gold} 0px,
        ${theme.palette.gold} 30.27%,
        ${theme.palette.gray} 30.27%)`,
  },
  highlightText: {
    backgroundImage: (theme: Theme) =>
      `linear-gradient(0, ${theme.palette.tertiary} 38%,  transparent 38%)`,
  },
  learnMore: {
    textDecoration: 'underline',
    textDecorationColor: 'white',
    '& svg': {
      color: 'white',
    },
  },
};

export default styles;

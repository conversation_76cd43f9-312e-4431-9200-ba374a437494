import { QueryClient } from '@tanstack/react-query';
import Layout from 'components/Layout';
import ChatFeature from 'features/chat';
import chatQuery from 'models/chat';
import type { IRoomInfo } from 'models/chat/types';
import type { GetServerSideProps } from 'next';
import type { ReactElement } from 'react';
import api from 'utils/api';
import { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

const ChatPage = ({ roomDetail }: { roomDetail: IRoomInfo }) => {
  return <ChatFeature roomDetail={roomDetail} role={ROLES.COUNSELOR} />;
};

ChatPage.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({
  res,
  req,
  query,
}) => {
  try {
    const roomId = query.roomId ? query.roomId[0] : '';
    if (roomId) {
      const qClient = new QueryClient();
      const roomDetail = await qClient.fetchQuery(
        chatQuery.getRoomDetail(roomId).queryKey,
        async () => {
          const { data } = await api.get(
            chatQuery.getRoomDetail(roomId).apiUrl,
            Helper.getTokenConfig(req, res),
          );
          return data;
        },
      );
      return {
        props: {
          roomDetail,
        },
      };
    }
    return {
      props: {},
    };
  } catch (e) {
    return {
      notFound: true,
    };
  }
};

export default ChatPage;

import { LoadingButton } from '@mui/lab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { Box, Stack, Typography } from '@mui/material';
import Tab from '@mui/material/Tab';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import CaseTag from 'components/Case/CaseTag';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import BackButton from 'components/UI/BackButton';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { ChatIcon } from 'icons';
import { get } from 'lodash';
import type {
  IAddCaseMemo,
  IProviderCaseDetail,
  IUpdateCaseStatus,
} from 'models/case/interface';
import caseQuery from 'models/case/query';
import chatQuery from 'models/chat';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import api from 'utils/api';
import { CaseStatusType, ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

import CaseDetail from './case-detail';
import CustomerProfile from './customer-consultation';

const CaseDetailPage = () => {
  const { data: currentUser } = useFetchUser({ enabled: false });
  const { mutateAsync: createRoom, isLoading: isCreatingRoom } = useMutate<
    unknown,
    { roomId: string }
  >(chatQuery.createRoom);
  const { query, replace, pathname } = useRouter();
  const { tab = 'case-detail' } = query;
  const caseId = query.caseId as string;
  const { setConfirmModal } = useGlobalState();
  const fetchCaseDetail = useFetchDetail({
    ...caseQuery.providerCaseDetail(caseId),
    enabled: false,
  });
  const { refetch: refetchCaseDetail } = fetchCaseDetail;
  const caseDetail = fetchCaseDetail.detail as IProviderCaseDetail;
  const fetchCustomerProfile = useFetchDetail({
    ...apiQuery.lawyerCustomerProfile(caseDetail.consumer._id),
  });
  const { refetch: refetchCustomerProfile } = fetchCustomerProfile;
  const customerProfile = fetchCustomerProfile.detail as CustomerData;

  const fetchCustomerConsultation = useFetchDetail({
    ...apiQuery.lawyerCustomerConsultation(caseDetail.consumer._id),
  });
  const { refetch: refetchConsultation } = fetchCustomerConsultation;
  const customerConsultation =
    fetchCustomerConsultation.detail as ConsultationRecord;

  const { mutateAsync: addBookingMemo, isLoading: isAdding } =
    useMutate<IAddCaseMemo>(caseQuery.addMemo(caseId));

  const { mutateAsync: updateCaseStatus, isLoading: isUpdatingStatus } =
    useMutate<IUpdateCaseStatus>(caseQuery.updateCaseStatus(caseId));

  const handleAddCaseMemo = async (values: Omit<IAddCaseMemo, 'caseId'>) => {
    await addBookingMemo(
      {
        ...values,
        caseId,
      },
      {
        onSettled: () => refetchCaseDetail(),
      },
    );
  };

  const handleCloseCase = () => {
    if (caseDetail.canClosed) {
      setConfirmModal({
        title: '案件終了',
        confirmText: '確定',
        content: (
          <Typography fontSize={{ xs: 12, tablet: 14 }}>
            <Typography
              fontWeight={700}
              component="span"
              fontSize={{ xs: 12, tablet: 14 }}
            >
              {caseDetail.consumer.fullName}
            </Typography>
            {`様の案件を終了します。\n案件を終了すると今後の面談の追加はできません。\n(`}
            <Typography
              fontWeight={700}
              component="span"
              fontSize={{ xs: 12, tablet: 14 }}
            >
              {caseDetail.consumer.fullName}
            </Typography>
            様から再度面談リクエストが届いた場合、面談を作成できるようになります。)
          </Typography>
        ),
        onConfirm: () =>
          updateCaseStatus(
            { status: CaseStatusType.CLOSED },
            {
              onSuccess: () => refetchCaseDetail(),
              onError: (e) => {
                if (get(e, 'code') === 'CASE_CLOSED_ERROR') {
                  setConfirmModal({
                    hideCancelButton: true,
                    icon: 'error',
                    content:
                      '未完了の面談があります\nすべての面談の決済を完了するか、面談をキャンセルしてください',
                  });
                }
              },
            },
          ),
      });
    } else {
      updateCaseStatus(
        { status: CaseStatusType.CLOSED },
        {
          onSuccess: () => refetchCaseDetail(),
          onError: (e) => {
            if (get(e, 'code') === 'CASE_CLOSED_ERROR') {
              setConfirmModal({
                hideCancelButton: true,
                icon: 'error',
                content:
                  '未完了の面談があります\nすべての面談の決済を完了するか、面談をキャンセルしてください',
              });
            }
          },
        },
      );
    }
  };

  const handleCreateRoom = () => {
    if (caseDetail.roomInfo?.roomId) {
      setTimeout(() =>
        window.open(
          `/counselor/messages/${caseDetail.roomInfo?.roomId}`,
          '_blank',
        ),
      );
    } else {
      createRoom(
        {
          type: 'ONE_BY_ONE_HAS_CASE',
          memberIds: [
            `CONSUMER_${caseDetail.consumer._id}`,
            `COUNSELOR_${currentUser?._id}`,
          ],
        },
        {
          onSuccess: ({ roomId }) =>
            setTimeout(() =>
              window.open(`/counselor/messages/${roomId}`, '_blank'),
            ),
        },
      );
    }
  };

  return (
    <TabContext value={tab as string}>
      <Box borderRadius={2} p="16px 32px 0px 16px" bgcolor="white">
        <Breadcrumbs
          omitIndexList={[0]}
          transformLabel={{
            '/counselor/my-page/cases/[caseId]': get(
              caseDetail,
              'consumer.fullName',
            ),
          }}
        />
        <Box display="flex" justifyContent="space-between" mt="12px" mb={1}>
          <Stack direction="row" flex={1}>
            <Box mt="4px" mr="3px" ml="11px">
              <BackButton url="/counselor/my-page/cases/" />
            </Box>
            <Box>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography fontSize={32} fontWeight="bold" color="heading">
                  {get(caseDetail, 'consumer.fullName')}
                </Typography>
                <Box>
                  <CaseTag status={caseDetail.status} />
                </Box>
              </Stack>
              <Typography fontSize={14} mt="1px">
                {get(caseDetail, 'consumer.katakanaName')}
              </Typography>
            </Box>
          </Stack>
          <Box>
            <Box display="flex" justifyContent="right" mt="3px">
              <LoadingButton
                variant="tertiary"
                loading={isCreatingRoom}
                fullWidth
                startIcon={<ChatIcon />}
                color="primary"
                className="tabletStyle"
                sx={{
                  minWidth: '148px',
                  mr:
                    caseDetail.status === CaseStatusType.SCHEDULE_IS_ADJUSTED
                      ? 1
                      : 0,
                }}
                onClick={handleCreateRoom}
              >
                チャットする
              </LoadingButton>

              {caseDetail.status === CaseStatusType.SCHEDULE_IS_ADJUSTED && (
                <LoadingButton
                  variant="outlined"
                  fullWidth
                  sx={{ minWidth: 112 }}
                  onClick={handleCloseCase}
                  loading={isUpdatingStatus}
                >
                  案件終了
                </LoadingButton>
              )}
            </Box>
          </Box>
        </Box>

        <TabList
          onChange={(_, newValue) =>
            replace({ pathname, query: { tab: newValue, caseId } }, undefined, {
              shallow: true,
            })
          }
        >
          <Tab label={t('caseDetail.tabTitle')} value="case-detail" />
          <Tab
            label={t('breadcrumb.myPageConsultation')}
            value="customer-detail"
          />
        </TabList>
      </Box>
      <TabPanel value="customer-detail" sx={{ p: 0 }}>
        <CustomerProfile
          data={customerConsultation}
          customerProfile={customerProfile}
          refetchCustomerProfile={refetchCustomerProfile}
          refetchConsultation={refetchConsultation}
          caseStatus={caseDetail.status}
        />
      </TabPanel>

      <TabPanel value="case-detail" sx={{ p: 0 }}>
        <CaseDetail onAddCaseMemo={handleAddCaseMemo} isAdding={isAdding} />
      </TabPanel>
    </TabContext>
  );
};

CaseDetailPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
    </Layout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const qClient = new QueryClient();
  const caseId = query.caseId as string;
  const caseDetail = await qClient.fetchQuery(
    caseQuery.providerCaseDetail(caseId).queryKey,
    async () => {
      const { data } = await api.get(
        caseQuery.providerCaseDetail(caseId).apiUrl,
        Helper.getTokenConfig(req, res),
      );
      return data;
    },
  );
  if (caseDetail.status === CaseStatusType.WAITING_ANWSER_FROM_PROVIDER) {
    return {
      redirect: {
        destination: `/counselor/my-page/cases/${query.caseId}/confirm`,
        permanent: true,
      },
    };
  }

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

export default CaseDetailPage;

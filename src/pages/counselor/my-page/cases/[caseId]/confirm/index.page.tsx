import { dehydrate, QueryClient } from '@tanstack/react-query';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import caseQuery from 'models/case/query';
import type { GetServerSideProps } from 'next';
import type { ReactElement } from 'react';
import ProviderConfirmCasePage from 'shared/provider/my-page/cases/confirm';
import api from 'utils/api';
import { API_PATH, CaseStatusType, ProviderType, ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

// Confirm case that has a first meeting
const ConfirmCasePage = () => {
  return <ProviderConfirmCasePage providerType={ProviderType.COUNSELOR} />;
};

ConfirmCasePage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout showScrollButton={false}>
      <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
    </Layout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const qClient = new QueryClient();
  const caseId = query.caseId as string;
  const caseDetail = await qClient.fetchQuery(
    caseQuery.providerCaseDetail(caseId).queryKey,
    async () => {
      const { data } = await api.get(
        caseQuery.providerCaseDetail(caseId).apiUrl,
        Helper.getTokenConfig(req, res),
      );
      return data;
    },
  );
  if (caseDetail.status !== CaseStatusType.WAITING_ANWSER_FROM_PROVIDER) {
    return {
      redirect: {
        destination: `/${API_PATH.COUNSELOR}/my-page/cases/${caseId}`,
        permanent: true,
      },
    };
  }
  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

export default ConfirmCasePage;

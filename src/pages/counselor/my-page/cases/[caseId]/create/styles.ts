import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  headerCreateMeeting: {
    p: '16px 30px 16px 35px',
    borderRadius: 2,
    bgcolor: 'white',
  },
  backButton: {
    p: '4px',
    svg: {
      width: { xs: 28, tablet: 32 },
      height: { xs: 28, tablet: 32 },
    },
  },
  confirmMeeting: {
    p: { xs: 2, tablet: 4 },
    bgcolor: 'white',
    borderRadius: { xs: 0, tablet: 2 },
  },
  textHeader: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'heading',
  },
  typeMeeting: {
    ml: { xs: 0, tablet: 4 },
    py: { xs: 0, tablet: 3 },
    pb: 0,
  },
  icon: {
    svg: { width: 32, height: 32, display: 'block' },
  },
  confirmWrapper: {
    position: 'sticky',
    bottom: 0,
    mb: 0,
    p: '24px 32px',
    display: 'flex',
    justifyContent: 'center',
    boxShadow: '0 -8px 16px -16px #d2dce1;',
    borderRadius: '16px 16px 0px 0px',
    backgroundColor: 'white',
    alignItems: 'center',
    zIndex: 2,
    '@media (max-width: 887px)': {
      p: '16px',
      borderRadius: 0,
    },
  },
  textPaymentInfo: {
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
  },
  titleText: {
    fontSize: 14,
    color: 'text.primary',
    maxWidth: 384,
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    wordBreak: 'break-word',
    verticalAlign: 'bottom',
  },
  typeMeetingMenu: {
    ml: { xs: 0, tablet: 4 },
    pb: 0,
    pt: 1,
  },
  wrapper: {
    '> .MuiContainer-root': {
      '> .MuiBox-root': {
        paddingBottom: 0,
      },
    },
    '#create-meeting-form': {
      '@media (max-width: 887px)': {
        '> .MuiBox-root': {
          borderRadius: 0,
        },
        '.MuiGrid-container': {
          '> .MuiGrid-root': {
            '> .MuiBox-root': {
              borderRadius: 0,
            },
          },
        },
      },
    },
  },
  wrapperButton: {
    display: 'flex',
    justifyContent: 'flex-end',
    '@media (max-width: 887px)': {
      '> div': {
        width: '100%',
      },
      button: {
        width: '100%',
      },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

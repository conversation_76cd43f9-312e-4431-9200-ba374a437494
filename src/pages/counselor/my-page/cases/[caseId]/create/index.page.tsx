import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Divider, Grid, Stack, Typography } from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import PaymentTooltipButton from 'components/Case/PaymentTooltipButton';
import { Radio, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import NumberField from 'components/Form/NumberField';
import TimeTableField from 'components/Form/TimeTableField';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import ButtonBack from 'components/UI/BackButton';
import dayjs from 'dayjs';
import { useFetchDetail, useFetchList, useFetchUser } from 'hooks';
import type { CounselorData } from 'hooks/useFetchUser/types';
import useHookForm from 'hooks/useHookForm';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { BackIcon, PaymentCardIcon, PaymentIcon, SheetIcon } from 'icons';
import get from 'lodash/get';
import type {
  ICounselorCreateMeeting,
  IProviderCaseDetail,
} from 'models/case/interface';
import caseQuery from 'models/case/query';
import type { ICounselorConsultationMenu } from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import type { ICalendarDetail } from 'models/shift/interface';
import shiftQuery from 'models/shift/query';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useEffect, useState } from 'react';
import { useWatch } from 'react-hook-form';
import OutlineMenu from 'shared/provider/my-page/outline-menu';
import { CaseStatusType, PAYMENT_METHOD, ROLES } from 'utils/constants';
import Helper from 'utils/helpers';
import queryClient from 'utils/queryClient';

import type { CreateMeetingValues } from './schema';
import schema from './schema';
import styles from './styles';

const CreateMeetingPage = () => {
  const { query, push, replace } = useRouter();
  const caseId = query.caseId as string;
  const meetingId = query.meetingId as string;

  const [startDate, setStartDate] = useState<string>(
    dayjs().startOf('d').toISOString(),
  );

  const { data: currentUser } = useFetchUser<CounselorData>({ enabled: true });
  const counselorId = currentUser?._id;

  const { mutateAsync: createMeeting, isLoading } =
    useMutate<ICounselorCreateMeeting>(
      caseQuery.providerCounselorCreateMeeting,
    );
  const {
    detail,
    isFetching,
    refetch: refetchCalendar,
  } = useFetchDetail<ICalendarDetail>(shiftQuery.calendarList(startDate));
  const calendar = detail.slots || [];

  // Only add checkCustomerId parameter if user role is CUSTOMER
  const userRole = Helper.getUserRole();
  const checkCustomerId =
    userRole === ROLES.CUSTOMER ? currentUser?._id : undefined;

  const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
    ...consultationQuery.list(counselorId as string, checkCustomerId),
    enabled: !!counselorId, // Only fetch when counselorId is available
  });
  const formattedMenuList = menuList.map((menu) => ({
    ...menu,
    value: `${menu.unitPrices[0].duration}分 - ${menu.title}`,
  }));

  const fetchCaseDetail = useFetchDetail({
    ...caseQuery.providerCaseDetail(caseId),
    enabled: false,
  });

  const caseDetail = fetchCaseDetail.detail as IProviderCaseDetail;

  const { control, handleSubmit, reset, setValue } =
    useHookForm<CreateMeetingValues>({
      mode: 'all',
      resolver: yupResolver(schema),
    });

  useEffect(() => {
    return () => {
      queryClient.removeQueries({ queryKey: ['currentUser', 'calendars'] });
    };
  }, []);

  const watchDiscount = useWatch({ name: 'discount', control }) || 0;
  const watchMenuId = useWatch({ name: 'menuId', control });
  const watchMenu = formattedMenuList.find((menu) => menu._id === watchMenuId);
  const basicPrice = watchMenu?.unitPrices[0].price || 0;

  useEffect(() => {
    if (menuList.length > 0) {
      let selectedMenuId: string | undefined;

      // Get URL parameters
      const urlDuration = query.duration ? Number(query.duration) : null;
      const urlMeetingType = query.meetingType as string;

      // If duration and meetingType are provided in URL, find matching menu
      if (urlDuration && urlMeetingType) {
        const matchingMenu = menuList.find(
          (menu) =>
            menu.meetingType === urlMeetingType &&
            menu.unitPrices?.[0]?.duration === urlDuration,
        );
        selectedMenuId = matchingMenu?._id;
      }

      // If no URL parameters or no matching menu found, use default selection logic
      if (!selectedMenuId) {
        selectedMenuId = Helper.getDefaultMenuId(menuList);
      }

      if (selectedMenuId) {
        reset({ menuId: selectedMenuId });
      }
    }
  }, [meetingId, menuList, reset, query.duration, query.meetingType]);

  useEffect(() => {
    if (watchMenuId) {
      setValue('finalizedDate', '');
      setValue('discount', '' as unknown as number);
    }
  }, [setValue, watchMenuId]);

  const handleSubmitForm = (values: CreateMeetingValues) => {
    const { finalizedDate, meetingUrl, discount } = values;
    const payload: ICounselorCreateMeeting = {
      finalizedDate,
      meetingUrl,
      caseId,
      startSlot: finalizedDate,
      paymentInfo: {
        discount: discount || 0,
        duration: watchMenu?.unitPrices[0].duration || 0,
        basicPrice,
      },
      consultationMenu: {
        title: watchMenu?.title || '',
        unitPrices: [
          {
            price: watchMenu?.unitPrices[0].price || 0,
            duration: watchMenu?.unitPrices[0].duration || 0,
          },
        ],
        content: watchMenu?.value,
        meetingType: watchMenu?.meetingType,
      },
    };

    const onSuccess = () => {
      replace({
        pathname: '/counselor/my-page/cases/[caseId]',
        query: { caseId },
      });
    };
    const onError = (error: any) => {
      if (get(error, 'code') === 'MEETNG_HAS_SLOT_IS_CLOSED') {
        setValue('finalizedDate', '');
        refetchCalendar();
      }
    };
    createMeeting(payload, { onSuccess, onError });
  };

  const renderDataOfMenu = OutlineMenu(formattedMenuList);

  return (
    <Box>
      <form id="create-meeting-form" onSubmit={handleSubmit(handleSubmitForm)}>
        <Box sx={styles.headerCreateMeeting}>
          <Box>
            <Breadcrumbs
              omitIndexList={[0]}
              transformLabel={{
                '/counselor/my-page/cases/[caseId]':
                  caseDetail.consumer.fullName,
              }}
            />
            <Box display="flex" mt={{ xs: '4px', tablet: '11px' }}>
              <Box m="5px 4px 0px -4px">
                <ButtonBack icon={<BackIcon />} sx={styles.backButton} />
              </Box>
              <Typography
                fontSize={{ xs: 24, tablet: 32 }}
                fontWeight="bold"
                color="heading"
              >
                {t('caseDetail.createMeeting')}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Grid
          container
          columnGap={2}
          mt={{ xs: 1, tablet: 2 }}
          mb={{
            xs: 1,
            tablet: 2,
          }}
          columns={29}
          minHeight="calc(100vh - 390px)"
        >
          <Grid
            item
            xs={29}
            md={19}
            mb={{
              xs: 1,
              tablet: 2,
            }}
          >
            <Box sx={styles.confirmMeeting}>
              <Stack spacing={1} direction="row" alignItems="center" mb={3}>
                <SheetIcon />
                <Typography sx={styles.textHeader}>
                  {t('confirmMeeting.meetingTitle')}
                </Typography>
              </Stack>
              <Stack spacing={{ xs: '20px', tablet: 2 }}>
                <Grid
                  container
                  columns={{
                    xs: 12,
                    tablet: 15,
                  }}
                >
                  <Label
                    labelCol={3}
                    label={t('confirmMeeting.meetingType')}
                    paddingTop={3}
                    required
                  />
                  <Grid item xs={12} columnSpacing={4}>
                    <Box sx={styles.typeMeeting}>
                      <Typography mb={1}>オンライン面談</Typography>
                      <TextField
                        labelCol={12}
                        name="meetingUrl"
                        control={control}
                        placeholder={t('confirmMeeting.placeholderMeetingUrl')}
                      />
                    </Box>
                  </Grid>
                </Grid>
                <>
                  {/* <Select
                    labelCol={3}
                    columns={13}
                    name="menuId"
                    label="メニュー"
                    placeholder="メニュー"
                    data={formattedMenuList}
                    control={control}
                    allowClear={false}
                    sx={{ maxWidth: 528, width: '100%' }}
                    IconComponent={(iconProps) => {
                      return (
                        <img
                          {...iconProps}
                          alt="arrow-down"
                          src="/icons/arrow-down.svg"
                          style={{ transform: 'unset' }}
                        />
                      );
                    }}
                    required
                  /> */}

                  <Grid
                    container
                    columns={{
                      xs: 12,
                      tablet: 15,
                    }}
                  >
                    <Label
                      labelCol={3}
                      label={'メニュー'}
                      paddingTop={0}
                      required
                    />
                    <Grid item xs={12} columnSpacing={4} mt={0}>
                      <Box sx={styles.typeMeetingMenu}>
                        <Radio
                          type="outline"
                          data={renderDataOfMenu}
                          control={control}
                          name="menuId"
                          required
                          columns={1}
                          row={false}
                          sx={{
                            marginTop: 0,
                          }}
                        />
                      </Box>
                    </Grid>
                  </Grid>
                  <TimeTableField
                    label={t('confirmMeeting.meetingDate')}
                    placeholder="yyyy年mm月dd日 hh時mm分"
                    name="finalizedDate"
                    control={control}
                    required
                    labelCol={3}
                    columns={13}
                    duration={watchMenu?.unitPrices[0].duration || 0}
                    startTime={startDate}
                    onNavigate={(date) => setStartDate(date)}
                    data={calendar}
                    loading={isFetching}
                    isHasBookingSlot
                  />
                </>
              </Stack>
            </Box>
          </Grid>
          <Grid item xs md>
            <Box borderRadius={2} bgcolor="white" p={4}>
              <Stack spacing={1} direction="row" alignItems="center" mb={4}>
                <Box sx={styles.icon}>
                  <PaymentIcon />
                </Box>
                <Typography sx={styles.textHeader}>決済方法 </Typography>
              </Stack>
              <Box display="flex">
                <Box
                  mr={1}
                  alignSelf="center"
                  sx={{
                    width: 'fit-content',
                    svg: {
                      display: 'block',
                      maxHeight: { xs: '20px', tablet: '24px' },
                      maxWidth: { xs: '30px', tablet: '36px' },
                    },
                  }}
                >
                  <PaymentCardIcon />
                </Box>
                <Typography>{PAYMENT_METHOD.CREDIT_CARD}</Typography>
              </Box>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="flex-end"
                mt={2}
              >
                <Typography fontWeight={700}>
                  {t('confirmMeeting.basicPrice')}
                </Typography>
                <Stack direction="row" spacing="2px" alignItems="flex-end">
                  <Typography fontWeight={700}>
                    {Helper.addComma(basicPrice)}
                  </Typography>
                  <Typography fontWeight={700}>円</Typography>
                </Stack>
              </Stack>
              <Typography fontSize={14} color="hint" mt={1} mb={2}>
                {t('confirmMeeting.description')}
              </Typography>

              <Box
                sx={{
                  '& .field-label': { fontWeight: 700 },
                }}
              >
                <NumberField
                  label={t('confirmMeeting.discount')}
                  placeholder="割引金額を入力"
                  name="discount"
                  control={control}
                  labelCol={5}
                  columns={18}
                  adornment={
                    <Typography ml={1} color="neutral7">
                      円
                    </Typography>
                  }
                  min={0}
                  max={Math.max(basicPrice, 0)}
                />
              </Box>
              <Divider sx={{ mt: 3, mb: 3 }} />
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                flexWrap="wrap"
              >
                <Stack direction="row">
                  <Typography mr="6px" fontWeight="bold" color="heading">
                    {t('confirmMeeting.finalPrice')}
                  </Typography>
                  <PaymentTooltipButton
                    title="お支払い予定金額に関して"
                    content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
                  />
                </Stack>
                <Stack direction="row" spacing="2px" alignItems="flex-end">
                  <Typography fontSize={24} fontWeight="bold" color="neutral7">
                    {watchDiscount <= basicPrice
                      ? Helper.addComma(basicPrice - watchDiscount)
                      : 0}
                  </Typography>
                  <Typography
                    fontWeight="bold"
                    color="neutral7"
                    lineHeight="30px"
                  >
                    円
                  </Typography>
                </Stack>
              </Stack>
            </Box>
          </Grid>
        </Grid>
        <Box sx={styles.confirmWrapper}>
          <Box
            flex={1}
            sx={{
              width: '100%',
            }}
          >
            <Box sx={styles.wrapperButton}>
              <Stack spacing={1} direction="row" alignItems="center">
                <Button
                  variant="outlined"
                  sx={{ maxHeight: 40, minWidth: 112 }}
                  onClick={() => {
                    push({
                      pathname: '/counselor/my-page/cases/[caseId]',
                      query: { caseId },
                    });
                  }}
                >
                  {t('global.cancel')}
                </Button>
                <LoadingButton
                  color="secondary"
                  variant="contained"
                  type="submit"
                  fullWidth
                  loading={isLoading}
                  sx={{ fontWeight: 'bold', maxHeight: 40, minWidth: 112 }}
                >
                  確定
                </LoadingButton>
              </Stack>
            </Box>
          </Box>
        </Box>
      </form>
    </Box>
  );
};

CreateMeetingPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <Stack sx={styles.wrapper}>
        <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
      </Stack>
    </Layout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const qClient = new QueryClient();
  const caseId = query.caseId as string;
  const promises = [
    Helper.prefetchDetail(
      caseQuery.providerCaseDetail(caseId),
      qClient,
      undefined,
      Helper.getTokenConfig(req, res),
    ),
  ];

  const [caseDetail] = await Promise.all(promises);

  if (caseDetail.status !== CaseStatusType.SCHEDULE_IS_ADJUSTED) {
    return {
      redirect: {
        destination: `/counselor/my-page/cases/${query.caseId}/confirm`,
        permanent: true,
      },
    };
  }

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

export default CreateMeetingPage;

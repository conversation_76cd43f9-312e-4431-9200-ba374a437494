import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  addMenuButton: {
    ml: '-4px',
    mb: '-4px',
    mt: '12px',
    svg: {
      width: 20,
      height: 20,
    },
    '& .MuiButton-startIcon': {
      mr: '4px',
    },
    '&.Mui-disabled': {
      color: '#A8B0B4',
      backgroundColor: 'unset',
      svg: {
        color: '#A8B0B4',
      },
    },
  },
  changeMenuButton: {
    ml: '-4px',
    svg: {
      width: 20,
      height: 20,
    },
    '& .MuiButton-startIcon': {
      mr: '4px',
    },
    '&.Mui-disabled': {
      color: '#A8B0B4',
      backgroundColor: 'unset',
      svg: {
        color: '#A8B0B4',
      },
    },
  },
  wrapper: {
    '> .MuiContainer-root': {
      '> .MuiBox-root': {
        paddingBottom: 0,
      },
    },
    '#update-meeting-form': {
      '@media (max-width: 887px)': {
        '> .MuiBox-root': {
          borderRadius: 0,
        },
        '.MuiGrid-container': {
          '> .MuiGrid-root': {
            '> .MuiBox-root': {
              borderRadius: 0,
            },
          },
        },
      },
    },
  },
  wrapperButton: {
    display: 'flex',
    justifyContent: 'flex-end',
    '@media (max-width: 887px)': {
      '> div': {
        width: '100%',
      },
      button: {
        width: '100%',
      },
    },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { t } from 'i18n';
import { isNaN } from 'lodash';
import { Regex } from 'utils/constants';
import type { InferType } from 'yup';
import { array, number, object, string } from 'yup';

dayjs.extend(isSameOrAfter);

const schema = object({
  discount: number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .min(0, t('validation.invalidField')),
  meetingUrl: string()
    .required()
    .test('validUrl', t('validation.invalidUrl'), (value) => {
      if (value) {
        return Regex.URL.test(value);
      }
      return true;
    }),
  menu: object({
    title: string().required(),
    content: string(),
    duration: number().required(),
    unitPrice: number().required(),
    meetingType: string(),
  }),
  menuId: string(),
  finalizedDate: string().required(),
  extendMenus: array().of(
    object({
      duration: number().required(),
      price: number().required(),
    }),
  ),
});
export type UpdateMeetingValues = InferType<typeof schema>;
export type IMenu = Pick<UpdateMeetingValues, 'menu'>;
export default schema;

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Divider,
  Grid,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import { dehydrate, QueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import { cancelModalProps } from 'components/Case/CounselorMeetingItem/const';
import CouponTag from 'components/Case/CouponTag';
import ExtendMenuForm from 'components/Case/ExtendedMenuForm';
import PaymentTooltipButton from 'components/Case/PaymentTooltipButton';
import ConfirmModal from 'components/ConfirmModal/modal';
import { Radio, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import NumberField from 'components/Form/NumberField';
import TimeTableField from 'components/Form/TimeTableField';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import ButtonBack from 'components/UI/BackButton';
import dayjs from 'dayjs';
import {
  useDeepCompareEffect,
  useFetchDetail,
  useFetchList,
  useFetchUser,
} from 'hooks';
import type { CounselorData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import useHookForm from 'hooks/useHookForm';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import {
  AddIcon,
  BackIcon,
  EditIcon,
  PaymentCardIcon,
  PaymentIcon,
  SheetIcon,
  TrashIcon,
} from 'icons';
import get from 'lodash/get';
import type {
  ICounselorUpdateMeeting,
  IMeetingsItem,
  IProviderCaseDetail,
} from 'models/case/interface';
import caseQuery from 'models/case/query';
import type { ICounselorConsultationMenu } from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import type { ICalendarDetail } from 'models/shift/interface';
import shiftQuery from 'models/shift/query';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useWatch } from 'react-hook-form';
import OutlineMenu from 'shared/provider/my-page/outline-menu';
import CaseUtil from 'utils/caseUtil';
import {
  CaseStatusType,
  ConsultationMenuMeetingType,
  MeetingStatusType,
  PAYMENT_METHOD,
  PaymentStatusType,
  ROLES,
} from 'utils/constants';
import Helper from 'utils/helpers';
import queryClient from 'utils/queryClient';

import styles from '../../create/styles';
import type { UpdateMeetingValues } from './schema';
import schema from './schema';
import editStyles from './styles';

const UpdateMeetingPage = () => {
  const ref = useRef<HTMLDivElement | null>(null);
  const { setConfirmModal } = useGlobalState();
  const { query, push, replace } = useRouter();
  const caseId = query.caseId as string;
  const meetingId = query.meetingId as string;
  const [extendMenuOpen, setExtendMenuOpen] = useState<
    | {
        duration: number;
        price: number;
      }
    | boolean
  >(false);
  const [startDate, setStartDate] = useState<string>(
    dayjs().startOf('d').toISOString(),
  );
  const { data: currentUser } = useFetchUser<CounselorData>({ enabled: true });
  const counselorId = currentUser?._id;
  const userRole = Helper.getUserRole();
  const checkCustomerId =
    userRole === ROLES.CUSTOMER ? currentUser?._id : undefined;

  const { mutateAsync: updateMeeting, isLoading: isLoadingUpdate } =
    useMutate<ICounselorUpdateMeeting>(caseQuery.updateMeeting(meetingId));
  const { detail, isFetching } = useFetchDetail<ICalendarDetail>(
    shiftQuery.calendarList(startDate),
  );
  const calendar = detail.slots || [];

  const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
    ...consultationQuery.list(counselorId as string, checkCustomerId),
    enabled: !!counselorId, // Only fetch when counselorId is available
  });
  const formattedMenuList = menuList.map((menu) => ({
    ...menu,
    value: `${menu.unitPrices[0].duration}分 - ${menu.title}`,
  }));

  const fetchCaseDetail = useFetchDetail({
    ...caseQuery.providerCaseDetail(caseId),
    enabled: false,
  });
  const fetchMeetingDetail = useFetchDetail<IMeetingsItem>({
    ...caseQuery.getMeetingDetail(meetingId),
    enabled: false,
  });

  const caseDetail = fetchCaseDetail.detail as IProviderCaseDetail;
  const meetingDetail = fetchMeetingDetail.detail;
  const couponInfo = meetingDetail.paymentInfo?.couponInfo;
  const allowUpdateFinalizedDateAndMenu =
    (dayjs(meetingDetail.finalizedDate).isAfter(dayjs()) &&
      meetingDetail.status === MeetingStatusType.SCHEDULE_IS_DECIDED) ||
    (meetingDetail.status === MeetingStatusType.WAITING_ANWSER_FROM_PROVIDER &&
      !meetingDetail.finalizedDate);

  const allowAddExtendedMenu =
    dayjs().isAfter(dayjs(meetingDetail.finalizedDate)) &&
    [
      MeetingStatusType.WAITING_ANWSER_FROM_PROVIDER,
      MeetingStatusType.SCHEDULE_IS_DECIDED,
    ].includes(meetingDetail.status);

  const defaultValues = useMemo(() => {
    const consultationMenu = formattedMenuList?.filter(
      (item) =>
        item?.title === meetingDetail.consultationMenu?.title &&
        item?.unitPrices[0]?.duration ===
          meetingDetail.consultationMenu?.unitPrices[0]?.duration &&
        item?.unitPrices[0]?.price ===
          meetingDetail.consultationMenu?.unitPrices[0]?.price,
    );
    return {
      finalizedDate:
        meetingDetail.finalizedDate || meetingDetail.expectDate[0].start,
      meetingUrl: meetingDetail.meetingUrl,
      discount: meetingDetail.paymentInfo?.discount,
      extendMenus: meetingDetail.extendMenus || [],
      menu: {
        ...consultationMenu?.[0],
        duration: meetingDetail.consultationMenu?.unitPrices[0]?.duration,
        title: meetingDetail.consultationMenu?.title,
        content: meetingDetail.consultationMenu?.content,
        unitPrice: meetingDetail.consultationMenu?.unitPrices[0]?.price,
      },
      menuId:
        formattedMenuList
          ?.filter(
            (item) =>
              item?.title === meetingDetail.consultationMenu?.title &&
              item?.unitPrices[0]?.duration ===
                meetingDetail.consultationMenu?.unitPrices[0]?.duration &&
              item?.unitPrices[0]?.price ===
                meetingDetail.consultationMenu?.unitPrices[0]?.price,
          )
          .filter(Boolean)?.[0]?._id || '',
    };
  }, [meetingDetail, formattedMenuList]);

  const { control, handleSubmit, reset, setValue } =
    useHookForm<UpdateMeetingValues>({
      mode: 'all',
      resolver: yupResolver(schema),
      defaultValues,
    });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  useEffect(() => {
    return () => {
      queryClient.removeQueries({ queryKey: ['currentUser', 'calendars'] });
    };
  }, []);
  const watchDiscount = useWatch({ name: 'discount', control }) || 0;
  const watchMenu = useWatch({ name: 'menu', control });
  const watchExtendMenus = useWatch({ name: 'extendMenus', control });
  const basicPrice =
    (watchMenu?.unitPrice || 0) +
    ((watchExtendMenus && watchExtendMenus[0]?.price) || 0);

  const couponAmount = couponInfo?.coupon
    ? CaseUtil.calculateFinalCouponAmount({
        totalPrice: basicPrice,
        issuedCoupon: couponInfo?.coupon,
      })
    : 0;
  const handleSubmitForm = (values: UpdateMeetingValues) => {
    const { finalizedDate, meetingUrl, discount, menu, extendMenus } = values;
    const currentMenu = formattedMenuList?.filter(
      (item) =>
        item?.title === menu?.title &&
        item?.unitPrices[0]?.duration === menu?.duration &&
        item?.unitPrices[0]?.price === menu?.unitPrice,
    );

    const payload: ICounselorUpdateMeeting = {
      finalizedDate,
      meetingUrl,
      startSlot: finalizedDate,
      paymentInfo: {
        discount: discount || 0,
        duration: menu.duration,
      },
      consultationMenu: {
        title: menu.title,
        content: menu.content,
        unitPrices: [{ duration: menu.duration, price: menu.unitPrice }],
        meetingType: menu?.meetingType,
        ...currentMenu?.[0],
      },
      extendMenus: allowAddExtendedMenu ? extendMenus : null,
    };

    const onSuccess = () => {
      replace({
        pathname: '/counselor/my-page/cases/[caseId]',
        query: { caseId },
      });
    };
    updateMeeting(payload, {
      onSuccess,
      onError: (e) => {
        if (
          get(e, 'code') === 'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT'
        ) {
          Helper.toast(get(e, 'error'), { type: 'error' });
        }
      },
    });
  };

  if (meetingId && fetchMeetingDetail.isLoading) {
    return null;
  }
  const handleSelectMenu = (value: string) => {
    const selected = formattedMenuList?.filter((item) => item?._id === value);
    const menuSelected = Helper.convertArrayToOptions(selected, [
      {
        alias: 'duration',
        getValue: (val) => {
          return val?.unitPrices?.[0]?.duration as unknown as string;
        },
      },
      {
        alias: 'unitPrice',
        getValue: (val) => {
          return val?.unitPrices?.[0]?.price as unknown as string;
        },
      },
      {
        alias: 'content',
        getValue: (val) => {
          return val?.value || '内容がありません';
        },
      },
      { key: 'title' },
      { key: 'meetingType' },
    ]);

    if (
      (menuSelected?.[0]?.duration as unknown as number) >
      (meetingDetail.consultationMenu?.unitPrices[0]?.duration || 0)
    ) {
      setValue('finalizedDate', '');
    }

    setValue('menu', {
      title: menuSelected?.[0]?.title as string,
      content: menuSelected?.[0]?.content,
      duration: Number(menuSelected?.[0]?.duration || 0),
      unitPrice: Number(menuSelected?.[0]?.unitPrice || 0),
      meetingType: menuSelected?.[0]?.meetingType,
    });
    setValue('discount', 0);
  };

  const renderDataOfMenu = OutlineMenu(formattedMenuList);

  const renderMenu = () => {
    if (allowAddExtendedMenu) {
      return (
        <Box
          ml={4}
          p="24px"
          sx={{
            borderRadius: '12px',
            border: '1px solid #E4EAED',
          }}
        >
          <Typography
            color="heading"
            fontSize={14}
            lineHeight={'20px'}
            fontWeight={600}
          >
            {watchMenu.title}
          </Typography>
          <Typography
            mt={1}
            mb={1}
            fontSize={12}
            lineHeight={'16px'}
            fontWeight={400}
          >
            相談タイプ：
            {
              ConsultationMenuMeetingType[
                `${
                  watchMenu.meetingType || ''
                }` as keyof typeof ConsultationMenuMeetingType
              ] as string
            }
          </Typography>
          <Typography
            color="heading"
            mt={1}
            mb={1}
            fontSize={14}
            lineHeight={'20px'}
            fontWeight={600}
          >
            {watchMenu?.duration}分 - {Helper.addComma(watchMenu?.unitPrice)}円
          </Typography>
          <Divider />
          {allowAddExtendedMenu && !watchExtendMenus?.length && (
            <Button
              size="small"
              sx={editStyles.addMenuButton}
              className="tabletStyle"
              color="primary"
              onClick={() => setExtendMenuOpen(true)}
              startIcon={<AddIcon />}
            >
              延長メニューを追加
            </Button>
          )}
          {allowAddExtendedMenu && !!watchExtendMenus?.length && (
            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              mt={2}
            >
              <Box>
                <Typography
                  color="heading"
                  fontSize={14}
                  lineHeight={'20px'}
                  fontWeight={600}
                >
                  延長メニュー
                </Typography>
                <Box
                  mt={1}
                  color="heading"
                  fontSize={14}
                  lineHeight={'20px'}
                  fontWeight={600}
                >
                  {watchExtendMenus[0]?.duration}分 -{' '}
                  {Helper.addComma(watchExtendMenus[0]?.price || 0)}円
                </Box>
              </Box>
              <Box display="flex" gap="12px">
                <IconButton
                  className="whiteOutlined"
                  onClick={() => {
                    if (watchExtendMenus && watchExtendMenus[0]) {
                      setExtendMenuOpen({
                        duration: watchExtendMenus[0].duration,
                        price: watchExtendMenus[0].price,
                      });
                    }
                  }}
                >
                  <EditIcon />
                </IconButton>
                <IconButton
                  className="whiteOutlined"
                  onClick={() =>
                    setConfirmModal({
                      title: '延長メニューの削除',
                      content: 'この延長メニューを削除しますか？',
                      onConfirm: () => setValue('extendMenus', []),
                    })
                  }
                >
                  <TrashIcon />
                </IconButton>
              </Box>
            </Box>
          )}
        </Box>
      );
    }
    return (
      <Box
        ref={ref}
        sx={{
          width: '100%',
        }}
      >
        <Radio
          type="outline"
          data={renderDataOfMenu}
          control={control}
          name="menuId"
          required
          columns={1}
          row={false}
          sx={{
            marginTop: 0,
          }}
          onClick={(e) => {
            const res = e as unknown as Event;
            const target = res?.target as HTMLInputElement;
            handleSelectMenu(target?.value);
          }}
        />
      </Box>
    );
  };

  return (
    <Box>
      <form id="update-meeting-form" onSubmit={handleSubmit(handleSubmitForm)}>
        <Box sx={styles.headerCreateMeeting}>
          <Box>
            <Breadcrumbs
              omitIndexList={[0, 4, 5]}
              transformLabel={{
                '/counselor/my-page/cases/[caseId]':
                  caseDetail.consumer.fullName,
              }}
            />
            <Box display="flex" mt={{ xs: '4px', tablet: '11px' }}>
              <Box m="5px 4px 0px -4px">
                <ButtonBack icon={<BackIcon />} sx={styles.backButton} />
              </Box>
              <Typography
                fontSize={{ xs: 24, tablet: 32 }}
                fontWeight="bold"
                color="heading"
              >
                {t('caseDetail.createMeeting')}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Grid
          container
          columnGap={2}
          my={{ xs: 1, tablet: 2 }}
          columns={29}
          minHeight="calc(100vh - 390px)"
        >
          <Grid item xs={29} md={19}>
            <Box sx={styles.confirmMeeting}>
              <Stack spacing={1} direction="row" alignItems="center" mb={3}>
                <SheetIcon />
                <Typography sx={styles.textHeader}>
                  {t('confirmMeeting.meetingTitle')}
                </Typography>
              </Stack>
              <Stack spacing={{ xs: '20px', tablet: 2 }}>
                <Grid
                  container
                  columns={{
                    base: 12,
                    md: 15,
                  }}
                >
                  <Label
                    labelCol={3}
                    paddingTop={0}
                    label={t('confirmMeeting.meetingType')}
                    required
                  />
                  <Grid
                    item
                    xs={12}
                    columnSpacing={4}
                    sx={{
                      width: '100%',
                      flexGrow: 1,
                      maxWidth: '100%',
                    }}
                  >
                    <Box
                      pl={{
                        base: 0,
                        md: 4,
                      }}
                    >
                      <Typography mb={1}>オンライン面談</Typography>
                      <TextField
                        labelCol={12}
                        name="meetingUrl"
                        control={control}
                        placeholder={t('confirmMeeting.placeholderMeetingUrl')}
                      />
                    </Box>
                  </Grid>
                </Grid>

                <Grid
                  container
                  columns={{
                    base: 12,
                    md: 15,
                  }}
                  sx={{
                    '.MuiGrid-root': {
                      marginTop: 0,
                    },
                  }}
                >
                  <Label
                    labelCol={3}
                    label="メニュー"
                    paddingTop={0}
                    required={dayjs(meetingDetail.finalizedDate).isBefore(
                      dayjs(),
                    )}
                  />
                  <Grid
                    item
                    xs={12}
                    sx={{
                      width: '100%',
                      flexGrow: 1,
                      maxWidth: '100%',
                    }}
                    pl={{
                      base: 0,
                      md: 4,
                    }}
                  >
                    {renderMenu()}
                  </Grid>
                </Grid>

                <TimeTableField
                  label={t('confirmMeeting.meetingDate')}
                  placeholder="yyyy年mm月dd日 hh時mm分"
                  name="finalizedDate"
                  control={control}
                  required
                  labelCol={3}
                  columns={13}
                  duration={watchMenu?.duration}
                  startTime={startDate}
                  onNavigate={(date) => setStartDate(date)}
                  data={calendar}
                  loading={isFetching}
                  isHasBookingSlot
                  disabled={!allowUpdateFinalizedDateAndMenu}
                />
              </Stack>
            </Box>
          </Grid>
          <Grid item xs md>
            <Box borderRadius={2} bgcolor="white" p={4}>
              <Stack spacing={1} direction="row" alignItems="center" mb={4}>
                <Box sx={styles.icon}>
                  <PaymentIcon />
                </Box>
                <Typography sx={styles.textHeader}>決済方法 </Typography>
              </Stack>
              <Box display="flex">
                <Box
                  mr={1}
                  alignSelf="center"
                  sx={{
                    width: 'fit-content',
                    svg: {
                      display: 'block',
                      maxHeight: { xs: '20px', tablet: '24px' },
                      maxWidth: { xs: '30px', tablet: '36px' },
                    },
                  }}
                >
                  <PaymentCardIcon />
                </Box>
                <Typography>{PAYMENT_METHOD.CREDIT_CARD}</Typography>
              </Box>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="flex-end"
                mt={2}
              >
                <Typography fontWeight={700}>
                  {t('confirmMeeting.basicPrice')}
                </Typography>
                <Stack direction="row" spacing="2px" alignItems="flex-end">
                  <Typography fontWeight={700}>
                    {Helper.addComma(basicPrice)}
                  </Typography>
                  <Typography fontWeight={700}>円</Typography>
                </Stack>
              </Stack>
              <Typography fontSize={14} color="hint" mt={1} mb={2}>
                {t('confirmMeeting.description')}
              </Typography>
              {couponInfo && (
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                  mb={2}
                  flexWrap="wrap"
                >
                  <Stack direction="row" alignItems="center">
                    <Typography fontWeight={700}>クーポン値引</Typography>
                    <CouponTag
                      code={couponInfo.coupon.code}
                      wrapperProps={{ ml: 1, mr: '6px' }}
                    />
                    <PaymentTooltipButton />
                  </Stack>
                  <Typography fontWeight={700}>
                    - {Helper.addComma(couponAmount || 0)}円
                  </Typography>
                </Stack>
              )}
              <Box
                sx={{
                  '& .field-label': { fontWeight: 700 },
                }}
              >
                <NumberField
                  label={t('confirmMeeting.discount')}
                  placeholder="割引金額を入力"
                  name="discount"
                  control={control}
                  labelCol={5}
                  columns={18}
                  adornment={
                    <Typography ml={1} color="neutral7">
                      円
                    </Typography>
                  }
                  min={0}
                  max={Math.max(basicPrice - couponAmount, 0)}
                />
              </Box>
              <Divider sx={{ mt: 3, mb: 3 }} />
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                flexWrap="wrap"
              >
                <Stack direction="row">
                  <Typography mr="6px" fontWeight="bold" color="heading">
                    {t('confirmMeeting.finalPrice')}
                  </Typography>
                  <PaymentTooltipButton
                    title="お支払い予定金額に関して"
                    content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
                  />
                </Stack>
                <Stack direction="row" spacing="2px" alignItems="flex-end">
                  <Typography fontSize={24} fontWeight="bold" color="neutral7">
                    {watchDiscount <= basicPrice - couponAmount
                      ? Helper.addComma(
                          basicPrice - watchDiscount - couponAmount,
                        )
                      : 0}
                  </Typography>
                  <Typography
                    fontWeight="bold"
                    color="neutral7"
                    lineHeight="30px"
                  >
                    円
                  </Typography>
                </Stack>
              </Stack>
            </Box>
          </Grid>
        </Grid>
        <Box sx={styles.confirmWrapper}>
          <Box
            flex={1}
            sx={{
              width: '100%',
            }}
          >
            <Box sx={editStyles.wrapperButton}>
              <Stack spacing={1} direction="row" alignItems="center">
                <Button
                  variant="outlined"
                  sx={{ maxHeight: 40, minWidth: 112 }}
                  onClick={() => {
                    push({
                      pathname: '/counselor/my-page/cases/[caseId]',
                      query: { caseId },
                    });
                  }}
                >
                  {t('global.cancel')}
                </Button>
                <LoadingButton
                  color="secondary"
                  variant="contained"
                  type="submit"
                  fullWidth
                  form="update-meeting-form"
                  loading={isLoadingUpdate}
                  sx={{ fontWeight: 'bold', maxHeight: 40, minWidth: 112 }}
                >
                  確定
                </LoadingButton>
              </Stack>
            </Box>
          </Box>
        </Box>
      </form>
      <ConfirmModal
        open={!!extendMenuOpen}
        {...cancelModalProps}
        onCancel={() => setExtendMenuOpen(false)}
        confirmButtonProps={{ form: 'extend-menu-form', type: 'submit' }}
        title="延長メニューを設定"
        content={
          <ExtendMenuForm
            defaultValues={
              typeof extendMenuOpen === 'object' ? extendMenuOpen : undefined
            }
            onSubmit={(values) => {
              setValue('extendMenus', [values]);
              setExtendMenuOpen(false);
            }}
          />
        }
      />
    </Box>
  );
};

UpdateMeetingPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <Stack sx={editStyles.wrapper}>
        <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
      </Stack>
    </Layout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const qClient = new QueryClient();
  const caseId = query.caseId as string;
  const meetingId = query.meetingId as string;
  const promises = [
    Helper.prefetchDetail(
      caseQuery.providerCaseDetail(caseId),
      qClient,
      undefined,
      Helper.getTokenConfig(req, res),
    ),
  ];
  promises.push(
    Helper.prefetchDetail(
      caseQuery.getMeetingDetail(meetingId),
      qClient,
      undefined,
      Helper.getTokenConfig(req, res),
    ),
  );
  const [caseDetail, meetingDetail] = await Promise.all(promises);
  if (
    meetingDetail &&
    ([
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
      MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
    ].includes(meetingDetail.status) ||
      (meetingDetail.status === MeetingStatusType.FINISHED &&
        meetingDetail?.paymentInfo?.status === PaymentStatusType.ERROR))
  ) {
    return {
      redirect: {
        destination: `/counselor/my-page/cases/${query.caseId}`,
        permanent: true,
      },
    };
  }
  if (caseDetail.status !== CaseStatusType.SCHEDULE_IS_ADJUSTED) {
    return {
      redirect: {
        destination: `/counselor/my-page/cases/${query.caseId}/confirm`,
        permanent: true,
      },
    };
  }

  return {
    props: {
      dehydratedState: dehydrate(qClient),
    },
  };
};

export default UpdateMeetingPage;

import { Box, Typography } from '@mui/material';
import ViewEditBlock from 'components/UI/ViewEditBlock/index';
import type { Dayjs } from 'dayjs';
import type { ConsultationRecord } from 'hooks/lawyer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import i18n from 'i18n';
import { KidIcon } from 'icons';
import { isEmpty, isUndefined } from 'lodash';
import { useMemo } from 'react';
import type { CaseStatusType, GenderType, ISchoolType } from 'utils/constants';
import { Gender, RadioOptions, SchoolTypeOptions } from 'utils/constants';

import type { IBlock } from './blocks';
import {
  divorceBlock,
  marriageBlock,
  partnerBlock,
  profileBlock,
  propertyBlock,
} from './blocks';

export interface ICustomerConsultation {
  data: ConsultationRecord;
  customerProfile: CustomerData;
  refetchCustomerProfile: () => void;
  refetchConsultation: () => void;
  caseStatus: CaseStatusType;
}
const CustomerConsultation = ({
  data,
  customerProfile,
}: ICustomerConsultation) => {
  const { t } = i18n;

  const kids = data?.kids?.kids;

  const childrenFields = useMemo(
    () =>
      (kids || []).map((_: unknown, index: number) => [
        { label: t('consultationRecord.kidOrder', { number: index + 1 }) },
        {
          label: t('consultationRecord.kidGender'),
          path: `kids.kids[${index}].gender`,
          renderValue: (value: GenderType) => Gender[value],
        },
        {
          label: t('consultationRecord.kidAge'),
          path: `kids.kids[${index}].age`,
          renderValue: (value: Dayjs) =>
            !isUndefined(value) &&
            t('consultationRecord.ageNumber', { number: value }),
        },
        {
          label: t('consultationRecord.schoolType'),
          path: `kids.kids[${index}].schoolType`,
          renderValue: (value: ISchoolType) =>
            value && SchoolTypeOptions[value],
        },
      ]),
    [kids, t],
  );

  const kidsFields: IBlock = {
    title: t('consultationRecord.kidInformation'),
    icon: <KidIcon />,
    fields: [
      {
        label: t('consultationRecord.kids'),
        path: 'kids.kids',
        renderValue: (value: unknown) =>
          value && (
            <>
              <Typography>
                {isEmpty(value) ? RadioOptions.no : RadioOptions.yes}
              </Typography>
              <Typography fontSize={14} mt={1} color="hint">
                {t('consultationRecord.under18')}
              </Typography>
            </>
          ),
      },
    ].concat(childrenFields.flat() as never),
  };

  return (
    <Box>
      <ViewEditBlock
        detail={customerProfile}
        block={profileBlock}
        formId="profile-form"
        hideEditModeButton
      />
      <ViewEditBlock
        detail={data}
        block={divorceBlock}
        formId="divorce-form"
        hideEditModeButton
      />
      <ViewEditBlock
        detail={data}
        block={partnerBlock}
        formId="partner-form"
        hideEditModeButton
      />
      <ViewEditBlock
        detail={data}
        block={marriageBlock}
        formId="marriage-form"
        hideEditModeButton
      />
      <ViewEditBlock
        detail={data}
        block={propertyBlock}
        formId="property-form"
        hideEditModeButton
      />
      <ViewEditBlock
        detail={data}
        block={kidsFields}
        formId="kid-form"
        hideEditModeButton
      />
    </Box>
  );
};

export default CustomerConsultation;

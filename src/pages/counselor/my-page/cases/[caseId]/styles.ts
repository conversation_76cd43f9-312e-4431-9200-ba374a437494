import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  editIcon: {
    color: 'white',
    fontSize: '16px',
    ':not(.Mui-disabled)': { svg: { color: 'white' } },
  },
  updatedContainer: {
    mt: '11px',
    bgcolor: '#FDF6E2',
    p: { xs: '4px 8px', tablet: 1 },
    borderRadius: { xs: '4px', tablet: '6px' },
    alignItems: 'center',
    svg: {
      color: 'primary.main',
      width: { xs: 16, tablet: 20 },
      height: { xs: 16, tablet: 20 },
    },
  },
  containerCase: {
    p: { xs: 2, tablet: 4 },
    bgcolor: 'white',
    borderRadius: { xs: 0, tablet: 2 },
  },
  caseInfo: {
    display: { xs: 'block', tablet: 'flex' },
    justifyContent: 'space-between',
    mb: 1,
  },
  title: {
    fontSize: { xs: 20, tablet: 24 },
    fontWeight: 'bold',
    lineHeight: { xs: '28px', tablet: '35px' },
    color: 'heading',
  },
  icon: (theme) => ({
    mt: { tablet: '2px' },
    svg: { width: 28, height: 32 },
    [theme.breakpoints.down('tablet')]: {
      svg: {
        display: 'block',
        width: 28,
        height: 28,
      },
    },
  }),
  alert: {
    my: 3,
    bgcolor: 'divine',
    borderRadius: '6px',
    display: 'flex',
    justifyContent: 'center',
    pt: '9px',
    pb: '7px',
  },
  checkError: {
    svg: {
      color: 'icon',
      width: { xs: 16, tablet: 20 },
      height: { xs: 16, tablet: 20 },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

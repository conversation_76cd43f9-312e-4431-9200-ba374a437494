import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import type { ReactElement } from 'react';
import React from 'react';
import ProviderCaseList from 'shared/provider/my-page/cases';
import { ProviderType, ROLES } from 'utils/constants';

const CaseList = () => {
  return <ProviderCaseList providerType={ProviderType.COUNSELOR} />;
};

CaseList.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
    </Layout>
  );
};

export default CaseList;

import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { Box, Container, Typography } from '@mui/material';
import Tab from '@mui/material/Tab';
import Breadcrumbs from 'components/Breadcrumbs';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import ProfileTab from 'components/Provider/Profile/ProfileTab';
import { t } from 'i18n';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { ROLES } from 'utils/constants';

import ConsultationList from './consultation';

const ProfilePage = () => {
  const { query, replace, pathname, isReady } = useRouter();
  const { tab = 'profile' } = query;

  if (!isReady) {
    return null;
  }
  return (
    <TabContext value={tab as string}>
      <Box borderRadius={2} p="16px 32px 0px 32px" bgcolor="white">
        <Breadcrumbs omitIndexList={[0]} />
        <Box display="flex" justifyContent="space-between" mt="12px" mb={1}>
          <Typography fontSize={32} fontWeight="bold" color="heading">
            {t('mypage.profile')}
          </Typography>
        </Box>

        <TabList
          sx={{ ml: -2 }}
          onChange={(_, newValue) =>
            replace({ pathname, query: { tab: newValue } }, undefined, {
              shallow: true,
            })
          }
        >
          <Tab label={t('lawyerProfile.basicInfo')} value="profile" />
          <Tab label={t('lawyerProfile.consultation')} value="consultations" />
        </TabList>
      </Box>
      <TabPanel value="profile" sx={{ p: 0 }}>
        <Container maxWidth="md" disableGutters>
          <ProfileTab />
        </Container>
      </TabPanel>
      <TabPanel value="consultations" sx={{ p: 0 }}>
        <Container maxWidth="md" disableGutters>
          <ConsultationList />
        </Container>
      </TabPanel>
    </TabContext>
  );
};

ProfilePage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
    </Layout>
  );
};

export default ProfilePage;

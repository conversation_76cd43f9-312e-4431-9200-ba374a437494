import {
  <PERSON>,
  Button,
  Container,
  Divider,
  <PERSON><PERSON><PERSON><PERSON>on,
  Stack,
  Typography,
} from '@mui/material';
import CustomTable from 'components/CustomTable';
import TruncateText from 'components/TruncateText';
import EmptyComponent from 'components/UI/EmptyComponent';
import { useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18next';
import { AddIcon, EditIcon, InfoIcon, SheetIcon, TrashIcon } from 'icons';
import type { MRT_ColumnDef } from 'material-react-table';
import type { ICreateConsultationMenu } from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import dynamic from 'next/dynamic';
import { useState } from 'react';
import { ConsultationMenuMeetingType } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from '../shift/styles';

const ConsultationMenuModal = dynamic(
  () => import('components/Counselor/ConsultationMenuModal'),
);
const ConsultationList = () => {
  const { setConfirmModal } = useGlobalState();
  const [addMenuModal, setAddMenuModal] = useState(false);
  const [idEdit, setIdEdit] = useState<string | undefined>();
  const [defaultValues, setDefaultValues] = useState({});

  const { mutateAsync: deleteConsultation } = useMutate(
    consultationQuery.delete,
  );

  const {
    list = [],
    total,
    isFetching,
    isLoading,
    refetch,
  } = useFetchList({ ...apiQuery.consultationMenu, omitKeys: ['tab'] });

  const columns = [
    {
      accessorKey: 'title',
      header: t('consultationMenu.title'),
      enableSorting: false,
      enableResizing: true,
      Cell: ({ cell }) => {
        return <TruncateText text={cell.getValue() as string} />;
      },
    },
    {
      accessorKey: 'content',
      header: t('consultationMenu.content'),
      enableSorting: false,
      Cell: ({ cell }) => {
        return <TruncateText text={cell.getValue() as string} />;
      },
    },
    {
      accessorKey: 'unitPrices[0].duration',
      header: t('consultationMenu.duration'),
      enableSorting: false,
      Cell: ({ row }) => {
        return <div>{`${row.original.unitPrices[0]?.duration}分`}</div>;
      },
    },
    {
      accessorKey: 'unitPrices[0].price',
      header: t('consultationMenu.unitPrice'),
      enableSorting: false,
      Cell: ({ row }) => {
        return (
          <div>{`${Helper.addComma(
            row.original.unitPrices[0]?.price as string,
          )}円`}</div>
        );
      },
    },
    {
      accessorKey: 'meetingType',
      header: t('consultationMenu.meetingType'),
      enableSorting: false,
      Cell: ({ cell }) => {
        return (
          <div>
            {
              ConsultationMenuMeetingType[
                `${
                  cell?.getValue() || ''
                }` as keyof typeof ConsultationMenuMeetingType
              ] as string
            }
          </div>
        );
      },
    },
    {
      accessorKey: 'action',
      header: '',
      maxSize: 100,
      columnDefType: 'display',
      Cell: ({ row }) => {
        return (
          <Stack direction="row" justifyContent="flex-end" mr={-1}>
            <IconButton
              sx={{ svg: { color: 'primary.main' }, mr: -1 }}
              onClick={() => {
                setAddMenuModal(true);
                setIdEdit(row.id);
                setDefaultValues({
                  title: row.original.title,
                  content: row.original.content,
                  price: row.original.unitPrices[0]?.price,
                  duration: row.original.unitPrices[0]?.duration,
                  meetingType:
                    row.original?.meetingType ||
                    Helper.convertObjectToOptions(
                      ConsultationMenuMeetingType,
                    )?.[0]?._id,
                });
              }}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              sx={{ svg: { color: 'neutral7' } }}
              onClick={() =>
                setConfirmModal({
                  onConfirm: () => {
                    deleteConsultation(
                      { _id: row.id as string },
                      {
                        onSuccess: () => refetch(),
                      },
                    );
                  },
                  title: t('consultationMenu.deleteTitle'),
                  content: t('consultationMenu.deleteContent'),
                })
              }
            >
              <TrashIcon />
            </IconButton>
          </Stack>
        );
      },
    },
  ] as MRT_ColumnDef<{
    title: string;
    content?: string;
    unitPrices: { duration: string; price: string }[];
    meetingType?: string;
  }>[];

  return (
    <Container maxWidth="md" disableGutters>
      <Box bgcolor="white" borderRadius={2}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          p="32px 32px 16px"
          mt={2}
        >
          <Stack direction="row" spacing={1} alignItems="center">
            <SheetIcon />
            <Typography fontSize={24} fontWeight="bold" color="heading">
              {t('breadcrumb.consultationMenu')}
            </Typography>
          </Stack>
          <Button
            variant="tertiary"
            sx={{ maxWidth: 96 }}
            fullWidth
            startIcon={<AddIcon />}
            color="primary"
            disabled={total === 10}
            onClick={() => {
              setAddMenuModal(true);
              setDefaultValues({
                ...defaultValues,
                meetingType: Helper.convertObjectToOptions(
                  ConsultationMenuMeetingType,
                )?.[0]?._id,
              });
            }}
          >
            {t('global.add')}
          </Button>
        </Box>
        {total === 10 && (
          <Box sx={styles.changeContainer}>
            <Stack direction="row" spacing={1}>
              <InfoIcon />
              <Typography
                color="neutral6"
                fontWeight={500}
                fontSize={{ xs: 12, tablet: 14 }}
                flex={1}
              >
                {t('consultationMenu.warning')}
              </Typography>
            </Stack>
          </Box>
        )}
        {total > 0 && <Divider sx={{ mt: total === 10 ? 3 : 2, mb: -2 }} />}
        {(isLoading || total > 0) && (
          <CustomTable
            total={total}
            isFetching={isFetching}
            isLoading={isLoading}
            data={list as {}[]}
            showPagination={false}
            columns={columns as MRT_ColumnDef<Record<string, any>>[]}
          />
        )}
        {!isLoading && total === 0 && (
          <EmptyComponent text={t('consultationMenu.empty')} />
        )}
        <ConsultationMenuModal
          open={addMenuModal}
          onClose={() => {
            setAddMenuModal(false);
            setIdEdit(undefined);
            setDefaultValues({});
          }}
          setAddMenuModal={setAddMenuModal}
          onSuccess={refetch}
          defaultValues={defaultValues as ICreateConsultationMenu}
          idEdit={idEdit}
          setIdEdit={setIdEdit}
          setDefaultValues={setDefaultValues}
        />
      </Box>
    </Container>
  );
};

export default ConsultationList;

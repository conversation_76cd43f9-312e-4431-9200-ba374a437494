import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import type { ReactElement } from 'react';
import ProviderAddConsultation from 'shared/provider/my-page/add-consultation';
import { ROLES } from 'utils/constants';

const AddConsultation = () => {
  return <ProviderAddConsultation />;
};

AddConsultation.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
    </Layout>
  );
};

export default AddConsultation;

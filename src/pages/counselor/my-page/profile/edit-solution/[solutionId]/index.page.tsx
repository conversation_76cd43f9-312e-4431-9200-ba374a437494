import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import type { ReactElement } from 'react';
import ProviderEditSolution from 'shared/provider/my-page/edit-solution/[solutionId]';
import { ROLES } from 'utils/constants';

const EditSolutionPage = () => {
  return <ProviderEditSolution />;
};

EditSolutionPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
    </Layout>
  );
};

export default EditSolutionPage;

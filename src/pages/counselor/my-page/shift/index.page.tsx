import { Box, IconButton } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import Breadcrumbs from 'components/Breadcrumbs';
import CalendarTutorial from 'components/Calendar/CalendarTutorial';
import Toolbar from 'components/Calendar/Toolbar';
import { getWeekDates } from 'components/Calendar/utils';
import MeetingItemTooltip from 'components/Case/CounselorMeetingItem/MeetingItemTooltip';
import NewCaseModal from 'components/Case/NewCaseModal';
import FullScreenDialog from 'components/FullScreen';
import Layout from 'components/Layout';
import SideMenuLayout from 'components/Layout/SideMenuLayout';
import WorkDaysModal from 'components/Provider/CounselorWorkDaysModal';
import dayjs from 'dayjs';
import { motion } from 'framer-motion';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { CounselorData, CustomerData } from 'hooks/useFetchUser/types';
import useMutate from 'hooks/useMutate';
import { SettingIcon } from 'icons';
import caseQuery from 'models/case/query';
import type {
  IBusySlot,
  ICalendarDetail,
  ISlot,
  UpdateSlotStatusPayload,
} from 'models/shift/interface';
import shiftQuery from 'models/shift/query';
import dynamic from 'next/dynamic';
import type { ReactElement } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  CaseStatusType,
  MeetingStatusType,
  ROLES,
  SlotStatus,
  WORK_TIMES,
} from 'utils/constants';

const Calendar = dynamic<any>(() => import('components/Calendar'), {
  ssr: false,
});

const getEventSlots = (start: string, end: string) => {
  const duration = dayjs(end).diff(dayjs(start), 'm');
  return Array.from(Array(duration / 30).keys()).map((number) =>
    dayjs(start)
      .add(number * 30, 'm')
      .toISOString(),
  );
};

const EVENT_COLOR = {
  [MeetingStatusType.WAITING_ANWSER_FROM_PROVIDER]: 'lemon',
  [MeetingStatusType.SCHEDULE_IS_DECIDED]: 'aliceBlue',
  [MeetingStatusType.FINISHED]: 'honeydew',
};

const getEventStyles = (event: IBusySlot) => {
  const isPastSlot = new Date(event.start) < new Date();
  if (
    [CaseStatusType.WAITING_ANWSER_FROM_PROVIDER].includes(
      event.meeting.case.status,
    )
  ) {
    return {
      color: '#fff',
      border: '1px solid #d2dce1',
      opacity: isPastSlot ? 0.75 : 1,
    };
  }

  return {
    bgcolor: EVENT_COLOR[event.meeting.status],
    opacity: isPastSlot ? 0.75 : 1,
  };
};

const ShifManagementPage = () => {
  const [openFullScreen, setOpenFullScreen] = useState(false);
  const queryClient = useQueryClient();
  const { data: user, refetch: refetchUser } = useFetchUser<CounselorData>({
    enabled: true,
  });
  const [openTutorial, setOpenTutorial] = useState(false);
  const [workDayOpen, setWorkDayOpen] = useState(false);
  const [startDate, setStartDate] = useState<string>(
    dayjs().subtract(1, 'd').startOf('w').add(1, 'd').toISOString(),
  );
  const allWeekDates = useMemo(() => getWeekDates(startDate), [startDate]);

  useEffect(() => {
    return () => {
      queryClient.removeQueries({ queryKey: ['currentUser', 'calendars'] });
    };
  }, [queryClient]);

  const {
    detail,
    isFetching,
    refetch: refetchCalendar,
  } = useFetchDetail<ICalendarDetail>(shiftQuery.calendarList(startDate));
  const { mutateAsync: updateSlotStatus } = useMutate<
    UpdateSlotStatusPayload,
    { isModified: boolean }
  >(shiftQuery.updateSlotStatus);
  const { mutateAsync: hideTutorial } = useMutate<{ schedule: boolean }>(
    shiftQuery.hideTutorial,
  );

  const [acceptModalData, setAcceptModalData] = useState<
    | { customerId: string; caseStatus: CaseStatusType; caseId: string }
    | undefined
  >();
  const { detail: consultationDetail, isFetching: isFetchingConsultation } =
    useFetchDetail<ConsultationRecord>({
      ...caseQuery.providerCustomerConsultation(
        acceptModalData?.customerId || '',
      ),
      enabled: !!acceptModalData?.customerId,
    });
  const { detail: customerProfile, isFetching: isFetchingCustomer } =
    useFetchDetail<CustomerData>({
      ...apiQuery.lawyerCustomerProfile(acceptModalData?.customerId || ''),
      enabled: !!acceptModalData?.customerId,
    });

  const { mutateAsync: acceptCase, isLoading: isAccepting } = useMutate(
    caseQuery.acceptCase(acceptModalData?.caseId || ''),
  );

  const { mutateAsync: declineCase, isLoading: isDeclining } = useMutate(
    caseQuery.declineCase(acceptModalData?.caseId || ''),
  );

  useEffect(() => {
    if (user?.showTutorial.schedule) {
      setOpenTutorial(true);
      hideTutorial(
        { schedule: false },
        {
          onSuccess: () => refetchUser(),
        },
      );
    }
  }, [hideTutorial, refetchUser, user?.showTutorial.schedule]);

  const { closeSlots, closeByGapSlots } = useMemo(
    () =>
      (detail?.slots || []).reduce(
        (
          prev: { closeSlots: string[]; closeByGapSlots: string[] },
          item: ISlot,
        ) => {
          return {
            closeByGapSlots:
              item.status === SlotStatus.CLOSED_BY_GAP_TIME
                ? [...prev.closeByGapSlots, item.start]
                : prev.closeByGapSlots,
            closeSlots: [
              SlotStatus.CLOSED_BY_PROVIDER,
              SlotStatus.CLOSED_BY_GAP_TIME,
            ].includes(item.status)
              ? [...prev.closeSlots, item.start]
              : prev.closeSlots,
          };
        },
        { closeSlots: [], closeByGapSlots: [] },
      ),
    [detail?.slots],
  );

  const { busySlots, busySlotTimes } = useMemo(
    () =>
      (detail?.busySlot || []).reduce(
        (
          prev: {
            busySlots: IBusySlot[];
            busySlotTimes: string[];
          },
          item,
        ) => {
          return {
            busySlots: [
              ...prev.busySlots,
              {
                ...item,
                id: item.meeting._id,
                title: item.meeting.consumer.fullName,
                content: `${item.meeting.consultationMenu.unitPrices[0].duration}分 - ${item.meeting.consultationMenu.title}`,
                ...getEventStyles(item),
              },
            ],
            busySlotTimes: [
              ...prev.busySlotTimes,
              ...getEventSlots(item.start, item.end),
            ],
          };
        },
        { busySlots: [], busySlotTimes: [] },
      ),
    [detail?.busySlot],
  );

  const handleSlotChange = useCallback(
    (slotTime: string, status: string) => {
      if (!closeByGapSlots.includes(slotTime)) {
        updateSlotStatus(
          {
            status:
              status === 'CLOSED'
                ? SlotStatus.OPEN
                : SlotStatus.CLOSED_BY_PROVIDER,
            slots: [
              {
                start: slotTime,
                end: dayjs(slotTime).add(30, 'm').toISOString(),
              },
            ],
          },
          {
            onSuccess: (data) => {
              if (data.isModified) {
                refetchCalendar();
              }
            },
          },
        );
      }
    },
    [closeByGapSlots, refetchCalendar, updateSlotStatus],
  );

  const handleTimeSelect = useCallback(
    (time: string) => {
      // All slots in a row has { start: string, end: string }
      const allSelectedSlots = allWeekDates.map((dateInWeek) => {
        const selectedTime = dayjs(time, 'HH:mm');
        const date = dayjs(dateInWeek)
          .set('hour', selectedTime.get('hour'))
          .set('minute', selectedTime.get('minute'));
        const endDate = date.add(30, 'm');
        const dateString = date.toISOString();
        return {
          start: dateString,
          end: endDate.toISOString(),
        };
      });
      // Check if all slots has any open slot, true -> close all, false -> open all in a row
      const allOpenlots = allSelectedSlots.filter(
        (slot) =>
          !busySlotTimes.includes(slot.start) &&
          !closeSlots.includes(slot.start) &&
          dayjs(slot.start) > dayjs() &&
          dayjs(slot.start) < dayjs().startOf('d').add(42, 'd'),
      );

      updateSlotStatus(
        {
          status:
            allOpenlots.length > 0
              ? SlotStatus.CLOSED_BY_PROVIDER
              : SlotStatus.OPEN,
          slots: allSelectedSlots,
        },
        {
          onSuccess: (data) => {
            if (data.isModified) {
              refetchCalendar();
            }
          },
        },
      );
    },
    [
      allWeekDates,
      busySlotTimes,
      closeSlots,
      refetchCalendar,
      updateSlotStatus,
    ],
  );

  const handleDateSelect = useCallback(
    (selectedDate: string) => {
      const allSelectedSlots = WORK_TIMES.map((time) => {
        const selectedTime = dayjs(time, 'HH:mm');
        const date = dayjs(selectedDate)
          .set('hour', selectedTime.get('hour'))
          .set('minute', selectedTime.get('minute'));
        const endDate = date.add(30, 'm');
        const dateString = date.toISOString();
        return {
          start: dateString,
          end: endDate.toISOString(),
        };
      });
      // Check if all slots has any open slot, true -> close all, false -> open all in a row
      const allOpenlots = allSelectedSlots.filter(
        (slot) =>
          !busySlotTimes.includes(slot.start) &&
          !closeSlots.includes(slot.start) &&
          dayjs(slot.start) > dayjs(),
      );
      updateSlotStatus(
        {
          status:
            allOpenlots.length > 0
              ? SlotStatus.CLOSED_BY_PROVIDER
              : SlotStatus.OPEN,
          slots: allSelectedSlots,
        },
        {
          onSuccess: (data) => {
            if (data.isModified) {
              refetchCalendar();
            }
          },
        },
      );
    },
    [busySlotTimes, closeSlots, refetchCalendar, updateSlotStatus],
  );

  const eventTooltip = useCallback((event: IBusySlot) => {
    return (
      <Box width={384}>
        <MeetingItemTooltip meeting={event.meeting} />
      </Box>
    );
  }, []);
  const onEventSelect = useCallback((data: IBusySlot) => {
    if (
      data.meeting.case.status === CaseStatusType.WAITING_ANWSER_FROM_PROVIDER
    ) {
      setAcceptModalData({
        customerId: data.meeting.consumer._id,
        caseStatus: data.meeting.case.status,
        caseId: data.meeting.case._id,
      });
    }
  }, []);

  return (
    <Box borderRadius={2} bgcolor="white" p="16px 32px 0px">
      <Box mb={3}>
        <Breadcrumbs omitIndexList={[0]} />
      </Box>
      <FullScreenDialog
        open={openFullScreen}
        onClose={() => setOpenFullScreen(false)}
      >
        <Box>
          <Toolbar
            isFullScreen={openFullScreen}
            onExpandClick={() => setOpenFullScreen(!openFullScreen)}
            onTooltipClick={() => setOpenTutorial(true)}
            startDate={startDate}
            onNavigate={(date) => setStartDate(date)}
            extraRender={
              <IconButton
                className="whiteOutlined"
                onClick={() => {
                  setWorkDayOpen(true);
                }}
              >
                <SettingIcon />
              </IconButton>
            }
          />
          <Box
            mt="18px"
            maxHeight={
              !openFullScreen ? 'calc(100vh - 343px)' : 'calc(100vh - 128px)'
            }
            overflow="auto"
            component={motion.div}
            key={startDate}
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.4 }}
            position="relative"
          >
            <Calendar
              closeSlots={closeSlots}
              startDate={startDate}
              isLoading={isFetching}
              events={busySlots}
              onSlotSelect={handleSlotChange}
              onDateSelect={handleDateSelect}
              onTimeSelect={handleTimeSelect}
              onEventSelect={onEventSelect}
              eventTooltip={eventTooltip}
              timeColumnWidth={openFullScreen ? 130 : 88}
            />
          </Box>
          {!openFullScreen && (
            <Box
              boxShadow="0 -8px 16px -12px #d2dce1"
              width={1}
              height="32px"
            />
          )}
        </Box>
      </FullScreenDialog>
      <WorkDaysModal open={workDayOpen} setOpen={() => setWorkDayOpen(false)} />
      <CalendarTutorial
        open={openTutorial}
        onClose={() => setOpenTutorial(false)}
      />
      <NewCaseModal
        consultationDetail={consultationDetail}
        customerProfile={customerProfile}
        caseStatus={acceptModalData?.caseStatus}
        loading={isFetchingCustomer || isFetchingConsultation}
        open={!!acceptModalData}
        isConfirming={isAccepting}
        isCanceling={isDeclining}
        onClose={() => setAcceptModalData(undefined)}
        onConfirm={() => {
          acceptCase(null, {
            onSuccess: () => {
              setAcceptModalData(undefined);
              refetchCalendar();
            },
          });
        }}
        onCancel={(values) => {
          declineCase(values, {
            onSuccess: () => {
              setAcceptModalData(undefined);
              refetchCalendar();
            },
          });
        }}
      />
    </Box>
  );
};

ShifManagementPage.getLayout = function getLayout(page: ReactElement) {
  return (
    <Layout>
      <SideMenuLayout role={ROLES.COUNSELOR}>{page}</SideMenuLayout>
    </Layout>
  );
};

export default ShifManagementPage;

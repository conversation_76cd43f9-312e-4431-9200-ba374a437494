const styles = {
  breadcrumbWrapper: {
    bgcolor: 'white',
    p: { xs: '8px 16px', tablet: '16px 16px 16px 32px' },
    borderRadius: { xs: 0, tablet: 2 },
    my: { xs: 1, tablet: 0 },
  },
  breadcrumbText: {
    fontSize: { xs: 24, tablet: 32 },
    fontWeight: 'bold',
    color: 'heading',
  },
  kidHelperText: {
    fontSize: { xs: 12, tablet: 14 },
    mt: 1,
    color: 'hint',
  },
  infoButton: {
    p: 0,
    ml: 1,
    svg: { color: 'icon', display: 'block' },
  },
  changeContainer: {
    bgcolor: '#edf1f3',
    py: 1,
    mx: 4,
    display: 'flex',
    justifyContent: { xs: ' flex-start', tablet: 'center' },
    borderRadius: { xs: '4px', tablet: '6px' },
    alignItems: 'center',
    svg: {
      color: 'icon',
      width: { xs: 16, tablet: 20 },
      height: { xs: 16, tablet: 20 },
    },
  },
} as const;

export default styles;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    mt: { xs: 1, tablet: '29px' },
    borderRadius: { xs: 0, tablet: 2 },
    bgcolor: 'white',
    p: { xs: 2, tablet: 4 },
  },
  title: {
    textAlign: 'center',
    fontSize: { xs: 24, tablet: 32 },
    color: 'heading',
    fontWeight: 'bold',
    mb: { xs: 2, tablet: '41px' },
  },
  description: {
    fontSize: { xs: 12, tablet: 14 },
    color: 'text.primary',
    textAlign: 'center',
    whiteSpace: { xs: 'pre-line', tablet: 'normal' },
  },
  content: {
    mt: { xs: 2, tablet: '40px' },
    borderRadius: { xs: 1, tablet: '12px' },
    border: (theme) => `1px solid ${theme.palette.neutral2}`,
    p: { xs: 2, tablet: '24px 32px' },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

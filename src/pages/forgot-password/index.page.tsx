import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Container, Typography } from '@mui/material';
import { TextField } from 'components/Form';
import Layout from 'components/Layout';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import type { ForgotPasswordPayload } from 'models/auth/interface';
import authQuery from 'models/auth/query';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import { useForm } from 'react-hook-form';
import { Regex } from 'utils/constants';
import { object, string } from 'yup';

import styles from './styles';

const ForgotPassword = () => {
  const { push, query } = useRouter();
  const { role } = query;
  const { mutateAsync: forgotPassword, isLoading } =
    useMutate<ForgotPasswordPayload>(authQuery.forgotPassword(role as string));
  const { setConfirmModal } = useGlobalState();
  const LoginSchema = object().shape({
    email: string()
      .required()
      .trim()
      .matches(Regex.EMAIL, t('validation.invalidField')),
  });

  const handleForgotPassword = (value: ForgotPasswordPayload) => {
    const email = value.email.toLowerCase();
    forgotPassword(
      { email },
      {
        onSuccess: () => {
          setConfirmModal({
            icon: 'success',
            content: t('forgotPassword.confirmText', { email }),
            hideCancelButton: true,
            confirmText: t('global.backToHome'),
            onConfirm: () => push('/'),
          });
        },
      },
    );
  };

  const { control, handleSubmit } = useForm<ForgotPasswordPayload>({
    resolver: yupResolver(LoginSchema),
    mode: 'onTouched',
  });

  return (
    <Container maxWidth="mw" disableGutters sx={styles.container}>
      <Typography sx={styles.title}>{t('forgotPassword.title')}</Typography>
      <Typography sx={styles.description}>
        {t('forgotPassword.description1')}
        <Typography
          sx={styles.description}
          display={{ xs: 'block', tablet: 'inline' }}
          component="span"
        >
          {t('forgotPassword.secondDescription1')}
        </Typography>
      </Typography>
      <Typography sx={styles.description}>
        {t('forgotPassword.description2')}
      </Typography>
      <form onSubmit={handleSubmit(handleForgotPassword)}>
        <Box mt={{ xs: 2, tablet: '40px' }}>
          <TextField
            labelCol={12}
            name="email"
            control={control}
            label={t('login.email')}
            placeholder={t('placeholder.email')}
          />
        </Box>
        <Box
          display="flex"
          justifyContent="center"
          mt={{ xs: 4, tablet: '40px' }}
        >
          <LoadingButton
            color="secondary"
            type="submit"
            variant="contained"
            size="large"
            className="tabletStyle"
            fullWidth
            loading={isLoading}
            sx={{
              maxWidth: 368,
            }}
          >
            {t('forgotPassword.submit')}
          </LoadingButton>
        </Box>
      </form>
    </Container>
  );
};

ForgotPassword.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};

export default ForgotPassword;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    pt: 2,
    pb: { xs: 1, tablet: 4 },
  },
  containerInfo: {
    mt: 2,
    bgcolor: 'white',
    borderRadius: { xs: 0, md: 2 },
    p: { xs: 2, tablet: 4 },
  },
  title: {
    textAlign: 'center',
    fontSize: { xs: 24, tablet: 32 },
    color: 'heading',
    fontWeight: 'bold',
    mb: { xs: 2, tablet: '40px' },
  },
  label: {
    mt: { xs: 3, tablet: 4 },
    color: 'heading',
    fontWeight: 'bold',
    fontSize: { xs: 18, tablet: 20 },
  },
  content: {
    mt: { xs: 1, tablet: 2 },
    color: 'heading',
    fontSize: { xs: 14, tablet: 16 },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

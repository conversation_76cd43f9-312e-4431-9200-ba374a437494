import { Box, Container, Typography } from '@mui/material';
import Breadcrumbs from 'components/Breadcrumbs';
import Layout from 'components/Layout';
import ContactForm from 'components/Layout/ContactForm';
import type { ContactUsFormSchema } from 'components/Layout/ContactForm/schema';
import OperatorContact from 'components/UI/OperatorContact';
import apiQuery from 'hooks/apiQuery';
import type { ContactUsPayload } from 'hooks/types';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import type { ReactElement } from 'react';
import { INQUIRY_CATEGORY } from 'utils/constants';
import seoConfig from 'utils/seo.config';

import styles from './styles';

const ContactUs = () => {
  const { push } = useRouter();
  const { mutateAsync: contactUs, isLoading } = useMutate<
    ContactUsPayload,
    unknown
  >(apiQuery.contactUs);

  const { setConfirmModal } = useGlobalState();
  const handleCreateContactUs = (values: ContactUsFormSchema) => {
    const { name, email, content, category } = values;
    contactUs(
      {
        name,
        email,
        content,
        category: {
          value: category,
          text: INQUIRY_CATEGORY[category as keyof typeof INQUIRY_CATEGORY],
        },
      },
      {
        onSuccess: () => {
          setConfirmModal({
            icon: 'success',
            content: t('contactUs.contentConfirm'),
            confirmText: t('global.backToHome'),
            hideCancelButton: true,
            onConfirm: () => push('/'),
          });
        },
      },
    );
  };

  return (
    <Container maxWidth="lg" disableGutters sx={styles.container}>
      <NextSeo {...seoConfig.contactUs} />
      <Box px={{ xs: 2, sl: 0 }}>
        <Breadcrumbs />
      </Box>
      <Container maxWidth="md" disableGutters sx={styles.containerForm}>
        <Typography sx={styles.contactHeader}>
          {t('breadcrumb.contactUs')}
        </Typography>
        <ContactForm onSubmit={handleCreateContactUs} loading={isLoading} />
      </Container>
      <Container maxWidth="md" disableGutters sx={styles.contactContainer}>
        <OperatorContact />
      </Container>
    </Container>
  );
};

ContactUs.getLayout = function getLayout(page: ReactElement) {
  return <Layout showMedia>{page}</Layout>;
};

export default ContactUs;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    pt: 2,
    pb: { xs: 1, tablet: 4 },
  },
  containerForm: {
    mt: { xs: 2, tablet: 4 },
    bgcolor: 'white',
    borderRadius: { xs: 0, md: 2 },
    p: { xs: 2, tablet: 4 },
  },
  contactContainer: {
    mt: { xs: 1, tablet: 2 },
    bgcolor: 'white',
    borderRadius: { xs: 0, md: 2 },
    padding: { xs: '24px 16px', tablet: 4 },
  },
  contactHeader: {
    textAlign: 'center',
    fontSize: { xs: 24, tablet: 32 },
    color: 'heading',
    fontWeight: 'bold',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

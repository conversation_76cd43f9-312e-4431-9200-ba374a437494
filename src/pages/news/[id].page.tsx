import { Box, Container, Typography } from '@mui/material';
import NewLayout from 'components/NewHome/Layout';
import { useFetchItem } from 'hooks';
import type { INewsItem } from 'models/news/interface';
import newsQuery from 'models/news/query';
import Image from 'next/image';
import { useRouter } from 'next/router';
import type { ReactElement } from 'react';
import React, { useEffect, useState } from 'react';

// Shimmer effect animation
const shimmer = (w: number, h: number) => `
<svg width="${w}" height="${h}" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <linearGradient id="g">
      <stop stop-color="#f6f7f8" offset="0%" />
      <stop stop-color="#edeef1" offset="20%" />
      <stop stop-color="#f6f7f8" offset="40%" />
      <stop stop-color="#f6f7f8" offset="100%" />
    </linearGradient>
  </defs>
  <rect width="${w}" height="${h}" fill="#f6f7f8" />
  <rect id="r" width="${w}" height="${h}" fill="url(#g)" />
  <animate xlink:href="#r" attributeName="x" from="-${w}" to="${w}" dur="1s" repeatCount="indefinite"  />
</svg>`;

const toBase64 = (str: string) =>
  typeof window === 'undefined'
    ? Buffer.from(str).toString('base64')
    : window.btoa(str);

const NewsDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [imageLoaded, setImageLoaded] = useState(false);

  const {
    item: newsItem,
    isLoading,
    refetch,
  } = useFetchItem<INewsItem>({
    ...newsQuery.newsDetail,
    getPath: id ? () => `${id}` : undefined,
  });

  useEffect(() => {
    if (id) {
      refetch();
    }
  }, [id, refetch]);

  // Process HTML content to fix incomplete URLs
  const processHtmlContent = (htmlContent: string) => {
    if (!htmlContent) return '';

    // First decode HTML entities
    const textArea = document.createElement('textarea');
    textArea.innerHTML = htmlContent;
    let decodedContent = textArea.value;

    // Replace href attributes that don't start with http://, https://, mailto:, tel:, or #
    decodedContent = decodedContent.replace(
      /href="([^"]*?)"/g,
      (match, url) => {
        // Skip if URL already has protocol, is relative (starts with /), is anchor (starts with #), or is special protocol
        if (
          url.startsWith('http://') ||
          url.startsWith('https://') ||
          url.startsWith('mailto:') ||
          url.startsWith('tel:') ||
          url.startsWith('#') ||
          url.startsWith('/')
        ) {
          return match;
        }

        // Add https:// to incomplete URLs
        return `href="https://${url}"`;
      },
    );

    // Add target="_blank" and rel="noopener noreferrer" to external links for security
    decodedContent = decodedContent.replace(
      /<a\s+([^>]*?)href="([^"]*?)"([^>]*?)>/gi,
      (match, beforeHref, url, afterHref) => {
        // Check if it's an external link
        const isExternal =
          url.startsWith('http://') ||
          url.startsWith('https://') ||
          url.startsWith('mailto:') ||
          url.startsWith('tel:');

        if (isExternal) {
          // Add target and rel attributes if not already present
          let attributes = beforeHref + afterHref;
          if (!attributes.includes('target=')) {
            attributes += ' target="_blank"';
          }
          if (!attributes.includes('rel=')) {
            attributes += ' rel="noopener noreferrer"';
          }
          return `<a ${attributes} href="${url}">`;
        }

        return match;
      },
    );

    return decodedContent;
  };

  // Format date to JST time format
  const formatDate = (dateString: string) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    // Add 9 hours for JST
    date.setHours(date.getHours() + 9);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}.${month}.${day}`;
  };

  // Render loading skeleton
  if (isLoading) {
    return (
      <Box
        component="section"
        sx={{ width: '100%', bgcolor: 'white', py: { xs: 6, md: 9 } }}
      >
        <Container maxWidth="lg">
          <Box sx={{ maxWidth: '900px', mx: 'auto' }}>
            <Box
              sx={{ height: '40px', width: '70%', bgcolor: '#eee', mb: 2 }}
            />
            <Box
              sx={{ height: '20px', width: '120px', bgcolor: '#eee', mb: 4 }}
            />
            <Box
              sx={{ height: '400px', width: '100%', bgcolor: '#eee', mb: 4 }}
            />
            <Box
              sx={{ height: '20px', width: '100%', bgcolor: '#eee', mb: 2 }}
            />
            <Box
              sx={{ height: '20px', width: '90%', bgcolor: '#eee', mb: 2 }}
            />
            <Box
              sx={{ height: '20px', width: '95%', bgcolor: '#eee', mb: 2 }}
            />
          </Box>
        </Container>
      </Box>
    );
  }

  // Handle case when news item doesn't exist
  if (!newsItem) {
    return (
      <Box
        component="section"
        sx={{ width: '100%', bgcolor: 'white', py: { xs: 6, md: 9 } }}
      >
        <Container maxWidth="lg">
          <Typography sx={{ textAlign: 'center', py: 8 }}>
            お知らせが見つかりませんでした
          </Typography>
        </Container>
      </Box>
    );
  }

  return (
    <Box>
      <Box
        component="section"
        sx={{ width: '100%', bgcolor: 'white', py: { xs: 6, md: 9 } }}
      >
        <Container maxWidth="lg">
          <Box sx={{ maxWidth: '900px', mx: 'auto' }}>
            {/* Title */}
            <Typography
              variant="h1"
              sx={{
                fontSize: { xs: '24px', md: '32px' },
                fontWeight: 700,
                color: '#262626',
                mb: 2,
                // Style anchor tags within the title
                '& a': {
                  color: 'inherit',
                  textDecoration: 'underline',
                  '&:hover': {
                    opacity: 0.8,
                  },
                },
              }}
              dangerouslySetInnerHTML={{
                __html: processHtmlContent(newsItem.title),
              }}
            />

            {/* Date */}
            <Typography
              sx={{
                fontSize: '16px',
                color: '#666',
                mb: 4,
              }}
            >
              {formatDate(newsItem.createdAt)}
            </Typography>

            {/* Image */}
            {newsItem.image?.originUrl && (
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: { xs: '250px', md: '400px' },
                  mb: 4,
                  backgroundColor: '#f6f7f8',
                }}
              >
                <Image
                  src={newsItem.image.originUrl}
                  alt={newsItem.title}
                  fill
                  style={{
                    objectFit: 'cover',
                    opacity: imageLoaded ? 1 : 0,
                    transition: 'opacity 0.3s ease-in-out',
                  }}
                  placeholder="blur"
                  blurDataURL={`data:image/svg+xml;base64,${toBase64(
                    shimmer(700, 400),
                  )}`}
                  onLoadingComplete={() => setImageLoaded(true)}
                  priority
                />
              </Box>
            )}

            {/* Content */}
            <Box
              sx={{
                fontSize: '16px',
                lineHeight: 1.7,
                color: '#262626',
                '& img': {
                  maxWidth: '100%',
                  height: 'auto',
                },
                '& a': {
                  color: 'inherit',
                  textDecoration: 'underline',
                  '&:hover': {
                    opacity: 0.8,
                  },
                },
              }}
              dangerouslySetInnerHTML={{
                __html: processHtmlContent(newsItem.content || ''),
              }}
            />
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

NewsDetailPage.getLayout = function getLayout(page: ReactElement) {
  return <NewLayout>{page}</NewLayout>;
};

export default NewsDetailPage;

import useEmblaCarousel from 'embla-carousel-react';
import { useCallback, useEffect, useState } from 'react';

const useCarousel = (data?: unknown) => {
  const [viewportRef, embla] = useEmblaCarousel({ loop: false });
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const scrollPrev = useCallback(
    (e: any) => {
      e.preventDefault();
      if (embla) {
        embla.scrollPrev();
      }
    },
    [embla],
  );
  const scrollNext = useCallback(
    (e: any) => {
      e.preventDefault();
      if (embla) {
        embla.scrollNext();
      }
    },
    [embla],
  );
  const scrollTo = useCallback(
    (index: number) => embla && embla.scrollTo(index),
    [embla],
  );

  const onSelect = useCallback(() => {
    if (!embla) return;
    setSelectedIndex(embla.selectedScrollSnap());
    setPrevBtnEnabled(embla.canScrollPrev());
    setNextBtnEnabled(embla.canScrollNext());
  }, [embla]);

  useEffect(() => {
    if (!embla) return;
    embla.on('select', onSelect);
    embla.on('reInit', onSelect);
    onSelect();
    setScrollSnaps(embla.scrollSnapList());
  }, [embla, onSelect]);

  useEffect(() => {
    if (embla) {
      embla.reInit();
      setScrollSnaps(embla.scrollSnapList());
    }
  }, [data, embla]);

  return {
    viewportRef,
    prevBtnEnabled,
    scrollTo,
    nextBtnEnabled,
    selectedIndex,
    scrollSnaps,
    scrollPrev,
    scrollNext,
  };
};

export default useCarousel;

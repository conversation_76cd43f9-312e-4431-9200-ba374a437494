import type { ConfirmModalProps } from 'components/ConfirmModal/modal';
import { isEmpty } from 'lodash';
import create from 'zustand';

const getAuthChannel = () => {
  try {
    return new BroadcastChannel('auth');
  } catch {
    return null;
  }
};

interface LoginModalPayload {
  redirect?: string;
  email?: string;
}
export interface GlobalState {
  loginModal: LoginModalPayload | boolean;
  setLoginModal: (payload: LoginModalPayload | boolean) => void;
  openConfirmModal: boolean;
  toggleConfirmModal: (payload: boolean) => void;
  confirmModal: ConfirmModalProps | {};
  setConfirmModal: (payload: ConfirmModalProps | {}) => void;
  authChannel: BroadcastChannel | null;
}
const useGlobalState = create<GlobalState>((set) => ({
  loginModal: false,
  setLoginModal: (payload) => set({ loginModal: payload }),
  openConfirmModal: false,
  toggleConfirmModal: (payload) => set({ openConfirmModal: payload }),
  confirmModal: {},
  setConfirmModal: (payload) =>
    set({ confirmModal: payload, openConfirmModal: !isEmpty(payload) }),
  authChannel: getAuthChannel(),
}));

export default useGlobalState;

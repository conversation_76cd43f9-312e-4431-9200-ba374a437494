import type {
  GenderType,
  ISchoolType,
  RadioOptionsType,
} from 'utils/constants';

export interface PurposePayload {
  purpose: string[];
}

export interface DivorceBackgroundPayload {
  youDivorce: string;
  partnerDivorce: string;
  reason: {
    value?: string;
    extraData?: string;
  }[];
}

export interface PartnerInformationPayload {
  age: string;
  job: string;
  annualIncome?: string;
  firstName: string;
  lastName: string;
  firstKatakanaName: string;
  lastKatakanaName: string;
}

export interface MarriageInformationPayload {
  marriedDate: string;
  isSeparated: string;
  separationDate?: string;
}

export interface PropertyInformationPayload {
  firstValueOfRealEstate?: number;
  currentValueOfRealEstate?: number;
  loan?: number;
  monthlyLoanPayment?: number;
  loanPaymentAtBonusMonth?: number;
  depositAndSaving?: number;
  depositAndSavingOfPartner?: number;
  lifeInsurance?: string;
  typeOfPensionOfYourself?: string;
  typeOfPensionOfPartner?: string;
  otherProperties?: string;
  supplementaryInfo?: string;
}

export interface KidsPayload {
  kids: {
    gender?: GenderType;
    age?: string;
    schoolType?: ISchoolType;
  }[];
}

export interface KidsFormPayload {
  hasKid?: Omit<RadioOptionsType, 'other'>;
  kids: {
    gender?: GenderType;
    schoolType?: ISchoolType;
    age?: string;
  }[];
}

export interface ExpectLawyerPayload {
  gender?: GenderType;
  age?: string;
  consultation?: string[];
  nothingSpecial?: boolean;
}

export interface PreferencePayload {
  gender?: GenderType;
  age?: string;
  consultation?: string[];
  nothingSpecial?: boolean;
}

export interface ConsultationRecord {
  backgroundOfDivorce?: {
    youDivorce: string;
    partnerDivorce: string;
    reason: {
      value: string;
      extraData?: string;
    }[];
  };
  partner?: {
    age: {
      _id: string;
      value: string;
      min: number;
      max: number;
    };
    job: string;
    annualIncome?: string;
    firstName: string;
    lastName: string;
    firstKatakanaName: string;
    lastKatakanaName: string;
  };
  marriageInformation?: {
    marriedDate: string;
    isSeparated: string;
    separationDate?: string;
  }; // form 5
  propertyInformation?: {
    firstValueOfRealEstate?: number;
    currentValueOfRealEstate?: number;
    loan?: number;
    monthlyLoanPayment?: number;
    loanPaymentAtBonusMonth?: number;
    depositAndSaving?: number;
    depositAndSavingOfPartner?: number;
    lifeInsurance?: string;
    typeOfPensionOfYourself?: string;
    typeOfPensionOfPartner?: string;
    otherProperties?: string;
    supplementaryInfo?: string;
  }; // form 6
  kids?: {
    kids: { gender?: GenderType; age?: string; schoolType?: ISchoolType }[];
  }; // form 7
  expectLawyer?: {
    gender?: GenderType;
    age?: {
      _id: string;
      value: string;
      min: number;
      max: number;
    };
    consultation?: { value: string; _id: string }[];
    nothingSpecial?: boolean;
  };
  expectCounselor?: {
    gender?: GenderType;
    age?: {
      _id: string;
      value: string;
      min: number;
      max: number;
    };
    nothingSpecial?: boolean;
    consultation?: { value: string; _id: string }[];
  };
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IBasicConsultationPayload {
  profile: {
    firstName: string;
    lastName: string;
    firstKatakanaName: string;
    lastKatakanaName: string;
    gender: string;
    phone?: string;
    currentAddress1: string;
  };
  backgroundOfDivorce: {
    youDivorce: string;
    partnerDivorce: string;
    additionalTopics?: string;
    reason: {
      value: string;
      extraData?: string;
    }[];
  };
  marriageInformation: {
    marriedDate: string;
    isSeparated: string;
    separationDate?: string;
  };
  expectLawyer?: {
    gender?: string;
    age?: string;
    consultation?: string[];
  };
  expectCounselor?: {
    gender?: string;
    age?: string;
    consultation?: string[];
  };
  guestOtp?: {
    guestExpiredAt: string;
    otpExpiredAt?: string | null;
    otpId: string;
    otpValue: string;
    otpVerify: boolean;
  };
}

export interface IBasicConsultationForm {
  firstName: string;
  lastName: string;
  firstKatakanaName: string;
  lastKatakanaName: string;
  gender: string;
  phone?: string;
  youDivorce: string;
  partnerDivorce: string;
  reason: { check?: string; value: string[]; extraData?: string };
  additionalTopics?: string;
  marriedDate: string;
  isSeparated: string;
  separationDate?: string;
  lawyerGender?: string;
  lawyerAge?: string;
  lawyerConsultation?: string[];
  preferenceGender?: string;
  preferenceAge?: string;
  preferenceConsultation?: string[];
  currentAddress1: string;
}

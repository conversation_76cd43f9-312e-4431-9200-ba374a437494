import type { UseMutateAsyncFunction } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import api from 'utils/api';

import type {
  DivorceBackgroundPayload,
  ExpectLawyerPayload,
  IBasicConsultationPayload,
  KidsPayload,
  MarriageInformationPayload,
  PartnerInformationPayload,
  PropertyInformationPayload,
  PurposePayload,
} from './types';

const useConsulationForm = (): {
  updatePurposeOfUse: UseMutateAsyncFunction<
    unknown,
    unknown,
    PurposePayload,
    unknown
  >;
  updateDivorceBackground: UseMutateAsyncFunction<
    unknown,
    unknown,
    DivorceBackgroundPayload,
    unknown
  >;
  updateParterInformation: UseMutateAsyncFunction<
    unknown,
    unknown,
    PartnerInformationPayload,
    unknown
  >;
  updateMarriageInformation: UseMutateAsyncFunction<
    unknown,
    unknown,
    MarriageInformationPayload,
    unknown
  >;
  updatePropertyInformation: UseMutateAsyncFunction<
    unknown,
    unknown,
    PropertyInformationPayload,
    unknown
  >;
  updateKidsInformation: UseMutateAsyncFunction<
    unknown,
    unknown,
    KidsPayload,
    unknown
  >;
  updateExpectLawyer: UseMutateAsyncFunction<
    unknown,
    unknown,
    ExpectLawyerPayload,
    unknown
  >;
  updateBasicConsultation: UseMutateAsyncFunction<
    unknown,
    unknown,
    IBasicConsultationPayload,
    unknown
  >;
  isUpdatingBasicConsultation: boolean;
  isUpdatingPurpose: boolean;
  isUpdatingDivorceBackground: boolean;
  isUpdatingPartner: boolean;
  isUpdatingMarriage: boolean;
  isUpdatingProperty: boolean;
  isUpdatingExpectLawyer: boolean;
  isUpdatingKidsInformation: boolean;
} => {
  const { mutateAsync: updatePurposeOfUse, isLoading: isUpdatingPurpose } =
    useMutation(
      (payload: PurposePayload) =>
        api.put('request/consumer/consultation-record/purpose', payload),
      {
        mutationKey: ['updatePurposeOfUse'],
      },
    );

  const {
    mutateAsync: updateDivorceBackground,
    isLoading: isUpdatingDivorceBackground,
  } = useMutation(
    (payload: DivorceBackgroundPayload) =>
      api.put(
        'request/consumer/consultation-record/background-of-divorce',
        payload,
      ),
    {
      mutationKey: ['updateDivorceBackground'],
    },
  );

  const { mutateAsync: updateParterInformation, isLoading: isUpdatingPartner } =
    useMutation(
      (payload: PartnerInformationPayload) =>
        api.put('request/consumer/consultation-record/partner', payload),
      {
        mutationKey: ['updatePartnerInformation'],
      },
    );

  const {
    mutateAsync: updateMarriageInformation,
    isLoading: isUpdatingMarriage,
  } = useMutation(
    async (payload: MarriageInformationPayload) => {
      const params = pick(payload, [
        'marriedDate',
        'isSeparated',
        'separationDate',
      ]);
      params.marriedDate = dayjs(params.marriedDate).toISOString();
      if (params.separationDate) {
        params.separationDate = dayjs(params.separationDate).toISOString();
      }
      await api.put(
        'request/consumer/consultation-record/marriage-information',
        params,
      );
    },
    {
      mutationKey: ['updateMarriageInformation'],
    },
  );

  const {
    mutateAsync: updatePropertyInformation,
    isLoading: isUpdatingProperty,
  } = useMutation(
    (payload: PropertyInformationPayload) =>
      api.put(
        'request/consumer/consultation-record/property-information',
        payload,
      ),
    {
      mutationKey: ['updatePropertyInformation'],
    },
  );

  const {
    mutateAsync: updateKidsInformation,
    isLoading: isUpdatingKidsInformation,
  } = useMutation(
    async (payload: KidsPayload) => {
      const params = pick(payload, ['kids']);

      await api.put('request/consumer/consultation-record/kids', params);
    },
    {
      mutationKey: ['updateKidsInformation'],
    },
  );

  const { mutateAsync: updateExpectLawyer, isLoading: isUpdatingExpectLawyer } =
    useMutation(
      (payload: ExpectLawyerPayload) =>
        api.put('request/consumer/consultation-record/expect-lawyer', payload),
      {
        mutationKey: ['updateExpectLawyer'],
      },
    );

  const {
    mutateAsync: updateBasicConsultation,
    isLoading: isUpdatingBasicConsultation,
  } = useMutation(
    (payload: IBasicConsultationPayload) => {
      const params = { ...payload };
      params.marriageInformation.marriedDate = dayjs(
        params.marriageInformation.marriedDate,
      ).toISOString();
      if (params.marriageInformation.separationDate) {
        params.marriageInformation.separationDate = dayjs(
          params.marriageInformation.separationDate,
        ).toISOString();
      }

      return api.put(
        'request/consumer/consultation-record/basic-info',
        payload,
      );
    },
    {
      mutationKey: ['updateBasicConsultation'],
    },
  );

  return {
    updatePurposeOfUse,
    isUpdatingPurpose,
    updateDivorceBackground,
    isUpdatingDivorceBackground,
    updateParterInformation,
    isUpdatingPartner,
    updateMarriageInformation,
    isUpdatingMarriage,
    updatePropertyInformation,
    isUpdatingProperty,
    updateKidsInformation,
    isUpdatingKidsInformation,
    updateExpectLawyer,
    isUpdatingExpectLawyer,
    updateBasicConsultation,
    isUpdatingBasicConsultation,
  };
};

export default useConsulationForm;

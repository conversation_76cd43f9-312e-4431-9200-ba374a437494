import type { UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type { AxiosRequestConfig } from 'axios';
import { useRouter } from 'next/router';
import api from 'utils/api';
import Helper from 'utils/helpers';

import type { IListResult, PaginationLoadMore } from './types';

export interface UseFetchListData<T> {
  list: (T & {
    _id: string;
  })[];
  total: number;
  isFetching: boolean;
  isLoading: boolean;
  refetch: () => Promise<unknown>;
  ids: string[];
  entities: Record<
    string,
    T & {
      _id: string;
    }
  >;
  isFetched: boolean;
  pagination?: PaginationLoadMore;
}

export interface UseFetchListOptions
  extends Omit<
    UseQueryOptions<unknown, unknown, unknown, unknown[]>,
    'queryFn' | 'queryKey'
  > {
  customParams?: Record<string, unknown>;
  queryKey: unknown[];
  apiUrl: string;
  omitKeys?: string[];
  useUrlQuery?: boolean;
  axiosConfig?: AxiosRequestConfig;
}

const getSortString = (orderBy: string, order: string): string => {
  if (order === 'true') {
    return `${orderBy}.desc`;
  }
  return `${orderBy}.asc`;
};

const useFetchList = <T extends { _id: string | number }>(
  options: UseFetchListOptions,
): UseFetchListData<T> => {
  const {
    queryKey,
    apiUrl,
    customParams,
    omitKeys,
    useUrlQuery = true,
    axiosConfig,
    ...otherOptions
  } = options;
  const router = useRouter();
  const searchParams = router.query;

  const params = {
    page: searchParams.page || 1,
    limit: searchParams.limit || 10,
    ...(useUrlQuery ? searchParams : {}),
    ...customParams, // Do not change the order. Please pass another param if you want to override the params
  };

  const formatParams = (_params: Record<string, unknown>) => {
    const formattedParams = { ..._params };
    if (formattedParams.lawyerId) {
      formattedParams.providerId = formattedParams.lawyerId;
      delete formattedParams.lawyerId;
    }
    if (formattedParams.orderBy && formattedParams.order) {
      formattedParams.sort = getSortString(
        formattedParams.orderBy as string,
        formattedParams.order as string,
      );
      delete formattedParams.order;
      delete formattedParams.orderBy;
    }
    if (omitKeys) {
      omitKeys.forEach((key) => {
        delete formattedParams[key];
      });
    }
    return formattedParams;
  };

  const formattedParams = formatParams(params);
  const {
    data = {},
    isFetching,
    refetch,
    isLoading,
    isFetched,
  } = useQuery(
    [...queryKey, formattedParams],
    async () => {
      const { data: result } = await api.get(apiUrl, {
        params: formattedParams,
        ...axiosConfig,
      });
      const currentData = { ...(result as IListResult<T>) };
      return {
        ...currentData,
        ...Helper.convertArrayToEntities<T>(currentData.docs),
      };
    },
    {
      enabled: (useUrlQuery && router.isReady) || !useUrlQuery,
      ...otherOptions,
    },
  );

  const result = {
    ...(data as IListResult<T> & {
      data?: T[];
      ids: string[];
      entities: Record<string, T & { _id: string }>;
      pagination?: PaginationLoadMore;
    }),
  };
  return {
    list: result.docs || result.data || [],
    total: result.total,
    isLoading,
    isFetching,
    refetch,
    ids: result.ids,
    entities: result.entities,
    isFetched,
    pagination: result.pagination,
  };
};
export default useFetchList;

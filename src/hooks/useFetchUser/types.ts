import type { IListItem } from 'hooks/types';
import type { BankInformation } from 'models/provider/interface';
import type { GenderType, RadioOptionsType, ROLES } from 'utils/constants';

export interface IOfficeInfo {
  _id: string;
  fullName: string;
  katakanaName: string;
}
export interface WorkDayData {
  active: boolean;
  startTime: string;
  endTime: string;
}
export interface LawyerData {
  attribute?: IListItem[];
  barAssociation?: string;
  birthday?: string;
  consultationField?: IListItem[];
  createdAt: Date;
  email: string;
  fullName: string;
  firstName: string;
  lastName: string;
  gender?: GenderType;
  images?: { key: string; originUrl: string }[];
  isVerifiedEmail: boolean;
  katakanaName: string;
  firstKatakanaName: string;
  lastKatakanaName: string;
  divorceHistory?: RadioOptionsType;
  children?: RadioOptionsType;
  marriageHistory?: RadioOptionsType;
  status: string;
  updatedAt: Date;
  videoUrl?: { value: string };
  catchphrase?: string;
  introduction?: string;
  _id: string;
  office: IOfficeInfo;
  type: ROLES.LAWYER;
  additionalEmail: string[];
  firebaseUserId?: string;
  hasOnlineSupport: string | undefined;
}

export interface CustomerData {
  _id: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
  status: string;
  isVerifiedEmail: boolean;
  age?: {
    _id: string;
    value: string;
    min: number;
    max: number;
  };
  currentAddress?: {
    address1: {
      _id: string;
      value: string;
    };
  };
  fullName?: string;
  firstName?: string;
  lastName?: string;
  gender?: GenderType;
  katakanaName?: string;
  firstKatakanaName?: string;
  lastKatakanaName?: string;
  job?: string;
  annualIncome?: string;
  isCompletedProfile?: boolean;
  extraStatus: string;
  phone?: string;
}

export interface CounselorData {
  _id: string;
  fullName: string;
  katakanaName: string;
  birthday?: string;
  email: string;
  barAssociation?: string;
  introduction?: string;
  office: {
    _id: string;
    fullName: string;
    katakanaName: string;
    businessHours: string;
    stationToTheOffice: string;
  };
  images: {
    key: string;
    originUrl: string;
    thumbSmall?: string;
    thumbMedium?: string;
    thumbLarge?: string;
  }[];
  videoUrl?: {
    value: string;
  };
  consultationField: {
    _id: string;
    value: string;
  }[];
  attribute: {
    _id: string;
    value: string;
  }[];
  nickname?: string;
  career?: string;
  numberSolutionCases?: number;
  certificate?: string[];
  meetingStyle?: {
    speakingRate: number;
    communicationRate: number;
    adviceRate: number;
  };
  extendIntroduction?: {
    hobbies?: string;
    solutionCases?: string;
    favoriteSubject?: string;
    importantInCounseling?: string;
  };
  gender?: GenderType;
  catchphrase?: string;
  type: ROLES.COUNSELOR;
  weeklySchedule: {
    applyDate?: string;
    sunday?: WorkDayData;
    monday?: WorkDayData;
    tuesday?: WorkDayData;
    wednesday?: WorkDayData;
    thursday?: WorkDayData;
    friday?: WorkDayData;
    saturday?: WorkDayData;
  };
  showTutorial: {
    schedule: boolean;
  };
  bankInfo?: BankInformation;
}

import type { UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import apiQuery from 'hooks/apiQuery';
import { get } from 'lodash';
import api from 'utils/api';
import type { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

export interface IDefaultUser {
  _id: string;
  email: string;
  firebaseUserId?: string;
}
const useFetchUser = <
  T extends {
    _id: string;
    email: string;
    firebaseUserId?: string;
  },
>(
  options: Omit<
    UseQueryOptions<unknown, unknown, T, string[]>,
    'queryFn' | 'queryKey'
  >,
) => {
  const { enabled, ...otherOptions } = options;
  const webCookie = Helper.getWebCookie();
  const role = get(webCookie, 'role') as ROLES;

  return useQuery(
    apiQuery.currentUser(role).queryKey,
    async () => {
      const { data: result } = await api.get(apiQuery.currentUser(role).apiUrl);
      return result;
    },
    {
      enabled: enabled && !!role,
      staleTime: Infinity,
      onError: (error) => {
        if (get(error, 'code') === 'E_UNAUTHORIZED_ACCESS') {
          Helper.removeWebCookie();
        }
      },
      ...otherOptions,
    },
  );
};

export default useFetchUser;

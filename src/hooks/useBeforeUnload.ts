/* eslint-disable no-param-reassign */
/* eslint-disable consistent-return */
import { useCallback, useEffect } from 'react';

export function on<T extends Window | Document | HTMLElement | EventTarget>(
  obj: T | null,
  ...args: Parameters<T['addEventListener']> | [string, Function | null, ...any]
): void {
  if (obj && obj.addEventListener) {
    obj.addEventListener(
      ...(args as Parameters<HTMLElement['addEventListener']>),
    );
  }
}

export function off<T extends Window | Document | HTMLElement | EventTarget>(
  obj: T | null,
  ...args:
    | Parameters<T['removeEventListener']>
    | [string, Function | null, ...any]
): void {
  if (obj && obj.removeEventListener) {
    obj.removeEventListener(
      ...(args as Parameters<HTMLElement['removeEventListener']>),
    );
  }
}

const useBeforeUnload = (enabled: boolean | (() => boolean) = true) => {
  const handler = useCallback(
    (event: BeforeUnloadEvent) => {
      const finalEnabled = typeof enabled === 'function' ? enabled() : true;

      if (!finalEnabled) {
        return;
      }

      event.preventDefault();

      event.returnValue = 'Default';

      return 'Default';
    },
    [enabled],
  );

  useEffect(() => {
    if (!enabled) {
      return;
    }

    on(window, 'beforeunload', handler);

    return () => off(window, 'beforeunload', handler);
  }, [enabled, handler]);
};

export default useBeforeUnload;

import type {
  QueryObserverResult,
  UseQueryOptions,
} from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import api from 'utils/api';

interface Options<T>
  extends Omit<
    UseQueryOptions<unknown, unknown, T, string[]>,
    'queryFn' | 'queryKey'
  > {
  queryKey: string[];
  apiUrl: string;
  customParams?: Record<string, unknown>;
  getPath?: (id?: string) => string;
}

const useFetchItem = <T>(
  options: Options<T>,
): {
  item: T;
  isLoading: boolean;
  isFetching: boolean;
  refetch: () => Promise<QueryObserverResult<T, unknown>>;
} => {
  const { isReady } = useRouter();
  const { queryKey, apiUrl, customParams, getPath, ...otherOptions } = options;

  const {
    data = {},
    isLoading,
    isFetching,
    refetch,
  } = useQuery(
    queryKey,
    async () => {
      const url = getPath ? `${apiUrl}/${getPath()}` : apiUrl;
      const { data: result } = await api.get(url, { params: customParams });
      return result;
    },
    {
      keepPreviousData: true,
      enabled: isReady && !!getPath,
      ...otherOptions,
    },
  );
  return {
    item: data as T,
    isLoading,
    isFetching,
    refetch,
  };
};

export default useFetchItem;

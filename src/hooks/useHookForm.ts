import useBeforeUnload from 'hooks/useBeforeUnload';
import type { FieldValues, UseFormProps, UseFormReturn } from 'react-hook-form';
import { useForm } from 'react-hook-form';

const useHookForm = <
  TFieldValues extends FieldValues = FieldValues,
  TContext = any,
>(
  props?: UseFormProps<TFieldValues, TContext>,
): UseFormReturn<TFieldValues, TContext> => {
  const formReturn = useForm<TFieldValues, TContext>(props);
  useBeforeUnload(formReturn.formState.isDirty);
  return formReturn;
};

export default useHookForm;

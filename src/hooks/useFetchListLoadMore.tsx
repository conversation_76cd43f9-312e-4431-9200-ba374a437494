import { useCallback, useState } from 'react';

import type { PaginationLoadMore } from './types';
import type { UseFetchListOptions } from './useFetchList';
import useFetchList from './useFetchList';

type Props = UseFetchListOptions & {
  nextLimit?: number;
  customParams: { page: number; limit: number; [x: string]: any };
};

export default function useFetchListLoadMore<
  T extends { _id: string | number },
>(props: Props) {
  const [list, setList] = useState<T[]>([]);
  const nextLimit = props.nextLimit || props.customParams.limit;
  const { page } = props.customParams;
  const [params, setParams] = useState(props.customParams);
  const [cursor, setCursor] = useState<PaginationLoadMore | null>(null);
  const res = useFetchList<T>({
    ...props,
    customParams: params,
    onSuccess(data) {
      const respon = data as { data: T[]; pagination: PaginationLoadMore };
      const items = [...respon.data];
      setList((pre) => [...pre, ...items]);
      if (respon.pagination) {
        setCursor(respon.pagination);
      }
    },
  });
  const handlerLoadMore = useCallback(() => {
    if (!cursor?.hasMore) return;
    setParams((pre) => ({
      ...pre,
      page: page + 1,
      limit: nextLimit,
      nextCursor: cursor?.nextCursor,
    }));
  }, [cursor?.nextCursor, cursor?.hasMore, nextLimit, page]);
  return { ...res, list, handlerLoadMore };
}

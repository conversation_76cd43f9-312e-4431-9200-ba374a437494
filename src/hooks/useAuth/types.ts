import type { ImageItemType } from 'hooks/types';
import type { GenderType } from 'utils/constants';

export interface LoginPayload {
  email: string;
  password: string;
}

export interface RegisterPayload {
  email: string;
  password: string;
}

export interface UpdateLawyerProfilePayload {
  firstName: string;
  lastName: string;
  firstKatakanaName: string;
  lastKatakanaName: string;
  gender: string;
  birthday: string;
  images: string[];
  videoUrl?: string;
  barAssociation: string;
  consultationField: string[];
  attribute: string[];
  marriageHistory?: string;
  divorceHistory?: string;
  children?: string;
  introduction?: string;
  catchphrase: string;
}

export interface UpdateLawyerProfileFormValues
  extends Omit<UpdateLawyerProfilePayload, 'images'> {
  images: ImageItemType[];
  email: string;
  officeName: string;
}

export interface SetFirstPasswordPayload {
  token: string;
  password: string;
}

export interface IUpdateCustomerProfile {
  firstName: string;
  lastName: string;
  firstKatakanaName: string;
  lastKatakanaName: string;
  currentAddress1: string;
  gender: GenderType;
  age: string;
  job: string;
  annualIncome?: string;
  phone?: string;
}

export interface UpdateCustomerProfileFormValues {
  _id: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
  status: string;
  isVerifiedEmail: boolean;
  birthday: Date;
  currentAddress: {
    postCode: string;
    address1: {
      _id: string;
      value: string;
    };
    address2: string;
    address3: string;
    address4?: string;
  };
  fullName: string;
  gender: GenderType;
  katakanaName: string;
  permanentAddress: {
    address1: {
      _id: string;
      value: string;
    };
    address2: string;
    address3: string;
  };
  phone?: string;
  job?: string;
  annualIncome?: string;
  monthlyIncome?: string;
}

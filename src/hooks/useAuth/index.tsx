import type { UseMutateAsyncFunction } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { auth } from 'features/chat/firebase';
import useAuthSignOut from 'features/chat/hooks/useAuthSignOut';
import { t } from 'i18n';
import { get, pick } from 'lodash';
import { useRouter } from 'next/router';
import api from 'utils/api';
import type { ROLES } from 'utils/constants';
import { API_PATH } from 'utils/constants';
import Helper from 'utils/helpers';

import type {
  IUpdateCustomerProfile,
  LoginPayload,
  RegisterPayload,
  UpdateLawyerProfileFormValues,
  UpdateLawyerProfilePayload,
} from './types';

const useAuth = (): {
  logOut: UseMutateAsyncFunction<unknown, unknown, void, unknown>;
  updateLawyerProfile: UseMutateAsyncFunction<
    unknown,
    unknown,
    UpdateLawyerProfileFormValues,
    unknown
  >;
  registerCustomer: UseMutateAsyncFunction<
    unknown,
    unknown,
    RegisterPayload,
    unknown
  >;
  updateCustomerProfile: UseMutateAsyncFunction<
    unknown,
    unknown,
    IUpdateCustomerProfile,
    unknown
  >;
  isLoggingOut: boolean;
  isUpdating: boolean;
  isRegistering: boolean;
  isCustomerUpdating: boolean;
} => {
  const { reload } = useRouter();
  const { mutateAsync: signOutFirebase } = useAuthSignOut(auth);

  const handleUpdateLawyerProfile = async (
    payload: UpdateLawyerProfileFormValues,
  ) => {
    const params = pick(payload, [
      'firstName',
      'lastName',
      'firstKatakanaName',
      'lastKatakanaName',
      'gender',
      'birthday',
      'videoUrl',
      'barAssociation',
      'consultationField',
      'attribute',
      'marriageHistory',
      'divorceHistory',
      'children',
      'introduction',
      'catchphrase',
    ]) as UpdateLawyerProfilePayload;
    if (params.birthday) {
      params.birthday = dayjs(params.birthday).format(
        'YYYY-MM-DDTHH:mm:ss.sss[Z]',
      );
    }
    if (payload.images) {
      params.images = payload.images.map((image) => image.key);
    }
    await api.put(`/account/provider/me`, params);
  };

  const handleUpdateCustomerProfile = async (
    payload: IUpdateCustomerProfile,
  ) => {
    const params = {
      ...pick(payload, [
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'age',
        'gender',
        'job',
        'annualIncome',
        'currentAddress1',
        'phone',
      ]),
    };

    await api.put(`/account/consumer/me`, params);
  };

  const handleRegisterCustomer = async (payload: LoginPayload) => {
    const params = pick(payload, ['email', 'password']);
    params.email = params.email.toLowerCase().trim();
    const { data } = await api.post('/account/consumer', params);
    return data;
  };
  const { mutateAsync: logOut, isLoading: isLoggingOut } = useMutation(
    async () => {
      const webCookie = Helper.getWebCookie();
      const role = get(webCookie, 'role');
      api.delete(`/account/${API_PATH[role as ROLES]}/logout`);
    },
    {
      onSettled: () => {
        Helper.removeWebCookie();
        signOutFirebase();
        setTimeout(async () => reload());
        // queryClient
        // .getQueryCache()
        // .findAll(['currentUser'])
        // .forEach((query) => query.setData(undefined));
      },
      mutationKey: ['logOut'],
    },
  );

  const { mutateAsync: updateLawyerProfile, isLoading: isUpdating } =
    useMutation(handleUpdateLawyerProfile, {
      onSuccess: () => {
        Helper.toast(t('validation.completed'));
      },
      mutationKey: ['updateLawyer'],
    });

  const { mutateAsync: registerCustomer, isLoading: isRegistering } =
    useMutation(handleRegisterCustomer, {
      mutationKey: ['register'],
    });

  const { mutateAsync: updateCustomerProfile, isLoading: isCustomerUpdating } =
    useMutation(handleUpdateCustomerProfile, {
      mutationKey: ['updateLawyer'],
    });
  return {
    logOut,
    isLoggingOut,
    updateLawyerProfile,
    isUpdating,
    registerCustomer,
    isRegistering,
    updateCustomerProfile,
    isCustomerUpdating,
  };
};

export default useAuth;

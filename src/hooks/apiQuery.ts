import type { ROLES } from 'utils/constants';
import { API_PATH } from 'utils/constants';

const apiQuery = {
  prefectures: {
    queryKey: ['public', 'prefectures'],
    apiUrl: '/sharing/common/prefectures',
    staleTime: Infinity,
    customParams: {
      page: 1,
      limit: 1000,
      sort: 'order.asc',
    },
  },
  consultations: {
    queryKey: ['public', 'consultations'],
    apiUrl: 'sharing/common/consultations',
    staleTime: Infinity,
    customParams: {
      page: 1,
      limit: 1000,
      sort: 'order.asc',
    },
  },
  attributes: {
    queryKey: ['public', 'attributes'],
    apiUrl: 'sharing/common/attributes',
    staleTime: Infinity,
    customParams: {
      page: 1,
      limit: 1000,
    },
  },
  divorceCauses: {
    queryKey: ['public', 'divorceCauses'],
    apiUrl: 'sharing/common/causes-of-divorce',
    staleTime: Infinity,
    customParams: {
      page: 1,
      limit: 1000,
      sort: 'order.asc',
    },
  },
  ages: {
    queryKey: ['public', 'ages'],
    apiUrl: '/sharing/common/ages',
    staleTime: Infinity,
    customParams: {
      page: 1,
      limit: 1000,
    },
  },
  calendars: {
    queryKey: ['currentUser', 'calendars', 'date'],
    apiUrl: '/sharing/common/calendars/date',
  },
  currentUser: (role: ROLES, guest_token?: string) => ({
    queryKey: ['currentUser', role],
    apiUrl: `/account/${API_PATH[role]}/me${
      guest_token ? `?guest_token=${guest_token}` : ''
    }`,
  }),
  currentGuestUser: (token: string) => ({
    queryKey: ['currentUser', 'guest'],
    apiUrl: `/account/guest/me?guest_token=${token}`,
    enabled: !!token,
  }),
  lawyerCustomerProfile: (customerId: string) => ({
    queryKey: ['currentUser', 'customer', customerId],
    apiUrl: `/account/provider/consumers/${customerId}`,
  }),
  lawyerCustomerConsultation: (customerId: string) => ({
    queryKey: ['currentUser', 'customer', customerId, 'consultation'],
    apiUrl: `/account/provider/consumers/${customerId}/consultation-record`,
  }),
  lawyerBookingDetail: (bookingId: string) => ({
    queryKey: ['currentUser', 'bookings', bookingId],
    apiUrl: `request/provider/bookings/${bookingId}`,
  }),
  lawyerList: {
    queryKey: ['public', 'lawyers'],
    apiUrl: '/account/consumer/providers',
  },
  lawyerSolutions: (lawyerId: string) => ({
    queryKey: ['public', 'lawyers', lawyerId, 'solutions'],
    apiUrl: `/account/consumer/providers/${lawyerId}/solutions`,
  }),
  lawyerSolutionDetail: (solutionId: string) => ({
    queryKey: ['public', 'lawyers', 'solutions', solutionId],
    apiUrl: `/account/consumer/solutions/${solutionId}`,
  }),
  lawyerDetail: (lawyerId: string) => ({
    queryKey: ['public', 'lawyers', lawyerId],
    apiUrl: `/account/consumer/providers/${lawyerId}`,
  }),

  lawyerArticleList: (lawyerId: string) => ({
    queryKey: ['public', 'articles', 'providerId', lawyerId],
    apiUrl: `/account/consumer/providers/${lawyerId}/interviews`,
  }),
  consultationDetail: {
    queryKey: ['currentUser', 'consultation-record'],
    apiUrl: '/request/consumer/consultation-record',
  },
  consultationDetailByGuest: (token: string) => ({
    queryKey: ['currentUser', 'consultation-record', 'guest'],
    apiUrl: `/request/guest/consultation-record?guest_token=${token}`,
  }),
  lawyerConsultations: (lawyerId: string) => ({
    queryKey: ['public', 'lawyers', lawyerId, 'consultations'],
    apiUrl: `/account/consumer/providers/${lawyerId}/consultation-menus`,
  }),
  recommendLawyerList: {
    queryKey: ['currentUser', 'recommend-lawyers'],
    apiUrl: '/account/consumer/recommend-lawyers',
  },
  customerConsultation: (customerId: string) => ({
    queryKey: ['currentUser', customerId, 'consultation-record'],
    apiUrl: '/account/provider/consumers/:id/consultation-record',
  }),
  solutionList: {
    queryKey: ['currentUser', 'solution-list'],
    apiUrl: '/account/provider/solutions',
    customParams: {
      limit: 5,
    },
  },
  solutionDetail: (solutionId: string) => ({
    queryKey: ['currentUser', 'solution-list', solutionId],
    apiUrl: `/account/consumer/solutions/${solutionId}`,
  }),
  consultationMenu: {
    queryKey: ['currentUser', 'consultation-menu'],
    apiUrl: '/account/provider/consultation-menus',
    customParams: {
      limit: 10,
    },
  },
  consultationMenuDetail: (menuId: string) => ({
    queryKey: ['currentUser', 'consultation-menu', menuId],
    apiUrl: `/account/provider/consultation-menus/${menuId}`,
  }),
  contactUs: {
    apiUrl: '/request/consumer/contactUs',
  },
};

export default apiQuery;

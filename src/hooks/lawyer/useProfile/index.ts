import type { UseMutateAsyncFunction } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import dayjs from 'dayjs';
import i18n from 'i18n';
import api from 'utils/api';
import Helper from 'utils/helpers';

import type { DivorcePayload, JobPayload, ProfilePayload } from './types';

const useProfile = (): {
  updateProfile: UseMutateAsyncFunction<
    unknown,
    unknown,
    ProfilePayload,
    unknown
  >;
  updateJob: UseMutateAsyncFunction<unknown, unknown, JobPayload, unknown>;
  updateDivorce: UseMutateAsyncFunction<
    unknown,
    unknown,
    DivorcePayload,
    unknown
  >;
  isUpdatingProfile: boolean;
  isUpdatingJob: boolean;
  isUpdatingDivorce: boolean;
} => {
  const { t } = i18n;
  const { mutateAsync: updateProfile, isLoading: isUpdatingProfile } =
    useMutation(
      (payload: ProfilePayload) => {
        const params = { ...payload };
        params.birthday = dayjs(params.birthday).format(
          'YYYY-MM-DDTHH:mm:ss.sss[Z]',
        );
        return api.put('/account/provider/me', params);
      },
      {
        mutationKey: ['updateProfile'],
        onSuccess: () => Helper.toast(t('validation.completed')),
      },
    );

  const { mutateAsync: updateJob, isLoading: isUpdatingJob } = useMutation(
    (payload: JobPayload) =>
      api.put('/account/provider/me/job-information', payload),
    {
      mutationKey: ['updateJob'],
      onSuccess: () => Helper.toast(t('validation.completed')),
    },
  );

  const { mutateAsync: updateDivorce, isLoading: isUpdatingDivorce } =
    useMutation(
      (payload: DivorcePayload) =>
        api.put('/account/provider/me/family-information', payload),
      {
        mutationKey: ['updateDivorce'],
        onSuccess: () => Helper.toast(t('validation.completed')),
      },
    );

  return {
    updateProfile,
    updateJob,
    updateDivorce,
    isUpdatingDivorce,
    isUpdatingProfile,
    isUpdatingJob,
  };
};

export default useProfile;

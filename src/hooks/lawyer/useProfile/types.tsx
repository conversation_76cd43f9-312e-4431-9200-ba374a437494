import type { GenderType } from 'utils/constants';

export interface PurposePayload {
  purpose: string[];
}

export interface ProfilePayload {
  firstName: string;
  lastName: string;
  firstKatakanaName: string;
  lastKatakanaName: string;
  gender: GenderType;
  birthday: string;
  images: string[];
  videoUrl?: string;
  catchphrase: string;
  introduction?: string;
}

export interface JobPayload {
  barAssociation: string;
  consultationField: string[];
  attribute: string[];
}

export interface DivorcePayload {
  marriageHistory?: string;
  divorceHistory?: string;
  children?: string;
}

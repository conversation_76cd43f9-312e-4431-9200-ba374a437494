import type { UseMutateAsyncFunction } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import api from 'utils/api';

import type {
  DivorceBackgroundPayload,
  IUpdateCustomer,
  KidsPayload,
  MarriageInformationPayload,
  PartnerInformationPayload,
  PropertyInformationPayload,
} from './types';

const useConsulationForm = (): {
  updateCustomer: UseMutateAsyncFunction<
    unknown,
    unknown,
    IUpdateCustomer & { customerId: string },
    unknown
  >;
  updateDivorceBackground: UseMutateAsyncFunction<
    unknown,
    unknown,
    DivorceBackgroundPayload & { customerId: string },
    unknown
  >;
  updateParterInformation: UseMutateAsyncFunction<
    unknown,
    unknown,
    PartnerInformationPayload & { customerId: string },
    unknown
  >;
  updateMarriageInformation: UseMutateAsyncFunction<
    unknown,
    unknown,
    MarriageInformationPayload & { customerId: string },
    unknown
  >;
  updatePropertyInformation: UseMutateAsyncFunction<
    unknown,
    unknown,
    PropertyInformationPayload & { customerId: string },
    unknown
  >;
  updateKidsInformation: UseMutateAsyncFunction<
    unknown,
    unknown,
    KidsPayload & { customerId: string },
    unknown
  >;
  isUpdatingCustomer: boolean;
  isUpdatingDivorceBackground: boolean;
  isUpdatingPartner: boolean;
  isUpdatingMarriage: boolean;
  isUpdatingProperty: boolean;
  isUpdatingKidsInformation: boolean;
} => {
  const { mutateAsync: updateCustomer, isLoading: isUpdatingCustomer } =
    useMutation(
      (payload: IUpdateCustomer & { customerId: string }) =>
        api.put(
          `/account/provider/consumers/${payload.customerId}`,
          pick(payload, ['annualIncome', 'job']),
        ),
      {
        mutationKey: ['updateDivorceBackground'],
      },
    );
  const {
    mutateAsync: updateDivorceBackground,
    isLoading: isUpdatingDivorceBackground,
  } = useMutation(
    (payload: DivorceBackgroundPayload & { customerId: string }) =>
      api.put(
        `request/provider/consumers/${payload.customerId}/consultation-record/background-of-divorce`,
        payload,
      ),
    {
      mutationKey: ['updateDivorceBackground'],
    },
  );

  const { mutateAsync: updateParterInformation, isLoading: isUpdatingPartner } =
    useMutation(
      (payload: PartnerInformationPayload & { customerId: string }) =>
        api.put(
          `request/provider/consumers/${payload.customerId}/consultation-record/partner`,
          payload,
        ),
      {
        mutationKey: ['updatePartnerInformation'],
      },
    );

  const {
    mutateAsync: updateMarriageInformation,
    isLoading: isUpdatingMarriage,
  } = useMutation(
    async (payload: MarriageInformationPayload & { customerId: string }) => {
      const params = pick(payload, [
        'marriedDate',
        'isSeparated',
        'separationDate',
      ]);
      params.marriedDate = dayjs(params.marriedDate).toISOString();
      if (params.separationDate) {
        params.separationDate = dayjs(params.separationDate).toISOString();
      }
      await api.put(
        `request/provider/consumers/${payload.customerId}/consultation-record/marriage-information`,
        params,
      );
    },
    {
      mutationKey: ['updateMarriageInformation'],
    },
  );

  const {
    mutateAsync: updatePropertyInformation,
    isLoading: isUpdatingProperty,
  } = useMutation(
    (payload: PropertyInformationPayload & { customerId: string }) =>
      api.put(
        `request/provider/consumers/${payload.customerId}/consultation-record/property-information`,
        payload,
      ),
    {
      mutationKey: ['updatePropertyInformation'],
    },
  );

  const {
    mutateAsync: updateKidsInformation,
    isLoading: isUpdatingKidsInformation,
  } = useMutation(
    async (payload: KidsPayload & { customerId: string }) => {
      const params = pick(payload, ['kids']);

      await api.put(
        `request/provider/consumers/${payload.customerId}/consultation-record/kids`,
        params,
      );
    },
    {
      mutationKey: ['updateKidsInformation'],
    },
  );

  return {
    updateCustomer,
    isUpdatingCustomer,
    updateDivorceBackground,
    isUpdatingDivorceBackground,
    updateParterInformation,
    isUpdatingPartner,
    updateMarriageInformation,
    isUpdatingMarriage,
    updatePropertyInformation,
    isUpdatingProperty,
    updateKidsInformation,
    isUpdatingKidsInformation,
  };
};

export default useConsulationForm;

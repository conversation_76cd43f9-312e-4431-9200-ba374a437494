import type { UseMutateAsyncFunction } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import dayjs from 'dayjs';
import type { IBasicConsultationPayload } from 'hooks/customer/useConsultationForm/types';
import api from 'utils/api';
import Helper from 'utils/helpers';

import type { ICheckGuestEmail, IResendOTP, IVerifyOTP } from './types';

const useGuestForm = (): {
  checkGuestEmail: UseMutateAsyncFunction<
    unknown,
    unknown,
    ICheckGuestEmail,
    unknown
  >;
  resendOTP: UseMutateAsyncFunction<unknown, unknown, IResendOTP, unknown>;
  verifyOTP: UseMutateAsyncFunction<unknown, unknown, IVerifyOTP, unknown>;
  updateBasicGuestConsultation: UseMutateAsyncFunction<
    unknown,
    unknown,
    IBasicConsultationPayload,
    unknown
  >;
  isLoadingCheckGuestEmail: boolean;
  isLoadingResendOTP: boolean;
  isLoadingVerifyOTP: boolean;
  isUpdatingBasicGuestConsultation: boolean;
} => {
  const { mutateAsync: checkGuestEmail, isLoading: isLoadingCheckGuestEmail } =
    useMutation(
      (payload: ICheckGuestEmail) => {
        return api.post('guest/email', payload);
      },
      {
        mutationKey: ['checkGuestEmail'],
      },
    );

  const { mutateAsync: resendOTP, isLoading: isLoadingResendOTP } = useMutation(
    (payload: IResendOTP) => {
      return api.post('guest/otp/resend', payload);
    },
    {
      mutationKey: ['resendOTP'],
    },
  );

  const { mutateAsync: verifyOTP, isLoading: isLoadingVerifyOTP } = useMutation(
    (payload: IVerifyOTP) => {
      return api.post('guest/otp/verify', payload);
    },
    {
      mutationKey: ['verifyOTP'],
    },
  );

  const {
    mutateAsync: updateBasicGuestConsultation,
    isLoading: isUpdatingBasicGuestConsultation,
  } = useMutation(
    (payload: IBasicConsultationPayload) => {
      const params = { ...payload };
      params.marriageInformation.marriedDate = dayjs(
        params.marriageInformation.marriedDate,
      ).toISOString();
      if (params.marriageInformation.separationDate) {
        params.marriageInformation.separationDate = dayjs(
          params.marriageInformation.separationDate,
        ).toISOString();
      }
      const webCookie = Helper.getWebCookieByGuest();

      if (
        !!webCookie &&
        (!webCookie?.otpId ||
          webCookie.otpId === 'undefined' ||
          webCookie.otpId === '')
      ) {
        Helper.toast('メールアドレスを認証してください。', { type: 'error' });
        return Promise.reject(new Error('Email verification required'));
      }

      return api.put(`/guest/profile?guest_token=${webCookie.otpId}`, payload);
    },
    {
      mutationKey: ['updateBasicGuestConsultation'],
    },
  );

  return {
    checkGuestEmail,
    resendOTP,
    verifyOTP,
    updateBasicGuestConsultation,
    isLoadingCheckGuestEmail,
    isLoadingResendOTP,
    isLoadingVerifyOTP,
    isUpdatingBasicGuestConsultation,
  };
};

export default useGuestForm;

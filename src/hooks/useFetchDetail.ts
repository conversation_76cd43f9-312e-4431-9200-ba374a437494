import type {
  QueryObserverResult,
  UseQueryOptions,
} from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import api from 'utils/api';

interface Options<T>
  extends Omit<
    UseQueryOptions<unknown, unknown, T, string[]>,
    'queryFn' | 'queryKey'
  > {
  queryKey: string[];
  omitKeys?: string[];
  apiUrl: string;
  customParams?: Record<string, unknown>;
}

const useFetchDetail = <T>(
  options: Options<T>,
): {
  detail: T;
  isLoading: boolean;
  isFetching: boolean;
  refetch: () => Promise<QueryObserverResult<T, unknown>>;
} => {
  const { isReady } = useRouter();
  const { queryKey, apiUrl, customParams, ...otherOptions } = options;

  const {
    data = {},
    isLoading,
    isFetching,
    refetch,
  } = useQuery(
    queryKey,
    async () => {
      const { data: result } = await api.get(apiUrl, { params: customParams });
      return result;
    },
    {
      keepPreviousData: true,
      enabled: isReady,
      ...otherOptions,
    },
  );
  return {
    detail: data as T,
    isLoading,
    isFetching,
    refetch,
  };
};

export default useFetchDetail;

import type { ISlotItem } from 'components/TimeTable/utils';
import type { ICounselorItem } from 'models/provider/interface';
import type {
  GenderType,
  ProviderType,
  RadioOptionsType,
} from 'utils/constants';

export type DeepKeys<T> = object extends T
  ? string
  : T extends readonly unknown[]
  ? DeepKeys<T[number]>
  : T extends object
  ? (keyof T & string) | DeepSubKeys<T, keyof T & string>
  : never;

type DeepSubKeys<T, K extends string> = K extends keyof T
  ? `${K}.${DeepKeys<T[K]>}`
  : never;

export interface IListItem {
  _id: string;
  value: string;
}

export interface IListResult<T> {
  docs: (T & { _id: string })[];
  total: number;
  limit: number;
  page: number;
  pages: number;
}

export interface PaginationLoadMore {
  nextCursor: string;
  hasMore: boolean;
}
export interface ILawyerProfile {
  attribute?: IListItem[];
  barAssociation?: string;
  birthday?: string;
  consultationField?: IListItem[];
  createdAt: Date;
  email: string;
  fullName: string;
  gender?: GenderType;
  images?: { key: string; originUrl: string }[];
  isVerifiedEmail: boolean;
  katakanaName: string;
  divorceHistory?: RadioOptionsType;
  children?: RadioOptionsType;
  marriageHistory?: RadioOptionsType;
  status: string;
  updatedAt: Date;
  videoUrl?: { value: string };
  introduction?: string;
  catchphrase?: string;
  _id: string;
  office: {
    _id: string;
    fullName: string;
    katakanaName: string;
    businessHours?: string;
    stationToTheOffice?: string;
    timeToTheOffice?: number;
    address?: {
      postCode: string;
      address1: {
        _id: string;
        value: string;
      };
      address2: string;
      address3: string;
      address4?: string;
    };
  };
  isPublic: boolean;
  type: ProviderType.LAWYER;
  freeConsultationMenu: boolean;
  hasOnlineSupport?: boolean | undefined;
}

export interface TReview {
  _id: string;
  point: number;
  comments?: string;
  provider: {
    _id: string;
  };
  consumer: {
    _id: string;
    age?: {
      _id: string;
      value: string;
      min: number | null;
      max: number | null;
    };
    gender: GenderType;
  };
}

export interface OfficeDetail {
  _id: string;
  office: {
    _id: string;
    fullName?: string;
    katakanaName?: string;
    businessHours?: string;
    stationToTheOffice?: string;
    timeToTheOffice?: number;
    email: string;
    website?: string;
    address?: {
      postCode: string;
      address1: {
        _id: string;
        value: string;
      };
      address2: string;
      address3: string;
      address4?: string;
    };
  };
}

export interface IProviderItem {
  _id: string;
  email: string;
  fullName: string;
  katakanaName: string;
  createdAt: string;
  introduction?: string;
  barAssociation?: string;
  catchphrase?: string;
  videoUrl?: { value: string };
  consultationField?: IListItem[];
  avgRating: number;
  totalReview: number;
  images?: {
    key: string;
    originUrl: string;
    thumbSharing?: string;
    thumbMedium?: string;
  }[];
  isPublic: boolean;
  freeConsultationMenu?: boolean;
}

export interface IPickupArticleDetail {
  _id: string;
  attribute: IListItem[];
  consultation: IListItem[];
  title: string;
  status: string;
  subTitle: string;
  image: {
    key?: string;
    originUrl?: string;
    thumbSharing?: string;
    thumbMedium?: string;
  };
  updatedAt: string;
  createdAt: string;
  leadParagraph: string;
  contents?: {
    subImage: {
      key?: string;
      originUrl?: string;
    };
    subTitle: string;
    content: string;
  }[];
  providers: ILawyerProfile[];
}

export interface IArticleDetail {
  _id: string;
  attribute: IListItem[];
  consultation: IListItem[];
  title: string;
  status: string;
  subTitle: string;
  image: {
    key?: string;
    originUrl?: string;
    thumbSharing?: string;
    thumbMedium?: string;
  };
  updatedAt: string;
  createdAt: string;
  leadParagraph: string;
  contents: [
    {
      subImage: {
        key?: string;
        originUrl?: string;
      };
      subTitle: string;
      content: string;
    },
  ];
  provider: ILawyerProfile;
}

export interface IPickupArticleListItem {
  _id: string;
  title: string;
  image: {
    key?: string;
    originUrl?: string;
    thumbSharing?: string;
  };
  updatedAt: string;
  leadParagraph: string;
}

export interface IArticleListItem {
  _id: string;
  provider: {
    _id: string;
    images: { originUrl: string; key: string }[];
    fullName: string;
    katakanaName: string;
    type: ProviderType;
  };
  title: string;
  image: {
    key?: string;
    originUrl?: string;
  };
  updatedAt: string;
  leadParagraph: string;
}

export interface IPrefecture {
  _id: string;
  value: string;
  nameEn: string;
  nameJp: string;
}

export interface ImageItemType {
  key: string;
  originUrl: string;
}

export interface IAge {
  _id: string;
  order: number;
  value: string;
  min: number;
  max: number;
  createdAt: string;
  updatedAt: string;
}

export interface ISolution {
  _id: string;
  title: string;
  consultationField: IListItem[];
  background: string;
  customer: {
    age: string;
    gender: GenderType;
  };
  partner: {
    age: string;
    gender: GenderType;
  };
  children: RadioOptionsType;
  content: string;
  benefit: string;
  point: string;
}

export interface IRecommendLawyerListItem {
  _id: string;
  fullName: string;
  katakanaName: string;
  images: { originUrl: string; key: string }[];
  createdAt: string;
}

export interface ICause {
  createdAt: string;
  hasExtra: false;
  order: number;
  updatedAt: string;
  value: string;
  _id: string;
}

export interface IExternalArticleListItem {
  _id: string;
  status: string;
  title: string;
  image: {
    key?: string;
    originUrl?: string;
  };
  updatedAt: string;
  createdAt: string;
  link: string;
}

export interface ContactUsPayload {
  name: string;
  email: string;
  content: string;
  category: {
    value: string;
    text: string;
  };
}

export interface ICanlendarSlot {
  chunkSize: number;
  dateValue: string;
  providers: ICounselorItem[];
  slots: ISlotItem[];
}

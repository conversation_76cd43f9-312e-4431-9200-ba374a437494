/* eslint-disable consistent-return */
import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Typography } from '@mui/material';
import ConfirmModal from 'components/ConfirmModal/modal';
import TextField from 'components/Form/TextField';
import { setCookie } from 'cookies-next';
import useGuestForm from 'hooks/guest';
import type { IGuestResponse } from 'hooks/guest/types';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { COUNTDOWN_RESEND_OTP } from 'utils/constants';

import type { TSchemaOfOTP } from './schema';
import schema from './schema';

type TOTPForm = {
  open: boolean;
};
const OTPForm = ({ open, ...props }: TOTPForm) => {
  const [seconds, setSeconds] = useState(COUNTDOWN_RESEND_OTP);
  const [isCountingDown, setIsCountingDown] = useState(true);

  // Reset countdown when modal first opens
  useEffect(() => {
    if (open) {
      setSeconds(COUNTDOWN_RESEND_OTP);
      setIsCountingDown(true);
    }
  }, [open]);

  // Handle countdown timer
  useEffect(() => {
    if (!isCountingDown || seconds <= 0) {
      if (seconds <= 0) setIsCountingDown(false);
      return;
    }

    const timer = setInterval(() => {
      setSeconds((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [seconds, isCountingDown]);

  const { t } = i18n;
  const {
    contentChildren,
    onCloseModal,
    email,
  }: Partial<
    Record<'contentChildren', JSX.Element | React.ReactNode | string> & {
      onCloseModal: () => void;
      email: string;
    }
  > = props;
  const router = useRouter();
  const { query, replace } = router;
  const { control, handleSubmit, reset } = useHookForm<TSchemaOfOTP>({
    resolver: yupResolver(schema()),
    mode: 'onTouched',
  });
  const { resendOTP, isLoadingResendOTP, verifyOTP } = useGuestForm();

  const handleResetCountdown = () => {
    setSeconds(COUNTDOWN_RESEND_OTP);
    setIsCountingDown(true);
  };

  const handleResendOTP = () => {
    if (isCountingDown || isLoadingResendOTP) return;

    handleResetCountdown();
    if (typeof query?.otpId === 'string' && query?.otpId) {
      resendOTP(
        {
          otpId: query.otpId,
        },
        {
          onSuccess: (res) => {
            const {
              data,
            }:
              | Partial<{
                  data: IGuestResponse;
                }>
              | undefined = res || {};

            if (!data?.isGuest) {
              replace({
                pathname: '/guest/consultation-form',
                query: {
                  ...query,
                  ...(data?.otpId && {
                    otpId: data?.otpId,
                  }),
                },
              });
            }
          },
        },
      );
    }
  };

  const handleSubmitOTP = (values: TSchemaOfOTP) => {
    verifyOTP(
      {
        ...values,
        otpId: query?.otpId,
      },
      {
        onSuccess: (res) => {
          const {
            data,
          }:
            | Partial<
                Record<
                  'data',
                  Record<
                    'token',
                    {
                      token: string;
                      type: string;
                    }
                  >
                >
              >
            | undefined = res || {};
          setCookie(
            'lawyer-web-cookie-by-guest',
            {
              otpId: query?.otpId as string,
              guest_token: data?.token?.token as string,
              isDraftUser: 'true',
              email,
            },
            {
              path: '/',
              maxAge: process.env.SESSION_TIME
                ? Number(process.env.SESSION_TIME)
                : 7776000,
            },
          );
          replace({
            pathname: '/guest/consultation-form',
            query: {
              ...query,
              isVerifyEmail: true,
              email,
            },
          });
          if (onCloseModal) {
            onCloseModal();
          }

          reset();
        },
      },
    );
  };

  return (
    <ConfirmModal
      open={open}
      title={'メールアドレス認証'}
      content={
        <>
          {contentChildren}
          <form>
            <TextField
              label="認証コード入力"
              labelCol={0}
              control={control}
              name="otpValue"
            />
          </form>
          <Typography
            mt={'14px'}
            component={Button}
            disableRipple
            disableFocusRipple
            disableTouchRipple
            sx={{
              cursor: isCountingDown ? 'default' : 'pointer',
              background: 'none !important',
              backgroundColor: 'transparent !important',
              color: isCountingDown ? '#999' : '#FFA700',
              '&:hover': {
                background: 'none !important',
                backgroundColor: 'transparent !important',
              },
              padding: 0,
              fontWeight: 400,
              fontSize: '14px',
              textAlign: 'left',
              boxShadow: 'none',
              minWidth: 'auto',
              textTransform: 'none',
            }}
            onClick={handleResendOTP}
            disabled={isCountingDown || isLoadingResendOTP}
          >
            コードを再送信
            {isCountingDown && (
              <Typography
                component="span"
                sx={{
                  color: 'error.main',
                  marginLeft: '4px',
                  fontSize: '14px',
                  textTransform: 'lowercase',
                }}
              >
                ({seconds}s)
              </Typography>
            )}
          </Typography>
        </>
      }
      confirmText={t('global.next')}
      cancelText={t('global.cancel')}
      onCancel={() => {
        if (onCloseModal) onCloseModal();
      }}
      onConfirm={() => {
        handleSubmit((values) => handleSubmitOTP(values))();
      }}
    />
  );
};
export default OTPForm;

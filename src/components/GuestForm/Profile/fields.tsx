import { Loading<PERSON>utton } from '@mui/lab';
import { Box, Grid, Stack, Typography } from '@mui/material';
import { Radio, Select, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import { useFetchList } from 'hooks';
import useGuestForm from 'hooks/guest';
import type { IGuestResponse } from 'hooks/guest/types';
import type { IUpdateCustomerProfile } from 'hooks/useAuth/types';
import useGlobalState from 'hooks/useGlobalState';
import { t } from 'i18n';
import { MailIcon, VerifyCheckedIcon } from 'icons';
import { isEmpty } from 'lodash';
import type { IAge, IPrefecture } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import { useRouter } from 'next/router';
import { memo, useCallback, useState } from 'react';
import type { FieldErrorsImpl } from 'react-hook-form';
import { type Control, useWatch } from 'react-hook-form';
import { ANNUAL_INCOME, Gender, Regex } from 'utils/constants';
import Helper from 'utils/helpers';

import OTPForm from '../OTPForm';

type TOnSubmit = IUpdateCustomerProfile & {
  email: string | undefined;
};
export interface IFields {
  isLawyer?: boolean;
  control: Control<TOnSubmit>;
  firstBooking?: boolean;
  currentUser?: {
    email: string;
    fullName?: string;
    katakanaName?: string;
    phone?: string;
    isVerifyEmail?: boolean;
  };
}
const Fields = ({
  currentUser,
  control,
  firstBooking,
  isLawyer,
  ...props
}: IFields) => {
  const { setLoginModal } = useGlobalState();
  const { list: prefectureList } = useFetchList<IPrefecture>(
    resourceQuery.prefectures,
  );
  const { list: ageList } = useFetchList<IAge>(resourceQuery.ages);
  const watchEmail = useWatch({ name: 'email', control });
  const {
    errors,
  }: Partial<{
    errors: Record<string, FieldErrorsImpl<TOnSubmit>>;
  }> = props;
  const { query, replace } = useRouter();
  const isCheckedEmailDisabled =
    isEmpty(watchEmail) ||
    typeof errors?.email !== 'undefined' ||
    !Regex.EMAIL.test(watchEmail as string);
  const { checkGuestEmail, isLoadingCheckGuestEmail } = useGuestForm();
  const [openOTPModal, setOpenOTPModal] = useState(false);
  const handleCloseOTPModal = () => setOpenOTPModal(false);

  const renderOTPForm = useCallback(() => {
    return (
      <>
        {openOTPModal && (
          <OTPForm
            open={openOTPModal}
            {...{
              contentChildren: (
                <Typography fontSize={14}>
                  {`６桁の認証コードをご登録のメールアドレス(`}
                  <Typography component={'span'} fontWeight={700}>
                    {watchEmail}
                  </Typography>
                  {`)に送信しました。メールをご確認いただき認証コードを入力してください`}
                </Typography>
              ),
              onCloseModal: handleCloseOTPModal,
              email: watchEmail,
            }}
          />
        )}
      </>
    );
  }, [watchEmail, openOTPModal]);

  return (
    <Stack rowGap={{ xs: '20px', tablet: 2 }}>
      <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
        <Label label={t('updateCustomerProfile.email')} labelCol={4} required />
        <Grid
          item
          xs={12}
          tablet={8}
          mt={isLawyer ? '12px' : 0}
          fontSize={{ xs: 14, tablet: 16 }}
          gap={16}
        >
          <Stack spacing={{ xs: 1, tablet: 2 }} direction="row">
            {(query?.isVerifyEmail || currentUser?.isVerifyEmail) &&
            currentUser?.email ? (
              <Box
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  paddingTop: 8,
                }}
              >
                <Typography>{watchEmail || currentUser?.email}</Typography>
                <Box
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginLeft: 16,
                  }}
                >
                  <VerifyCheckedIcon />
                  <Typography color="#51B873" ml={'4px'}>
                    認証済み
                  </Typography>
                </Box>
              </Box>
            ) : (
              <>
                <Box width={1}>
                  <TextField
                    labelCol={0}
                    control={control}
                    name="email"
                    placeholder={t('updateCustomerProfile.email')}
                  />
                </Box>
                <Box
                  width={'170px'}
                  style={{
                    ...((isCheckedEmailDisabled ||
                      isLoadingCheckGuestEmail) && {
                      pointerEvents: 'none',
                    }),
                  }}
                >
                  <LoadingButton
                    size="large"
                    variant="contained"
                    startIcon={<MailIcon color="#FFA700" />}
                    sx={{
                      bgcolor: '#FDF3D8',
                      height: 48,
                      '&:hover': {
                        bgcolor: '#FDF3D8',
                      },
                    }}
                    onClick={() => {
                      checkGuestEmail(
                        {
                          email: watchEmail as string,
                        },
                        {
                          onSuccess: (res) => {
                            const {
                              data,
                            }:
                              | Partial<{
                                  data: IGuestResponse;
                                }>
                              | undefined = res || {};

                            if (!data?.isGuest) {
                              setLoginModal({ email: watchEmail as string });
                            } else {
                              replace({
                                pathname: '/guest/consultation-form',
                                query: {
                                  ...query,
                                  ...(data?.otpId && {
                                    otpId: data?.otpId,
                                  }),
                                },
                              });
                              setOpenOTPModal(true);
                            }
                          },
                        },
                      );
                    }}
                  >
                    <Typography fontSize={14} fontWeight={500} color="#FFA700">
                      認証する
                    </Typography>
                  </LoadingButton>
                </Box>
              </>
            )}
          </Stack>
        </Grid>
      </Grid>
      <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
        <Label
          label={t('updateCustomerProfile.fullName')}
          required
          labelCol={4}
        />
        <Grid
          item
          xs={12}
          tablet={8}
          mt={isLawyer ? '12px' : 0}
          fontSize={{ xs: 14, tablet: 16 }}
        >
          <Stack spacing={{ xs: 1, tablet: 2 }} direction="row">
            <Box width={1}>
              <TextField
                labelCol={0}
                control={control}
                name="lastName"
                maxLength={10}
                placeholder={t('updateCustomerProfile.lastName')}
              />
            </Box>
            <Box width={1}>
              <TextField
                labelCol={0}
                control={control}
                name="firstName"
                maxLength={10}
                placeholder={t('updateCustomerProfile.firstName')}
              />
            </Box>
          </Stack>
        </Grid>
      </Grid>
      <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
        <Label
          label={t('updateCustomerProfile.katakanaName')}
          required
          labelCol={4}
        />
        <Grid
          item
          xs={12}
          tablet={8}
          mt={isLawyer ? '12px' : 0}
          fontSize={{ xs: 14, tablet: 16 }}
        >
          <Stack spacing={{ xs: 1, tablet: 2 }} direction="row">
            <Box width={1}>
              <TextField
                labelCol={0}
                control={control}
                name="lastKatakanaName"
                maxLength={10}
                placeholder={t('updateCustomerProfile.lastKanaName')}
              />
            </Box>
            <Box width={1}>
              <TextField
                labelCol={0}
                control={control}
                name="firstKatakanaName"
                maxLength={10}
                placeholder={t('updateCustomerProfile.firstKanaName')}
              />
            </Box>
          </Stack>
        </Grid>
      </Grid>
      <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
        <Label label={t('updateCustomerProfile.phone')} labelCol={4} required />
        <Grid
          item
          xs={12}
          tablet={8}
          mt={isLawyer ? '12px' : 0}
          fontSize={{ xs: 14, tablet: 16 }}
        >
          <TextField
            labelCol={0}
            control={control}
            name="phone"
            placeholder={t('updateCustomerProfile.phone')}
          />
        </Grid>
      </Grid>
      <Radio
        data={Helper.convertObjectToOptions(Gender)}
        control={control}
        label={t('updateCustomerProfile.gender')}
        name="gender"
        required
        labelCol={4}
        viewMode={isLawyer}
      />
      <Select
        data={prefectureList}
        control={control}
        label={t('updateCustomerProfile.address')}
        placeholder={t('updateCustomerProfile.selectPrefecture')}
        name="currentAddress1"
        required
        labelCol={4}
        viewMode={isLawyer}
      />
      {!firstBooking && (
        <>
          <Select
            data={ageList}
            control={control}
            label={t('updateCustomerProfile.age')}
            name="age"
            placeholder={t('placeholder.ageSelect')}
            required
            labelCol={4}
            viewMode={isLawyer}
          />
          <TextField
            control={control}
            label={t('updateCustomerProfile.job')}
            maxLength={30}
            name="job"
            labelCol={4}
            placeholder={t('placeholder.job')}
            required
          />
          <Select
            labelCol={4}
            data={ANNUAL_INCOME.map((i) => ({ _id: i, value: i }))}
            control={control}
            label={t('updateCustomerProfile.annualIncome')}
            placeholder={t('placeholder.annualIncome')}
            name="annualIncome"
          />
        </>
      )}
      {renderOTPForm()}
    </Stack>
  );
};

export default memo(Fields);

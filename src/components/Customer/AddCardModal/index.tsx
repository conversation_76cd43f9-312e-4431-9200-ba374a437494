import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Stack, Typography } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import valid from 'card-validator';
import { TextField } from 'components/Form';
import MaskField from 'components/Form/MaskedField';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import {
  AmexCardIcon,
  DinerCardIcon,
  JCBCardIcon,
  MasterCardIcon,
  VisaCardIcon,
} from 'icons';
import type {
  AddCardPayload,
  EditCardPayload,
  ICardsList,
} from 'models/card/interface';
import cardQuery from 'models/card/query';
import { useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import type { ValidCardNiceType } from 'utils/constants';
import { CardIcon } from 'utils/constants';
import Helper from 'utils/helpers';
import type { InferType } from 'yup';

import schema from './schema';
import styles from './styles';

export type AddCardForm = InferType<typeof schema>;
export interface IAddCardModal {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  editPayload?: {
    card: ICardsList;
    isEdit: boolean;
  };
}

const AddCardModal = ({
  open,
  onClose,
  onSuccess,
  editPayload,
}: IAddCardModal) => {
  const webCookie = Helper.getWebCookieByGuest();
  const card = editPayload?.card;
  const isEdit = editPayload?.isEdit;

  const { control, handleSubmit, reset } = useForm<AddCardForm>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues: card
      ? {
          card_expire: `${card.details.expireMonth}/${card.details.expireYear}`,
          card_number: Helper.formatCardNumberText(card.details.lastNumber),
          cardholder_name: card.details.cardholderName || '',
          security_code: '••••',
        }
      : {},
    shouldUnregister: true,
    context: {
      isEdit,
    },
  });
  const { mutateAsync: getCardToken, isLoading: isGettingToken } = useMutate<
    AddCardForm,
    { token: string }
  >(cardQuery.getCardToken);
  const { mutateAsync: addCard, isLoading: isAddingCard } =
    useMutate<AddCardPayload>(
      webCookie?.otpId
        ? cardQuery.addCardByGuest(webCookie?.otpId as string)
        : cardQuery.addCard,
    );
  const { mutateAsync: editCard, isLoading: isEditingCard } = useMutate<
    EditCardPayload & { _id: string }
  >(cardQuery.editCardByGuest);

  const cardNumberValue = useWatch({ control, name: 'card_number' });
  const cardType = valid.number(cardNumberValue);

  const handleAddCard = async (values: AddCardForm) => {
    const data = await getCardToken({
      ...values,
      card_number: values.card_number.trim(),
      security_code: values.security_code.trim(),
    });
    addCard(
      { token: data.token },
      {
        onSuccess: () => {
          if (onSuccess) {
            onSuccess();
          }
        },
      },
    );
  };

  const handleEditCard = async (values: AddCardForm) => {
    editCard(
      {
        _id: card?.id as string,
        cardholderName: values.cardholder_name,
        cardExpire: values.card_expire,
        defaultCard: !!card?.details.default,
      },
      {
        onSuccess: () => {
          if (onSuccess) {
            onSuccess();
          }
        },
      },
    );
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Dialog
      onClose={(_, reason) => {
        if (reason === 'backdropClick') {
          onClose();
        }
      }}
      open={open}
      aria-labelledby="add-card-modal-title"
      aria-describedby="add-card-modal-description"
      maxWidth="mw"
      fullWidth
    >
      <DialogTitle id="add-card-modal-title" sx={styles.dialogTitle}>
        <Typography
          fontSize={{ xs: 24, tablet: 32 }}
          fontWeight="bold"
          color="heading"
          textAlign="center"
        >
          {t(isEdit ? 'card.editCard' : 'card.addCard')}
        </Typography>
      </DialogTitle>
      <DialogContent sx={styles.dialogContent}>
        <DialogContentText id="add-card-modal-description" component="div">
          <Typography textAlign="center" fontSize={{ xs: 12, tablet: 14 }}>
            {t('card.paymentInfo')}
          </Typography>
          <Box>
            <form
              id="add-card"
              onSubmit={handleSubmit(isEdit ? handleEditCard : handleAddCard)}
            >
              <Stack
                spacing={{ xs: '20px', tablet: 2 }}
                mt={{ xs: 2, tablet: 3 }}
              >
                {isEdit ? (
                  <TextField
                    name="card_number"
                    label={t('card.cardNumber')}
                    placeholder="#### #### #### ####"
                    control={control}
                    labelCol={12}
                    disabled
                    readOnly
                  />
                ) : (
                  <MaskField
                    name="card_number"
                    label={t('card.cardNumber')}
                    placeholder="#### #### #### ####"
                    format="###################"
                    control={control}
                    labelCol={12}
                    endAdornment={
                      cardType.card?.type && (
                        <Box sx={styles.cardIcon}>
                          {
                            CardIcon[
                              cardType.card?.niceType as ValidCardNiceType
                            ]
                          }
                        </Box>
                      )
                    }
                    inputProps={{ inputMode: 'numeric' }}
                  />
                )}
                <TextField
                  name="cardholder_name"
                  label={t('card.cardholderName')}
                  placeholder="カード名義人"
                  labelCol={12}
                  inputProps={{ inputMode: 'text' }}
                  control={control}
                  maxLength={45}
                />
                <MaskField
                  format="##/##"
                  name="card_expire"
                  label={t('card.expDate')}
                  placeholder="MM/YY"
                  control={control}
                  labelCol={12}
                  inputProps={{ inputMode: 'numeric' }}
                />
                {isEdit ? (
                  <TextField
                    name="security_code"
                    label={t('card.securityCode')}
                    placeholder="###"
                    control={control}
                    labelCol={12}
                    disabled
                    readOnly
                  />
                ) : (
                  <MaskField
                    name="security_code"
                    label={t('card.securityCode')}
                    placeholder="###"
                    control={control}
                    labelCol={12}
                    format="####"
                    inputProps={{ inputMode: 'numeric' }}
                  />
                )}
              </Stack>
              <Typography
                textAlign="center"
                fontSize={{ xs: 12, tablet: 14 }}
                mt={{ xs: 4, tablet: 5 }}
                color="placeholder"
              >
                {t('card.cardCanUse')}
              </Typography>
              <Stack direction="row" gap={1} justifyContent="center" mt="12px">
                <MasterCardIcon />
                <VisaCardIcon />
                <JCBCardIcon />
                <AmexCardIcon />
                <DinerCardIcon />
              </Stack>
              <Stack
                direction="row"
                gap={{ xs: '7px', tablet: 2 }}
                mt={{ xs: 4, tablet: 5 }}
                justifyContent="center"
              >
                <Button
                  variant="outlined"
                  fullWidth
                  size="large"
                  sx={{ maxWidth: 200 }}
                  className="tabletStyle"
                  onClick={onClose}
                >
                  {t('global.cancel')}
                </Button>
                <LoadingButton
                  loading={isGettingToken || isAddingCard || isEditingCard}
                  variant="contained"
                  color="secondary"
                  type="submit"
                  form="add-card"
                  fullWidth
                  size="large"
                  className="tabletStyle"
                  sx={{ maxWidth: 200 }}
                >
                  保存
                </LoadingButton>
              </Stack>
              <Typography
                textAlign="center"
                fontSize={{ xs: 12, tablet: 14 }}
                mt={{ xs: 2, tablet: 4 }}
              >
                {t('card.paymentNote')}
              </Typography>
            </form>
          </Box>
        </DialogContentText>
      </DialogContent>
    </Dialog>
  );
};

export default AddCardModal;

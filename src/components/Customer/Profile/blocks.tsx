import { Box, Chip, Stack, Typography } from '@mui/material';
import type { I<PERSON>lockField } from 'components/UI/ViewEditBlock';
import dayjs from 'dayjs';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import { t } from 'i18n';
import { DivorceIcon, ProfileIcon, PropertyIcon, RingIcon } from 'icons';
import { isUndefined, orderBy } from 'lodash';
import type { ReactNode } from 'react';
import type {
  GenderType,
  RadioOptionsType,
  YesNoOptionsType,
} from 'utils/constants';
import {
  Gender,
  MomentFormat,
  RadioOptions,
  YES_NO_OPTIONS,
  YesNoOptions,
} from 'utils/constants';

export interface IBlock<T = unknown> {
  title: string;
  icon: ReactNode;
  fields: IBlockField<T>[];
}
export const profileBlock: {
  title: string;
  icon: ReactNode;
  fields: IBlockField[];
} = {
  title: 'ご相談者のプロフィール',
  icon: <ProfileIcon />,
  fields: [
    {
      label: t('updateCustomerProfile.fullName'),
      path: 'fullName',
    },
    {
      label: t('updateCustomerProfile.katakanaName'),
      path: 'katakanaName',
    },
    {
      label: t('updateCustomerProfile.email'),
      path: 'email',
    },
    {
      label: t('updateCustomerProfile.gender'),
      path: 'gender',
      renderValue: (value) => Gender[value as GenderType],
    },
    {
      label: t('updateCustomerProfile.phone'),
      path: 'phone',
    },
    {
      label: t('updateCustomerProfile.address'),
      path: 'currentAddress.address1.value',
    },
    {
      label: t('updateCustomerProfile.age'),
      path: 'age.value',
    },
    {
      label: t('updateCustomerProfile.job'),
      path: 'job',
    },
    {
      label: t('updateCustomerProfile.annualIncome'),
      path: 'annualIncome',
    },
  ],
};
export const divorceBlock: IBlock = {
  title: t('consultationRecord.divorceBackground'),
  icon: <DivorceIcon />,
  fields: [
    {
      label: 'consultationRecord.youDivorce',
      path: 'backgroundOfDivorce.youDivorce',
      renderValue: (value: YesNoOptionsType) => YES_NO_OPTIONS[value],
    },
    {
      label: 'consultationRecord.partnerDivorce',
      path: 'backgroundOfDivorce.partnerDivorce',
      renderValue: (value: YesNoOptionsType) =>
        YES_NO_OPTIONS[value as YesNoOptionsType],
    },
    {
      label: 'consultationRecord.reason',
      path: 'backgroundOfDivorce.reason',
      renderValue: (
        value: {
          value: string;
          text: string;
          extraData?: string;
        }[],
      ) => {
        if (value && value.length > 0) {
          return (
            <Stack spacing={1}>
              {orderBy(value, (item) => Number(item.value.slice(15))).map(
                (i) => (
                  <Stack direction="row" key={i.value} spacing={1}>
                    <Box>
                      <Chip label={i.text} />
                    </Box>
                    <Typography>{i.extraData}</Typography>
                  </Stack>
                ),
              )}
            </Stack>
          );
        }
        return null;
      },
    },
    {
      label: 'その他ご相談したい内容をご記入下さい',
      path: 'backgroundOfDivorce.additionalTopics',
    },
  ],
};

export const partnerBlock: IBlock<ConsultationRecord> = {
  title: t('consultationRecord.partnerInformation'),
  icon: <ProfileIcon />,
  fields: [
    {
      label: 'お名前',
      path: 'partner.firstKatakanaName',
      renderValue: (_, detail) => {
        if (detail && detail.partner?.firstName && detail.partner.lastName) {
          return `${detail.partner.lastName} ${detail.partner.firstName}`;
        }
        return null;
      },
    },
    {
      label: 'お名前 (フリガナ)',
      path: 'partner.firstKatakanaName',
      renderValue: (_, detail) => {
        if (
          detail &&
          detail.partner?.firstKatakanaName &&
          detail.partner.lastKatakanaName
        ) {
          return `${detail.partner.lastKatakanaName} ${detail.partner.firstKatakanaName}`;
        }
        return null;
      },
    },
    {
      label: '年齢',
      path: 'partner.age.value',
    },
    {
      label: '職業',
      path: 'partner.job',
    },
    {
      label: 'consultationRecord.annualIncome',
      path: 'partner.annualIncome',
    },
  ],
};

export const marriageBlock: IBlock = {
  title: t('consultationRecord.marriageInformation'),
  icon: <RingIcon />,
  fields: [
    {
      label: 'consultationRecord.marriedDate',
      path: 'marriageInformation.marriedDate',
      renderValue: (value) =>
        value && dayjs(value).format(MomentFormat.JP_YEAR_MONTH_DATE),
    },
    {
      label: 'consultationRecord.isSeparated',
      path: 'marriageInformation.isSeparated',
      renderValue: (value: YesNoOptionsType) => YesNoOptions[value],
    },
    {
      label: 'consultationRecord.separationDate',
      path: 'marriageInformation.separationDate',
      renderValue: (value) =>
        value && dayjs(value).format(MomentFormat.JP_YEAR_MONTH_DATE),
    },
  ],
};

export const propertyBlock: IBlock = {
  title: t('consultationRecord.propertyInformation'),
  icon: <PropertyIcon />,
  fields: [
    {
      label: 'consultationRecord.firstValueOfRealEstate',
      path: 'propertyInformation.firstValueOfRealEstate',
      renderValue: (value) => !isUndefined(value) && `${value}万円`,
    },
    {
      label: 'consultationRecord.currentValueOfRealEstate',
      path: 'propertyInformation.currentValueOfRealEstate',
      renderValue: (value) => !isUndefined(value) && `${value}万円`,
    },
    {
      label: 'consultationRecord.loan',
      path: 'propertyInformation.loan',
      renderValue: (value) => !isUndefined(value) && `${value}万円`,
    },
    {
      label: 'consultationRecord.depositAndSaving',
      path: 'propertyInformation.depositAndSaving',
      renderValue: (value) => !isUndefined(value) && `${value}円`,
    },
    {
      label: 'consultationRecord.depositAndSavingOfPartner',
      path: 'propertyInformation.depositAndSavingOfPartner',
      renderValue: (value) => !isUndefined(value) && `${value}円`,
    },
    {
      label: 'consultationRecord.lifeInsurance',
      path: 'propertyInformation.lifeInsurance',
      renderValue: (value: RadioOptionsType) => RadioOptions[value],
    },
    {
      label: 'consultationRecord.typeOfPensionOfYourself',
      path: 'propertyInformation.typeOfPensionOfYourself',
    },
    {
      label: 'consultationRecord.typeOfPensionOfPartner',
      path: 'propertyInformation.typeOfPensionOfPartner',
    },
    {
      label: 'consultationRecord.otherProperties',
      path: 'propertyInformation.otherProperties',
    },
    {
      label: 'consultationRecord.supplementaryInfo',
      path: 'propertyInformation.supplementaryInfo',
    },
  ],
};

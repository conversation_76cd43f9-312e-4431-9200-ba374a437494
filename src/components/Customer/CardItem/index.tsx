import { LoadingButton } from '@mui/lab';
import { Box, Chip, IconButton, Stack, Typography } from '@mui/material';
import HelperText from 'components/Form/HelperText';
import { motion } from 'framer-motion';
import useBreakpoint from 'hooks/useBreakpoint';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { EditIcon, MoreIcon, MyPagePaymentIcon, TrashIcon } from 'icons';
import { get } from 'lodash';
import type { ICardsList } from 'models/card/interface';
import cardQuery from 'models/card/query';
import dynamic from 'next/dynamic';
import { useState } from 'react';
import { CardIcon } from 'utils/constants';
import errors from 'utils/errors';
import Helper from 'utils/helpers';

import styles from './styles';

const DropdownMenu = dynamic(() => import('components/UI/DropdownMenu'));

export interface ICardList {
  card: ICardsList;
  refetch: () => void;
  isCheck?: boolean;
  editCallback?: (card: ICardsList) => void;
}

const dropdownMenus = [
  {
    icon: MyPagePaymentIcon,
    label: t('card.setAsPrimary'),
    key: 'setPrimary',
  },
  {
    icon: TrashIcon,
    label: t('card.deleteButton'),
    key: 'delete',
  },
  {
    icon: EditIcon,
    label: t('global.edit'),
    key: 'edit',
  },
];

const CardItem = ({ card, refetch, isCheck, editCallback }: ICardList) => {
  const { setConfirmModal } = useGlobalState();
  const isBreakpoint = useBreakpoint({});
  const [cardId, setCardId] = useState('');
  const { mutateAsync: setPrimaryCard, isLoading } = useMutate(
    cardQuery.setPrimary,
  );
  const { mutateAsync: deleteCard } = useMutate(cardQuery.deleteCard);

  const handleDeleteCard = (_id: string) => {
    deleteCard(
      { _id },
      {
        onSuccess: () => refetch(),
        onError: (e) => {
          setConfirmModal({
            icon: 'error',
            hideCancelButton: true,
            content: errors[get(e, 'code') as never],
          });
        },
      },
    );
  };

  const handleSetPrimaryCard = (_id: string) => {
    setCardId(_id);
    setPrimaryCard(
      { _id },
      {
        onSuccess: () => refetch(),
      },
    );
  };

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const handleClickMoreButton = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMeetingAction = (key: string) => {
    setAnchorEl(null);
    if (key === 'delete') {
      setConfirmModal({
        title: 'card.title',
        onConfirm: () => handleDeleteCard(card.id),
        content: 'card.message',
      });
    }
    if (key === 'setPrimary') {
      handleSetPrimaryCard(card.id);
    }
    if (key === 'edit') {
      editCallback?.(card);
    }
  };

  return (
    <Box key={card.id} component={motion.div} layout>
      <Box sx={styles.cardItem}>
        {!isBreakpoint ? (
          <>
            <Stack direction="row" spacing={2} alignItems="center">
              <Box sx={styles.cardIcon}>{CardIcon[card.details.brand]}</Box>
              <Typography fontWeight={500} color="heading">
                {Helper.formatCardNumberText(card.details.lastNumber)}
              </Typography>
            </Stack>
            <Stack direction="row" spacing={2} alignItems="center">
              {card.details.default ? (
                <Chip
                  label={t('card.primary')}
                  sx={{
                    bgcolor: '#fdf3d8',
                    color: 'primary.main',
                  }}
                  className="tabletStyle"
                />
              ) : (
                <LoadingButton
                  variant="text"
                  style={{
                    textTransform: 'initial',
                    marginRight: '-16px',
                  }}
                  loading={isLoading && cardId === card.id}
                  onClick={() => handleSetPrimaryCard(card.id)}
                >
                  {t('card.setAsPrimary')}
                </LoadingButton>
              )}
              {isCheck && (
                <IconButton
                  size="small"
                  className="whiteOutlined"
                  sx={{ svg: { color: 'neutral7' } }}
                  onClick={() =>
                    setConfirmModal({
                      title: 'card.title',
                      onConfirm: () => handleDeleteCard(card.id),
                      content: 'card.message',
                    })
                  }
                >
                  <TrashIcon />
                </IconButton>
              )}
              {editCallback && (
                <IconButton
                  size="small"
                  className="whiteOutlined"
                  sx={{ svg: { color: 'neutral7' } }}
                  onClick={() => editCallback(card)}
                >
                  <EditIcon />
                </IconButton>
              )}
            </Stack>
          </>
        ) : (
          <>
            <Stack direction="row" alignItems="center">
              <Box sx={styles.cardIcon}>{CardIcon[card.details.brand]}</Box>
              <Typography
                fontWeight={500}
                color="heading"
                fontSize={14}
                ml="12px"
                mr="8px"
              >
                {Helper.formatCardNumberText(card.details.lastNumber)}
              </Typography>
              {card.details.default && (
                <Chip
                  label={t('card.primary')}
                  sx={{
                    bgcolor: '#fdf3d8',
                    color: 'primary.main',
                  }}
                  className="tabletStyle"
                />
              )}
            </Stack>
            <Box>
              <IconButton
                size="medium"
                sx={styles.moreIcon}
                onClick={handleClickMoreButton}
                id="more-button"
              >
                <MoreIcon />
              </IconButton>
            </Box>
          </>
        )}
        <DropdownMenu
          menus={dropdownMenus.map((item) => {
            if (['setPrimary'].includes(item.key) && card.details.default) {
              return {
                ...item,
                disabled: true,
              };
            }

            if (['delete'].includes(item.key) && !isCheck) {
              return {
                ...item,
                disabled: true,
              };
            }
            return item;
          })}
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
          onMenuItemClick={handleMeetingAction}
        />
      </Box>

      {!card.details.cardholderName && (
        <HelperText error={t('inlineMessage.GIM18')} />
      )}
    </Box>
  );
};
export default CardItem;

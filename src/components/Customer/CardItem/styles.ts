import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  cardItem: {
    borderRadius: '12px',
    border: (theme) => `solid 1px ${theme.palette.neutral4}`,
    display: 'flex',
    p: { xs: 2, tablet: 3 },
    justifyContent: 'space-between',
  },
  cardIcon: (theme) => ({
    svg: { width: 48, height: 32, display: 'block' },
    [theme.breakpoints.down('tablet')]: {
      svg: {
        width: 42,
        height: 28,
        display: 'block',
      },
    },
  }),
  moreIcon: {
    p: 0,
    svg: { width: 20, height: 20 },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

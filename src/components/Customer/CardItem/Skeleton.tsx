import { Box, Skeleton, Stack } from '@mui/material';
import { times } from 'lodash';

import styles from './styles';

const ItemSkeleton = () => {
  return (
    <Stack gap={2}>
      {times(3).map((number) => {
        return (
          <Box sx={styles.cardItem} key={number}>
            <Stack
              direction="row"
              spacing={{ xs: '12px', tablet: 2 }}
              alignItems="center"
            >
              <Skeleton
                variant="rounded"
                sx={{
                  width: { xs: 42, tablet: 48 },
                  height: { xs: 28, tablet: 32 },
                }}
              />
              <Skeleton
                variant="text"
                sx={{ fontSize: { xs: 14, tablet: 16 } }}
                width={100}
              />
            </Stack>
            <Stack direction="row" spacing={2} alignItems="center">
              <Skeleton
                variant="rounded"
                sx={{
                  height: { xs: 20, tablet: 24 },
                  width: { xs: 58, tablet: 63 },
                }}
              />
              <Skeleton
                variant="circular"
                sx={{
                  width: { xs: 20, tablet: 32 },
                  height: { xs: 20, tablet: 32 },
                }}
              />
            </Stack>
          </Box>
        );
      })}
    </Stack>
  );
};

export default ItemSkeleton;

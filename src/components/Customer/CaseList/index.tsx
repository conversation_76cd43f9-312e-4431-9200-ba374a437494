import { Box, Stack, Typography } from '@mui/material';
import CaseTag from 'components/Case/CaseTag';
import ListPagination from 'components/CustomPagination/ListPagination';
import dayjs from 'dayjs';
import i18n from 'i18n';
import { get, isEmpty } from 'lodash';
import type { ICaseListItem } from 'models/case/interface';
import Link from 'next/link';
import { MomentFormat, ProviderType } from 'utils/constants';

const CaseList = ({
  data = [],
  total,
}: {
  data: ICaseListItem[];
  total: number;
}) => {
  const { t } = i18n;
  return (
    <>
      <Stack p={2} gap={1} mt={1} bgcolor="white">
        {!isEmpty(data) ? (
          data.map((caseItem) => {
            return (
              <Link
                href={{
                  pathname: `/customer/my-page/cases/${caseItem._id}`,
                  query: {
                    tab: 'counselor',
                  },
                }}
                key={caseItem._id}
              >
                <Box borderRadius={1} border="solid 1px #d2dce1">
                  <Box p={2}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography fontWeight="bold" color="heading">
                        {caseItem.provider.nickname ||
                          caseItem.provider.fullName}
                      </Typography>
                      <CaseTag status={caseItem.status} />
                    </Box>
                    <Typography fontSize={14} mt={1} color="text.primary">
                      {t('caseList.finalizedBookingDateMobile', {
                        date: get(caseItem, 'finalizedDate')
                          ? dayjs(get(caseItem, 'finalizedDate')).format(
                              MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                            )
                          : '-',
                      })}
                    </Typography>
                    {caseItem.provider.type === ProviderType.COUNSELOR && (
                      <Typography fontSize={14} color="text.primary" mt="4px">
                        {t('caseList.duration', {
                          number: caseItem.duration,
                        })}
                      </Typography>
                    )}
                    <Typography fontSize={10} mt={1} color="hint">
                      {t('caseDetail.caseId', {
                        caseId: caseItem._id,
                        createdAt: dayjs(get(caseItem, 'createdAt')).format(
                          MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                        ),
                      })}
                    </Typography>
                  </Box>
                </Box>
              </Link>
            );
          })
        ) : (
          <Typography align="center" fontStyle="italic" fontSize={14}>
            {t('global.noData')}
          </Typography>
        )}
      </Stack>
      {total > 0 && (
        <Box display="flex" justifyContent="center" p={2}>
          <ListPagination total={total} />
        </Box>
      )}
    </>
  );
};

export default CaseList;

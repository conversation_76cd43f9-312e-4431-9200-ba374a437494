import valid from 'card-validator';
import { t } from 'i18n';
import { object, string } from 'yup';

const validationSchema = object({
  card_number: string()
    .required()
    .test('isValidCard', t('validation.invalidCardNumber'), (value) => {
      const validator = valid.number(value);
      if (
        validator?.card?.niceType &&
        ![
          'Visa',
          'Mastercard',
          'JCB',
          'Diners Club',
          'American Express',
        ].includes(validator?.card?.niceType)
      ) {
        return false;
      }
      return valid.number(value).isValid;
    }),
  cardholder_name: string()
    .trim()
    .required()
    .matches(/^[a-zA-Z0-9\s]+$/, t('validation.invalidHolderFormat'))
    .min(2, t('validation.invalidHolderNameTooShort'))
    .max(45, t('validation.invalidHolderName')),
  card_expire: string()
    .required()
    .test(
      'isValidCard',
      t('validation.invalidExp'),
      (value) =>
        valid.expirationDate(
          value,
          process.env.MAX_YEAR_CREDIT_CARD
            ? Number(process.env.MAX_YEAR_CREDIT_CARD)
            : undefined,
        ).isValid,
    ),
  security_code: string()
    .required()
    .min(3, t('validation.invalidCvc'))
    .when('card_number', {
      is: (value: string) => {
        const validator = valid.number(value);
        return (
          validator.isValid &&
          validator?.card?.niceType &&
          [
            'Visa',
            'Mastercard',
            'JCB',
            'Diners Club',
            'American Express',
          ].includes(validator?.card?.niceType)
        );
      },
      then: string().trim().length(3, t('validation.invalidCvc')).required(),
    })
    .when('card_number', {
      is: (value: string) => {
        const validator = valid.number(value);
        return (
          validator.isValid && validator?.card?.niceType === 'American Express'
        );
      },
      then: string().trim().length(4, t('validation.invalidCvc')).required(),
    }),
});

export default validationSchema;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  dialogTitle: { p: { xs: 2, tablet: '32px 32px 24px' } },
  dialogContent: {
    p: { xs: '0px 16px 16px', tablet: '0px 32px 32px' },
  },
  cardIcon: {
    mr: 2,
    svg: { display: 'block' },
  },
  radio: {
    '&.MuiFormGroup-root': {
      width: '100%',
      pl: '12px',
      gap: { xs: 1, tablet: 2 },
      mt: 4,
    },
    label: {
      border: '1px solid #d2dce1',
      borderRadius: '12px',
      p: '9px 6px',
    },
  },
  addCardButton: {
    ml: '-4px',
    mt: 1,
    svg: {
      width: 20,
      height: 20,
    },
    '& .MuiButton-startIcon': {
      mr: '4px',
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

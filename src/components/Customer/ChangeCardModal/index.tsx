import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Chip, Stack, Typography } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import valid from 'card-validator';
import { Radio, TextField } from 'components/Form';
import MaskField from 'components/Form/MaskedField';
import { AnimatePresence, motion } from 'framer-motion';
import { useFetchList } from 'hooks';
import useDeepCompareEffect from 'hooks/useDeepCompareEffect';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import {
  AddIcon,
  AmexCardIcon,
  DinerCardIcon,
  JCBCardIcon,
  MasterCardIcon,
  VisaCardIcon,
} from 'icons';
import type {
  IExtraTdsPayload,
  IRequest3DsRes,
} from 'models/booking/interface';
import type { AddCardPayload, ICardsList } from 'models/card/interface';
import cardQuery from 'models/card/query';
import caseQuery from 'models/case/query';
import { useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import type { ValidCardNiceType } from 'utils/constants';
import { CardIcon } from 'utils/constants';
import Helper from 'utils/helpers';
import type { InferType } from 'yup';

import schema from './schema';
import styles from './styles';

export interface IChangeCreditCardModal {
  open: boolean;
  onClose: () => void;
  onSuccess?: ({
    isDirty,
    changeCardResponse,
  }: {
    isDirty: boolean;
    changeCardResponse?: IRequest3DsRes;
  }) => void;
  meetingId: string;
  usingCard?: string;
  caseId: string;
  use3Ds?: boolean;
}

type AddCardForm = InferType<typeof schema>;

// TOTO: Separate add card form to component
const ChangeCreditCardModal = ({
  open,
  onClose,
  onSuccess,
  meetingId,
  usingCard,
  caseId,
  use3Ds,
}: IChangeCreditCardModal) => {
  const [step, setStep] = useState('card-list');
  const { control, handleSubmit, reset } = useForm<AddCardForm>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
  });
  const {
    control: selectCardControl,
    handleSubmit: handleSelectCardSubmit,
    reset: selectCardReset,
    formState: { isDirty },
  } = useForm<{ card: string }>({
    mode: 'onTouched',
  });
  const { list: cardListFull, refetch } = useFetchList<ICardsList>({
    ...cardQuery.cardsList,
    enabled: open,
  });
  const cardList = cardListFull.filter((card) => card.details.cardholderName);
  const { mutateAsync: getCardToken, isLoading: isGettingToken } = useMutate<
    AddCardForm,
    { token: string }
  >(cardQuery.getCardToken);
  const { mutateAsync: addCard, isLoading: isAddingCard } =
    useMutate<AddCardPayload>(cardQuery.addCard);
  const { mutateAsync: updateMeetingCard, isLoading: isUpdatingMeetingCard } =
    useMutate<{
      paymentInfo: { card: string };
    }>(caseQuery.updateMeetingCard(meetingId));
  const {
    mutateAsync: updateMeetingCard3Ds,
    isLoading: isUpdatingMeetingCard3Ds,
  } = useMutate<
    { paymentInfo: { card: string }; extraPayload: IExtraTdsPayload },
    IRequest3DsRes
  >(caseQuery.updateMeetingCard3Ds(meetingId));

  const cardNumberValue = useWatch({ control, name: 'card_number' });
  const cardType = valid.number(cardNumberValue);

  const handleAddCard = async (values: AddCardForm) => {
    const data = await getCardToken({
      ...values,
      card_number: values.card_number.trim(),
      security_code: values.security_code.trim(),
    });
    addCard(
      { token: data.token },
      {
        onSuccess: () => {
          setStep('card-list');
          reset();
          refetch();
        },
      },
    );
  };

  const handleChangeCard = async (values: { card: string }) => {
    let changeCardRes: IRequest3DsRes | undefined;
    if (isDirty) {
      if (use3Ds) {
        changeCardRes = await updateMeetingCard3Ds({
          paymentInfo: values,
          extraPayload: { caseId },
        });
      } else {
        await updateMeetingCard({
          paymentInfo: values,
        });
      }
    }
    if (onSuccess) {
      onSuccess({ isDirty, changeCardResponse: changeCardRes });
    }
  };

  useDeepCompareEffect(() => {
    if (cardList) {
      selectCardReset({
        card: usingCard || cardList.find((card) => card.details.default)?.id,
      });
    }
  }, [cardList, selectCardReset, usingCard]);

  useEffect(() => {
    if (!open) {
      reset();
      selectCardReset();
    }
  }, [open, reset, selectCardReset]);

  return (
    <Dialog
      onClose={(_, reason) => {
        if (reason === 'backdropClick') {
          onClose();
        }
      }}
      open={open}
      aria-labelledby="change-card-modal-title"
      aria-describedby="change-card-modal-description"
      maxWidth="mw"
      fullWidth
    >
      <DialogTitle id="change-card-modal-title" sx={styles.dialogTitle}>
        <Typography
          fontSize={24}
          fontWeight="bold"
          color="heading"
          textAlign="center"
        >
          {t('card.changeCard')}
        </Typography>
      </DialogTitle>
      <DialogContent sx={styles.dialogContent}>
        <DialogContentText id="change-card-modal-description" component="div">
          <Typography textAlign="center" fontSize={{ xs: 12, tablet: 14 }}>
            {t('card.titleChangeCard')}
          </Typography>
          <Box>
            <form
              id={step}
              onSubmit={
                step === 'add-card'
                  ? handleSubmit(handleAddCard)
                  : handleSelectCardSubmit(handleChangeCard)
              }
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={step}
                  initial={{ y: 10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  exit={{ y: -10, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {step === 'card-list' ? (
                    <Box>
                      <Radio
                        labelCol={0}
                        name="card"
                        row={false}
                        control={selectCardControl}
                        data={cardList.map((card) => ({
                          _id: card.id,
                          value: (
                            <Stack
                              ml="6px"
                              direction="row"
                              spacing="12px"
                              alignItems="center"
                              sx={{
                                svg: {
                                  height: {
                                    xs: 28,
                                    tablet: 24,
                                  },
                                  width: {
                                    xs: 42,
                                    tablet: 36,
                                  },
                                },
                              }}
                            >
                              {CardIcon[card.details.brand]}
                              <Typography
                                fontSize={{ xs: 14, tablet: 16 }}
                                ml={{ xs: 1, tablet: '12px' }}
                                color="heading"
                                fontWeight={500}
                              >
                                {Helper.formatCardNumberText(
                                  card.details.lastNumber,
                                )}
                              </Typography>
                              {card.details.default && (
                                <Chip
                                  label="デフォルト"
                                  sx={{
                                    bgcolor: '#fdf3d8',
                                    color: 'primary.main',
                                  }}
                                  className="tabletStyle"
                                />
                              )}
                            </Stack>
                          ),
                        }))}
                        sx={styles.radio}
                      />
                      <Box>
                        <Button
                          size="small"
                          onClick={() => setStep('add-card')}
                          sx={styles.addCardButton}
                          className="tabletStyle"
                          startIcon={<AddIcon />}
                          color="primary"
                        >
                          新しいカードを追加
                        </Button>
                      </Box>
                    </Box>
                  ) : (
                    <>
                      <Stack spacing={2} mt={{ xs: 2, tablet: 3 }}>
                        <MaskField
                          name="card_number"
                          label={t('card.cardNumber')}
                          placeholder="#### #### #### ####"
                          format="###################"
                          control={control}
                          labelCol={12}
                          endAdornment={
                            cardType.card?.type && (
                              <Box sx={styles.cardIcon}>
                                {
                                  CardIcon[
                                    cardType.card?.niceType as ValidCardNiceType
                                  ]
                                }
                              </Box>
                            )
                          }
                          inputProps={{ inputMode: 'numeric' }}
                        />
                        <TextField
                          name="cardholder_name"
                          label={t('card.cardholderName')}
                          placeholder="カード名義人"
                          labelCol={12}
                          inputProps={{ inputMode: 'text' }}
                          control={control}
                          maxLength={45}
                        />
                        <MaskField
                          format="##/##"
                          name="card_expire"
                          label={t('card.expDate')}
                          placeholder="MM/YY"
                          control={control}
                          labelCol={12}
                          inputProps={{ inputMode: 'numeric' }}
                        />
                        <MaskField
                          name="security_code"
                          label={t('card.securityCode')}
                          placeholder="###"
                          control={control}
                          labelCol={12}
                          format="####"
                          inputProps={{ inputMode: 'numeric' }}
                        />
                      </Stack>
                      <Typography
                        textAlign="center"
                        fontSize={14}
                        mt={{ xs: 4, tablet: 5 }}
                      >
                        {t('card.cardCanUse')}
                      </Typography>
                      <Stack
                        direction="row"
                        gap={1}
                        justifyContent="center"
                        mt="12px"
                      >
                        <MasterCardIcon />
                        <VisaCardIcon />
                        <JCBCardIcon />
                        <AmexCardIcon />
                        <DinerCardIcon />
                      </Stack>
                    </>
                  )}
                </motion.div>
              </AnimatePresence>

              <Stack
                direction="row"
                gap={{ xs: 1, tablet: 2 }}
                mt={{ xs: 4, tablet: 5 }}
                justifyContent="center"
              >
                <Button
                  variant="outlined"
                  fullWidth
                  sx={{ maxWidth: 200 }}
                  size="large"
                  className="tabletStyle"
                  onClick={() =>
                    step === 'card-list' ? onClose() : setStep('card-list')
                  }
                >
                  {t('global.cancel')}
                </Button>
                <LoadingButton
                  loading={
                    isGettingToken ||
                    isAddingCard ||
                    isUpdatingMeetingCard ||
                    isUpdatingMeetingCard3Ds
                  }
                  variant="contained"
                  color="secondary"
                  type="submit"
                  fullWidth
                  className="tabletStyle"
                  size="large"
                  sx={{ maxWidth: 200 }}
                >
                  保存
                </LoadingButton>
              </Stack>
              <Typography
                textAlign="center"
                fontSize={{ xs: 12, tablet: 14 }}
                mt={{ xs: 2, tablet: 4 }}
              >
                {t('card.paymentNote')}
              </Typography>
            </form>
          </Box>
        </DialogContentText>
      </DialogContent>
    </Dialog>
  );
};

export default ChangeCreditCardModal;

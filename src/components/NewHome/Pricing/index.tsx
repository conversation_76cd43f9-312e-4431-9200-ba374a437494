import { Box, Container, Typography } from '@mui/material';
import React from 'react';

interface PricingCardProps {
  minutes: string;
  label: string;
  description: React.ReactNode;
  price: string;
  maxPrice: string;
  isRecommended?: boolean;
  isExtension?: boolean;
}

const PricingCard: React.FC<PricingCardProps> = ({
  minutes,
  label,
  description,
  price,
  maxPrice,
  isRecommended = false,
  isExtension = false,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        width: '100%',
        borderRadius: '16px',
        overflow: 'hidden',
        mb: 3,
        flexDirection: { xs: 'column', md: 'row' },
        gap: { xs: '4px', md: '0px' },
      }}
    >
      {/* Left column - minutes and label */}
      <Box
        sx={{
          width: { xs: '100%', md: '346px' },
          backgroundColor: isRecommended
            ? '#FF5400'
            : { xs: '#FFFAF0', md: '#FFFFFF' },
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          py: {
            md: 4,
            xs: 2,
          },
          borderTopLeftRadius: {
            md: '16px',
            xs: '16px',
          },
          borderTopRightRadius: {
            md: '0px',
            xs: '16px',
          },
          borderBottomLeftRadius: {
            md: '16px',
            xs: '0px',
          },
        }}
      >
        <Box
          sx={{
            backgroundColor: isRecommended ? '#FF5400' : '#FFFFFF',
            py: 0.5,
            px: 2,
            mb: 2,
            borderRadius: '6px',
            ...(isExtension && {
              border: '1px solid #262626',
              backgroundColor: '#FFFFFF',
              width: '160px',
              height: '30px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }),
            ...(isRecommended && {
              border: 'none',
              backgroundColor: '#FFFFFF',
              width: '160px',
              height: '30px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }),
            ...([
              '短時間の相談なら',
              '2回目以降なら',
              'じっくり深くなら',
              'フォローアップ',
            ].includes(label) && {
              backgroundColor: '#FEA500',
              width: '160px',
              height: '30px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }),
          }}
        >
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 500,
              textAlign: 'center',
              color: isRecommended ? '#FF5400' : '#262626',
              ...(isExtension && {
                color: '#262626',
              }),
              ...([
                '短時間の相談なら',
                '2回目以降なら',
                'じっくり深くなら',
                'フォローアップ',
              ].includes(label) && {
                color: '#FFFFFF',
              }),
            }}
          >
            {label}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'flex-end' }}>
          <Typography
            sx={{
              fontSize: '36px',
              fontWeight: 700,
              lineHeight: 1.4,
              color: isRecommended ? '#FFFFFF' : '#262626',
            }}
          >
            {minutes}
          </Typography>
          <Typography
            sx={{
              fontSize: '20px',
              fontWeight: 700,
              ml: 0.5,
              mb: 0.5,
              color: isRecommended ? '#FFFFFF' : '#262626',
            }}
          >
            分
          </Typography>
        </Box>
      </Box>

      {/* Content area - split into two columns on desktop */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          ml: {
            md: '10px',
            xs: 0,
          },
        }}
      >
        {/* Middle column - description */}
        <Box
          sx={{
            flex: 1,
            backgroundColor: isRecommended
              ? '#FFF0E8'
              : { xs: '#FFFAF0', md: '#FFFFFF' },
            display: 'flex',
            justifyContent: {
              xs: 'center',
              md: 'flex-start',
            },
            alignItems: 'center',
            padding: { xs: 0, md: 4 },
            paddingTop: { xs: 2, md: 0 },
            paddingX: { xs: 2, md: 4 },
          }}
        >
          <Box
            sx={{
              fontSize: '16px',
              fontWeight: 700,
              lineHeight: 1.5,
              color: isRecommended ? '#FF5400' : '#262626',
              textAlign: {
                xs: 'center',
                md: 'left',
              },
            }}
          >
            {description}
          </Box>
        </Box>

        {/* Right column - pricing */}
        <Box
          sx={{
            width: { xs: '100%', md: '346px' },
            backgroundColor: isRecommended
              ? '#FFF0E8'
              : { xs: '#FFFAF0', md: '#FFFFFF' },
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: { xs: 'center', md: 'flex-start' },
            padding: { xs: 2, md: 4 },
            borderTopRightRadius: {
              md: '16px',
              xs: '0px',
            },
            borderBottomRightRadius: '16px',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'flex-end' }}>
            <Typography
              sx={{
                fontSize: { xs: '32px', md: '36px' },
                fontWeight: 700,
                color: isRecommended ? '#FF5400' : '#262626',
              }}
            >
              {price}
            </Typography>
            <Typography
              sx={{
                fontSize: '20px',
                fontWeight: 700,
                ml: 0.5,
                mb: 0.5,
                color: isRecommended ? '#FF5400' : '#262626',
              }}
            >
              円〜
            </Typography>
          </Box>
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 700,
              color: '#262626',
              mt: 0.5,
            }}
          >
            最大 {maxPrice}円
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

const PricingSection: React.FC = () => {
  return (
    <Box
      sx={{
        width: '100%',
        backgroundColor: {
          md: '#FFFAF0',
          xs: '#FFFFFF',
        },
        py: { xs: 6, md: 9 },
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: { xs: 3, md: 10 },
          }}
        >
          {/* Individual Counseling Section */}
          <Box sx={{ width: '100%' }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '24px', md: '32px' },
                fontWeight: 700,
                color: '#262626',
                textAlign: 'center',
                mb: { xs: 4, md: 6 },
              }}
            >
              個人カウンセリング料金
            </Typography>

            {/* Cards */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%',
              }}
            >
              {/* 30 minutes - short consultation */}
              <PricingCard
                minutes="30"
                label="短時間の相談なら"
                description={
                  <>
                    <Box display={{ xs: 'none', md: 'block' }}>
                      すでに一度相談をしていて、
                      <br />
                      状況の報告や、
                      <br />
                      少し話を聞いてほしいときに。
                    </Box>
                    <Box display={{ xs: 'block', md: 'none' }}>
                      すでに一度相談をしていて、
                      <br />
                      状況の報告や、少し話を聞いてほしいときに。
                    </Box>
                  </>
                }
                price="4,000"
                maxPrice="7,000"
              />

              {/* 60 minutes - follow-up */}
              <PricingCard
                minutes="60"
                label="2回目以降なら"
                description={
                  <>
                    <Box display={{ xs: 'none', md: 'block' }}>
                      <>
                        前回からの状況の変化を把握、
                        <br />
                        メンタル面のサポートや、
                        <br />
                        対応策の見直しをアドバイス。
                      </>
                    </Box>
                    <Box display={{ xs: 'block', md: 'none' }}>
                      <>
                        前回からの状況の変化を把握、 メンタル面
                        <br />
                        のサポートや、 対応策の見直しをアドバイス。
                      </>
                    </Box>
                  </>
                }
                price="8,000"
                maxPrice="12,000"
              />

              {/* 90 minutes - recommended for first time */}
              <PricingCard
                minutes="90"
                label="初回におすすめ"
                description={
                  <>
                    <Box display={{ xs: 'none', md: 'block' }}>
                      <>
                        一人では解決できない
                        <br />
                        悩みを伺いながら整理して、
                        <br />
                        具体的な解決策をアドバイス。
                      </>
                    </Box>
                    <Box display={{ xs: 'block', md: 'none' }}>
                      <>
                        一人では解決できない悩みを伺いながら整
                        <br />
                        理して、具体的な解決策をアドバイス。
                      </>
                    </Box>
                  </>
                }
                price="12,000"
                maxPrice="21,000"
                isRecommended={true}
              />

              {/* 120 minutes - in-depth */}
              <PricingCard
                minutes="120"
                label="じっくり深くなら"
                description={
                  <>
                    <Box display={{ xs: 'none', md: 'block' }}>
                      <>
                        深い問題や複数の課題のために、
                        <br />
                        オーダーメイドで解決策を提案。
                        <br />
                        長期的な視点も含めてアドバイス。
                      </>
                    </Box>
                    <Box display={{ xs: 'block', md: 'none' }}>
                      <>
                        深い問題や複数の課題のために、
                        オーダーメイドで解決策を提案。
                        長期的な視点も含めてアドバイス。
                      </>
                    </Box>
                  </>
                }
                price="16,000"
                maxPrice="28,000"
              />

              {/* 30 minutes - extension */}
              <PricingCard
                minutes="30"
                label="延長"
                description={
                  <>
                    時間が足りない時には、
                    <br />
                    30分単位で延長可能。
                  </>
                }
                price="4,000"
                maxPrice="7,000"
                isExtension={true}
              />
            </Box>

            <Typography
              sx={{
                fontSize: { xs: '12px', md: '14px' },
                fontWeight: 500,
                color: '#262626',
                textAlign: 'center',
                mt: 2,
              }}
            >
              ※各カウンセリング料金は専門家により異なります。詳しくは各専門家ページをご確認ください。価格は消費税込。
            </Typography>
          </Box>

          {/* Couple Counseling Section */}
          <Box sx={{ width: '100%' }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '24px', md: '32px' },
                fontWeight: 700,
                color: '#262626',
                textAlign: 'center',
                mb: { xs: 4, md: 6 },
              }}
            >
              夫婦カウンセリング料金
            </Typography>

            {/* Cards */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%',
              }}
            >
              {/* 60 minutes - follow-up */}
              <PricingCard
                minutes="60"
                label="フォローアップ"
                description={
                  <>
                    複数回のカウンセリングを通して
                    <br />
                    明確になった課題への対応の
                    <br />
                    進捗確認や対応策のアドバイス。
                  </>
                }
                price="10,000"
                maxPrice="16,000"
              />

              {/* 90 minutes - follow-up */}
              <PricingCard
                minutes="90"
                label="2回目以降なら"
                description={
                  <>
                    前回からの状況の変化や、
                    <br />
                    実践した内容の振り返り。
                    <br />
                    対応策の見直しをアドバイス。
                  </>
                }
                price="15,000"
                maxPrice="24,000"
              />

              {/* 120 minutes - recommended for first time */}
              <PricingCard
                minutes="120"
                label="初回におすすめ"
                description={
                  <>
                    夫婦の議論が平行線の方へ、
                    <br />
                    夫婦それぞれの面談をした後、
                    <br />
                    お二人での面談など柔軟に実施。
                  </>
                }
                price="20,000"
                maxPrice="32,000"
                isRecommended={true}
              />

              {/* 30 minutes - extension */}
              <PricingCard
                minutes="30"
                label="延長"
                description={
                  <>
                    時間が足りない時には、
                    <br />
                    30分単位で延長可能。
                  </>
                }
                price="5,000"
                maxPrice="9,000"
                isExtension={true}
              />
            </Box>

            <Typography
              sx={{
                fontSize: { xs: '12px', md: '14px' },
                fontWeight: 500,
                color: '#262626',
                textAlign: 'center',
                mt: 2,
              }}
            >
              ※各カウンセリング料金は専門家により異なります。詳しくは各専門家ページをご確認ください。価格は消費税込。
            </Typography>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default PricingSection;

import type { Theme } from '@mui/material';
import type { SxProps } from '@mui/system';

type StylesType = Record<string, SxProps<Theme>>;

export const styles: StylesType = {
  expertsWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    padding: { xs: '40px 0', md: '72px 0' },
    gap: { xs: 4, md: 6 },
  },
  titleSection: {
    display: 'flex',
    alignItems: 'flex-end',
    gap: '8px',
  },
  logoContainer: {
    width: 111,
    height: 48,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    color: '#FEA500',
    fontWeight: 700,
    fontSize: { xs: '24px', md: '32px' },
    lineHeight: 1.4,
  },
  subtitleText: {
    color: '#262626',
    fontWeight: 700,
    fontSize: { xs: '18px', md: '24px' },
    lineHeight: 1.4,
    textAlign: 'center',
    maxWidth: { xs: '90%', md: '800px' },
    marginTop: { xs: 3, md: '48px' },
    marginBottom: { xs: 3, md: '48px' },
  },
  contentContainer: {
    display: 'flex',
    flexDirection: { xs: 'column', md: 'row' },
    gap: { xs: 4, md: 6 },
    width: '100%',
    maxWidth: '1200px',
  },
  expertCard: {
    background: '#FFFAF0',
    borderRadius: 2,
    padding: { xs: '24px 20px', md: '32px 50px' },
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 3,
    flex: 1,
  },
  titleContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: 3,
  },
  iconContainer: {
    width: 48,
    height: 48,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 'auto',
    position: 'relative',
  },
  textContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: 0.5,
  },
  roleSubtitle: {
    color: (theme) => theme.palette.primary.main,
    fontWeight: 700,
    fontSize: '18px',
    lineHeight: 1.4,
  },
  roleTitle: {
    color: '#262626',
    fontWeight: 700,
    fontSize: '24px',
    lineHeight: 1.4,
  },
  expertsGrid: {
    display: 'grid',
    gridTemplateColumns: { xs: 'repeat(4, 1fr)', md: 'repeat(4, 1fr)' },
    gap: { xs: 2, md: 4 },
    justifyContent: 'center',
    width: '100%',
  },
  expertItem: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 1,
    width: '100%',
    cursor: 'pointer',
    transition: 'transform 0.2s ease, opacity 0.2s ease',
    '&:hover': {
      transform: 'translateY(-2px)',
      opacity: 0.8,
    },
  },
  avatar: {
    width: {
      md: '72px',
      xs: '64px',
    },
    height: {
      md: '72px',
      xs: '64px',
    },
    borderRadius: '50%',
    border: '0.5px solid #D8D9DA',
    overflow: 'hidden',
  },
  expertRole: {
    color: (theme) => theme.palette.primary.main,
    fontWeight: 500,
    fontSize: '10px',
    lineHeight: 1.2,
    textAlign: 'center',
  },
  expertName: {
    color: '#262626',
    fontWeight: 700,
    fontSize: '12px',
    lineHeight: 1.3,
    textAlign: 'center',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: '100%',
    maxWidth: {
      xs: '64px',
      md: '72px',
    },
  },
  button: {
    borderRadius: '9999px',
    padding: '12px 16px',
    fontWeight: 700,
    fontSize: '16px',
    lineHeight: 1.5,
    textTransform: 'none',
    width: { xs: '100%', sm: '350px' },
    height: '48px',
  },
  counselorButton: {
    backgroundColor: '#FF5400',
    color: '#FFFFFF',
    '&:hover': {
      backgroundColor: '#E34D00',
    },
  },
  lawyerButton: {
    backgroundColor: '#009285',
    color: '#FFFFFF',
    '&:hover': {
      backgroundColor: '#007F75',
    },
  },
  registerButton: {
    backgroundColor: '#FFFFFF',
    color: '#FF5400',
    border: '1px solid #FF7A33',
    '&:hover': {
      backgroundColor: '#FFF5F0',
    },
    marginTop: { xs: 3, md: 6 },
  },
};

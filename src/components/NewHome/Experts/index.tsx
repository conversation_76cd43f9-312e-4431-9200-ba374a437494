import { Box, Button, Container, Typography } from '@mui/material';
import { useFetchList } from 'hooks';
import type { ICounselorItem, ILawyerItem } from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  PROVIDER_LIMIT,
  PROVIDER_LIST_PATH,
  ProviderType,
} from 'utils/constants';

import { styles } from './styles';

interface ExpertsProps {
  counselorConsultationIds?: string[];
  lawyerConsultationIds?: string[];
}

const Experts = ({
  counselorConsultationIds,
  lawyerConsultationIds,
}: ExpertsProps = {}) => {
  // Fetch lawyer data
  const { list: lawyerList, isLoading: isLawyerLoading } =
    useFetchList<ILawyerItem>({
      ...providerQuery.providerList(ProviderType.LAWYER),
      omitKeys: ['id'],
      customParams: {
        providerType: [ProviderType.LAWYER],
        limit: PROVIDER_LIMIT,
        consultationField:
          lawyerConsultationIds && lawyerConsultationIds.length > 0
            ? lawyerConsultationIds
            : undefined,
      },
    });

  // Fetch counselor data
  const { list: counselorList, isLoading: isCounselorLoading } =
    useFetchList<ICounselorItem>({
      ...providerQuery.providerList(ProviderType.COUNSELOR),
      omitKeys: ['id'],
      customParams: {
        providerType: [ProviderType.COUNSELOR],
        limit: PROVIDER_LIMIT,
        consultationField:
          counselorConsultationIds && counselorConsultationIds.length > 0
            ? counselorConsultationIds
            : undefined,
      },
    });

  // Use only the first 4 items from each list
  const displayLawyers = lawyerList.slice(0, 4);
  const displayCounselors = counselorList.slice(0, 4);

  const router = useRouter();

  return (
    <Box sx={styles.expertsWrapper}>
      <Container maxWidth="lg">
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {/* Title Section */}
          <Box sx={styles.titleSection}>
            <Box sx={styles.logoContainer}>
              <Image
                src="/images/experts/expert-logo.svg"
                alt="Logo"
                width={111}
                height={48}
              />
            </Box>
            <Typography variant="h2" sx={styles.titleText}>
              専門家
            </Typography>
          </Box>

          {/* Subtitle */}
          <Typography sx={styles.subtitleText}>
            夫婦問題・離婚の相談を多く手がける、悩みに寄り添う信頼の専門家
          </Typography>

          {/* Content */}
          <Box sx={styles.contentContainer}>
            {/* Counselor Card */}
            <Box sx={styles.expertCard}>
              <Box sx={styles.titleContainer}>
                <Box sx={styles.iconContainer}>
                  <Image
                    src="/images/search/counselor-icon.svg"
                    alt="カウンセラー"
                    width={50}
                    height={50}
                    style={{
                      display: 'block',
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                    }}
                  />
                </Box>
                <Box sx={styles.textContainer}>
                  <Typography sx={{ ...styles.roleSubtitle, color: '#FF5400' }}>
                    心の整理なら
                  </Typography>
                  <Typography sx={styles.roleTitle}>カウンセラー</Typography>
                </Box>
              </Box>

              <Box sx={styles.expertsGrid}>
                {isCounselorLoading
                  ? Array(4)
                      .fill(0)
                      .map((_, index) => (
                        <Box key={index} sx={styles.expertItem}>
                          <Box sx={styles.avatar}>
                            <Box
                              sx={{
                                width: 72,
                                height: 72,
                                backgroundColor: '#eee',
                                borderRadius: '50%',
                              }}
                            />
                          </Box>
                          <Typography
                            sx={{ ...styles.expertRole, color: '#FF5400' }}
                          >
                            カウンセラー
                          </Typography>
                          <Typography sx={styles.expertName}>
                            ロード中...
                          </Typography>
                        </Box>
                      ))
                  : displayCounselors.map((expert, index) => (
                      <Box
                        key={expert._id || index}
                        sx={styles.expertItem}
                        onClick={() =>
                          router.push(
                            `${PROVIDER_LIST_PATH.COUNSELOR}/${expert._id}`,
                          )
                        }
                        className="clickable-item"
                      >
                        <Box sx={styles.avatar}>
                          <Image
                            src={
                              expert.images?.[0]?.originUrl ||
                              '/images/placeholder-avatar.jpg'
                            }
                            alt={expert.fullName}
                            width={72}
                            height={72}
                            style={{
                              width: '100%',
                              height: '100%',
                            }}
                          />
                        </Box>
                        <Typography
                          sx={{ ...styles.expertRole, color: '#FF5400' }}
                        >
                          カウンセラー
                        </Typography>
                        <Typography sx={styles.expertName}>
                          {expert.nickname || expert.fullName}
                        </Typography>
                      </Box>
                    ))}
              </Box>

              <Link
                href="/counselors"
                passHref
                style={{ width: '100%', textAlign: 'center' }}
              >
                <Button
                  fullWidth
                  variant="contained"
                  sx={{
                    borderRadius: '9999px',
                    padding: '12px 16px',
                    fontWeight: 700,
                    fontSize: '16px',
                    lineHeight: 1.5,
                    textTransform: 'none',
                    width: { xs: '100%', sm: '350px' },
                    height: '48px',
                    backgroundColor: '#FF5400',
                    color: '#FFFFFF',
                    '&:hover': {
                      backgroundColor: '#E34D00',
                    },
                  }}
                >
                  カウンセラーを探す
                </Button>
              </Link>
            </Box>

            {/* Lawyer Card */}
            <Box sx={styles.expertCard}>
              <Box sx={styles.titleContainer}>
                <Box sx={styles.iconContainer}>
                  <Image
                    src="/images/search/lawyer-icon.svg"
                    alt="弁護士"
                    width={50}
                    height={50}
                    style={{
                      display: 'block',
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                    }}
                  />
                </Box>
                <Box sx={styles.textContainer}>
                  <Typography sx={{ ...styles.roleSubtitle, color: '#009285' }}>
                    離婚の手続きなら
                  </Typography>
                  <Typography sx={styles.roleTitle}>弁護士</Typography>
                </Box>
              </Box>

              <Box sx={styles.expertsGrid}>
                {isLawyerLoading
                  ? Array(4)
                      .fill(0)
                      .map((_, index) => (
                        <Box key={index} sx={styles.expertItem}>
                          <Box sx={styles.avatar}>
                            <Box
                              sx={{
                                width: 72,
                                height: 72,
                                backgroundColor: '#eee',
                                borderRadius: '50%',
                              }}
                            />
                          </Box>
                          <Typography
                            sx={{ ...styles.expertRole, color: '#009285' }}
                          >
                            弁護士
                          </Typography>
                          <Typography sx={styles.expertName}>
                            ロード中...
                          </Typography>
                        </Box>
                      ))
                  : displayLawyers.map((expert, index) => (
                      <Box
                        key={expert._id || index}
                        sx={styles.expertItem}
                        onClick={() =>
                          router.push(
                            `${PROVIDER_LIST_PATH.LAWYER}/${expert._id}`,
                          )
                        }
                        className="clickable-item"
                      >
                        <Box sx={styles.avatar}>
                          <Image
                            src={
                              expert.images?.[0]?.originUrl ||
                              '/images/placeholder-avatar.jpg'
                            }
                            alt={expert.fullName}
                            width={72}
                            height={72}
                            style={{
                              width: '100%',
                              height: '100%',
                            }}
                          />
                        </Box>
                        <Typography
                          sx={{ ...styles.expertRole, color: '#009285' }}
                        >
                          弁護士
                        </Typography>
                        <Typography sx={styles.expertName}>
                          {expert.fullName}
                        </Typography>
                      </Box>
                    ))}
              </Box>

              <Link
                href="/lawyers"
                passHref
                style={{ width: '100%', textAlign: 'center' }}
              >
                <Button
                  fullWidth
                  variant="contained"
                  sx={{
                    borderRadius: '9999px',
                    padding: '12px 16px',
                    fontWeight: 700,
                    fontSize: '16px',
                    lineHeight: 1.5,
                    textTransform: 'none',
                    width: { xs: '100%', sm: '350px' },
                    height: '48px',
                    backgroundColor: '#009285',
                    color: '#FFFFFF',
                    '&:hover': {
                      backgroundColor: '#007F75',
                    },
                  }}
                >
                  弁護士を探す
                </Button>
              </Link>
            </Box>
          </Box>

          {/* Register Button */}
          <Box
            sx={{
              width: '100%',
              maxWidth: '1200px',
              display: 'flex',
              justifyContent: 'center',
              padding: { xs: '0 20px', md: '0 50px' },
            }}
          >
            <Link
              href="/contact-us"
              passHref
              style={{ width: '100%', textAlign: 'center' }}
            >
              <Button
                fullWidth
                variant="outlined"
                sx={{
                  borderRadius: '9999px',
                  padding: '12px 16px',
                  fontWeight: 700,
                  fontSize: '16px',
                  lineHeight: 1.5,
                  textTransform: 'none',
                  width: { xs: '100%', sm: '350px' },
                  height: '48px',
                  backgroundColor: '#FFFFFF',
                  color: '#FF5400',
                  border: '1px solid #FF7A33',
                  '&:hover': {
                    backgroundColor: '#FFF5F0',
                  },
                  marginTop: { xs: 3, md: 6 },
                }}
              >
                専門家の登録はこちら
              </Button>
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Experts;

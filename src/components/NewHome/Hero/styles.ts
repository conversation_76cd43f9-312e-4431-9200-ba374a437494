import type { Theme } from '@mui/material';
import type { SxProps } from '@mui/system';

type StylesType = Record<string, SxProps<Theme>>;

export const styles: StylesType = {
  heroWrapper: {
    background:
      'linear-gradient(90deg, #FFD700 0%, #FF9700 32.28%, #FF7800 100%)',
    overflow: 'hidden',
  },
  container: {
    px: { xs: 2, md: 3 },
  },
  contentContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: { xs: 3, md: 4 },
    height: '100%',
    justifyContent: 'center',
    py: { xs: '40px', md: 6 },
    position: 'relative',
    zIndex: 2,
  },
  mainHeading: {
    color: '#262626',
    fontSize: { xs: '28px', sm: '32px', md: '36px' },
    fontWeight: 700,
    lineHeight: 1.4,
  },
  subHeading: {
    color: '#FFFFFF',
    fontSize: { xs: '16px', md: '16px' },
    fontWeight: 700,
    lineHeight: 1.5,
  },
  counselorLawyerSection: {
    display: 'flex',
    alignItems: 'center',
    gap: 1,
  },
  counselorCircle: {
    width: 100,
    height: 100,
    borderRadius: '50%',
    bgcolor: '#FF5400',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  lawyerCircle: {
    width: 100,
    height: 100,
    borderRadius: '50%',
    bgcolor: '#009285',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  iconContainer: {
    position: 'absolute',
    inset: 0,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  roleText: {
    color: '#FFFFFF',
    fontSize: '14px',
    fontWeight: 700,
    textAlign: 'center',
    zIndex: 2,
    position: 'relative',
  },
  iconClose: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
};

import { Box, Container, Grid, Stack, Typography } from '@mui/material';
import Image from 'next/image';

// @ts-ignore
import { styles } from './styles';

const Hero = () => {
  const imgs = [
    '/images/hero/hero1.png',
    '/images/hero/hero2.png',
    '/images/hero/hero3.png',
    '/images/hero/hero4.png',
    '/images/hero/hero5.png',
  ];

  return (
    <Box sx={styles.heroWrapper}>
      <Container maxWidth="lg" disableGutters sx={styles.container}>
        <Grid container spacing={0}>
          <Grid item xs={12} md={5}>
            <Box sx={styles.contentContainer}>
              <Typography variant="h1" sx={styles.mainHeading}>
                離婚したい、
                <br />
                したくない、
                <br />
                ぜんぶ。
              </Typography>

              <Box sx={styles.counselorLawyerSection}>
                <Box sx={styles.counselorCircle}>
                  <Box sx={styles.iconContainer}>
                    <Image
                      src="/images/hero/counselor-icon.svg"
                      alt="カウンセラー"
                      width={52}
                      height={52}
                      style={{ filter: 'brightness(0) invert(1)', opacity: 1 }}
                    />
                  </Box>
                  <Typography sx={styles.roleText}>カウンセラー</Typography>
                </Box>

                <Box sx={styles.iconClose}>
                  <Image
                    src="/images/hero/icon-close.svg"
                    alt="+"
                    width={24}
                    height={24}
                  />
                </Box>

                <Box sx={styles.lawyerCircle}>
                  <Box sx={styles.iconContainer}>
                    <Image
                      src="/images/hero/lawyer-icon.svg"
                      alt="弁護士"
                      width={52}
                      height={52}
                      style={{ filter: 'brightness(0) invert(1)', opacity: 1 }}
                    />
                  </Box>
                  <Typography sx={styles.roleText}>弁護士</Typography>
                </Box>
              </Box>

              <Typography
                sx={styles.subHeading}
                display={{ xs: 'none', md: 'block' }}
              >
                夫婦問題の心の整理から
                <br />
                離婚手続きまで、
                <br />
                夫婦の悩みをぜんぶサポート
              </Typography>
              <Typography
                sx={styles.subHeading}
                display={{ xs: 'block', md: 'none' }}
              >
                夫婦問題の心の整理から
                <br />
                離婚手続きまで、夫婦の
                <br />
                悩みをぜんぶサポート
              </Typography>

              <Box
                display={{ xs: 'block', md: 'none' }}
                position={'absolute'}
                bottom={0}
                right={-16}
                zIndex={-1}
                height="420px"
              >
                <Image
                  src="/images/hero/hero_larger_2.png"
                  alt="herolager"
                  width={256}
                  height={420}
                  quality={100}
                  style={{
                    width: '256px',
                    height: '100%',
                  }}
                  priority
                />
              </Box>
            </Box>
          </Grid>

          <Grid
            item
            xs={12}
            md={7}
            sx={{
              display: {
                xs: 'none',
                md: 'flex',
              },
              justifyContent: 'flex-end',
            }}
          >
            <Box sx={{ width: '825px', height: '500px', position: 'relative' }}>
              <Image
                src="/images/hero/hero_persons.png"
                alt="Happy Couple"
                width={1025}
                height={700}
                quality={100}
                style={{
                  width: '825px',
                  height: '500px',
                  objectFit: 'cover',
                  objectPosition: 'center',
                }}
                priority
              />
            </Box>
          </Grid>
        </Grid>
      </Container>
      <Stack
        sx={{
          display: {
            md: 'none',
          },
        }}
        direction={'row'}
        spacing={0}
      >
        {imgs.map((img, index) => {
          return (
            <Box
              key={img}
              sx={{
                width: '100%',
                position: 'relative',
                display: 'inline-block',
                background:
                  index !== imgs.length - 1
                    ? 'linear-gradient(to right, #FFD700, #FF7800);'
                    : 'linear-gradient(to left, #FFD700, #FF7800)',
              }}
            >
              <Image
                quality={100}
                src={img}
                alt={`Hero Image ${index + 1}`}
                width={175}
                height={206}
                style={{
                  width: '100%',
                  height: '100%',
                }}
                priority
              />
            </Box>
          );
        })}
      </Stack>
    </Box>
  );
};

export default Hero;

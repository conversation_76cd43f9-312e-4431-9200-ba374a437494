import type { Theme } from '@mui/material';
import type { SxProps } from '@mui/system';

type StylesType = Record<string, SxProps<Theme>>;

export const styles: StylesType = {
  searchWrapper: {
    background: 'linear-gradient(90deg, #FFD700 0%, #FF9700 50%, #FF7800 100%)',
    py: { xs: 5, md: 9 },
  },
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 0,
  },
  sectionHeading: {
    color: '#262626',
    fontSize: { xs: '24px', md: '24px' },
    fontWeight: 700,
    textAlign: 'center',
    mb: { xs: 4, md: 4 },
  },
  card: {
    backgroundColor: '#FFFAF0',
    borderRadius: '16px',
    p: { xs: 3, md: 4 },
    display: 'flex',
    flexDirection: 'column',
    gap: 3,
    height: '100%',
  },
  cardHeader: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 3,
  },
  iconContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: 0.5,
  },
  cardSubtitleCounselor: {
    color: '#FF5400',
    fontSize: '18px',
    fontWeight: 700,
    lineHeight: 1.4,
  },
  cardSubtitleLawyer: {
    color: '#009285',
    fontSize: '18px',
    fontWeight: 700,
    lineHeight: 1.4,
  },
  cardTitle: {
    color: '#262626',
    fontSize: '24px',
    fontWeight: 700,
    lineHeight: 1.4,
  },
  cardContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: 1.5,
    alignItems: 'center',
    px: 0,
  },
  dateContainer: {
    display: 'flex',
    flexDirection: 'row',
    gap: 2,
    width: '100%',
    maxWidth: '350px',
  },
  dateButton: {
    border: '1px solid #FF5400',
    borderRadius: '6px',
    padding: '6px 10px',
    backgroundColor: '#FFFFFF',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    flex: 1,
    '&:hover': {
      backgroundColor: '#FFF0E6',
    },
  },
  selectButtonCounselor: {
    border: '1px solid #FF5400',
    borderRadius: '6px',
    padding: '6px 10px',
    backgroundColor: '#FFFFFF',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 1.5,
    cursor: 'pointer',
    width: '100%',
    maxWidth: '350px',
    '&:hover': {
      backgroundColor: '#FFF0E6',
    },
  },
  selectButtonLawyer: {
    border: '1px solid #009285',
    borderRadius: '6px',
    padding: '6px 10px',
    backgroundColor: '#FFFFFF',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 1.5,
    cursor: 'pointer',
    width: '100%',
    maxWidth: '350px',
    '&:hover': {
      backgroundColor: '#E6F7F6',
    },
  },
  buttonText: {
    color: '#262626',
    fontSize: '14px',
    fontWeight: 700,
  },
  buttonTextSingleLine: {
    color: '#262626',
    fontSize: '14px',
    fontWeight: 700,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxWidth: '100%',
  },
  counselorSearchButton: {
    backgroundColor: '#FF5400',
    color: '#FFFFFF',
    fontWeight: 700,
    borderRadius: '333px',
    padding: '12px 16px',
    fontSize: '16px',
    mt: 2,
    width: '100%',
    maxWidth: '350px',
    '&:hover': {
      backgroundColor: '#E64C00',
    },
  },
  lawyerSearchButton: {
    backgroundColor: '#009285',
    color: '#FFFFFF',
    fontWeight: 700,
    borderRadius: '333px',
    padding: '12px 16px',
    fontSize: '16px',
    mt: 2,
    width: '100%',
    maxWidth: '350px',
    '&:hover': {
      backgroundColor: '#007E73',
    },
  },
  lineButton: {
    backgroundColor: '#FFFFFF',
    color: '#262626',
    fontWeight: 700,
    borderRadius: '333px',
    padding: '12px 16px',
    fontSize: '16px',
    width: '100%',
    maxWidth: '350px',
    height: '48px',
    margin: 0,
    '&:hover': {
      backgroundColor: '#F5F5F5',
    },
  },
};

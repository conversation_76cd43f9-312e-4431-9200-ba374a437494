import {
  Box,
  Button,
  Container,
  Grid,
  Popover,
  TextField,
  Typography,
} from '@mui/material';
import { StaticDatePicker } from '@mui/x-date-pickers';
import ConsultationFieldDialog from 'components/Counselor/CounselorFilterPage/ConsultationFieldDialog';
import CheckboxModal from 'components/Provider/CheckboxModal';
import dayjs from 'dayjs';
import { useFetchList } from 'hooks';
import type { IListItem, IPrefecture } from 'hooks/types';
import { t } from 'i18next';
import { CoverAreaIcon, MapIcon } from 'icons';
import { isEmpty } from 'lodash';
import resourceQuery from 'models/resource/query';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useState } from 'react';
import { ProviderType } from 'utils/constants';
import { getLineUrl } from 'utils/getLineUrl';

// @ts-ignore
import { styles } from './styles';

interface ConsultationFilter {
  duration: number;
  date: Date | null;
  meetingType: string;
  consultationField: string[];
}

const SearchSection = () => {
  const router = useRouter();

  // Get theme ID from URL parameter
  const { id: themeId } = router.query;

  // Theme to counselor consultation field name mapping
  const themeConsultationNameMapping: Record<string, string> = {
    '1': '夫婦カウンセリング',
    '2': 'DV／モラハラ',
    '3': '修復',
    '4': '夫婦カウンセリング',
    '5': '夫婦カウンセリング',
    '6': '離婚',
    '7': '浮気',
    '8': 'その他 \n(離婚後の子育て、\n共同養育等)',
  };

  // Theme to lawyer consultation field names mapping (multiple fields per theme)
  const themeLawyerConsultationMapping: Record<string, string[]> = {
    '1': ['モラハラ', '男性弁護', '女性弁護'],
    '2': [
      '離婚手続き',
      '協議離婚',
      '離婚調停',
      '裁判離婚',
      '慰謝料',
      'DV',
      'モラハラ',
      '男性弁護',
      '女性弁護',
    ],
    '3': ['離婚準備', '不貞行為', 'モラハラ'],
    '4': [],
    '5': [
      '離婚準備',
      '離婚手続き',
      '協議離婚',
      '離婚調停',
      '裁判離婚',
      '慰謝料',
      '婚姻費用',
      '財産分与',
      '年金分割',
      '借金',
      '男性弁護',
      '女性弁護',
    ],
    '6': [
      '離婚準備',
      '離婚手続き',
      '協議離婚',
      '離婚調停',
      '裁判離婚',
      '婚姻費用',
    ],
    '7': [
      '離婚手続き',
      '協議離婚',
      '離婚調停',
      '裁判離婚',
      '不貞行為',
      '慰謝料',
      '男性弁護',
      '女性弁護',
    ],
    '8': [
      '離婚手続き',
      '協議離婚',
      '離婚調停',
      '裁判離婚',
      '親権',
      '面会交流',
      '養育費',
      '養育費回収',
      '男性弁護',
      '女性弁護',
    ],
  };

  const { list: consultationList } = useFetchList<IListItem>({
    ...resourceQuery.consultations,
    customParams: {
      ...resourceQuery.consultations.customParams,
      providerType: 'COUNSELOR',
    },
  });

  const { ids: prefectureIds, entities: prefectureEntities } =
    useFetchList<IPrefecture>(resourceQuery.prefectures);
  const { ids: consultationIds, entities: consultationEntities } =
    useFetchList<IPrefecture>({
      ...resourceQuery.consultations,
      customParams: {
        ...resourceQuery.consultations.customParams,
        providerType: [ProviderType.LAWYER],
      },
    });
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null,
  );

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;
  const [openDialog, setOpenDialog] = useState(false);
  const [openFilterModal, setOpenFilterModal] = useState<string | boolean>();
  const isPrefectureModal = openFilterModal === 'prefecture';
  const [counselorFilter, setCounselorFilter] = useState<ConsultationFilter>({
    duration: 30,
    date: null,
    meetingType: 'SINGLE',
    consultationField: [],
  });
  const valuesLabel = counselorFilter.consultationField.map((item) => {
    const label = consultationList
      .find((consultation) => consultation.value === item)
      ?.value?.replace(/\n/g, ' ');
    return label || item;
  });
  const [lawyerFilter, setLawyerFilter] = useState<{
    consultationField: string[];
    prefecture: string[];
  }>({
    consultationField: [],
    prefecture: [],
  });

  // Pre-populate counselor consultation field when theme and data are available
  useEffect(() => {
    if (themeId && typeof themeId === 'string' && consultationList.length > 0) {
      const consultationName = themeConsultationNameMapping[themeId];
      if (consultationName && counselorFilter.consultationField.length === 0) {
        setCounselorFilter((prev) => ({
          ...prev,
          consultationField: [consultationName],
        }));
      }
    }
  }, [
    themeId,
    consultationList,
    themeConsultationNameMapping,
    counselorFilter.consultationField.length,
  ]);

  // Pre-populate lawyer consultation field when theme and data are available
  useEffect(() => {
    if (
      themeId &&
      typeof themeId === 'string' &&
      consultationIds &&
      consultationIds.length > 0
    ) {
      const consultationFieldNames = themeLawyerConsultationMapping[themeId];
      if (
        consultationFieldNames &&
        lawyerFilter.consultationField.length === 0
      ) {
        // Map consultation field names to entity IDs
        const entityIds = consultationFieldNames
          .map((fieldName) =>
            consultationIds.find(
              (consultationId) =>
                consultationEntities[consultationId]?.value?.trim() ===
                fieldName.trim(),
            ),
          )
          .filter((entityId): entityId is string => entityId !== undefined);

        if (entityIds.length > 0) {
          setLawyerFilter((prev) => ({
            ...prev,
            consultationField: entityIds,
          }));
        }
      }
    }
  }, [
    themeId,
    consultationIds,
    consultationEntities,
    themeLawyerConsultationMapping,
    lawyerFilter.consultationField.length,
  ]);

  function handleCounselorFilterSubmit() {
    function handleFormattedDate(date: string) {
      if (!date) return '';
      const tokyoStartOfDay = dayjs.utc(date).tz('Asia/Tokyo').startOf('day');
      return tokyoStartOfDay.utc().toISOString();
    }
    const formattedDateTime = handleFormattedDate(
      counselorFilter.date?.toISOString() || '',
    );

    if (!counselorFilter.consultationField.length && !counselorFilter.date) {
      router.push({
        pathname: '/counselors/filter',
      });
      return;
    }

    router.push({
      pathname: '/counselors/filter',
      query: {
        consultationField: counselorFilter.consultationField as string[],
        date: formattedDateTime,
      },
    });
  }

  function handleDateQuickSelect(selectedDate: Date) {
    function handleFormattedDate(date: string) {
      if (!date) return '';
      const tokyoStartOfDay = dayjs.utc(date).tz('Asia/Tokyo').startOf('day');
      return tokyoStartOfDay.utc().toISOString();
    }

    const formattedDateTime = handleFormattedDate(selectedDate.toISOString());

    router.push({
      pathname: '/counselors/filter',
      query: {
        consultationField: counselorFilter.consultationField as string[],
        date: formattedDateTime,
      },
    });
  }

  function handleLawyerFilterSubmit() {
    router.push({
      pathname: '/lawyers',
      query: {
        consultationField: lawyerFilter.consultationField as string[],
        prefecture: lawyerFilter.prefecture as string[],
      },
    });
  }

  const renderFilterString = useCallback(
    (value: string[], ids: string[], entities: Record<string, IListItem>) => {
      // query not ready or not load resource yet
      if (!router.isReady || isEmpty(ids)) {
        return '';
      }
      if (isEmpty(value)) {
        return t('global.noSelected');
      }
      if (value.length === ids.length) {
        return t('global.selectAll');
      }
      return value.map((pre) => entities[pre]?.value).join(' • ');
    },
    [router.isReady],
  );

  return (
    <Box sx={styles.searchWrapper}>
      <Container maxWidth="lg">
        <Box sx={{ ...styles.container, gap: 0 }}>
          <Typography variant="h2" sx={styles.sectionHeading}>
            夫婦問題・離婚の専門家に、オンラインで気軽に相談
          </Typography>

          <Grid container spacing={4} sx={{ mb: 0 }}>
            {/* Counselor Card */}
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  ...styles.card,
                  display: 'flex',
                  flexDirection: 'column',
                  px: { xs: 3, md: '50px' },
                  py: { xs: 4, md: 4 },
                }}
              >
                <Box sx={styles.cardHeader}>
                  <Box sx={styles.iconContainer}>
                    <Image
                      src="/images/search/counselor-icon.svg"
                      alt="Counselor"
                      width={48}
                      height={48}
                    />
                  </Box>
                  <Box sx={styles.titleContainer}>
                    <Typography sx={styles.cardSubtitleCounselor}>
                      心の整理なら
                    </Typography>
                    <Typography sx={styles.cardTitle}>カウンセラー</Typography>
                  </Box>
                </Box>

                <Box
                  sx={{
                    ...styles.cardContent,
                    flex: 1,
                    justifyContent: 'space-between',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 1.5,
                      alignItems: 'center',
                      width: '100%',
                    }}
                  >
                    <Box sx={styles.dateContainer}>
                      <Box
                        component="button"
                        sx={styles.dateButton}
                        onClick={() => {
                          handleDateQuickSelect(new Date());
                        }}
                      >
                        <Typography sx={styles.buttonText}>今日</Typography>
                      </Box>
                      <Box
                        component="button"
                        sx={styles.dateButton}
                        onClick={() => {
                          const tomorrow = new Date();
                          tomorrow.setDate(tomorrow.getDate() + 1);
                          handleDateQuickSelect(tomorrow);
                        }}
                      >
                        <Typography sx={styles.buttonText}>明日</Typography>
                      </Box>
                    </Box>

                    <Box
                      component="button"
                      sx={styles.selectButtonCounselor}
                      aria-describedby={id}
                      onClick={handleClick}
                    >
                      <Image
                        src="/images/search/calendar-icon.svg"
                        alt="Calendar"
                        width={20}
                        height={20}
                      />
                      <Typography sx={styles.buttonText}>
                        {counselorFilter.date ? (
                          <>
                            {dayjs(counselorFilter.date).format(
                              'YYYY年MM月DD日 (ddd)',
                            )}
                          </>
                        ) : (
                          '日付を選択'
                        )}
                      </Typography>
                    </Box>
                    <Popover
                      anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'center',
                      }}
                      transformOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                      }}
                      id={id}
                      open={open}
                      anchorEl={anchorEl}
                      onClose={handleClose}
                    >
                      <StaticDatePicker
                        renderInput={(params) => <TextField {...params} />}
                        value={counselorFilter.date}
                        inputFormat="YYYY年MM月DD日 (ddd)"
                        minDate={new Date()}
                        maxDate={
                          new Date(
                            new Date().setDate(new Date().getDate() + 42),
                          )
                        }
                        components={{
                          ActionBar: () => null,
                        }}
                        onChange={(newValue) => {
                          setCounselorFilter({
                            ...counselorFilter,
                            date: newValue,
                          });
                        }}
                        showToolbar={false}
                        showDaysOutsideCurrentMonth={false}
                        disableHighlightToday
                      />
                    </Popover>
                    <Box
                      component="button"
                      sx={styles.selectButtonCounselor}
                      onClick={() => {
                        setOpenDialog(true);
                      }}
                    >
                      <Image
                        src="/images/search/chat-icon.svg"
                        alt="Chat"
                        width={20}
                        height={20}
                      />
                      <Typography sx={styles.buttonTextSingleLine}>
                        {valuesLabel.length > 0
                          ? `${valuesLabel.join(' • ')}`
                          : '相談テーマを選択'}
                      </Typography>
                    </Box>
                    {openDialog && (
                      <ConsultationFieldDialog
                        value={counselorFilter.consultationField}
                        consultationList={consultationList}
                        open={openDialog}
                        onClose={() => setOpenDialog(false)}
                        onClear={() => {
                          setCounselorFilter({
                            ...counselorFilter,
                            consultationField: [],
                          });
                        }}
                        onAgree={(values) => {
                          setCounselorFilter({
                            ...counselorFilter,
                            consultationField: values,
                          });
                          setOpenDialog(false);
                        }}
                        onCancel={() => {
                          setOpenDialog(false);
                        }}
                      />
                    )}
                  </Box>

                  <Button
                    onClick={() => {
                      handleCounselorFilterSubmit();
                    }}
                    variant="contained"
                    sx={{
                      ...styles.counselorSearchButton,
                      mb: 0,
                    }}
                    fullWidth
                  >
                    カウンセラーを探す
                  </Button>
                </Box>
              </Box>
            </Grid>

            {/* Lawyer Card */}
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  ...styles.card,
                  display: 'flex',
                  flexDirection: 'column',
                  px: { xs: 3, md: '50px' },
                  py: { xs: 4, md: 4 },
                }}
              >
                <Box sx={styles.cardHeader}>
                  <Box sx={styles.iconContainer}>
                    <Image
                      src="/images/search/lawyer-icon.svg"
                      alt="Lawyer"
                      width={48}
                      height={48}
                    />
                  </Box>
                  <Box sx={styles.titleContainer}>
                    <Typography sx={styles.cardSubtitleLawyer}>
                      離婚の手続きなら
                    </Typography>
                    <Typography sx={styles.cardTitle}>弁護士</Typography>
                  </Box>
                </Box>

                <Box
                  sx={{
                    ...styles.cardContent,
                    flex: 1,
                    justifyContent: 'space-between',
                    mt: '21px',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 1.5,
                      alignItems: 'center',
                      width: '100%',
                    }}
                  >
                    <Box
                      component="button"
                      sx={styles.selectButtonLawyer}
                      onClick={() => {
                        setOpenFilterModal('prefecture');
                      }}
                    >
                      <Image
                        src="/images/search/location-icon.svg"
                        alt="Location"
                        width={20}
                        height={20}
                      />
                      <Typography sx={styles.buttonTextSingleLine}>
                        {lawyerFilter.prefecture.length > 0 ? (
                          <>
                            {renderFilterString(
                              lawyerFilter.prefecture,
                              prefectureIds,
                              prefectureEntities,
                            )}
                          </>
                        ) : (
                          'エリアで選択'
                        )}
                      </Typography>
                    </Box>

                    <Box
                      component="button"
                      sx={styles.selectButtonLawyer}
                      onClick={() => {
                        setOpenFilterModal('consultationField');
                      }}
                    >
                      <Image
                        src="/images/search/topic-icon.svg"
                        alt="Topic"
                        width={20}
                        height={20}
                      />
                      <Typography sx={styles.buttonTextSingleLine}>
                        {lawyerFilter.consultationField.length > 0 ? (
                          <>
                            {renderFilterString(
                              lawyerFilter.consultationField,
                              consultationIds,
                              consultationEntities,
                            )}
                          </>
                        ) : (
                          '相談テーマを選択'
                        )}
                      </Typography>
                    </Box>
                  </Box>

                  <Button
                    variant="contained"
                    onClick={handleLawyerFilterSubmit}
                    sx={{
                      ...styles.lawyerSearchButton,
                      mb: 0,
                    }}
                    fullWidth
                  >
                    弁護士を探す
                  </Button>
                </Box>

                {openFilterModal && (
                  <CheckboxModal
                    icon={isPrefectureModal ? <MapIcon /> : <CoverAreaIcon />}
                    title={
                      isPrefectureModal
                        ? t('lawyerList.prefecture')
                        : t('lawyerList.consultationField')
                    }
                    open={!!openFilterModal}
                    entities={
                      isPrefectureModal
                        ? prefectureEntities
                        : consultationEntities
                    }
                    onClose={() => setOpenFilterModal(false)}
                    ids={isPrefectureModal ? prefectureIds : consultationIds}
                    defaultValues={
                      isPrefectureModal
                        ? lawyerFilter.prefecture
                        : lawyerFilter.consultationField
                    }
                    onSubmit={(values) => {
                      if (isPrefectureModal) {
                        setLawyerFilter({
                          ...lawyerFilter,
                          prefecture: values,
                        });
                      } else {
                        setLawyerFilter({
                          ...lawyerFilter,
                          consultationField: values,
                        });
                      }
                      setOpenFilterModal(false);
                    }}
                  />
                )}
              </Box>
            </Grid>
          </Grid>

          {/* LINE Button with exact 48px spacing */}
          <Box
            sx={{
              mt: { xs: '32px', md: '48px' },
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <Button
              variant="contained"
              startIcon={
                <Image
                  src="/images/search/line-button-icon.svg"
                  alt="LINE"
                  width={24}
                  height={24}
                />
              }
              sx={styles.lineButton}
              onClick={() => {
                window.open(getLineUrl(), '_blank');
              }}
            >
              LINEで相談&予約する
            </Button>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default SearchSection;

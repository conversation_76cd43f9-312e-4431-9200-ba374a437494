import { Box, Button, Container, Typography } from '@mui/material';
import { useFetchList } from 'hooks';
import type { INewsItem } from 'models/news/interface';
import newsQuery from 'models/news/query';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react';

interface NewsItemProps {
  date: string;
  content: React.ReactNode;
  id: string;
}

const NewsItem: React.FC<NewsItemProps> = ({ date, content, id }) => {
  const handleContentClick = (e: React.MouseEvent) => {
    // If the clicked element is a link or inside a link, stop propagation
    const target = e.target as HTMLElement;
    const clickedLink = target.closest('a');
    if (clickedLink) {
      e.stopPropagation();
    }
  };

  return (
    <Box
      component={Link}
      href={`/news/${id}`}
      sx={{
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        alignItems: 'flex-start',
        width: '100%',
        gap: { xs: 1, md: '50px' },
        py: 2,
        borderBottom: '1px solid #FF5400',
        textDecoration: 'none',
        cursor: 'pointer',
        '&:hover': {
          bgcolor: 'rgba(255, 84, 0, 0.05)',
        },
      }}
    >
      <Typography
        sx={{
          fontSize: '16px',
          fontWeight: 500,
          color: '#262626',
          minWidth: '120px',
          flexShrink: 0,
          lineHeight: 1.5,
        }}
      >
        {date}
      </Typography>
      <Box sx={{ flex: 1 }} onClick={handleContentClick}>
        {content}
      </Box>
    </Box>
  );
};

const NewsSection = () => {
  const router = useRouter();
  // Fetch news using the custom hook
  const { list: newsList, isLoading } = useFetchList<INewsItem>({
    ...newsQuery.newsList,
  });

  // Process HTML content to fix incomplete URLs and normalize for consistent height
  const processHtmlContent = (htmlContent: string) => {
    if (!htmlContent) return '';

    // First decode HTML entities
    const textArea = document.createElement('textarea');
    textArea.innerHTML = htmlContent;
    let decodedContent = textArea.value;

    // Replace href attributes that don't start with http://, https://, mailto:, tel:, or #
    decodedContent = decodedContent.replace(
      /href="([^"]*?)"/g,
      (match, url) => {
        // Skip if URL already has protocol, is relative (starts with /), is anchor (starts with #), or is special protocol
        if (
          url.startsWith('http://') ||
          url.startsWith('https://') ||
          url.startsWith('mailto:') ||
          url.startsWith('tel:') ||
          url.startsWith('#') ||
          url.startsWith('/')
        ) {
          return match;
        }

        // Add https:// to incomplete URLs
        return `href="https://${url}"`;
      },
    );

    // Add target="_blank" and rel="noopener noreferrer" to external links for security
    decodedContent = decodedContent.replace(
      /<a\s+([^>]*?)href="([^"]*?)"([^>]*?)>/gi,
      (match, beforeHref, url, afterHref) => {
        // Check if it's an external link
        const isExternal =
          url.startsWith('http://') ||
          url.startsWith('https://') ||
          url.startsWith('mailto:') ||
          url.startsWith('tel:');

        if (isExternal) {
          // Add target and rel attributes if not already present
          let attributes = beforeHref + afterHref;
          if (!attributes.includes('target=')) {
            attributes += ' target="_blank"';
          }
          if (!attributes.includes('rel=')) {
            attributes += ' rel="noopener noreferrer"';
          }
          return `<a ${attributes} href="${url}">`;
        }

        return match;
      },
    );

    // Remove problematic block elements that can cause height issues
    decodedContent = decodedContent.replace(/<\/(div|p|h[1-6]|br)>/gi, ' ');
    decodedContent = decodedContent.replace(/<(div|p|h[1-6]|br)[^>]*>/gi, ' ');

    // Clean up multiple spaces
    decodedContent = decodedContent.replace(/\s+/g, ' ').trim();

    return decodedContent;
  };

  // Format date to JST time format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    // Add 9 hours for JST
    date.setHours(date.getHours() + 9);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}.${month}.${day}`;
  };

  // Render news content based on loading/empty state
  const renderNewsContent = () => {
    if (isLoading) {
      return Array(3)
        .fill(0)
        .map((_, index) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', md: 'row' },
              alignItems: 'flex-start',
              width: '100%',
              gap: { xs: 1, md: '50px' },
              py: 2,
              borderBottom: '1px solid #FF5400',
            }}
          >
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: 500,
                color: '#262626',
                minWidth: '120px',
                flexShrink: 0,
                bgcolor: '#eee',
                height: '24px',
                width: '80px',
                borderRadius: '4px',
              }}
            />
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: 500,
                color: '#262626',
                bgcolor: '#eee',
                height: '24px',
                width: '70%',
                borderRadius: '4px',
              }}
            />
          </Box>
        ));
    }

    if (newsList.length === 0) {
      return (
        <Typography sx={{ textAlign: 'center', py: 4 }}>
          現在お知らせはありません
        </Typography>
      );
    }

    return newsList.map((item) => (
      <NewsItem
        key={item._id}
        id={item._id}
        date={formatDate(item.createdAt)}
        content={
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 500,
              color: '#262626',
              lineHeight: 1.5,
              overflow: 'hidden',
              maxHeight: { xs: 'calc(1.5em * 3)', md: 'calc(1.5em * 2)' },
              // Normalize HTML elements to prevent height variations
              '& *': {
                fontSize: 'inherit !important',
                fontWeight: 'inherit !important',
                lineHeight: 'inherit !important',
                margin: '0 !important',
                padding: '0 !important',
                display: 'inline !important',
                verticalAlign: 'baseline !important',
              },
              '& a': {
                color: 'inherit',
                textDecoration: 'underline',
                '&:hover': {
                  opacity: 0.8,
                },
              },
            }}
            dangerouslySetInnerHTML={{ __html: processHtmlContent(item.title) }}
          />
        }
      />
    ));
  };

  return (
    <Box
      sx={{
        width: '100%',
        bgcolor: '#FFFAF0',
        py: { xs: 6, md: 9 },
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 6,
      }}
      id="news-section"
    >
      <Container maxWidth="lg">
        {/* Title */}
        <Typography
          variant="h2"
          sx={{
            fontSize: { xs: '24px', md: '30px' },
            fontWeight: 700,
            color: '#262626',
            textAlign: 'center',
            mb: { xs: 4, md: 6 },
          }}
        >
          NEWS
        </Typography>

        {/* News Items */}
        <Box
          sx={{
            width: '100%',
            maxWidth: '1098px',
            display: 'flex',
            flexDirection: 'column',
            mx: 'auto',
            mb: { xs: 4, md: 6 },
          }}
        >
          <Box
            sx={{
              borderBottom: '1px solid #FF5400',
            }}
          ></Box>
          {renderNewsContent()}
        </Box>

        {/* "More" Button */}
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="outlined"
            sx={{
              width: { xs: '100%', md: '300px' },
              height: '48px',
              bgcolor: 'white',
              border: '1px solid #FFB81F',
              borderRadius: '9999px',
              color: '#FEA500',
              fontWeight: 700,
              '&:hover': {
                bgcolor: '#FFF5E4',
                border: '1px solid #FFB81F',
              },
            }}
            onClick={() => router.push('/news')}
          >
            もっと見る
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default NewsSection;

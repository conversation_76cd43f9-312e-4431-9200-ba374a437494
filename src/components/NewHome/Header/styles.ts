import type { Theme } from '@mui/material';
import type { SxProps } from '@mui/system';

type StylesType = Record<string, SxProps<Theme>>;

export const styles: StylesType = {
  appBar: {
    boxShadow: 'none',
    backgroundColor: 'white',
    padding: 0,
    zIndex: 1100,
  },
  container: {
    padding: { xs: '8px 16px', md: '16px 24px' },
  },
  logoContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  consultButton: {
    backgroundColor: '#FF5400',
    color: 'white',
    fontWeight: 'bold',
    padding: { xs: '6px 8px', md: '10px 12px' },
    borderRadius: '333333px',
    textTransform: 'none',
    fontSize: '14px',
    '&:hover': {
      backgroundColor: '#e64c00',
    },
    width: { xs: '80px', md: '150px' },
    height: { xs: '32px', md: '40px' },
  },
  loginButton: {
    backgroundColor: '#009285',
    color: 'white',
    fontWeight: 'bold',
    padding: { xs: '6px 7px', md: '10px 12px' },
    borderRadius: '333333px',
    textTransform: 'none',
    fontSize: '14px',
    whiteSpace: 'nowrap',
    '&:hover': {
      backgroundColor: '#007e73',
    },
    width: { xs: '84px', md: '150px' },
    height: { xs: '32px', md: '40px' },
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  myPageButton: {
    backgroundColor: '#009285',
    color: 'white',
    fontWeight: 'bold',
    padding: { xs: '6px 8px', md: '10px 12px' },
    borderRadius: '333333px',
    textTransform: 'none',
    fontSize: '14px',
    whiteSpace: 'nowrap',
    '&:hover': {
      backgroundColor: '#007e73',
    },
    width: { xs: '110px', md: '150px' },
    minWidth: { xs: '110px', md: '150px' },
    height: { xs: '32px', md: '40px' },
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatButton: {
    backgroundColor: '#FFFFFF',
    border: '1px solid #E0E0E0',
    borderRadius: '50%',
    width: { xs: '32px', md: '40px' },
    height: { xs: '32px', md: '40px' },
    padding: '8px',
    '&:hover': {
      backgroundColor: '#F5F5F5',
    },
    '& svg': {
      width: { xs: '20px', md: '24px' },
      height: { xs: '20px', md: '24px' },
    },
  },
  chatBadge: {
    '.MuiBadge-dot': {
      width: '12px',
      height: '12px',
      borderRadius: '50%',
      transform: 'scale(1) translate(calc(50% - 1px), calc(-50% + 3px))',
      '@media (max-width: 768px)': {
        width: '8px',
        height: '8px',
        transform: 'scale(1) translate(calc(50% - 4px), calc(-50% + 3px))',
      },
    },
  },
  menuIconContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '8px',
    cursor: 'pointer',
    minWidth: { xs: '32px', md: '40px' },
    minHeight: { xs: '32px', md: '40px' },
    borderRadius: '4px',
    '&:hover': {
      backgroundColor: '#f5f5f5',
    },
  },
  hamburgerIcon: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    width: '24px',
    height: '18px',
  },
  hamburgerLine: {
    width: '100%',
    height: '3px',
    backgroundColor: '#3C2D2D',
    borderRadius: '3px',
  },
  lineBanner: {
    backgroundColor: '#FFFAF0',
    width: '100%',
  },
  bannerText: {
    fontFamily: 'Noto Sans JP',
    fontWeight: 500,
    fontSize: { xs: '14px', md: '16px' },
    color: '#262626',
  },
  // Hamburger menu styles
  menuDrawer: {
    width: { xs: '280px', md: '320px' },
    backgroundColor: 'white',
    boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
  },
  menuHeader: {
    padding: '20px 16px',
    borderBottom: '1px solid #e0e0e0',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuTitle: {
    fontWeight: 700,
    fontSize: '20px',
    color: '#262626',
  },
  closeButton: {
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '36px',
    height: '36px',
    borderRadius: '50%',
    '&:hover': {
      backgroundColor: '#f5f5f5',
    },
  },
  closeIcon: {
    fontSize: '24px',
    fontWeight: 700,
    color: '#3C2D2D',
  },
  menuList: {
    padding: '16px 0',
  },
  menuItem: {
    padding: '12px 24px',
    '&:hover': {
      backgroundColor: '#f5f5f5',
    },
    '& .MuiListItemText-primary': {
      fontWeight: 500,
      fontSize: '16px',
      color: '#262626',
    },
  },
};

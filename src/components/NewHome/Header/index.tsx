import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Stack,
  Typography,
  useMediaQuery,
} from '@mui/material';
import AppBar from '@mui/material/AppBar';
import { useTheme } from '@mui/material/styles';
import { useQuery } from '@tanstack/react-query';
import type { IUserDoc } from 'features/chat/types';
import { useFetchDetail } from 'hooks';
import useGlobalState from 'hooks/useGlobalState';
import { ChatIcon } from 'icons';
import type { IBannerItem } from 'models/banner/interface';
import bannerQuery from 'models/banner/query';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

// @ts-ignore
import { styles } from './styles';

const messageLink = {
  [ROLES.CUSTOMER]: '/customer/messages',
  [ROLES.COUNSELOR]: '/counselor/messages',
  [ROLES.LAWYER]: '/lawyer/messages',
};

const Header = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { setLoginModal } = useGlobalState();
  // Add client-side only state to prevent hydration mismatch
  const [mounted, setMounted] = useState(false);
  // Add state for hamburger menu
  const [menuOpen, setMenuOpen] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  const [headerHeight, setHeaderHeight] = useState(150);
  const [banner, setBanner] = useState({} as IBannerItem);
  const { detail: fetchBannerDetail } = useFetchDetail<IBannerItem>({
    ...bannerQuery.getLastBanner(),
  });

  // Check if current page should show hamburger menu
  const shouldShowHamburgerMenu =
    router.pathname === '/' ||
    router.pathname === '/theme' ||
    router.pathname === '/consulting-marriage-worries';

  // Add refs for text overflow detection
  const bannerTextRef = useRef<HTMLDivElement>(null);
  const bannerContainerRef = useRef<HTMLDivElement>(null);
  const [isTextOverflowing, setIsTextOverflowing] = useState(false);

  // Only show interactive elements after client-side hydration
  useEffect(() => {
    setBanner(fetchBannerDetail);
    setMounted(true);

    // Calculate header height dynamically
    const calculateHeaderHeight = () => {
      if (headerRef.current) {
        const height = headerRef.current.offsetHeight;
        // Minimal padding to just clear the header
        setHeaderHeight(height);
      }
    };

    calculateHeaderHeight();
    window.addEventListener('resize', calculateHeaderHeight);

    return () => {
      window.removeEventListener('resize', calculateHeaderHeight);
    };
  }, [fetchBannerDetail]);

  // Check if banner text is overflowing
  useEffect(() => {
    if (mounted && bannerTextRef.current && bannerContainerRef.current) {
      const checkTextOverflow = () => {
        const textWidth = bannerTextRef.current?.scrollWidth || 0;
        const containerWidth = bannerContainerRef.current?.clientWidth || 0;
        setIsTextOverflowing(textWidth > containerWidth);
        return textWidth > containerWidth;
      };

      checkTextOverflow();
      window.addEventListener('resize', checkTextOverflow);

      return () => {
        window.removeEventListener('resize', checkTextOverflow);
      };
    }
    return undefined;
  }, [mounted, banner]);

  // Menu items based on the current page
  const getMenuItems = () => {
    // Common menu items for all pages
    const commonItems = [
      { label: 'リコ活専門家', section: 'experts-section' },
      { label: 'リコ活テーマ', section: 'themes-section' },
      { label: '使い方', section: 'howto-section' },
      { label: '料金', section: 'pricing-section' },
    ];

    // Additional items only for home page
    const homePageItems = [
      { label: '特集記事', section: 'articles-section' },
      { label: 'NEWS', section: 'news-section' },
    ];

    // Return different menu items based on current page
    if (
      router.pathname === '/consulting-marriage-worries' ||
      router.pathname === '/theme'
    ) {
      return commonItems;
    }

    // Default for home and other pages
    return [...commonItems, ...homePageItems];
  };

  // Get the appropriate menu items for current page
  const menuItems = getMenuItems();

  // Handle scrolling to section
  const scrollToSection = (sectionId: string) => {
    setMenuOpen(false); // Close menu after selection

    // First check if the section exists on the current page
    const sectionOnCurrentPage = document.getElementById(sectionId);

    // If section exists on current page, scroll to it
    if (sectionOnCurrentPage) {
      const elementPosition = sectionOnCurrentPage.getBoundingClientRect().top;
      const offsetPosition =
        elementPosition + window.pageYOffset - headerHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
      return;
    }

    // If section doesn't exist on current page, navigate to home page
    if (router.pathname !== '/' && !sectionOnCurrentPage) {
      router.push(`/#${sectionId}`).then(() => {
        setTimeout(() => {
          const section = document.getElementById(sectionId);
          if (section) {
            const elementPosition = section.getBoundingClientRect().top;
            const offsetPosition =
              elementPosition + window.pageYOffset - headerHeight;

            window.scrollTo({
              top: offsetPosition,
              behavior: 'smooth',
            });
          }
        }, 500);
      });
    }
  };

  // Check if user is logged in
  const isLoggedIn = !!Helper.getWebCookie();
  const userRole = Helper.getUserRole();

  // Process banner URL to fix incomplete URLs
  const processBannerUrl = (url: string) => {
    if (!url) return '/';

    // If URL already has protocol or is relative, return as is
    if (
      url.startsWith('http://') ||
      url.startsWith('https://') ||
      url.startsWith('mailto:') ||
      url.startsWith('tel:') ||
      url.startsWith('#') ||
      url.startsWith('/')
    ) {
      return url;
    }

    // Add https:// to incomplete URLs
    return `https://${url}`;
  };

  // Get the appropriate link for the login/my page button
  const getMyPageLink = () => {
    if (userRole === ROLES.CUSTOMER) {
      return '/customer/my-page';
    }
    if (userRole === ROLES.LAWYER) {
      return '/lawyer/my-page';
    }
    if (userRole === ROLES.COUNSELOR) {
      return '/counselor/my-page';
    }
    return '/login';
  };

  // Get chat message link based on user role
  const getChatLink = () => {
    if (!isLoggedIn || !userRole) return '';
    return messageLink[userRole as keyof typeof messageLink] || '';
  };

  // Check if current page is a chat page
  const isChatPage = [
    '/counselor/messages/[[...roomId]]',
    '/lawyer/messages/[[...roomId]]',
    '/customer/messages/[[...roomId]]',
  ].includes(router.pathname);

  // Get unread messages
  const { data: userDoc } = useQuery<IUserDoc>(['currentUser', 'userDoc'], {
    staleTime: Infinity,
    enabled: isLoggedIn,
  });

  // Get the appropriate logo navigation URL based on user role
  const getLogoUrl = () => {
    if (userRole === ROLES.LAWYER) {
      return '/lawyer/my-page';
    }
    if (userRole === ROLES.COUNSELOR) {
      return '/counselor/my-page';
    }
    return '/';
  };

  return (
    <>
      <AppBar
        component="nav"
        position="sticky"
        color="default"
        elevation={0}
        sx={styles.appBar}
        ref={headerRef}
      >
        <Container maxWidth="lg" disableGutters sx={styles.container}>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            width="100%"
          >
            <Link href={getLogoUrl()} passHref>
              <Box component="div" sx={styles.logoContainer}>
                <Image
                  src="/images/logo-icon.svg"
                  alt="Logo"
                  width={isMobile ? 100 : 150}
                  height={isMobile ? 44 : 66}
                  priority
                />
              </Box>
            </Link>

            {mounted && (
              <Stack
                direction="row"
                spacing={isMobile ? 1 : 2}
                alignItems="center"
              >
                {/* Only show consultation button when not logged in */}
                {!isLoggedIn && (
                  <Button
                    variant="contained"
                    color="primary"
                    sx={styles.consultButton}
                    onClick={() => router.push('/consulting-marriage-worries')}
                  >
                    {isMobile ? '相談する' : '悩みを相談する'}
                  </Button>
                )}

                <Button
                  variant="contained"
                  color="secondary"
                  sx={isLoggedIn ? styles.myPageButton : styles.loginButton}
                  onClick={() => {
                    if (!isLoggedIn) {
                      setLoginModal(true);
                    } else {
                      router.push(getMyPageLink());
                    }
                  }}
                >
                  {isLoggedIn ? 'マイページ' : 'ログイン'}
                </Button>

                {/* Add chat button when logged in */}
                {isLoggedIn && !isChatPage && (
                  <IconButton
                    size="large"
                    sx={styles.chatButton}
                    component={Link}
                    href={getChatLink()}
                  >
                    <Badge
                      variant="dot"
                      color="error"
                      sx={styles.chatBadge}
                      invisible={!!((userDoc?.unreadRooms || []).length === 0)}
                    >
                      <ChatIcon />
                    </Badge>
                  </IconButton>
                )}

                {shouldShowHamburgerMenu && (
                  <Box
                    sx={styles.menuIconContainer}
                    onClick={() => setMenuOpen(true)}
                  >
                    <Box sx={styles.hamburgerIcon}>
                      <Box sx={styles.hamburgerLine}></Box>
                      <Box sx={styles.hamburgerLine}></Box>
                      <Box sx={styles.hamburgerLine}></Box>
                    </Box>
                  </Box>
                )}
              </Stack>
            )}
          </Box>
        </Container>
      </AppBar>
      {mounted && banner && (
        <Link
          href={processBannerUrl(banner.url || '')}
          aria-disabled={!!banner.url}
          onClick={(e) => {
            if (!banner.url) {
              e.preventDefault();
            }
          }}
          className="lineBanner"
          target="_blank"
          rel="noopener noreferrer"
          passHref
        >
          <Box sx={styles.lineBanner}>
            <Container maxWidth="lg" disableGutters>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  width: '100%',
                }}
              >
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  gap={2}
                  py={1.75}
                  ref={bannerContainerRef}
                  sx={{
                    overflow: 'hidden',
                    position: 'relative',
                    display: 'inline-flex',
                    maxWidth: '90%',
                  }}
                >
                  {banner.imageUrl && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        position: 'relative',
                        zIndex: 2,
                        ...(isMobile && { pl: 0.5 }), // Reduce padding from 16px to 4px
                      }}
                    >
                      <Image
                        src={`${banner.imageUrl}`}
                        alt={banner.article}
                        width={24}
                        height={24}
                      />
                    </Box>
                  )}

                  <Box
                    sx={{
                      flex: 1,
                      overflow: 'hidden',
                      position: 'relative',
                    }}
                  >
                    <Box
                      component="span"
                      ref={bannerTextRef}
                      sx={{
                        ...styles.bannerText,
                        whiteSpace: 'nowrap', // Always force single line
                        position: 'relative',
                        ...(isTextOverflowing
                          ? {
                              display: 'inline-block',
                              animation: 'marquee 30s linear infinite',
                              '@keyframes marquee': {
                                '0%': { transform: 'translateX(0)' },
                                '100%': { transform: 'translateX(-100%)' },
                              },
                            }
                          : {
                              display: 'block',
                              textOverflow: 'ellipsis',
                              overflow: 'hidden',
                              maxWidth: '100%',
                            }),
                      }}
                    >
                      {banner.article}
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Container>
          </Box>
        </Link>
      )}

      {/* Hamburger Menu Drawer - only shown on specific pages */}
      {shouldShowHamburgerMenu && (
        <Drawer
          anchor="right"
          open={menuOpen}
          onClose={() => setMenuOpen(false)}
          PaperProps={{
            sx: styles.menuDrawer,
          }}
        >
          <Box sx={styles.menuHeader}>
            <Typography variant="h6" sx={styles.menuTitle}>
              メニュー
            </Typography>
            <Box sx={styles.closeButton} onClick={() => setMenuOpen(false)}>
              <Box sx={styles.closeIcon}>×</Box>
            </Box>
          </Box>

          <List sx={styles.menuList}>
            {menuItems.map((item) => (
              <ListItem
                key={item.section}
                button
                onClick={() => scrollToSection(item.section)}
                sx={styles.menuItem}
              >
                <ListItemText primary={item.label} />
              </ListItem>
            ))}
          </List>
        </Drawer>
      )}
    </>
  );
};

export default Header;

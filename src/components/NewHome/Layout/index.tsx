import { Box, IconButton } from '@mui/material';
import Footer from 'components/Layout/Footer';
import NewHeader from 'components/NewHome/Header';
import { useScroll } from 'framer-motion';
import { OrangeArrowUp } from 'icons';
import { useRouter } from 'next/router';
import type { ReactNode } from 'react';
import { useEffect, useState } from 'react';
// Import header with no SSR to avoid hydration issues
// const NewHeader = dynamic(() => import('../Header'), {
//   ssr: false,
// });

// Styles for the scroll-to-top button
const scrollButtonStyles = {
  width: { xs: 40, tablet: 56 },
  height: { xs: 40, tablet: 56 },
  position: 'fixed',
  right: 24,
  zIndex: 1001,
  transition: 'all 0.6s cubic-bezier(0.33, 1, 0.68, 1)',
  bgcolor: 'white',
  border: (theme: any) => `solid 2px ${theme.palette.orange}`,
  '&:hover': {
    bgcolor: 'white',
  },
} as const;

interface NewLayoutProps {
  children: ReactNode;
  showFooter?: boolean;
  showScrollButton?: boolean;
}

export default function NewLayout({
  children,
  showFooter = true,
  showScrollButton = true,
}: NewLayoutProps) {
  const { pathname } = useRouter();

  // Scroll-to-top button state
  const [opacity, setOpacity] = useState(0);
  const { scrollY } = useScroll();

  // Handle scroll position for opacity
  useEffect(() => {
    return scrollY.onChange((latest) => {
      if (latest > document.documentElement.clientHeight && showScrollButton) {
        setOpacity(1);
      } else setOpacity(0);
    });
  }, [scrollY, showScrollButton]);

  // Initialize client-side only features after mounting
  useEffect(() => {
    // Removed unused state initialization
  }, []);

  const isChatPage = [
    '/counselor/messages/[[...roomId]]',
    '/lawyer/messages/[[...roomId]]',
    '/customer/messages/[[...roomId]]',
  ].includes(pathname);

  return (
    <Box sx={{ position: 'relative' }}>
      <NewHeader />
      <Box
        component="main"
        sx={{
          minHeight: {
            xs: !isChatPage ? `calc(100vh - 136px)` : 'calc(100vh - 68px)',
            tablet: !isChatPage ? 'calc(100vh - 259px)' : 'calc(100vh - 98px)',
          },
        }}
      >
        {children}
      </Box>

      {/* Scroll-to-top button */}
      <IconButton
        sx={
          [
            scrollButtonStyles,
            {
              opacity,
              pointerEvents: opacity === 1 ? 'normal' : 'none',
              bottom: { xs: 160, tablet: 40 },
              transform: `scale(${opacity ? 1 : 0})`,
            },
          ] as never
        }
        color="primary"
        onClick={() =>
          window.scrollTo({
            top: 0,
            behavior: 'smooth',
          })
        }
      >
        <OrangeArrowUp />
      </IconButton>

      {showFooter && <Footer showMedia isChatPage={isChatPage} />}
    </Box>
  );
}

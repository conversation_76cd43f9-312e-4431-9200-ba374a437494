import { Box, Container, Skeleton, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

// @ts-ignore
import { styles } from './styles';

type ThemeId = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | 'default';

const THEME_CONTENTS: Record<ThemeId, string> = {
  '1': '教育方針、金銭感覚、生活習慣、将来設計――\n\n結婚当初は気づかなかった価値観の違いが、\n次第に大きな溝となっていませんか？\n\n約3人に一人が悩みを抱える価値観の違いは、\nお互いの理解を深めることで\n改善できるケースもありますが、時には\n新しい人生を選択する決断が必要な場合も。\n\nカウンセリングから離婚手続きまで、\nあなたに寄り添ったアドバイスを提供します。',
  '2': '言葉や身体的な暴力、威圧的な態度、\n監視や行動の制限、経済的DV――\n\n「自分が悪いから」\n「我慢すれば変わってくれるかも」と思い込み、\n孤立している方も少なくありません。\n\n心の整理が必要な方はカウンセリングを、\n今後の生活について具体的なアドバイスが\n必要な方は弁護士にご相談ください。',
  '3': 'セックスレス、離婚の危機、\n何となく続く気まずい関係――\n\n最も身近な存在だからこそ、夫婦間の小さな\n行き違いが大きな対立を生むことがあります。\n\n夫婦の問題は第三者の視点を交えることで、\n新たな気づきが生まれます。\n\nより良い関係を築くためのアプローチから、\n新たな人生の選択まで、\nあなたに寄り添いながらサポートします。',
  '4': '将来への不安や、パートナーへの不信感など、\n漠然とした不安を抱えていませんか？\n\n「離婚すべきか迷っている」といった方、\n不安を整理したい方はカウンセリングを。\n\n離婚を決意していて具体的な\nアドバイスが必要な方は、\n弁護士への相談をオススメします。',
  '5': '離婚後の生活費、財産分与、養育費、\n慰謝料、年金分割など、お金の問題は\n多くの人が直面する重要な問題です。\n\n離婚を決意されている方は弁護士面談で\n具体的な生活設計を、\n\n離婚の意思がはっきりしていない方は\nカウンセリングで心の整理と共に経済面の\n不安についても整理することができます。',
  '6': '別居は夫婦の新たな関係を\n考えるための大切な時間です。\n\n別居を検討中の方、\nすでに別居中の方、\n家庭内別居の方、\nどのような選択をすべきか悩まれていませんか？\n\n離婚を見据えて準備をしたい方は弁護士に、\nパートナーとの関係修復や心の整理が必要な方は\nカウンセラーにご相談ください。',
  '7': 'パートナーの裏切りによる心の傷……\nその痛みから、新しい一歩を踏み出すための\n選択肢は必ず見つかります。\n\n離婚や慰謝料など法的な対応をお考えの方は\n弁護士に、\n関係修復の可能性を探りたい方、心の整理が\n必要な方はカウンセラーにご相談ください。\n\nまた、自身の不倫や浮気に悩む方も、\n一人で抱え込まず、専門家にご相談ください。',
  '8': '離婚や別居の際、最も配慮が必要なのが\n子どもたちへの影響です。\n\n「子どもにどう伝えるべきか」\n「この先どう接していくか」など、親として\n様々な悩みがあるのではないでしょうか。\n\n養育費や面会交流などの法的な取り決めは\n弁護士が、\n子どもへの接し方や心のケアについては\nカウンセラーが、\nそれぞれの専門性を活かしてサポートします。',
  default: '',
};

const THEME_SUBTITLES: Record<ThemeId, string> = {
  '1': '「性格が合わない」は離婚理由 第一位',
  '2': '「我慢」は解決になりません',
  '3': '新しい関係への再出発',
  '4': '「漠然とした不安」から、「次の一歩」へ',
  '5': 'お金の不安を具体的な安心に',
  '6': '別々に過ごす日々は、再出発への準備時間',
  '7': '心の痛みを、あなたらしい選択の力に',
  '8': '子どもの幸せを第一に、今できることを',
  default: '夫婦問題・離婚の専門家に、オンラインで気軽に相談',
};

const ThemeDetailSection = () => {
  const router = useRouter();
  const { id } = router.query;
  const [themeContent, setThemeContent] = useState<string | null>(null);
  const [themeSubtitle, setThemeSubtitle] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);

    // If we don't have an ID yet (on initial load), don't set any content
    if (!router.isReady) return;

    // Determine the theme ID to use
    const themeId =
      id && typeof id === 'string' && Object.keys(THEME_CONTENTS).includes(id)
        ? (id as ThemeId)
        : 'default';

    // Set the content
    setThemeContent(THEME_CONTENTS[themeId]);
    setThemeSubtitle(THEME_SUBTITLES[themeId]);

    // Mark as loaded
    setIsLoading(false);
  }, [id, router.isReady]);

  // Loading skeleton
  if (isLoading || !themeContent || !themeSubtitle) {
    return (
      <Box sx={styles.themeDetailWrapper}>
        <Container maxWidth="lg">
          <Skeleton
            variant="text"
            width="60%"
            height={40}
            sx={{ mx: 'auto', mb: 3 }}
          />
          <Skeleton variant="text" width="90%" sx={{ mx: 'auto' }} />
          <Skeleton variant="text" width="85%" sx={{ mx: 'auto' }} />
          <Skeleton variant="text" width="80%" sx={{ mx: 'auto' }} />
          <Skeleton variant="text" width="90%" sx={{ mx: 'auto' }} />
          <Skeleton variant="text" width="50%" sx={{ mx: 'auto' }} />
        </Container>
      </Box>
    );
  }

  return (
    <Box sx={styles.themeDetailWrapper}>
      <Container maxWidth="lg">
        {/* Theme content subtitle */}
        <Typography variant="h3" sx={styles.contentSubtitle}>
          {themeSubtitle}
        </Typography>

        {/* Theme content text */}
        <Typography sx={styles.contentText}>
          {themeContent!.split('\n').map((line, index) => (
            <React.Fragment key={index}>
              {line}
              <br />
            </React.Fragment>
          ))}
        </Typography>
      </Container>
    </Box>
  );
};

export default ThemeDetailSection;

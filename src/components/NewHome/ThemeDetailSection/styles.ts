import type { SxProps, Theme } from '@mui/material/styles';

export interface StylesType {
  [key: string]: SxProps<Theme>;
}

export const styles: StylesType = {
  themeDetailWrapper: {
    width: '100%',
    backgroundColor: '#FFFFFF',
    paddingTop: { xs: 6, md: 9 },
    paddingBottom: { xs: 6, md: 9 },
  },
  titleSection: {
    display: 'flex',
    alignItems: 'flex-end',
    gap: 1,
    justifyContent: 'center',
    marginBottom: { xs: 3, md: 4 },
  },
  logoContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  titleText: {
    fontSize: '32px',
    fontWeight: 700,
    color: '#FEA500',
  },
  themeTitle: {
    fontSize: { xs: '28px', md: '36px' },
    fontWeight: 700,
    color: '#262626',
    textAlign: 'center',
    marginBottom: { xs: 3, md: 4 },
  },
  contentSubtitle: {
    fontSize: { xs: '20px', md: '24px' },
    fontWeight: 700,
    color: '#FF8C00',
    textAlign: 'center',
    marginBottom: { xs: 3, md: 4 },
  },
  contentText: {
    fontSize: '16px',
    fontWeight: 500,
    color: '#262626',
    lineHeight: 1.5,
    textAlign: 'center',
    maxWidth: '800px',
    margin: '0 auto',
  },
};

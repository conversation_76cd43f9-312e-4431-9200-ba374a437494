import type { Theme } from '@mui/material';
import type { SxProps } from '@mui/system';

type StylesType = Record<string, SxProps<Theme>>;

export const styles: StylesType = {
  themesWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    padding: { xs: '40px 0', md: '72px 0' },
    backgroundColor: '#FFFFFF',
  },
  titleSection: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: { xs: '32px', md: '48px' },
    width: '100%',
    gap: '8px',
  },
  logoContainer: {
    width: 111,
    height: 48,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    color: '#FEA500',
    fontWeight: 700,
    fontSize: { xs: '24px', md: '32px' },
    lineHeight: 1.4,
    textAlign: 'center',
  },
  themesGrid: {
    display: 'grid',
    gridTemplateColumns: {
      xs: 'repeat(2, 1fr)',
      md: 'repeat(4, 1fr)',
    },
    gap: {
      xs: '16px',
      md: '32px',
    },
    width: '100%',
    justifyContent: 'center',
  },
  themeCard: {
    width: '100%',
    maxWidth: { xs: '100%', sm: '230px', md: '220px', lg: '252px' },
    borderRadius: '16px',
    overflow: 'hidden',
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    '&:hover': {
      boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.15)',
      opacity: 0.9,
      transform: 'translateY(-2px)',
    },
    margin: '0 auto',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: {
      md: '180px',
      xs: '130px',
    },
  },
  titleContainer: {
    backgroundColor: '#FEA500',
    padding: '12px 16px',
    display: 'flex',
    justifyContent: 'center',
  },
  themeTitle: {
    fontWeight: 700,
    fontSize: {
      md: '20px',
      xs: '14px',
    },
    color: '#FFFFFF',
    textAlign: 'center',
  },
};

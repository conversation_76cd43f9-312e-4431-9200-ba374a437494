import { Box, Container, Typography } from '@mui/material';
import Image from 'next/image';
import { useRouter } from 'next/router';

import { styles } from './styles';

// Individual theme card component
interface ThemeCardProps {
  id: number;
  imageSrc: string;
  title: string;
  priority?: boolean;
}

const ThemeCard = ({
  id,
  imageSrc,
  title,
  priority = false,
}: ThemeCardProps) => {
  const router = useRouter();

  const handleThemeClick = () => {
    // Use router.push for better navigation handling
    router.push(`/theme?id=${id}`, undefined, { shallow: false });
  };

  return (
    <Box sx={styles.themeCard} onClick={handleThemeClick}>
      <Box sx={styles.imageContainer}>
        <Image
          src={imageSrc}
          alt={title}
          fill
          style={{ objectFit: 'cover' }}
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 230px, (max-width: 1024px) 220px, 252px"
          quality={80}
          priority={priority}
        />
      </Box>
      <Box sx={styles.titleContainer}>
        <Typography sx={styles.themeTitle}>{title}</Typography>
      </Box>
    </Box>
  );
};

const ThemesSection = () => {
  const themes = [
    { id: 1, imageSrc: '/images/themes/theme1.jpg', title: '価値観の不一致' },
    { id: 2, imageSrc: '/images/themes/theme2.jpg', title: 'DV・モラハラ' },
    { id: 3, imageSrc: '/images/themes/theme3.jpg', title: '夫婦関係' },
    { id: 4, imageSrc: '/images/themes/theme4.jpg', title: 'メンタル面の不安' },
    { id: 5, imageSrc: '/images/themes/theme5.jpg', title: '経済的な悩み' },
    { id: 6, imageSrc: '/images/themes/theme6.jpg', title: '別居' },
    { id: 7, imageSrc: '/images/themes/theme7.jpg', title: '不倫・浮気' },
    { id: 8, imageSrc: '/images/themes/theme8.jpg', title: '子ども' },
  ];

  return (
    <Box sx={styles.themesWrapper}>
      <Container maxWidth="lg">
        {/* Title with logo */}
        <Box sx={styles.titleSection}>
          <Box sx={styles.logoContainer}>
            <Image
              src="/images/themes/theme_logo_icon.svg"
              alt="リコ活"
              width={111}
              height={48}
            />
          </Box>
          <Typography variant="h2" sx={styles.titleText}>
            テーマ
          </Typography>
        </Box>

        {/* Themes grid */}
        <Box sx={styles.themesGrid}>
          {themes.map((theme, index) => (
            <ThemeCard
              key={theme.id}
              id={theme.id}
              imageSrc={theme.imageSrc}
              title={theme.title}
              priority={index < 4} // Prioritize first 4 images
            />
          ))}
        </Box>
      </Container>
    </Box>
  );
};

export default ThemesSection;

import { Box, Button, Container, Grid, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { useFetchList } from 'hooks';
import type { IExternalArticleListItem } from 'models/article/interface';
import articleQuery from 'models/article/query';
import Image from 'next/image';
import React from 'react';
import { MomentFormat } from 'utils/constants';
import Helper from 'utils/helpers';

// Article card component
const ArticleCard = ({ article }: { article: IExternalArticleListItem }) => {
  return (
    <Box
      onClick={() => window.open(Helper.formatUrl(article.link), '_blank')}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '16px',
        border: '1px solid #DBE3E7',
        overflow: 'hidden',
        width: '100%',
        maxWidth: '346px',
        cursor: 'pointer',
        height: '100%',
      }}
    >
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          aspectRatio: '320/180',
        }}
      >
        <Image
          alt={article.title}
          src={article.image?.originUrl || '/images/default-image.png'}
          fill
          style={{
            objectFit: 'cover',
            borderTopRightRadius: '16px',
            borderTopLeftRadius: '16px',
          }}
        />
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          p: 3,
          flex: 1,
        }}
      >
        <Typography
          variant="h3"
          sx={{
            fontSize: '18px',
            fontWeight: 700,
            color: '#262626',
            lineHeight: 1.45,
            overflow: 'hidden',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
          }}
        >
          {article.title}
        </Typography>
        <Typography
          sx={{
            fontSize: '14px',
            fontWeight: 500,
            color: '#FF8C00',
          }}
        >
          {dayjs(article.updatedAt).format(MomentFormat.DOT_YEAR_MONTH_DATE)}
        </Typography>
      </Box>
    </Box>
  );
};

// Loading placeholder for articles
const ArticleCardSkeleton = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '16px',
        border: '1px solid #DBE3E7',
        overflow: 'hidden',
        width: '100%',
        maxWidth: '346px',
        height: '100%',
      }}
    >
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          aspectRatio: '320/180',
          backgroundColor: '#f0f0f0',
        }}
      />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          p: 3,
        }}
      >
        <Box
          sx={{
            height: '24px',
            width: '90%',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px',
          }}
        />
        <Box
          sx={{
            height: '14px',
            width: '40%',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px',
          }}
        />
      </Box>
    </Box>
  );
};

const ExternalArticles = () => {
  // Fetch external articles
  const { list: externalArticleList, isLoading } =
    useFetchList<IExternalArticleListItem>({
      ...articleQuery.externalArticleList,
      staleTime: 60 * 1000,
      customParams: { sort: 'updatedAt.desc' },
    });

  // Display only the first 3 articles
  const displayArticles = externalArticleList.slice(0, 3);

  return (
    <Box
      sx={{
        width: '100%',
        bgcolor: 'white',
        py: { xs: 6, md: 9 },
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }}
    >
      <Container maxWidth="lg">
        {/* Title */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-end',
            gap: 1,
            justifyContent: 'center',
            mb: { xs: 4, md: 6 },
          }}
        >
          <Box
            sx={{
              width: '111px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Image
              src="/images/experts/expert-logo.svg"
              alt="Logo"
              width={111}
              height={48}
              style={{ objectFit: 'contain' }}
            />
          </Box>
          <Typography
            variant="h2"
            sx={{
              fontSize: { xs: '24px', md: '30px' },
              fontWeight: 700,
              color: '#FEA500',
            }}
          >
            リコ活 MEDIA
          </Typography>
        </Box>

        {/* Articles Grid for larger screens */}
        <Box
          sx={{
            display: { xs: 'none', md: 'block' },
            width: '100%',
            mb: { xs: 4, md: 6 },
          }}
        >
          <Grid
            container
            spacing={3}
            justifyContent="center"
            sx={{
              maxWidth: '1098px',
              mx: 'auto',
            }}
          >
            {isLoading
              ? Array(3)
                  .fill(0)
                  .map((_, index) => (
                    <Grid item xs={12} md={4} key={`skeleton-${index}`}>
                      <ArticleCardSkeleton />
                    </Grid>
                  ))
              : displayArticles.map((article) => (
                  <Grid item xs={12} md={4} key={article._id}>
                    <ArticleCard article={article} />
                  </Grid>
                ))}
          </Grid>
        </Box>

        {/* Mobile list view */}
        <Box
          sx={{
            display: { xs: 'block', md: 'none' },
            width: '100%',
            mb: { xs: 4, md: 6 },
          }}
        >
          {isLoading ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {Array(3)
                .fill(0)
                .map((_, index) => (
                  <Box
                    key={`mobile-skeleton-${index}`}
                    display="flex"
                    gap="12px"
                  >
                    <Box
                      sx={{
                        width: 72,
                        height: 72,
                        backgroundColor: '#f0f0f0',
                        borderRadius: '12px',
                      }}
                    />
                    <Box sx={{ flex: 1 }}>
                      <Box
                        sx={{
                          height: '14px',
                          width: '90%',
                          backgroundColor: '#f0f0f0',
                          borderRadius: '4px',
                          mb: 1,
                        }}
                      />
                      <Box
                        sx={{
                          height: '14px',
                          width: '70%',
                          backgroundColor: '#f0f0f0',
                          borderRadius: '4px',
                        }}
                      />
                    </Box>
                  </Box>
                ))}
            </Box>
          ) : (
            <Box display="flex" flexDirection="column" gap={2}>
              {displayArticles.map((article) => (
                <Box
                  display="flex"
                  key={article._id}
                  gap="12px"
                  onClick={() =>
                    window.open(Helper.formatUrl(article.link), '_blank')
                  }
                  sx={{ cursor: 'pointer' }}
                >
                  <Image
                    src={
                      article.image?.originUrl || '/images/default-image.png'
                    }
                    alt=""
                    width={72}
                    height={72}
                    style={{ borderRadius: '12px', objectFit: 'cover' }}
                  />
                  <Typography
                    fontSize={14}
                    color="#464646"
                    fontWeight={500}
                    sx={{
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                    }}
                  >
                    {article.title}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}
        </Box>

        {/* "More" Button */}
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="contained"
            sx={{
              width: { xs: '100%', md: '300px' },
              height: '48px',
              bgcolor: '#FEA500',
              color: 'white',
              borderRadius: '9999px',
              fontWeight: 700,
              '&:hover': {
                bgcolor: '#F59000',
              },
            }}
            onClick={() => window.open('/media/', '_blank')}
          >
            リコ活 MEDIA を見る
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default ExternalArticles;

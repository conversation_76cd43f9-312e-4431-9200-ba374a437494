import type { SxProps, Theme } from '@mui/material';

type StylesType = {
  [key: string]: SxProps<Theme>;
};

export const styles: StylesType = {
  howToWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    padding: { xs: '40px 0', md: '72px 0' },
    backgroundColor: '#FFFAF0',
  },
  titleSection: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: { xs: '32px', md: '48px' },
    width: '100%',
    gap: '8px',
  },
  logoContainer: {
    width: 111,
    height: 48,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    color: '#FF8C00',
    fontWeight: 700,
    fontSize: { xs: '24px', md: '32px' },
    lineHeight: 1.4,
    textAlign: 'center',
  },
  contentContainer: {
    display: 'flex',
    flexDirection: { xs: 'column', md: 'row' },
    gap: { xs: '32px', md: '24px' },
    width: '100%',
    justifyContent: 'center',
    maxWidth: '1100px',
  },
  howToCard: {
    display: 'flex',
    flexDirection: {
      xs: 'row',
      md: 'column',
    },
    gap: {
      xs: '32px',
      md: '16px',
    },
    width: { xs: '100%', md: 'calc(33.33% - 16px)' },
    maxWidth: { xs: '100%', md: '350px' },
  },
  howToImageWrapper: {
    position: 'relative',
    flexShrink: 0,
    paddingBottom: '18px', // Space for the half-overlapped number,
    width: {
      xs: '88px',
      md: '100%',
    },
    height: {
      xs: '88px',
      md: 'auto',
    },
  },
  howToImageContainer: {
    position: 'relative',

    paddingTop: '100%', // Square aspect ratio
    borderRadius: '16px',
    overflow: 'hidden',
  },
  howToNumber: {
    position: 'absolute',
    left: {
      md: '16px',
      xs: 'auto',
    },
    bottom: {
      md: '0px',
      xs: 'auto',
    },
    right: {
      xs: '-18px',
      md: 'auto',
    },
    top: {
      xs: '6px',
      md: 'auto',
    },
    color: '#000000',
    fontWeight: 700,
    fontSize: { xs: '28px', md: '42px' },
    lineHeight: 1,
    zIndex: 2,
  },
  howToTitle: {
    color: '#FF8C00',
    fontWeight: 700,
    fontSize: { xs: '18px', md: '20px' },
    lineHeight: 1.4,
    marginBottom: {
      xs: '12px',
      md: '16px',
    },
  },
  howToDescription: {
    color: '#333333',
    fontWeight: 500,
    fontSize: { xs: '14px', md: '14px' },
    lineHeight: 1.5,
  },
};

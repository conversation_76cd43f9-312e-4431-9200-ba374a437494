import { Box, Container, Typography } from '@mui/material';
import Image from 'next/image';

import { styles } from './styles';

const HowTo = () => {
  const datas = [
    {
      id: 1,
      imageSrc: '/images/howto/how_to_1.png',
      title: '日時、専門家で面談申込み',
      description: '希望する専門家へ24時間いつでも面談申し込みできる',
    },
    {
      id: 2,
      imageSrc: '/images/howto/how_to_2.png',
      title: '専門家のメールで予約確定',
      description: '入力いただいたカルテを確認後 登録メールに専門家がご連絡',
    },
    {
      id: 3,
      imageSrc: '/images/howto/how_to_3.png',
      title: 'オンライン or 直接面談',
      description: 'どんな専門家ともカルテがあるからスムースに悩みを相談できる',
    },
  ];

  return (
    <Box sx={styles.howToWrapper}>
      <Container maxWidth="lg">
        {/* Title Section */}
        <Box sx={styles.titleSection}>
          <Box sx={styles.logoContainer}>
            <Image
              src="/images/howto/howto_logo_icon.svg"
              alt="リコ活"
              width={111}
              height={48}
            />
          </Box>
          <Typography variant="h2" sx={styles.titleText}>
            の使い方
          </Typography>
        </Box>

        {/* Content */}
        <Box sx={styles.contentContainer}>
          {datas.map((data, index) => {
            return (
              <Box sx={styles.howToCard} key={data.id}>
                <Box sx={styles.howToImageWrapper}>
                  <Box sx={styles.howToImageContainer}>
                    <Image
                      src={data.imageSrc}
                      alt="日時、専門家で面談申込み"
                      fill
                      style={{
                        objectFit: 'cover',
                        borderRadius: '16px',
                      }}
                    />
                  </Box>
                  <Typography sx={styles.howToNumber}>0{index + 1}</Typography>
                </Box>
                <Box>
                  <Typography sx={styles.howToTitle}>{data.title}</Typography>
                  <Typography sx={styles.howToDescription}>
                    {data.description}
                  </Typography>
                </Box>
              </Box>
            );
          })}
        </Box>
      </Container>
    </Box>
  );
};

export default HowTo;

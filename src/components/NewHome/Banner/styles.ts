import type { Theme } from '@mui/material';
import type { SxProps } from '@mui/system';

type StylesType = Record<string, SxProps<Theme>>;

export const styles: StylesType = {
  bannerWrapper: {
    backgroundImage: 'linear-gradient(to bottom, #FFFAF0, #FFFAF0)',
    padding: { xs: '40px 0', md: '80px 0' },
  },
  contentContainer: {
    display: 'flex',
    flexDirection: { xs: 'column', md: 'row' },
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: { xs: 4, md: 6 },
  },
  textContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    gap: 3,
  },
  mainHeading: {
    fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
    fontWeight: 700,
    lineHeight: 1.2,
    color: '#333',
  },
  accentText: {
    color: '#FF5400',
    display: 'inline',
    position: 'relative',
  },
  subHeading: {
    fontSize: { xs: '1.25rem', md: '1.5rem' },
    fontWeight: 500,
    color: '#555',
    marginTop: '1rem',
  },
  buttonContainer: {
    marginTop: { xs: 3, md: 5 },
  },
  consultButton: {
    backgroundColor: '#FF5400',
    color: 'white',
    fontWeight: 'bold',
    padding: '12px 24px',
    borderRadius: '8px',
    textTransform: 'none',
    fontSize: '1rem',
    '&:hover': {
      backgroundColor: '#e64c00',
    },
    height: '40px',
  },
  imageContainer: {
    flex: 1,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroImage: {
    width: { xs: '100%', md: '100%' },
    height: { xs: '250px', md: '350px' },
    backgroundColor: '#f0f0f0',
    borderRadius: '8px',
    // Use background image placeholder until we get the actual image
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  },
};

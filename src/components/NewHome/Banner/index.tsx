import { Box, Button, Container, Typography } from '@mui/material';
import { useEffect, useState } from 'react';

// @ts-ignore
import { styles } from './styles';

const MainBanner = () => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Box sx={styles.bannerWrapper}>
      <Container maxWidth="lg">
        <Box sx={styles.contentContainer}>
          <Box sx={styles.textContent}>
            <Typography component="h1" sx={styles.mainHeading}>
              <Box component="span" sx={styles.accentText}>
                専門家
              </Box>
              に<br />
              法律相談をするなら
            </Typography>

            <Typography sx={styles.subHeading}>
              弁護士とLINEで気軽に法律相談
            </Typography>

            {mounted && (
              <Box sx={styles.buttonContainer}>
                <Button variant="contained" sx={styles.consultButton}>
                  無料でLINEに相談する
                </Button>
              </Box>
            )}
          </Box>

          <Box sx={styles.imageContainer}>
            {/* Placeholder for hero image */}
            <Box sx={styles.heroImage} />
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default MainBanner;

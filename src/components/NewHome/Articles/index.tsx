import { Box, Button, Container, Typography } from '@mui/material';
import ArticleMobileList from 'components/Home/ArticleMobileList';
import dayjs from 'dayjs';
import { useFetchList } from 'hooks';
import type { IPickupArticleListItem } from 'models/article/interface';
import articleQuery from 'models/article/query';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react';
import { ARTICLE_SORT_ORDER, MomentFormat } from 'utils/constants';

interface ArticleCardProps {
  imageSrc: string;
  title: string;
  date: string;
  onClick?: () => void;
}

const ArticleCard: React.FC<ArticleCardProps> = ({
  imageSrc,
  title,
  date,
  onClick,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '16px',
        border: '1px solid #DBE3E7',
        overflow: 'hidden',
        width: '100%',
        maxWidth: {
          xs: '100%',
          md: '346px',
        },
        cursor: onClick ? 'pointer' : 'default',
      }}
      onClick={onClick}
    >
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          height: '200px',
        }}
      >
        <Image
          src={imageSrc}
          alt={title.replace('\n', ' ')}
          fill
          style={{ objectFit: 'cover' }}
        />
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          p: 3,
        }}
      >
        <Typography
          variant="h3"
          sx={{
            fontSize: '18px',
            fontWeight: 700,
            color: '#262626',
            lineHeight: 1.45,
            whiteSpace: 'pre-line',
          }}
        >
          {title}
        </Typography>
        <Typography
          sx={{
            fontSize: '14px',
            fontWeight: 500,
            color: '#FF8C00',
          }}
        >
          {date}
        </Typography>
      </Box>
    </Box>
  );
};

// Loading placeholder for articles
const ArticleCardSkeleton: React.FC = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '16px',
        border: '1px solid #DBE3E7',
        overflow: 'hidden',
        width: '100%',
        maxWidth: '346px',
      }}
    >
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          height: '200px',
          backgroundColor: '#f0f0f0',
        }}
      />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          p: 3,
        }}
      >
        <Box
          sx={{
            height: '24px',
            width: '90%',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px',
          }}
        />
        <Box
          sx={{
            height: '24px',
            width: '60%',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px',
          }}
        />
        <Box
          sx={{
            height: '14px',
            width: '40%',
            backgroundColor: '#f0f0f0',
            borderRadius: '4px',
          }}
        />
      </Box>
    </Box>
  );
};

const ArticlesSection = () => {
  const router = useRouter();

  // Fetch pickup articles from API
  const { list: articleList, isLoading } = useFetchList<IPickupArticleListItem>(
    {
      ...articleQuery.pickupArticleList,
      staleTime: 60 * 1000,
      customParams: { sort: ARTICLE_SORT_ORDER },
    },
  );

  // Take up to 6 articles to display
  const displayArticles = articleList.slice(0, 6);
  const articleFirst = displayArticles[0];

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        width: '100%',
        py: { xs: 6, md: 9 },
        gap: { xs: 5, md: 6 },
      }}
    >
      <Container maxWidth="lg">
        {/* Title */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-end',
            gap: 1,
            justifyContent: 'center',
            mb: { xs: 4, md: 6 },
          }}
        >
          <Box
            sx={{
              width: '111px',
              height: '48px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Image
              src="/images/experts/expert-logo.svg"
              alt="Logo"
              width={111}
              height={48}
              style={{ objectFit: 'contain' }}
            />
          </Box>
          <Typography
            variant="h2"
            sx={{
              fontSize: { xs: '24px', md: '30px' },
              fontWeight: 700,
              color: '#FEA500',
            }}
          >
            特集記事
          </Typography>
        </Box>

        {/* Articles Grid */}
        <Box
          sx={{
            display: {
              xs: 'none',
              md: 'flex',
            },
            flexDirection: 'column',
            width: '100%',
            maxWidth: '1098px',
            gap: '30px',
            mx: 'auto',
          }}
        >
          {/* Row 1 */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: {
                xs: '1fr',
                md: 'repeat(2, 1fr)',
                lg: 'repeat(3, 1fr)',
              },
              gap: { xs: 3, md: '30px' },
              width: '100%',
              justifyItems: 'center',
            }}
          >
            {isLoading
              ? Array(3)
                  .fill(0)
                  .map((_, index) => (
                    <ArticleCardSkeleton key={`skeleton-1-${index}`} />
                  ))
              : displayArticles
                  .slice(0, 3)
                  .map((article) => (
                    <ArticleCard
                      key={article._id}
                      imageSrc={
                        article.image?.originUrl ||
                        '/images/article/article-placeholder.svg'
                      }
                      title={article.title}
                      date={dayjs(article.updatedAt).format(
                        MomentFormat.DOT_YEAR_MONTH_DATE,
                      )}
                      onClick={() =>
                        router.push(`/pickup-articles/${article._id}`)
                      }
                    />
                  ))}
          </Box>

          {/* Row 2 */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: {
                xs: '1fr',
                md: 'repeat(2, 1fr)',
                lg: 'repeat(3, 1fr)',
              },
              gap: { xs: 3, md: '30px' },
              width: '100%',
              justifyItems: 'center',
            }}
          >
            {isLoading
              ? Array(3)
                  .fill(0)
                  .map((_, index) => (
                    <ArticleCardSkeleton key={`skeleton-2-${index}`} />
                  ))
              : displayArticles
                  .slice(3, 6)
                  .map((article) => (
                    <ArticleCard
                      key={article._id}
                      imageSrc={
                        article.image?.originUrl ||
                        '/images/article/article-placeholder.svg'
                      }
                      title={article.title}
                      date={dayjs(article.updatedAt).format(
                        MomentFormat.DOT_YEAR_MONTH_DATE,
                      )}
                      onClick={() =>
                        router.push(`/pickup-articles/${article._id}`)
                      }
                    />
                  ))}
          </Box>
        </Box>
        <Box sx={{ display: { xs: 'block', md: 'none' } }}>
          {articleFirst && (
            <ArticleCard
              imageSrc={
                articleFirst.image?.originUrl ||
                '/images/article/article-placeholder.svg'
              }
              title={articleFirst.title}
              date={dayjs(articleFirst.updatedAt).format(
                MomentFormat.DOT_YEAR_MONTH_DATE,
              )}
              onClick={() =>
                router.push(`/pickup-articles/${articleFirst._id}`)
              }
            />
          )}
          <Box mt={3}>
            <ArticleMobileList
              showDate
              data={displayArticles.slice(1, 4)}
              onItemClick={({ _id }) => router.push(`/pickup-articles/${_id}`)}
            />
          </Box>
        </Box>
        {/* CTA Button */}
        <Box
          sx={{
            display: {
              md: 'flex',
              xs: 'none',
            },
            justifyContent: 'center',
            mt: { xs: 4, md: 5 },
          }}
        >
          <Link href="/pickup-articles" passHref>
            <Button
              variant="contained"
              disableElevation
              sx={{
                bgcolor: '#FEA500',
                color: 'white',
                fontWeight: 700,
                py: 1.5,
                px: 2,
                borderRadius: '9999px',
                width: { xs: '100%', md: '350px' },
                height: '48px',
                '&:hover': {
                  bgcolor: '#F59000',
                },
              }}
            >
              「特集記事の一覧を見る」へ
            </Button>
          </Link>
        </Box>
      </Container>
    </Box>
  );
};

export default ArticlesSection;

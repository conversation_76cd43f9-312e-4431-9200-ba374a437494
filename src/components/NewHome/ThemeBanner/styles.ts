import type { SxProps, Theme } from '@mui/material/styles';

export interface StylesType {
  [key: string]: SxProps<Theme>;
}

export const styles: StylesType = {
  bannerWrapper: {
    width: '100%',
    padding: 0,
  },
  bannerContainer: {
    position: 'relative',
    width: '100%',
    height: 0,
    paddingTop: { xs: '56.25%', sm: '40%', md: '27.78%' },
    overflow: 'hidden',
    backgroundColor: '#FFFFFF',
    backgroundSize: 'cover',
    backgroundPosition: 'center top',
    backgroundRepeat: 'no-repeat',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 1,
  },
  textContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
  },
  textContentBox: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: { xs: 1, sm: 2 },
  },
  titleSection: {
    display: 'flex',
    alignItems: 'flex-end',
    gap: 1,
    justifyContent: 'center',
    marginBottom: { xs: 1, md: 2 },
  },
  logoContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  titleText: {
    color: '#FEA500',
    fontWeight: 700,
    fontSize: { xs: '24px', sm: '28px', md: '32px' },
    lineHeight: 1.3,
    textAlign: 'center',
  },
  themeText: {
    color: '#262626',
    fontWeight: 700,
    fontSize: { xs: '28px', sm: '36px', md: '40px' },
    lineHeight: 1.4,
    textAlign: 'center',
  },
};

import { Box, Skeleton, Typography, useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

// @ts-ignore
import { styles } from './styles';

type ThemeId = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | 'default';

const PC_THEME_BACKGROUNDS: Record<ThemeId, string> = {
  '1': '/images/themes/D-PC-Banner-Theme-1.jpg',
  '2': '/images/themes/D-PC-Banner-Theme-2.jpg',
  '3': '/images/themes/D-PC-Banner-Theme-3.jpg',
  '4': '/images/themes/D-PC-Banner-Theme-4.jpg',
  '5': '/images/themes/D-PC-Banner-Theme-5.jpg',
  '6': '/images/themes/D-PC-Banner-Theme-6.jpg',
  '7': '/images/themes/D-PC-Banner-Theme-7.jpg',
  '8': '/images/themes/D-PC-Banner-Theme-8.jpg',
  default: '/images/consulting-banner.jpg',
};

const MOBILE_THEME_BACKGROUNDS: Record<ThemeId, string> = {
  '1': '/images/themes/D-SP-Banner-Theme-1.jpg',
  '2': '/images/themes/D-SP-Banner-Theme-2.jpg',
  '3': '/images/themes/D-SP-Banner-Theme-3.jpg',
  '4': '/images/themes/D-SP-Banner-Theme-4.jpg',
  '5': '/images/themes/D-SP-Banner-Theme-5.jpg',
  '6': '/images/themes/D-SP-Banner-Theme-6.jpg',
  '7': '/images/themes/D-SP-Banner-Theme-7.jpg',
  '8': '/images/themes/D-SP-Banner-Theme-8.jpg',
  default: '/images/consulting-banner.jpg',
};

const THEME_TITLES: Record<ThemeId, string> = {
  '1': '【価値観の不一致】',
  '2': '【DV・モラハラ】',
  '3': '【夫婦関係】',
  '4': '【メンタル面の不安】',
  '5': '【経済的な悩み】',
  '6': '【別居】',
  '7': '【不倫・浮気】',
  '8': '【子ども】',
  default: '【価値観の不一致】',
};

const ThemeBanner = () => {
  const router = useRouter();
  const { id } = router.query;
  const [backgroundImage, setBackgroundImage] = useState<string | null>(null);
  const [themeTitle, setThemeTitle] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    setIsLoading(true);

    // If we don't have an ID yet (on initial load), don't set any content
    if (!router.isReady) return;

    // Determine the theme ID to use
    const themeId =
      id &&
      typeof id === 'string' &&
      Object.keys(PC_THEME_BACKGROUNDS).includes(id)
        ? (id as ThemeId)
        : 'default';

    // Select background image based on device
    const backgroundImages = isMobile
      ? MOBILE_THEME_BACKGROUNDS
      : PC_THEME_BACKGROUNDS;

    // Set the content
    setBackgroundImage(backgroundImages[themeId]);
    setThemeTitle(THEME_TITLES[themeId]);

    // Mark as loaded
    setIsLoading(false);
  }, [id, router.isReady, isMobile]);

  // Early return for loading state
  if (isLoading || !backgroundImage || !themeTitle) {
    return (
      <Box sx={styles.bannerWrapper}>
        <Box sx={styles.bannerContainer}>
          <Box sx={styles.textContainer}>
            <Box sx={styles.textContentBox}>
              <Box sx={styles.titleSection}>
                <Box sx={styles.logoContainer}>
                  <Skeleton variant="rectangular" width={111} height={48} />
                </Box>
                <Skeleton variant="text" width={100} height={40} />
              </Box>
              <Skeleton variant="text" width={240} height={60} />
            </Box>
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={styles.bannerWrapper}>
      <Box
        sx={{
          ...styles.bannerContainer,
          backgroundImage: `url("${backgroundImage}")`,
          paddingTop: isMobile
            ? 'calc(180 / 375 * 100%)'
            : { xs: '56.25%', sm: '40%', md: '27.78%' },
        }}
      >
        <Box sx={styles.textContainer}>
          <Box sx={styles.textContentBox}>
            <Box sx={styles.titleSection}>
              <Box sx={styles.logoContainer}>
                <Image
                  src="/images/experts/expert-logo.svg"
                  alt="リコ活"
                  width={111}
                  height={48}
                />
              </Box>
              <Typography variant="h2" sx={styles.titleText}>
                テーマ
              </Typography>
            </Box>
            <Typography variant="h1" sx={styles.themeText}>
              {themeTitle}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ThemeBanner;

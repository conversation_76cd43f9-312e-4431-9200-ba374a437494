import type { SxProps, Theme } from '@mui/material';

type StylesType = {
  [key: string]: SxProps<Theme>;
};

export const styles: StylesType = {
  ctaWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    aspectRatio: '4.6/1',
    minHeight: { xs: '362px', md: '312px' },
    position: 'relative',
    overflow: 'hidden',
    py: { xs: '56px', md: '72px' },
  },
  bgImage: {
    position: 'absolute',
    display: { xs: 'none', md: 'block' },
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 0,
    opacity: 1,
    '& img': {
      objectFit: 'cover',
      objectPosition: 'center',
    },
  },
  bgImageMB: {
    display: { xs: 'block', md: 'none' },
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 0,
    opacity: 1,
    '& img': {
      objectFit: 'cover',
      objectPosition: 'center',
    },
  },
  contentContainer: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: { xs: '16px', md: '48px' },
    width: '100%',
    height: '100%',
    maxWidth: '1440px',
    padding: { xs: '16px', md: '0' },
    zIndex: 2,
  },
  titleText: {
    color: '#262626',
    fontWeight: 700,
    fontSize: { xs: '20px', md: '28px' },
    lineHeight: 1.4,
    textAlign: 'center',
    maxWidth: '800px',
  },
  buttonContainer: {
    display: 'flex',
    flexDirection: { xs: 'column', md: 'row' },
    justifyContent: 'center',
    alignItems: 'center',
    gap: { xs: '16px', md: '50px' },
    width: '100%',
  },
  orangeButton: {
    backgroundColor: '#FF5400',
    color: '#FFFFFF',
    borderRadius: '9999px',
    padding: '12px 16px',
    width: { xs: '100%', md: '300px' },
    height: '48px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: 700,
    fontSize: '16px',
    cursor: 'pointer',
    transition: 'background-color 0.3s',
    '&:hover': {
      backgroundColor: '#E04A00',
    },
  },
  lineButton: {
    backgroundColor: '#FFFFFF',
    color: '#262626',
    borderRadius: '9999px',
    padding: '8px 6px',
    width: { xs: '100%', md: '300px' },
    height: '48px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '16px',
    fontWeight: 700,
    fontSize: '16px',
    cursor: 'pointer',
    transition: 'background-color 0.3s',
    '&:hover': {
      backgroundColor: '#F5F5F5',
    },
  },
  lawyerButton: {
    backgroundColor: '#009285',
    color: '#FFFFFF',
    borderRadius: '9999px',
    padding: '12px 16px',
    width: { xs: '100%', md: '300px' },
    height: '48px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: 700,
    fontSize: '16px',
    cursor: 'pointer',
    transition: 'background-color 0.3s',
    '&:hover': {
      backgroundColor: '#007F75',
    },
  },
  lineIcon: {
    width: '24px',
    height: '24px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
};

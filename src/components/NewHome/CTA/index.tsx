import { Box, Button, Typography } from '@mui/material';
import Image from 'next/image';
import Link from 'next/link';

import { styles } from './styles';

const CTA = () => {
  return (
    <Box sx={styles.ctaWrapper}>
      {/* Background image */}
      <Box sx={styles.bgImage}>
        <Image
          src="/images/CTA/CTA_bg.png"
          alt=""
          fill
          sizes="100vw"
          priority
        />
      </Box>
      <Box sx={styles.bgImageMB}>
        <Image
          src="/images/CTA/bg_mb_2.png"
          alt=""
          fill
          sizes="100vw"
          priority
        />
      </Box>

      <Box sx={styles.contentContainer}>
        {/* Title */}
        <Typography variant="h2" sx={styles.titleText}>
          夫婦問題・離婚の専門家にオンラインで気軽に相談
        </Typography>

        {/* Buttons */}
        <Box sx={styles.buttonContainer}>
          {/* Orange Button */}
          <Link
            href="/counselors"
            passHref
            style={{ width: '100%', maxWidth: '300px' }}
          >
            <Button
              variant="contained"
              disableElevation
              sx={styles.orangeButton}
            >
              カウンセラーを探す
            </Button>
          </Link>

          {/* Lawyer <PERSON> */}
          <Link
            href="/lawyers"
            passHref
            style={{ width: '100%', maxWidth: '300px' }}
          >
            <Button
              variant="contained"
              disableElevation
              sx={styles.lawyerButton}
            >
              弁護士を探す
            </Button>
          </Link>
        </Box>
      </Box>
    </Box>
  );
};

export default CTA;

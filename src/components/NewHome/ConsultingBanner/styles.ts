import type { SxProps, Theme } from '@mui/material/styles';

export interface StylesType {
  [key: string]: SxProps<Theme>;
}

export const styles: StylesType = {
  bannerWrapper: {
    width: '100%',
    padding: 0,
  },
  bannerContainer: {
    position: 'relative',
    width: '100%',
    height: { xs: '180px', sm: '250px', md: '350px' },
    overflow: 'hidden',
    backgroundImage: {
      xs: 'url("/images/consulting-banner_mb.jpg")',
      md: 'url("/images/consulting-banner.jpg")',
    },
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.45)',
    zIndex: 1,
  },
  textContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
  },
  hintText: {
    color: '#FFFFFF',
    fontWeight: 700,
    fontSize: { xs: '28px', sm: '36px', md: '40px' },
    lineHeight: 1.4,
    textAlign: 'center',
  },
};

import { Box, Container, Stack, Typography } from '@mui/material';
import Image from 'next/image';
import React from 'react';

const HeadlinesSection = () => {
  const imgs = [
    '/images/hero/hero1.png',
    '/images/hero/hero2.png',
    '/images/hero/hero3.png',
    '/images/hero/hero4.png',
    '/images/hero/hero5.png',
  ];
  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        backgroundColor: '#FFB81F', // Fallback color
        overflow: 'hidden',
        height: { xs: 'auto', md: '500px' },
      }}
    >
      {/* Background Gradient */}
      <Box
        sx={{
          position: 'absolute',
          inset: 0,
          background:
            'linear-gradient(90deg, #FFD700 0%, #FF9700 50%, #FF7800 100%)',
          zIndex: 0,
        }}
      />

      <Container
        maxWidth="lg"
        sx={{
          position: 'relative',
          zIndex: 1,
          height: '100%',
          px: { xs: 2, md: 3 },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: 'stretch',
            height: { md: '100%' },
            position: 'relative',
          }}
        >
          {/* Left side - Text content */}
          <Box
            sx={{
              width: { xs: '100%', md: '30%' },
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              py: { xs: 4, md: 8 },
              pb: { xs: 0 },
              pr: { md: 4 },
              position: 'relative',
              zIndex: 2,
            }}
          >
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '24px', md: '30px' },
                fontWeight: 700,
                color: 'white',
                mb: {
                  md: 4,
                  xs: 2,
                },
                whiteSpace: 'nowrap',
                overflow: 'visible',
              }}
            >
              幸せに向かう再出発へ。
            </Typography>

            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: 700,
                color: '#262626',
                whiteSpace: 'pre-line',
                lineHeight: 1.75,
                mb: '32px',
                display: { xs: 'none', md: 'inline' },
              }}
            >
              家族のカタチが多様化する時代。 夫婦問題や離婚で悩むすべての人に、
              最新の実績を持つ専門家とのマッチングを提供。
              さらに、最新の情報や価値観をシェア。
              すべては、幸せに向けた再出発のために。
              離婚したい、したくない、ぜんぶ。
            </Typography>

            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: 700,
                color: '#262626',
                whiteSpace: 'pre-line',
                lineHeight: 1.75,
                mb: {
                  md: '32px',
                  xs: 2,
                },
                display: { xs: 'inline', md: 'none' },
              }}
            >
              家族のカタチが多様化する時代。 夫婦問題や離婚で悩むすべての人に、
              最新の実績を持つ専門家とのマッチングを提供。
              さらに、最新の情報や価値観をシェア。
              すべては、幸せに向けた再出発のために。
            </Typography>
            <Box display="flex">
              <Box>
                <Typography
                  sx={{
                    fontSize: '16px',
                    fontWeight: 700,
                    color: '#262626',
                    whiteSpace: 'pre-line',
                    lineHeight: 1.75,
                    mb: '16px',
                    display: { xs: 'block', md: 'none' },
                  }}
                >
                  離婚したい、
                  <br />
                  したくない、 <br />
                  ぜんぶ。 <br />
                </Typography>
                <Image
                  src="/images/white_logo.svg"
                  alt="Logo"
                  width={103}
                  height={40}
                  priority
                />
              </Box>
              <Box
                sx={{
                  display: { xs: 'block', md: 'none' },
                  flex: 1,
                  textAlign: 'right',
                  height: '208px',
                }}
              >
                <Image
                  src="/images/healine.png"
                  // style={{
                  //   position: 'absolute',
                  //   top: '-4px',
                  //   right: 0,
                  //   zIndex: 1,
                  // }}
                  alt="Logo Text"
                  width={212}
                  height={208}
                />
              </Box>
            </Box>
          </Box>

          {/* Image - Positioned to fill the right side */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: { md: '75%' },
              height: '100%',
              display: { xs: 'none', md: 'block' },
              zIndex: 1,
            }}
          >
            <Image
              src="/images/headlines/hero_1_image.png"
              alt="Woman smiling with people"
              width={825}
              height={500}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                objectPosition: 'center',
              }}
              priority
            />
          </Box>

          {/* Mobile image - shown only on mobile */}
          <Stack
            sx={{
              display: {
                md: 'none',
              },
            }}
            mx={'-16px'}
            direction={'row'}
            spacing={0}
          >
            {imgs.map((img, index) => {
              return (
                <Box
                  key={img}
                  sx={{
                    width: '100%',
                    position: 'relative',
                    display: 'inline-block',
                    background:
                      index !== imgs.length - 1
                        ? 'linear-gradient(to right, #FFD700, #FF7800);'
                        : 'linear-gradient(to left, #FFD700, #FF7800)',
                  }}
                >
                  <Image
                    quality={100}
                    src={img}
                    alt={`Hero Image ${index + 1}`}
                    width={175}
                    height={206}
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                    priority
                  />
                </Box>
              );
            })}
          </Stack>
        </Box>
      </Container>
    </Box>
  );
};

export default HeadlinesSection;

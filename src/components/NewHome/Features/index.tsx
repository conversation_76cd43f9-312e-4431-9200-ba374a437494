import { Box, Container, Typography } from '@mui/material';
import Image from 'next/image';

import { styles } from './styles';

const Features = () => {
  return (
    <Box sx={styles.featuresWrapper}>
      <Container maxWidth="lg">
        {/* Title Section */}
        <Box sx={styles.titleSection}>
          <Box sx={styles.logoContainer}>
            <Image
              src="/images/features/feature_logo_icon.svg"
              alt="リコ活"
              width={111}
              height={48}
            />
          </Box>
          <Typography variant="h2" sx={styles.titleText}>
            3つの特徴
          </Typography>
        </Box>

        {/* Content */}
        <Box sx={styles.contentContainer}>
          {/* Feature 1 */}
          <Box sx={styles.featureCard}>
            <Box sx={styles.featureImageWrapper}>
              <Box sx={styles.featureImageContainer}>
                <Image
                  src="/images/features/feature-1.png"
                  alt="カウンセラー × 弁護士"
                  fill
                  style={{
                    objectFit: 'cover',
                    borderRadius: '16px',
                  }}
                />
              </Box>
              <Typography sx={styles.featureNumber}>01</Typography>
            </Box>
            <Box
              px={{
                md: 0,
                xs: '20px',
              }}
            >
              <Typography sx={styles.featureTitle}>
                カウンセラー × 弁護士
              </Typography>
              <Typography sx={styles.featureDescription}>
                カウンセラーは心の整理を、弁護士は
                <br />
                法的な離婚手続きをサポート
              </Typography>
            </Box>
          </Box>

          {/* Feature 2 */}
          <Box sx={styles.featureCard}>
            <Box sx={styles.featureImageWrapper}>
              <Box sx={styles.featureImageContainer}>
                <Image
                  src="/images/features/feature-2.png"
                  alt="オンラインで気軽に相談"
                  fill
                  style={{
                    objectFit: 'cover',
                    borderRadius: '16px',
                  }}
                />
              </Box>
              <Typography sx={styles.featureNumber}>02</Typography>
            </Box>
            <Box
              px={{
                md: 0,
                xs: '20px',
              }}
            >
              <Typography sx={styles.featureTitle}>
                オンラインで気軽に相談
              </Typography>
              <Typography sx={styles.featureDescription}>
                仕事の合間や終わった後、お休みなど、
                <br />
                時間も場所も選ばずいつでも相談できる
              </Typography>
            </Box>
          </Box>

          {/* Feature 3 */}
          <Box sx={styles.featureCard}>
            <Box sx={styles.featureImageWrapper}>
              <Box sx={styles.featureImageContainer}>
                <Image
                  src="/images/features/feature-3.png"
                  alt="専門家共同開発のカルテ"
                  fill
                  style={{
                    objectFit: 'cover',
                    borderRadius: '16px',
                  }}
                />
              </Box>
              <Typography sx={styles.featureNumber}>03</Typography>
            </Box>
            <Box
              px={{
                md: 0,
                xs: '20px',
              }}
            >
              <Typography sx={styles.featureTitle}>
                専門家共同開発のカルテ
              </Typography>
              <Typography sx={styles.featureDescription}>
                うまく話せない、自分でもわからない...
                <br />
                しっかり整理できるから解決につながる
              </Typography>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Features;

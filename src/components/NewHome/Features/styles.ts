import type { Theme } from '@mui/material';
import type { SxProps } from '@mui/system';

type StylesType = Record<string, SxProps<Theme>>;

export const styles: StylesType = {
  featuresWrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    padding: { xs: '40px 0', md: '72px 0' },
    backgroundColor: '#FFFAF0',
  },
  titleSection: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: { xs: '24px', md: '48px' },
    width: '100%',
    gap: '8px',
  },
  logoContainer: {
    width: 111,
    height: 48,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    color: '#FF8C00',
    fontWeight: 700,
    fontSize: { xs: '24px', md: '32px' },
    lineHeight: 1.4,
    textAlign: 'center',
  },
  contentContainer: {
    display: 'flex',
    flexDirection: { xs: 'column', md: 'row' },
    gap: { xs: '32px', md: '24px' },
    width: '100%',
    justifyContent: 'center',
    maxWidth: '1100px',
  },
  featureCard: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    width: { xs: '100%', md: 'calc(33.33% - 16px)' },
    maxWidth: { xs: '100%', md: '350px' },
  },
  featureImageWrapper: {
    position: 'relative',
    width: '100%',
    paddingBottom: '18px', // Space for the half-overlapped number
  },
  featureImageContainer: {
    position: 'relative',
    width: '100%',
    paddingTop: '100%', // Square aspect ratio
    borderRadius: '16px',
    overflow: 'hidden',
  },
  featureNumber: {
    position: 'absolute',
    left: '16px',
    bottom: {
      md: '0px',
      xs: '4px',
    },
    color: '#000000',
    fontWeight: 700,
    fontSize: { xs: '36px', md: '42px' },
    lineHeight: 1,
    zIndex: 2,
  },
  iconOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    padding: '16px',
  },
  iconsContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: '250px',
  },
  iconWrapper: {
    width: 48,
    height: 48,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  featureTitle: {
    color: '#FF8C00',
    fontWeight: 700,
    fontSize: { xs: '24px', md: '24px' },
    lineHeight: 1.4,
  },
  featureDescription: {
    color: '#333333',
    fontWeight: 500,
    fontSize: { xs: '16px', md: '16px' },
    lineHeight: 1.5,
  },
};

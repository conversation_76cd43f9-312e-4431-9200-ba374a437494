import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useScroll } from 'framer-motion';
import React, { useEffect, useState } from 'react';
import { getLineUrl } from 'utils/getLineUrl';

// Removed LINE_GREEN as background is now white

const FloatingContainer = styled(Box, {
  shouldForwardProp: (prop) =>
    prop !== 'isVisible' &&
    prop !== 'dynamicBottom' &&
    prop !== 'dynamicRight' &&
    prop !== 'isMobile',
})<{
  isVisible: boolean;
  dynamicBottom: number;
  dynamicRight: number;
  isMobile: boolean;
}>(({ isVisible, dynamicBottom, dynamicRight, isMobile, theme }) => ({
  position: 'fixed',
  // Desktop positioning - dynamic based on scroll-to-top button visibility
  right: isMobile ? 'auto' : dynamicRight,
  left: isMobile ? '50%' : 'auto',
  transform: isMobile ? 'translateX(-50%)' : 'none',
  bottom: dynamicBottom,
  width: 320, // Increased width for better spacing between icon and text
  height: 64,
  backgroundColor: 'white',
  borderRadius: 32,
  display: isVisible ? 'flex' : 'none',
  alignItems: 'center',
  justifyContent: 'flex-start',
  padding: '0 16px',
  cursor: 'pointer',
  zIndex: 1000,
  boxShadow: '0px 4px 4px 0px rgba(98, 46, 0, 0.2)',
  transition: 'all 0.6s cubic-bezier(0.33, 1, 0.68, 1)', // Match scroll-to-top button transition
  // Mobile adjustments - center positioning
  [theme.breakpoints.down('tablet')]: {
    width: 280,
    left: '50% !important',
    right: 'auto !important',
    transform: 'translateX(-50%) !important',
  },
  '&:hover': {
    backgroundColor: '#f5f5f5',
    boxShadow: '0px 6px 8px 0px rgba(98, 46, 0, 0.25)',
    [theme.breakpoints.up('tablet')]: {
      transform: 'translateY(-2px)', // Desktop hover effect
    },
    [theme.breakpoints.down('tablet')]: {
      transform: 'translateX(-50%) translateY(-2px) !important',
    },
  },
  '&:active': {
    backgroundColor: '#eeeeee',
    [theme.breakpoints.up('tablet')]: {
      transform: 'translateY(0px)', // Desktop active effect
    },
    [theme.breakpoints.down('tablet')]: {
      transform: 'translateX(-50%) translateY(0px) !important',
    },
  },
}));

const ContentContainer = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flex: 1,
});

const LineIconContainer = styled(Box)({
  position: 'absolute',
  left: 16,
  top: '50%',
  transform: 'translateY(-50%)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
});

const TitleText = styled(Typography)({
  color: '#333333',
  fontSize: 16,
  fontWeight: 600,
  lineHeight: 1,
  userSelect: 'none',
});

const CloseButton = styled(Box)({
  position: 'absolute',
  right: 16,
  top: '50%',
  transform: 'translateY(-50%)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 24,
  height: 24,
  cursor: 'pointer',
  '&:hover': {
    opacity: 0.8,
  },
});

interface FloatingLineButtonProps {
  onClose?: () => void;
}

const FloatingLineButton: React.FC<FloatingLineButtonProps> = ({ onClose }) => {
  const [isVisible, setIsVisible] = useState(true);
  const [opacity, setOpacity] = useState(0);

  // Use same scroll logic as scroll-to-top button
  const { scrollY } = useScroll();

  // Handle scroll position for opacity (same as scroll-to-top button)
  useEffect(() => {
    return scrollY.onChange((latest) => {
      if (latest > document.documentElement.clientHeight) {
        setOpacity(1);
      } else {
        setOpacity(0);
      }
    });
  }, [scrollY]);

  const handleClick = () => {
    window.open(getLineUrl(), '_blank', 'noopener,noreferrer');
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsVisible(false);
    if (onClose) {
      onClose();
    }
  };

  // Calculate dynamic bottom position (match scroll-to-top button positioning exactly)
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => setIsMobile(window.innerWidth < 768);
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  const dynamicBottom = isMobile ? 90 : 40; // xs: 90 (below scroll-to-top button), tablet: fixed 40px

  // Desktop positioning logic
  const scrollToTopVisible = opacity === 1;
  let dynamicRight = 40;

  if (scrollToTopVisible) {
    dynamicRight = isMobile ? 40 : 106;
  }

  return (
    <FloatingContainer
      isVisible={isVisible}
      dynamicBottom={dynamicBottom}
      dynamicRight={dynamicRight}
      isMobile={isMobile}
      onClick={handleClick}
      sx={{
        opacity: isVisible ? 1 : 0,
        pointerEvents: isVisible ? 'normal' : 'none',
        // Force center on mobile with inline styles to override everything
        ...(isMobile
          ? {
              left: '50% !important',
              right: 'auto !important',
              transform: `translateX(-50%) scale(${
                isVisible ? 1 : 0
              }) !important`,
            }
          : {
              transform: `scale(${isVisible ? 1 : 0})`,
            }),
      }}
    >
      <LineIconContainer>
        <img
          src="/images/line-icon.svg"
          alt="LINE"
          width={44}
          height={44}
          style={{ display: 'block' }}
        />
      </LineIconContainer>
      <ContentContainer>
        <TitleText>LINEで相談&予約する</TitleText>
      </ContentContainer>
      <CloseButton onClick={handleClose}>
        <img
          src="/images/close_line_icon.svg"
          alt="Close"
          width={12}
          height={12}
          style={{ display: 'block' }}
        />
      </CloseButton>
    </FloatingContainer>
  );
};

export default FloatingLineButton;

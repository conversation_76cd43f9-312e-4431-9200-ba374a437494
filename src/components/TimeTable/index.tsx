import { Box, Stack } from '@mui/material';
import type { ResponsiveStyleValue } from '@mui/system';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import { motion } from 'framer-motion';
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { WORK_TIMES } from 'utils/constants';

import SlotCell from './SlotCell';
import TimeColumn from './TimeColumn';
import type { ISlotItem } from './utils';
import {
  formatSlots,
  getSelectedSlots,
  getSelectedSlotStyle,
  getSevenDatesFromNow,
  groudSlotsByDate,
} from './utils';

// Enable timezone plugin
dayjs.extend(timezone);

interface TimeTableProps {
  data: ISlotItem[];
  duration: number;
  onChange?: (timeSlot: string) => void;
  startTime: string;
  cellHeight?: number;
  dotHeight?: number;
  value?: string | null;
  closeStatus?: string[];
  openStatus?: string[];
  decidedStatus?: string[];
  isHasBookingSlot?: boolean;
  disabledFutureMinutes?: number;
  allDates?: string[];
  spacing?: ResponsiveStyleValue<number | string>;
  border?: string;
  isConvert?: boolean;
  maxEndTime?: number;
  hourExtra?: number;
  enableSkipSlot?: boolean; // New prop to enable skip slot functionality
}

const TimeTable = forwardRef<HTMLDivElement, TimeTableProps>(
  (
    {
      data,
      duration,
      onChange,
      startTime,
      value,
      closeStatus,
      openStatus,
      isHasBookingSlot,
      decidedStatus,
      disabledFutureMinutes = 0,
      allDates,
      spacing = '18px',
      border,
      isConvert,
      maxEndTime = 42,
      hourExtra = 0,
      enableSkipSlot = false,
    },
    ref,
  ) => {
    const valueRenderRef = useRef<boolean>(false);
    const [selected, setSelected] = useState<string | null>();
    const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
    const [skipSlot, setSkipSlot] = useState<number | null>(null);
    const internalContainerRef = useRef<HTMLDivElement>(null);
    const step = 30;
    const totalSlot = duration / step;

    const groupByDateObj = useMemo(() => groudSlotsByDate({ data }), [data]);
    const { openSlots, formattedSlots, slotsHasBooking } = useMemo(
      () =>
        formatSlots({
          data: groupByDateObj,
          totalSlot,
          closeStatus,
          openStatus,
          decidedStatus,
          disabledFutureMinutes,
        }),
      [
        groupByDateObj,
        totalSlot,
        closeStatus,
        openStatus,
        decidedStatus,
        disabledFutureMinutes,
      ],
    );

    const allDatesFromNow = useMemo(
      () => allDates || getSevenDatesFromNow(startTime),
      [allDates, startTime],
    );

    // Calculate skip slot based on earliest available slot
    useEffect(() => {
      if (enableSkipSlot && data && data.length > 0) {
        const currentTime = dayjs();
        const availableSlots = data.filter(
          (slot) =>
            openSlots.includes(slot.start) &&
            dayjs(slot.start).isBefore(
              currentTime.add(7, 'day').startOf('day'),
            ) &&
            dayjs(slot.start).isAfter(currentTime),
        );

        if (availableSlots.length > 0) {
          // Sort slots by time to find the earliest
          const earliestSlot = availableSlots.sort((a, b) => {
            const timeA = dayjs(a.start).tz('Asia/Tokyo');
            const timeB = dayjs(b.start).tz('Asia/Tokyo');
            const hourA = timeA.hour();
            const minuteA = timeA.minute();
            const hourB = timeB.hour();
            const minuteB = timeB.minute();

            // Compare hour first, then minute if hours are equal
            if (hourA !== hourB) return hourA - hourB;
            return minuteA - minuteB;
          })[0];

          if (earliestSlot) {
            // Calculate skipSlot based on earliest slot time and WORK_TIMES
            const calculatedSkipSlot =
              (dayjs(earliestSlot.start).tz('Asia/Tokyo').hour() -
                Number(WORK_TIMES[0]?.toString().substring(0, 2) || '0')) *
              2;

            setSkipSlot(calculatedSkipSlot); // Auto-scroll to the earliest slot after a short delay
            setTimeout(() => {
              const targetContainer = internalContainerRef?.current;
              if (targetContainer) {
                const targetElement = targetContainer.querySelector(
                  `[data-slot="${earliestSlot.start}"]`,
                ) as HTMLElement;

                if (targetElement) {
                  targetContainer.scrollTo({
                    // each skip slot is 48px
                    top: 48 * calculatedSkipSlot,
                    behavior: 'smooth',
                  });
                }
              }
            }, 100);
          }
        }
      }
    }, [data, enableSkipSlot]);

    // Expose skipSlot value for container height calculation
    useEffect(() => {
      if (
        enableSkipSlot &&
        skipSlot !== null &&
        internalContainerRef?.current
      ) {
        const container = internalContainerRef.current;
        const containerHeight = `${(WORK_TIMES.length - skipSlot) * 48}px`;

        Object.assign(container.style, {
          height: containerHeight,
          overflowY: 'auto',
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none', // IE and Edge
        });

        // Hide webkit scrollbar
        const style = document.createElement('style');
        style.textContent = `
        .skip-slot-container::-webkit-scrollbar {
          display: none;
        }
      `;
        document.head.appendChild(style);
        container.classList.add('skip-slot-container');
      }
    }, [skipSlot, enableSkipSlot]);

    useEffect(() => {
      if (value && !valueRenderRef.current) {
        setSelected(value);
        const chooseSlots = getSelectedSlots({
          allSlots: formattedSlots,
          selectedSlot: value,
          totalSlot,
        });
        setSelectedSlots(chooseSlots);
        valueRenderRef.current = true;
      } else if (!value) {
        setSelected(null);
        setSelectedSlots([]);
      }
    }, [formattedSlots, totalSlot, value]);

    useEffect(() => {
      return () => {
        setSelected(null);
        setSelectedSlots([]);
        valueRenderRef.current = false;
      };
    }, []);

    const onClickSlot = useCallback(
      (selectedSlot: string) => {
        const chooseSlots = getSelectedSlots({
          allSlots: formattedSlots,
          selectedSlot,
          totalSlot,
        });
        setSelected(selectedSlot);
        setSelectedSlots(chooseSlots);
        if (chooseSlots.length > 0 && chooseSlots[0] && onChange) {
          onChange(chooseSlots[0]);
        }
      },
      [formattedSlots, onChange, totalSlot],
    );

    const timeTableContent = (
      <Stack direction="row" spacing={spacing} ref={ref}>
        <TimeColumn />
        <Box
          display="flex"
          flexDirection="row"
          width={1}
          component={motion.div}
          key={startTime}
          initial={{ x: 20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.4 }}
        >
          {allDatesFromNow.map((day) => {
            return (
              <Box
                display="flex"
                flexDirection="column"
                key={day}
                flex="1 0 0%"
              >
                {WORK_TIMES.map((time) => {
                  const selectedTime = dayjs(time, 'HH:mm');
                  const date = isConvert
                    ? dayjs(day)
                        .tz('Asia/Tokyo')
                        .set('hour', selectedTime.get('hour'))
                        .set('minute', selectedTime.get('minute'))
                    : dayjs(day)
                        .set('hour', selectedTime.get('hour'))
                        .set('minute', selectedTime.get('minute'));
                  const dateString = date.toISOString();
                  const slotStyle = getSelectedSlotStyle(
                    selectedSlots,
                    selected,
                  );
                  return (
                    <SlotCell
                      hourExtra={hourExtra}
                      maxEndTime={maxEndTime}
                      border={border}
                      selected={selected === dateString}
                      onSlotSelect={onClickSlot}
                      disabled={
                        isHasBookingSlot
                          ? !openSlots.includes(dateString) &&
                            !slotsHasBooking.includes(dateString)
                          : !openSlots.includes(dateString)
                      }
                      isHasBooking={
                        isHasBookingSlot
                          ? slotsHasBooking.includes(dateString)
                          : false
                      }
                      disabledFutureMinutes={disabledFutureMinutes}
                      key={dateString}
                      timeSlot={dateString}
                      data-slot={dateString} // Add data attribute for targeting
                      {...(selected === dateString ? slotStyle : {})}
                    />
                  );
                })}
              </Box>
            );
          })}
        </Box>
      </Stack>
    );

    if (enableSkipSlot) {
      return (
        <Box
          ref={internalContainerRef}
          sx={{
            position: 'relative',
            // Height and overflow will be set by useEffect
          }}
        >
          {timeTableContent}
        </Box>
      );
    }

    return timeTableContent;
  },
);

TimeTable.displayName = 'TimeTable';

const areEqual = (prevProps: TimeTableProps, nextProps: TimeTableProps) => {
  return JSON.stringify(prevProps) === JSON.stringify(nextProps);
};

export default memo(TimeTable, areEqual);

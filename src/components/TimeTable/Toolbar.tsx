import {
  Box,
  Button,
  IconButton,
  Stack,
  SvgIcon,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import { ArrowLeftIcon, ArrowRightIcon } from 'icons';
import { MomentFormat } from 'utils/constants';

import DateHeader from './DateHeader';
import styles from './styles';

export const getSevenDatesFromNow = (date: string) => {
  const startOfWeek = dayjs(date);
  return [...Array(7).keys()].map((index) =>
    startOfWeek.add(index, 'day').toISOString(),
  );
};
interface ToolbarProps {
  startTime: string;
  onNavigate: (date: string) => void;
}
const Toolbar = ({ startTime, onNavigate }: ToolbarProps) => {
  const year = dayjs(startTime).format(MomentFormat.JP_YEAR);
  const formatStartTime = dayjs(startTime).format(MomentFormat.JP_MONTH_DATE);
  const formatEndTime = dayjs(startTime)
    .add(6, 'day')
    .format(MomentFormat.JP_MONTH_DATE);

  return (
    <>
      <Stack
        direction="row"
        spacing="21px"
        display="flex"
        justifyContent="space-between"
        alignItems={'center'}
      >
        <Button
          variant="whiteOutlined"
          size="small"
          fullWidth
          className="tabletStyle"
          sx={{ maxWidth: { xs: 80, tablet: 112 }, height: 40 }}
          onClick={() => onNavigate(dayjs().startOf('d').toISOString())}
        >
          今日
        </Button>
        <Stack direction="row" spacing="61px">
          <Box
            width="230px"
            justifyContent="center"
            display={{ xs: 'none', tablet: 'block' }}
          >
            <Typography sx={styles.titleTime}>{`${year}`}</Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <IconButton
              sx={styles.iconButton}
              onClick={() =>
                onNavigate(dayjs(startTime).subtract(7, 'd').toISOString())
              }
              disabled={dayjs() >= dayjs(startTime)}
            >
              <SvgIcon fontSize="small" component={ArrowLeftIcon} />
            </IconButton>
            <IconButton
              sx={styles.iconButton}
              disabled={dayjs(startTime).add(13, 'd').diff(dayjs(), 'd') >= 42}
              onClick={() =>
                onNavigate(dayjs(startTime).add(7, 'd').toISOString())
              }
            >
              <SvgIcon fontSize="small" component={ArrowRightIcon} />
            </IconButton>
          </Stack>
        </Stack>
      </Stack>
      <Box
        display={{ xs: 'block', tablet: 'none' }}
        mt={{ xs: '4px', tablet: 0 }}
      >
        <Typography sx={styles.titleTime}>{`${year}`}</Typography>
      </Box>
      <Typography sx={styles.titleTime}>
        {`${formatStartTime} - ${formatEndTime}`}
      </Typography>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        mt={{ xs: 2, tablet: 3 }}
      >
        <Stack spacing={2} direction="row">
          <Stack spacing="4px" direction="row" sx={styles.titleText}>
            <Box width="13.3px" height="13.3px" bgcolor="#edf1f3"></Box>
            <Typography fontSize={12}>{'空きなし'}</Typography>
          </Stack>
          <Stack spacing="4px" direction="row" sx={styles.titleText}>
            <Box
              width="10.7px"
              height="10.7px"
              border="1.3px solid #FCAB28"
              borderRadius="100%"
            ></Box>
            <Typography fontSize={12}>{'空きあり'}</Typography>
          </Stack>
          <Stack spacing="4px" direction="row" sx={styles.titleText}>
            <Box
              width="10.7px"
              height="10.7px"
              bgcolor="#ebc35a"
              borderRadius="100%"
            ></Box>
            <Typography fontSize={12}>{'現在の予約'}</Typography>
          </Stack>
        </Stack>
      </Box>
      <DateHeader startTime={startTime} />
    </>
  );
};

export default Toolbar;

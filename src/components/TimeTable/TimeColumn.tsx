import { Box, Typography } from '@mui/material';
import { memo, useEffect, useRef } from 'react';
import { WORK_TIMES } from 'utils/constants';

import { CELL_HEIGHT } from './utils';

const TimeColumn = ({ cellHeight = CELL_HEIGHT }: { cellHeight?: number }) => {
  const scrollToRef = useRef<null | HTMLDivElement>(null);

  useEffect(() => {
    if (scrollToRef.current) {
      scrollToRef.current.scrollIntoView();
    }
  }, []);

  return (
    <Box display="flex" flexDirection="column">
      {WORK_TIMES.map((item, index) => (
        <Box
          key={index}
          width="35px"
          height={cellHeight}
          display="flex"
          alignItems="center"
          justifyContent="center"
          ref={item === '07:00' ? scrollToRef : null}
        >
          <Typography fontSize={{ xs: 10, tablet: 12 }}>{item}</Typography>
        </Box>
      ))}
    </Box>
  );
};

export default memo(TimeColumn);

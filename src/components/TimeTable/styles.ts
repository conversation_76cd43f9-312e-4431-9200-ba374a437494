import type { SxProps, Theme } from '@mui/material';

const styles: Record<string, SxProps<Theme>> = {
  titleTime: {
    fontSize: { xs: 18, tablet: 24 },
    fontWeight: 'bold',
    color: 'heading',
    textAlign: 'center',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: '2',
    WebkitBoxOrient: 'vertical',
  },
  iconButton: {
    p: '5px',
    border: 'solid 1px #d2dce1',
    width: '30px',
    height: '30px',
  },
  titleText: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
};

export default styles;

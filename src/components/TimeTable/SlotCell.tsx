import { Box, ButtonBase } from '@mui/material';
import dayjs from 'dayjs';
import { motion } from 'framer-motion';
import { CloseIcon } from 'icons';
import { memo } from 'react';

interface SlotCellProps {
  timeSlot: string;
  selected: boolean;
  onSlotSelect?: (date: string) => void;
  translateY?: number;
  height?: number;
  disabled?: boolean;
  isHasBooking?: boolean;
  disabledFutureMinutes?: number;
  border?: string;
  maxEndTime?: number;
  hourExtra?: number;
  'data-slot'?: string; // Add support for data attribute
}

const cellHeight = 48;

const SlotCell = ({
  timeSlot,
  disabled,
  translateY = 0,
  height = 0,
  selected,
  onSlotSelect,
  isHasBooking,
  disabledFutureMinutes = 0,
  border = '1px solid #FCAB28',
  maxEndTime = 42,
  hourExtra = 0,
  'data-slot': dataSlot,
  ...rest
}: SlotCellProps) => {
  const currentDate = new Date();
  if (hourExtra) {
    currentDate.setHours(currentDate.getHours() + hourExtra);
  }

  const isPastSlot =
    new Date(timeSlot).getTime() < currentDate.getTime() ||
    dayjs().startOf('d').add(maxEndTime, 'day') <= dayjs(timeSlot);

  let slotBgColor = 'white';
  if (isPastSlot) {
    slotBgColor =
      'repeating-linear-gradient( -18deg, #d2dce1, #d2dce1 0px, #FFF 2px, #fff 8px )';
  } else if (
    disabled ||
    // Not allow booking in nearest 60 minutes
    dayjs(timeSlot).diff(dayjs(), 'minute') < disabledFutureMinutes
  ) {
    slotBgColor = '#edf1f3';
  }

  return (
    <ButtonBase
      disabled={isPastSlot || disabled}
      data-slot={dataSlot || timeSlot} // Add the data-slot attribute
      sx={{
        height: cellHeight,
        border: '1px solid #d2dce1',
        background: slotBgColor,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'background-color 0.3s ease',
      }}
      onClick={() => {
        if (!disabled && onSlotSelect && !isHasBooking) {
          onSlotSelect(timeSlot);
        }
      }}
      {...rest}
    >
      {!isPastSlot &&
        !disabled &&
        (isHasBooking ? (
          <Box sx={{ svg: { color: 'neutral7' } }}>
            <CloseIcon />
          </Box>
        ) : (
          <Box
            borderRadius="100%"
            border={selected ? '' : border}
            width="16px"
            height="16px"
            position="relative"
            component={motion.div}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.2 }}
          >
            <Box
              sx={{
                pointerEvents: 'none',
                transition: '.4s ease',
                opacity: selected ? 1 : 0,
                transform: `translate(0px, ${selected ? translateY : 0}px)`,
              }}
              borderRadius={selected ? '8px' : '50%'}
              width={17}
              bgcolor="#ebc35a"
              height={selected ? height : 16}
              position="absolute"
              top={-1.5}
              left={-0.5}
              zIndex={1}
            />
          </Box>
        ))}
    </ButtonBase>
  );
};

export default memo(SlotCell);

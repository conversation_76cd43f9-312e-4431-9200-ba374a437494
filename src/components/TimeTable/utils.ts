import dayjs from 'dayjs';

export const CELL_HEIGHT = 48;
export const DOT_HEIGHT = 18;

export interface ISlotItem {
  start: string;
  end: string;
  status: string;
}
const calcSlotHeight = (number: number) =>
  CELL_HEIGHT * (number - 1) + DOT_HEIGHT;

export const getSelectedSlots = ({
  allSlots,
  selectedSlot,
  totalSlot,
}: {
  allSlots: ISlotItem[][];
  selectedSlot: string;
  totalSlot: number;
}) => {
  let selectedSlots: string[] = [];
  if (selectedSlot) {
    allSlots.map((slots) => {
      return slots.map((el, indexSlot) => {
        if (el.start === selectedSlot) {
          for (
            let i = indexSlot + totalSlot - 1;
            i > indexSlot - totalSlot;
            i -= 1
          ) {
            if (selectedSlots.length === totalSlot) {
              break;
            }
            if (slots[i]?.status === 'OPEN') {
              const selectedTime = slots[i]?.start as string;
              selectedSlots.push(selectedTime);
            } else {
              selectedSlots = [];
            }
          }
        }
        return null;
      });
    });
  }
  return selectedSlots.sort(
    (a, b) => new Date(a).getTime() - new Date(b).getTime(),
  );
};

export const getSelectedSlotStyle = (
  selectedSlots: string[],
  slot?: string | null,
) => {
  let translateY = 0;
  if (slot) {
    const pos = selectedSlots.findIndex((i) => i === slot);
    if (pos === selectedSlots.length - 1) {
      translateY =
        (calcSlotHeight(selectedSlots.length) -
          (CELL_HEIGHT - DOT_HEIGHT) / 2) *
        -1;
    } else if (pos > 0) {
      translateY = CELL_HEIGHT * pos * -1;
    }
  }
  return { translateY, height: calcSlotHeight(selectedSlots.length) };
};

export const getSevenDatesFromNow = (date: string) => {
  const startOfWeek = dayjs(date);
  return [...Array(7).keys()].map((index) =>
    startOfWeek.add(index, 'day').toISOString(),
  );
};

export const groudSlotsByDate = ({ data }: { data: ISlotItem[] }) => {
  const groupByDateObj: {
    [date: string]: ISlotItem[];
  } = {};
  for (let i = 0; i < data.length; i += 1) {
    const slot = data[i]!;
    const dateString = dayjs(slot.start).startOf('d').toISOString();
    if (groupByDateObj[dateString]) {
      const prevSlots = groupByDateObj[dateString];
      if (
        prevSlots &&
        !prevSlots.map((slt) => slt.start).includes(slot.start)
      ) {
        groupByDateObj[dateString] = [...prevSlots, slot];
      }
    } else {
      groupByDateObj[dateString] = [slot];
    }
  }
  return groupByDateObj;
};

export const formatSlots = ({
  data = {},
  totalSlot = 30,
  closeStatus = ['CLOSED'],
  openStatus = ['OPEN'],
  decidedStatus = [],
  disabledFutureMinutes = 0,
}: {
  data: { [date: string]: ISlotItem[] };
  totalSlot: number;
  closeStatus?: string[];
  openStatus?: string[];
  decidedStatus?: string[];
  disabledFutureMinutes?: number;
}) => {
  const closeSlots: string[] = [];
  const openSlots: string[] = [];
  const slotsHasBooking: string[] = [];

  const formattedSlots = Object.values(data).map((slots) => {
    return slots.map((slot, indexSlot) => {
      let count = 0;
      const minIndex = indexSlot - (totalSlot - 1);
      const maxIndex = indexSlot + (totalSlot - 1);
      for (let i = minIndex; i <= maxIndex; i += 1) {
        if (count >= totalSlot) {
          break;
        }
        if (
          openStatus.includes(slots[i]?.status || '') &&
          dayjs(slots[i]?.start).diff(dayjs(), 'minute') > disabledFutureMinutes
        ) {
          count += 1;
        } else {
          count = 0;
        }
      }
      if (
        (closeStatus.includes(slot.status) || count < totalSlot) &&
        !closeSlots.includes(slot.start)
      ) {
        closeSlots.push(slot.start);
      }
      if (
        decidedStatus.includes(slot.status) &&
        !slotsHasBooking.includes(slot.start)
      ) {
        slotsHasBooking.push(slot.start);
      }
      if (count >= totalSlot && !openSlots.includes(slot.start)) {
        openSlots.push(slot.start);
      }
      return {
        ...slot,
        status: count >= totalSlot ? 'OPEN' : 'CLOSED',
        end: slot.end,
        start: slot.start,
      };
    });
  });
  return {
    formattedSlots,
    openSlots,
    closeSlots,
    slotsHasBooking,
  };
};

import { Box, Grid, Stack } from '@mui/material';
import TruncateText from 'components/TruncateText';
import dayjs from 'dayjs';
import type { IConsultationItem } from 'models/consultation/interface';
import { useMemo } from 'react';
import Helper from 'utils/helpers';

import styles from './styles';

const BookingConsultationMenuList = ({
  data = [],
  borderRadius = '8px',
}: {
  data: IConsultationItem[];
  borderRadius?: any;
}) => {
  const formatData = useMemo(() => {
    const unitPrices = data.find((item) => item.unitPrices)?.unitPrices || [];
    return [
      {
        _id: 'unit-price',
        title: '相談料',
        content: `${unitPrices[0]?.duration || 30}分 ${Helper.addComma(
          unitPrices[0]?.price || 0,
        )}円`,
        createdAt: dayjs().toISOString(),
        updatedAt: dayjs().toISOString(),
      } as IConsultationItem,
    ].concat(data);
  }, [data]);

  return (
    <Stack sx={styles.menuContainer}>
      {formatData.map((consultation, index) => (
        <Grid
          container
          columns={15}
          key={consultation._id}
          sx={styles.itemContainer}
        >
          <Grid item xs={5} tablet={5}>
            <Box
              className="consultation-title"
              sx={[
                {
                  borderTopLeftRadius: index === 0 ? borderRadius : 0,
                  borderBottomLeftRadius:
                    index === formatData.length - 1
                      ? { xs: 0, tablet: borderRadius }
                      : 0,
                },
                styles.title,
              ]}
            >
              <TruncateText
                textProps={{
                  fontSize: 12,
                  fontWeight: 500,
                  color: 'heading',
                }}
                text={consultation.title}
                lines={5}
              />
            </Box>
          </Grid>

          <Grid item xs className="consultation-content">
            <Box
              fontWeight={500}
              fontSize={12}
              minHeight={40}
              p={{ xs: '10px 8px', tablet: '12px 16px' }}
              sx={[
                {
                  borderTopRightRadius: index === 0 ? borderRadius : 0,
                  borderBottomRightRadius:
                    index === formatData.length - 1 ? borderRadius : 0,
                },
              ]}
            >
              <TruncateText
                textProps={{
                  fontSize: 12,
                }}
                text={consultation.content || ''}
                lines={5}
              />
            </Box>
          </Grid>
        </Grid>
      ))}
    </Stack>
  );
};

export default BookingConsultationMenuList;

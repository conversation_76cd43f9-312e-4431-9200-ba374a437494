const styles = {
  menuContainer: {
    spacing: 0,
    border: '1px solid #d2dce1',
    borderRadius: '12px',
    overflow: 'hidden',
  },
  itemContainer: {
    border: 'unset',
    borderRadius: 'unset',
    '.consultation-title, .consultation-price': {
      borderBottom: '1px solid #D2DCE1',
      p: { xs: '10px 8px 10px 10px', tablet: '12px 16px' },
    },
    '.consultation-content': {
      borderBottom: '1px solid #D2DCE1',
    },
    '&:last-child': {
      '.consultation-title, .consultation-price': {
        borderBottom: 'unset',
      },
      '.consultation-content': {
        borderBottom: 'unset',
      },
    },
  },
  title: () => ({
    p: { xs: '10px 16px', tablet: '12px 16px' },
    height: 1,
    fontSize: '12px',
    borderRight: '1px solid #D2DCE1',
  }),
} as const;
export default styles;

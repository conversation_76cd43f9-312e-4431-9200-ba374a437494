import type { Theme } from '@mui/material';

const styles = {
  menuContainer: {
    spacing: { xs: 2, tablet: 0 },
    border: { tablet: '1px solid #d2dce1' },
    borderRadius: { tablet: '12px' },
    overflow: { tablet: 'hidden' },
  },
  menuDuration: {
    color: 'heading',
    fontSize: 14,
    lineHeight: { tablet: '24px' },
  },
  menuPrice: {
    color: 'heading',
    textAlign: 'right',
    fontSize: 14,
    lineHeight: { tablet: '24px' },
  },
  itemContainer: {
    border: { xs: '1px solid #D2DCE1', tablet: 'unset' },
    borderRadius: { xs: 1, tablet: 'unset' },
    '.consultation-price': {
      borderBottom: { tablet: '1px solid #D2DCE1' },
    },
    '.consultation-title': {
      borderBottom: '1px solid #D2DCE1',
    },
    '&:last-child': {
      '.consultation-title, .consultation-price': {},
    },
  },
  title: (theme: Theme) => ({
    bgcolor: '#F6F8F9',
    p: { xs: '10px 16px', tablet: '12px 16px' },
    height: 1,
    fontSize: 14,
    fontWeight: 500,
    [theme.breakpoints.down('tablet')]: {
      borderTopLeftRadius: '12px',
      borderTopRightRadius: '12px',
      boxShadow: 'unset',
    },
  }),
} as const;
export default styles;

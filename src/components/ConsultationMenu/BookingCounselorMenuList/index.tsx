import { Box, Grid, Stack, Typography } from '@mui/material';
import TruncateText from 'components/TruncateText';
import type { IConsultationItem } from 'models/consultation/interface';
import Helper from 'utils/helpers';

import styles from './styles';

type ConsultationItemWithMeetingType = IConsultationItem & {
  meetingType?: string;
};

const BookingCounselorMenuList = ({
  data = [],
}: {
  data: ConsultationItemWithMeetingType[];
}) => {
  return (
    <Stack spacing={{ xs: 2, tablet: 0 }} sx={styles.menuContainer}>
      {data.map((consultation, index) => {
        // Determine background color based on meetingType
        const getBackgroundColor = () => {
          if (consultation.meetingType === 'SINGLE') {
            return '#EDF1F3';
          }
          return 'white';
        };

        return (
          <Grid
            container
            columns={15}
            key={consultation._id}
            sx={styles.itemContainer}
          >
            <Grid item xs={15} tablet={6}>
              <Box
                className="consultation-title"
                sx={[
                  {
                    borderTopLeftRadius: index === 0 ? '12px' : 0,
                    borderBottomLeftRadius:
                      index === data.length - 1 ? { xs: 0, tablet: '12px' } : 0,
                    bgcolor: getBackgroundColor(),
                  },
                  // Override the default styles.title background color
                  {
                    ...styles.title,
                    bgcolor: getBackgroundColor(),
                  },
                ]}
              >
                <TruncateText
                  textProps={{
                    fontSize: 14,
                    fontWeight: 500,
                    color: 'heading',
                  }}
                  text={consultation.title}
                  lines={5}
                />
              </Box>
            </Grid>
            <Grid item xs={15} tablet={9}>
              <Box className="consultation-price" height={1}>
                <Box
                  maxWidth="175px"
                  display="flex"
                  justifyContent="space-between"
                  padding="12px 16px"
                >
                  <Typography sx={styles.menuDuration}>
                    {consultation.unitPrices &&
                      `${consultation.unitPrices[0]?.duration}分`}
                  </Typography>
                  <Typography sx={styles.menuPrice}>
                    {consultation.unitPrices &&
                      consultation.unitPrices[0]?.price &&
                      `${Helper.addComma(consultation.unitPrices[0].price)}円`}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        );
      })}
    </Stack>
  );
};

export default BookingCounselorMenuList;

import type { Theme } from '@mui/material';

const styles = {
  menuContainer: {
    spacing: { xs: 2, tablet: 0 },
    border: { tablet: '1px solid #d2dce1' },
    borderRadius: { tablet: '12px' },
    overflow: { tablet: 'hidden' },
    mt: {
      xs: 2,
      tablet: 4,
    },
  },
  menuDuration: {
    color: 'heading',
    fontSize: { xs: 14, tablet: 16 },
    lineHeight: { tablet: '24px' },
    p: {
      xs: '10px 16px 10px 0px',
      tablet: '8px 16px 8px 32px',
    },
  },
  menuPrice: {
    color: 'heading',
    textAlign: 'right',
    fontSize: { xs: 14, tablet: 16 },
    lineHeight: { tablet: '24px' },
    p: { xs: '10px 0px 10px 0px', tablet: '8px 16px 8px 0px' },
  },
  itemContainer: {
    border: { xs: '1px solid #D2DCE1', tablet: 'unset' },
    borderRadius: { xs: 1, tablet: 'unset' },
    '.consultation-title, .consultation-price': {
      borderBottom: '1px solid #D2DCE1',
    },
    '.consultation-content': {
      borderBottom: { xs: 'unset', tablet: '1px solid #D2DCE1' },
    },
    '&:last-child': {
      '.consultation-title, .consultation-price': {
        borderBottom: { xs: '1px solid #D2DCE1', tablet: 'unset' },
      },
      '.consultation-content': {
        borderBottom: 'unset',
      },
    },
  },
  title: (theme: Theme) => ({
    bgcolor: '#F6F8F9',
    p: { xs: '10px 16px', tablet: '8px 32px' },
    height: 1,
    [theme.breakpoints.down('tablet')]: {
      borderTopLeftRadius: '8px',
      borderTopRightRadius: '8px',
      boxShadow: 'unset',
    },
  }),
} as const;
export default styles;

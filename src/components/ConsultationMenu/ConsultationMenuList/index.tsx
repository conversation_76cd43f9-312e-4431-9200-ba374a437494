import { Box, Grid, Stack, Typography } from '@mui/material';
import TruncateText from 'components/TruncateText';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import type { IConsultationItem } from 'models/consultation/interface';
import { useMemo } from 'react';
import { ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

const ConsultationMenuList = ({
  data = [],
  providerType,
}: {
  data: IConsultationItem[];
  providerType: ProviderType;
}) => {
  const isLawyer = providerType === ProviderType.LAWYER;
  const formatData = useMemo(() => {
    const unitPrices = data.find((item) => item.unitPrices)?.unitPrices || [];
    if (!isEmpty(unitPrices) && providerType === ProviderType.LAWYER) {
      return [
        {
          _id: 'unit-price',
          title: '相談料',
          content: `${unitPrices[0]?.duration || 30}分 ${Helper.addComma(
            unitPrices[0]?.price || 0,
          )}円`,
          createdAt: dayjs().toISOString(),
          updatedAt: dayjs().toISOString(),
        } as IConsultationItem,
      ].concat(data);
    }
    return data;
  }, [data, providerType]);

  return (
    <Stack spacing={{ xs: 2, tablet: 0 }} sx={styles.menuContainer}>
      {formatData.map((consultation, index) => (
        <Grid
          container
          columns={15}
          key={consultation._id}
          sx={styles.itemContainer}
        >
          <Grid item xs={15} tablet={5}>
            <Box
              className="consultation-title"
              sx={[
                {
                  borderTopLeftRadius: index === 0 ? '8px' : 0,
                  borderBottomLeftRadius:
                    index === formatData.length - 1
                      ? { xs: 0, tablet: '8px' }
                      : 0,
                },
                styles.title,
              ]}
            >
              <TruncateText
                textProps={{
                  fontSize: { xs: 14, tablet: 16 },
                  fontWeight: 500,
                  color: 'heading',
                }}
                text={consultation.title}
                lines={5}
              />
            </Box>
          </Grid>
          {!isLawyer && (
            <>
              <Grid item xs={15} tablet={4}>
                <Box
                  className="consultation-price"
                  display="flex"
                  justifyContent={{ tablet: 'space-between' }}
                  mx={{ xs: 2, tablet: 0 }}
                  height={1}
                >
                  <Typography sx={styles.menuDuration}>
                    {consultation.unitPrices &&
                      `${consultation.unitPrices[0]?.duration}分`}
                  </Typography>
                  <Typography sx={styles.menuPrice}>
                    {consultation.unitPrices &&
                      consultation.unitPrices[0]?.price &&
                      `${Helper.addComma(consultation.unitPrices[0].price)}円`}
                  </Typography>
                </Box>
              </Grid>
            </>
          )}
          <Grid item xs className="consultation-content">
            <Box
              fontWeight={500}
              fontSize={{ xs: 14, tablet: 16 }}
              minHeight={40}
              p={{ xs: '10px 16px', tablet: '8px 16px 8px 32px' }}
              sx={[
                {
                  borderTopRightRadius: index === 0 ? '8px' : 0,
                  borderBottomRightRadius:
                    index === formatData.length - 1 ? '8px' : 0,
                },
              ]}
            >
              <TruncateText
                text={consultation.content || ''}
                lines={5}
                textProps={{
                  color: 'heading',
                  fontSize: { xs: 14, tablet: 16 },
                }}
              />
            </Box>
          </Grid>
        </Grid>
      ))}
    </Stack>
  );
};

export default ConsultationMenuList;

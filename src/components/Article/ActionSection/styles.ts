import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  actions: {
    p: 4,
    display: 'flex',
    justifyContent: 'center',
    bgcolor: 'white',
  },
  actionText: {
    fontSize: { xs: 14, tablet: 16 },
    fontWeight: 500,
    color: 'heading',
  },
  actionButton: {
    mt: 2,
    width: { xs: 311, tablet: 240 },
    '.MuiButton-startIcon': {
      mr: { xs: 9, tablet: 2 },
    },
  },
  actionWrapper: {
    flexDirection: { lg: 'row' },
    justifyContent: 'space-between',
    maxWidth: { tablet: '600px' },
    width: 1,
    gap: '36px',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import { Box, Button, Stack, Typography } from '@mui/material';
import { ArrowRightIcon } from 'icons';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { PROVIDER_LIST_PATH } from 'utils/constants';

import styles from './styles';

const NewConsultationButton = dynamic(
  () => import('components/Home/HomeConsultationButton'),
  { ssr: false },
);

const ActionSection = () => {
  return (
    <div>
      <Box
        p={{ xs: '40px 16px 44px', tablet: '64px 16px' }}
        justifyContent="center"
        bgcolor="#fff5cc"
      >
        <Typography
          fontSize={{ xs: 24, md: 36 }}
          color="#FCAB28"
          fontWeight={500}
          textAlign="center"
          mb={{ xs: '36px', tablet: 5 }}
          whiteSpace={{ xs: 'pre-line', tablet: 'normal' }}
        >
          {`離婚、夫婦問題・修復も\nオンラインで無料相談`}
        </Typography>
        <Box display="flex" justifyContent="center">
          <NewConsultationButton afterLoginText="相談がうまくいく" />
        </Box>
      </Box>
      <Box sx={styles.actions}>
        <Stack
          alignItems="center"
          sx={{
            ...styles.actionWrapper,
            maxWidth: 762,
          }}
        >
          <Box
            width={1}
            maxWidth={358}
            display="flex"
            flexDirection="column"
            gap={{ xs: '12px', tablet: 2 }}
          >
            <Typography sx={{ ...styles.actionText }} align="center">
              離婚なら
            </Typography>
            <Button
              variant="outlined"
              size="large"
              LinkComponent={Link}
              href={PROVIDER_LIST_PATH.LAWYER}
              className="shadow tabletStyle"
              fullWidth
              sx={{ maxWidth: 358 }}
              endIcon={
                <ArrowRightIcon color="#fdc84b" width={32} height={32} />
              }
            >
              弁護士を探す
            </Button>
          </Box>
          <Box
            maxWidth={358}
            width={1}
            display="flex"
            flexDirection="column"
            gap={{ xs: '12px', tablet: 2 }}
          >
            <Typography
              sx={styles.actionText}
              className="tabletStyle"
              align="center"
            >
              夫婦問題・修復なら
            </Typography>
            <Button
              LinkComponent={Link}
              href={PROVIDER_LIST_PATH.COUNSELOR}
              variant="outlined"
              size="large"
              fullWidth
              sx={{ maxWidth: 358 }}
              className="shadow tabletStyle"
              endIcon={
                <ArrowRightIcon color="#fdc84b" width={32} height={32} />
              }
            >
              カウンセラーを探す
            </Button>
          </Box>
        </Stack>
      </Box>
    </div>
  );
};

export default ActionSection;

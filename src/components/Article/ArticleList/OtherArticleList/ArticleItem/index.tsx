import type { SxProps, Theme } from '@mui/material';
import { Box, Stack, Typography } from '@mui/material';
import type { IPickupArticleListItem } from 'hooks/types';
import { get } from 'lodash';
import Image from 'next/image';
import Link from 'next/link';

import styles from './styles';

const ArticleItem = ({
  data,
  sx,
  pathname,
}: {
  data: IPickupArticleListItem;
  sx: SxProps<Theme>;
  pathname: string;
}) => {
  return (
    <Stack
      direction="row"
      spacing="12px"
      sx={[{ alignItems: 'center' }, ...(Array.isArray(sx) ? sx : [sx])]}
      className="article-item"
    >
      <Box
        width={{ xs: 72, tablet: 80 }}
        height={{ xs: 72, tablet: 80 }}
        borderRadius={1}
        overflow="hidden"
        sx={{ transform: 'translateZ(0)' }}
      >
        <Link
          href={{
            pathname,
            query: {
              articleId: data._id,
            },
          }}
        >
          <Image
            src={get(data, 'image.originUrl') || '/images/default-image.png'}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '8px',
            }}
            className="pointer"
            width={1}
            height={1}
            sizes="100vw"
            alt=""
          />
        </Link>
      </Box>
      <Box flex={1}>
        <Link
          href={{
            pathname,
            query: {
              articleId: data._id,
            },
          }}
        >
          <Typography
            sx={styles.title}
            className="line-clamp three-line article-title"
          >
            {data.title}
          </Typography>
        </Link>
      </Box>
    </Stack>
  );
};

export default ArticleItem;

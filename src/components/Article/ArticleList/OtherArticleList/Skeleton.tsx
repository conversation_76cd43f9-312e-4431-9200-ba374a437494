import { Box, Skeleton, Stack } from '@mui/material';
import { times } from 'lodash';

const SkeletonList = () => {
  return (
    <>
      {times(3).map((number) => {
        return (
          <Stack direction="row" spacing={2} key={number} flex={1}>
            <Skeleton width={80} height={80} variant="rounded" />
            <Box flex={1}>
              <Skeleton variant="text" sx={{ fontSize: 16 }} />
            </Box>
          </Stack>
        );
      })}
    </>
  );
};

export default SkeletonList;

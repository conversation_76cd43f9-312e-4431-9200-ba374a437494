import type { SxProps, Theme } from '@mui/material';
import { Divider, Stack } from '@mui/material';
import type { IPickupArticleListItem } from 'hooks/types';
import { isEmpty } from 'lodash';

import ArticleItem from './ArticleItem';
import SkeletonList from './Skeleton';

export interface IPickupArticleList {
  data: IPickupArticleListItem[];
  sx?: SxProps<Theme>;
  cellStyle?: SxProps<Theme>;
  divider?: boolean;
  loading?: boolean;
  pathname: string;
}
const ArticleList = ({
  data = [],
  sx = [],
  cellStyle = [],
  divider = false,
  loading,
  pathname,
}: IPickupArticleList) => {
  return (
    <Stack
      spacing={{ xs: 2, tablet: 3 }}
      divider={divider && <Divider />}
      sx={sx}
    >
      {loading && isEmpty(data) && <SkeletonList />}
      {!loading &&
        data.map((item) => (
          <ArticleItem
            data={item}
            key={item._id}
            sx={cellStyle}
            pathname={pathname}
          />
        ))}
    </Stack>
  );
};

export default ArticleList;

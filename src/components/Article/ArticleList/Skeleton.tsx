import { Grid, Skeleton } from '@mui/material';
import { times } from 'lodash';

const SkeletonList = () => {
  return (
    <>
      {times(3).map((number) => {
        return (
          <Grid container columnSpacing={2} key={number}>
            <Grid item xs={4}>
              <Skeleton
                variant="rectangular"
                width="100%"
                sx={{ pt: 'calc(302/542 * 100%)' }}
              />
            </Grid>
            <Grid item xs={8}>
              <Skeleton variant="text" sx={{ fontSize: 24 }} />
              <Skeleton variant="text" />
            </Grid>
          </Grid>
        );
      })}
    </>
  );
};

export default SkeletonList;

import { Box, Divider, Grid, Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import type { IArticleListItem, IPickupArticleListItem } from 'hooks/types';
import { t } from 'i18n';
import { get, isEmpty } from 'lodash';
import Image from 'next/image';
import Link from 'next/link';
import { MomentFormat, PROVIDER_LIST_PATH } from 'utils/constants';

import SkeletonList from './Skeleton';
import styles from './styles';

export interface IArticleList {
  data: IArticleListItem[] | IPickupArticleListItem[];
  loading: boolean;
  showLawyer?: boolean;
  pathname: string;
  divider?: boolean;
  spacing?: (number | string)[] | number | object | string;
}
const ArticleList = ({
  data,
  loading,
  showLawyer,
  pathname,
  divider,
  spacing = 4,
}: IArticleList) => {
  return (
    <Stack
      divider={divider && <Divider />}
      rowGap={spacing as never}
      className="article-list-wrapper"
    >
      {loading && isEmpty(data) && <SkeletonList />}
      {!loading &&
        data.map((article) => {
          return (
            <Grid
              container
              columns={14}
              key={article._id}
              className="article-item-grid"
            >
              <Grid item xs={5}>
                <Box
                  borderRadius={{ xs: '6px', tablet: '12px' }}
                  overflow="hidden"
                  sx={{
                    transform: 'translateZ(0)',
                    img: {
                      borderRadius: { xs: '6px', tablet: '12px' },
                    },
                  }}
                >
                  <Link
                    href={{
                      pathname,
                      query: {
                        articleId: article._id,
                      },
                    }}
                  >
                    <Image
                      priority
                      alt=""
                      width={270}
                      height={152}
                      className="scale pointer"
                      src={
                        get(article, 'image.originUrl') ||
                        '/images/default-image.png'
                      }
                      sizes="32vw"
                      style={{
                        aspectRatio: '270 / 152',
                        width: '100%',
                        height: 'auto',
                        objectFit: 'cover',
                      }}
                    />
                  </Link>
                </Box>
              </Grid>
              <Grid item xs={9} pl={2}>
                <Link
                  href={{
                    pathname,
                    query: {
                      articleId: article._id,
                    },
                  }}
                >
                  <Typography sx={styles.title} className="article-title">
                    {article.title}
                  </Typography>
                </Link>
                {showLawyer ? (
                  (article as IArticleListItem).provider && (
                    <Stack
                      direction="row"
                      spacing={{ xs: '4px', tablet: 1 }}
                      alignItems="center"
                      mt={{ xs: '4px', tabet: 1 }}
                    >
                      <Box
                        width={{ xs: 28, tablet: 40 }}
                        height={{ xs: 28, tablet: 40 }}
                        borderRadius="50%"
                        overflow="hidden"
                      >
                        <Link
                          href={{
                            pathname: `${
                              PROVIDER_LIST_PATH[
                                get(article, 'provider.type') as never
                              ]
                            }/[lawyerId]`,
                            query: {
                              lawyerId: (article as IArticleListItem).provider
                                ._id,
                            },
                          }}
                        >
                          <Image
                            src={
                              get(article, 'provider.images[0].originUrl') ||
                              '/images/default-avatar.png'
                            }
                            className="pointer"
                            width={1}
                            height={1}
                            alt=""
                            sizes="8vw"
                            style={{
                              width: '100%',
                              height: 'auto',
                              objectFit: 'cover',
                            }}
                          />
                        </Link>
                      </Box>
                      <Box flex={1}>
                        <Typography fontSize={10} color="hint" fontWeight={500}>
                          {t('articleList.featuredExpert')}
                        </Typography>
                        <Link
                          href={{
                            pathname: `${
                              PROVIDER_LIST_PATH[
                                get(article, 'provider.type') as never
                              ]
                            }/[lawyerId]`,
                            query: {
                              lawyerId: (article as IArticleListItem).provider
                                ._id,
                            },
                          }}
                        >
                          <Typography
                            fontSize={{ xs: 12, tablet: 14 }}
                            fontWeight={500}
                            color="heading"
                            className="pointer"
                          >
                            {get(article, 'provider.fullName')}
                          </Typography>
                        </Link>
                      </Box>
                    </Stack>
                  )
                ) : (
                  <Typography
                    mt={{ xs: '4px', tablet: '12px' }}
                    fontSize={{ xs: 14, tablet: 16 }}
                    className="article-leadparagraph line-clamp two-line"
                  >
                    {article.leadParagraph}
                  </Typography>
                )}
                <Typography
                  color="hint"
                  fontSize={{ xs: 10, tablet: 12 }}
                  mt={{ xs: '4px', tablet: '12px' }}
                >
                  {t('articleList.postedDate')}:{' '}
                  {dayjs(article.updatedAt).format(
                    MomentFormat.JP_YEAR_MONTH_DATE,
                  )}
                </Typography>
              </Grid>
            </Grid>
          );
        })}
    </Stack>
  );
};

export default ArticleList;

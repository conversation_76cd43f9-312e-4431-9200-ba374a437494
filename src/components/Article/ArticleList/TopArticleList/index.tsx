import { Box, Grid, Stack, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import dayjs from 'dayjs';
import type { IArticleListItem, IPickupArticleListItem } from 'hooks/types';
import { t } from 'i18n';
import { get, isEmpty } from 'lodash';
import Image from 'next/image';
import Link from 'next/link';
import { Fragment } from 'react';
import Slider from 'react-slick';
import { MomentFormat, PROVIDER_LIST_PATH } from 'utils/constants';

import Skeleton from './Skeleton';
import styles from './styles';

const TopArticleList = ({
  data = [],
  loading,
  showLawyer = false,
  pathname,
}: {
  data: IArticleListItem[] | IPickupArticleListItem[];
  loading: boolean;
  showLawyer?: boolean;
  pathname: string;
}) => {
  const theme = useTheme();
  const settings = {
    arrows: false,
    infinite: false,
    slidesToShow: 3,
    rows: 1,
    variableWidth: false,
    className: 'list-slider',
    responsive: [
      {
        breakpoint: theme.breakpoints.values.tablet,
        settings: {
          slidesToShow: 1.65,
        },
      },
    ],
  };

  const firstArticle = data[0];
  const firstHref = {
    pathname,
    query: {
      articleId: firstArticle?._id,
    },
  };

  const otherArticles = data.slice(1, 4);

  if (loading) {
    return <Skeleton />;
  }
  return (
    <>
      {firstArticle && (
        <Grid
          container
          columnSpacing={{ tablet: 3 }}
          rowGap={{ xs: 1, tablet: 4 }}
          key={firstArticle._id}
        >
          <Grid item xs={12} tablet={8}>
            <Box sx={styles.imageContainer}>
              <Link href={firstHref}>
                <Image
                  priority
                  alt={firstArticle._id}
                  sizes="(max-width: 768px) 100vw, 60vw"
                  style={styles.responsiveImage}
                  width={1}
                  height={302 / 542}
                  className="scale pointer"
                  src={
                    firstArticle.image?.originUrl || '/images/default-image.png'
                  }
                />
              </Link>
            </Box>
          </Grid>
          <Grid item xs={12} tablet={4}>
            <Link href={firstHref} legacyBehavior>
              <Typography sx={styles.title}>{firstArticle.title}</Typography>
            </Link>

            <Typography
              sx={{
                ...styles.leadParagraph,
                WebkitLineClamp: showLawyer
                  ? { xs: 1, tablet: 3 }
                  : { xs: 2, tablet: 5 },
              }}
            >
              {firstArticle.leadParagraph}
            </Typography>
            {(firstArticle as IArticleListItem).provider && (
              <Stack
                direction="row"
                alignItems="center"
                spacing={{ xs: '4px', tablet: 1 }}
                mt={{ xs: 1, tablet: 2 }}
              >
                <Link
                  href={`${
                    PROVIDER_LIST_PATH[
                      get(firstArticle, 'provider.type') as never
                    ]
                  }/${get(firstArticle, 'provider._id')}`}
                >
                  <Box sx={styles.lawyerAvatar}>
                    <Image
                      src={
                        get(firstArticle, 'provider.images[0].originUrl') ||
                        '/images/default-avatar.png'
                      }
                      style={styles.responsiveImage}
                      width={1}
                      height={1}
                      sizes="8vw"
                      alt="provider_image"
                    />
                  </Box>
                </Link>
                <Box flex={1}>
                  <Typography fontSize={10} color="hint" fontWeight={500}>
                    {t('articleList.featuredExpert')}
                  </Typography>
                  <Link
                    href={`${
                      PROVIDER_LIST_PATH[
                        get(firstArticle, 'provider.type') as never
                      ]
                    }/${get(firstArticle, 'provider._id')}`}
                  >
                    <Typography sx={styles.lawyerFullname}>
                      {get(firstArticle, 'provider.fullName')}
                    </Typography>
                  </Link>
                </Box>
              </Stack>
            )}
            <Typography
              color="hint"
              fontSize={{ xs: 10, tablet: 12 }}
              mt={{
                xs: showLawyer ? '4px' : 1,
                tablet: showLawyer ? 1 : '12px',
              }}
            >
              {t('articleList.postedDate')}:{' '}
              {dayjs(firstArticle.updatedAt).format(
                MomentFormat.JP_YEAR_MONTH_DATE,
              )}
            </Typography>
          </Grid>
        </Grid>
      )}
      {!isEmpty(otherArticles) && (
        <Box sx={styles.otherArticleContainer}>
          <Slider {...settings}>
            {otherArticles.map((article) => {
              const href = {
                pathname,
                query: {
                  articleId: article._id,
                },
              };
              return (
                <Fragment key={article._id}>
                  <Box sx={styles.otherArticleItem}>
                    <Box
                      borderRadius="12px"
                      overflow="hidden"
                      sx={styles.imageContainer}
                    >
                      <Link href={href}>
                        <Image
                          alt=""
                          style={styles.responsiveImage}
                          width={1}
                          height={302 / 542}
                          sizes="(max-width: 768px) 56vw, 30vw"
                          className="scale pointer"
                          src={
                            article.image?.originUrl ||
                            '/images/default-image.png'
                          }
                        />
                      </Link>
                    </Box>
                    <Link href={href} legacyBehavior>
                      <Typography
                        sx={styles.otherArticleTitle}
                        className="line-clamp two-line pointer"
                      >
                        {article.title}
                      </Typography>
                    </Link>

                    {(article as IArticleListItem).provider && (
                      <Stack
                        direction="row"
                        spacing={{ xs: '4px', tablet: 1 }}
                        mt={{ xs: '4px', tablet: 1 }}
                        alignItems="center"
                      >
                        <Box
                          width={{ xs: 28, tablet: 40 }}
                          borderRadius="50%"
                          overflow="hidden"
                        >
                          <Link
                            href={`${
                              PROVIDER_LIST_PATH[
                                get(firstArticle, 'provider.type') as never
                              ]
                            }/${get(article, 'provider._id')}`}
                          >
                            <Image
                              src={
                                get(article, 'provider.images[0].originUrl') ||
                                '/images/default-avatar.png'
                              }
                              style={styles.responsiveImage}
                              width={1}
                              height={1}
                              sizes="8vw"
                              alt="provider_image"
                            />
                          </Link>
                        </Box>
                        <Box flex={1}>
                          <Typography
                            fontSize={10}
                            color="hint"
                            fontWeight={500}
                          >
                            {t('articleList.featuredExpert')}
                          </Typography>
                          <Link
                            href={`${
                              PROVIDER_LIST_PATH[
                                get(firstArticle, 'provider.type') as never
                              ]
                            }/${get(article, 'provider._id')}`}
                          >
                            <Typography sx={styles.lawyerFullname}>
                              {get(article, 'provider.fullName')}
                            </Typography>
                          </Link>
                        </Box>
                      </Stack>
                    )}
                    <Typography
                      color="hint"
                      fontSize={{ xs: 10, tablet: 12 }}
                      mt={{ xs: '4px', tablet: showLawyer ? 1 : '2px' }}
                    >
                      {t('articleList.postedDate')}:{' '}
                      {dayjs(article.updatedAt).format(
                        MomentFormat.JP_YEAR_MONTH_DATE,
                      )}
                    </Typography>
                  </Box>
                </Fragment>
              );
            })}
          </Slider>
        </Box>
      )}
    </>
  );
};

export default TopArticleList;

import { Grid, Skeleton } from '@mui/material';
import { times } from 'lodash';

const SkeletonList = () => {
  return (
    <Grid container columnSpacing={3} mt={4} rowGap={4}>
      <Grid item xs={4} tablet={8}>
        <Skeleton
          variant="rectangular"
          width="100%"
          sx={{ pt: 'calc(302/542 * 100%)' }}
        />
      </Grid>
      <Grid item xs={8} tablet={4}>
        <Skeleton variant="text" sx={{ fontSize: 24 }} />
        <Skeleton variant="text" />
      </Grid>
      {times(3).map((number) => (
        <Grid item xs={12} tablet={4} key={number}>
          <Grid container columnSpacing={{ xs: 3, tablet: 0 }}>
            <Grid item xs={4} tablet={12}>
              <Skeleton
                variant="rectangular"
                width="100%"
                sx={{ pt: 'calc(302/542 * 100%)' }}
              />
            </Grid>
            <Grid item xs={8} tablet={12}>
              <Skeleton variant="text" sx={{ fontSize: 18 }} />
              <Skeleton variant="text" sx={{ fontSize: 12 }} />
            </Grid>
          </Grid>
        </Grid>
      ))}
    </Grid>
  );
};

export default SkeletonList;

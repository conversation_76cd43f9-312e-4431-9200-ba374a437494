import type { Theme } from '@mui/material';

const styles = {
  imageContainer: {
    borderRadius: { xs: 1, tablet: '12px' },
    overflow: 'hidden',
    aspectRatio: '542 / 302',
    transform: 'translateZ(0)',
  },
  responsiveImage: {
    width: '100%',
    height: 'auto',
    objectFit: 'cover',
  },
  title: {
    fontSize: { xs: 18, tablet: 24 },
    fontWeight: 'bold',
    color: 'heading',
    wordBreak: 'break-word',
    cursor: 'pointer',
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    display: '-webkit-box',
    WebkitLineClamp: { xs: 2, tablet: 3 },
  },
  leadParagraph: {
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    display: '-webkit-box',
    fontSize: { xs: 14, tablet: 16 },
    mt: { xs: 1, tablet: '12px' },
    wordBreak: 'break-word',
  },
  lawyerAvatar: {
    width: { xs: 28, tablet: 32 },
    height: { xs: 28, tablet: 32 },
    borderRadius: '50%',
    overflow: 'hidden',
  },
  lawyerFullname: {
    fontSize: { xs: 12, tablet: 14 },
    fontWeight: 500,
    color: 'heading',
  },
  otherArticleContainer: (theme: Theme) => ({
    mt: { xs: '20px', tablet: 3 },
    mr: { xs: -2, tablet: '-12px' },
    ml: { xs: -2, tablet: '-12px' },
    '& .list-slider': {
      '& .slick-track': {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        alignItems: 'stretch',
      },
    },
    '.slick-slide': {
      height: 'auto',
      px: '12px',
      '>div': {
        height: '100%',
      },
    },
    [theme.breakpoints.down('tablet')]: {
      '.slick-slide': {
        pr: 'calc(16px * 2 / 3)',
        pl: 'calc(16px * 2 / 3)',

        '&:first-of-type': {
          pl: 2,
          pr: 'calc(16px / 3)',
        },
        '&:last-child': {
          pr: 2,
          pl: 'calc(16px / 3)',
        },
      },
    },
  }),
  otherArticleItem: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
  },
  otherArticleTitle: {
    fontSize: { tablet: 18 },
    fontWeight: 'bold',
    mt: { xs: 1, tablet: '12px' },
    color: 'heading',
    flex: '1 1 auto',
  },
} as const;

export default styles;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  title: {
    fontSize: { xs: 24, tablet: 28 },
    fontWeight: 'bold',
    color: 'heading',
  },
  infoContainer: {
    display: { xs: 'block', tablet: 'flex' },
    justifyContent: 'space-between',
    mt: { xs: 1, tablet: 2 },
    alignItems: 'center',
  },
  imageContainer: {
    mt: { xs: '12px', tablet: 3 },
    img: {
      display: 'block',
      width: '100%',
      height: 'auto',
      objectFit: 'cover',
      borderRadius: { xs: 1, tablet: '12px' },
    },
  },
  leadParagraph: {
    mt: { xs: 2, tablet: 3 },
    fontSize: { xs: 14, tablet: 16 },
    color: 'text.primary',
  },
  postedDate: {
    color: 'hint',
    fontSize: { xs: 10, tablet: 12 },
    mt: { xs: '12px', tablet: 0 },
  },
  subTitle: {
    mt: { xs: 2, tablet: 3 },
    bgcolor: 'backgroundColor',
    borderRadius: { xs: 1, tablet: '12px' },
    p: { xs: '12px 16px', tablet: '16px 32px' },
    '.sub-title': {
      '.item-sub-title': {
        fontSize: { xs: 14, tablet: 16 },
        color: 'heading',
      },
      '&:hover': {
        textDecoration: 'underline',
        textUnderlineOffset: '2px',
      },
    },
  },
  imageContent: {
    '& img': {
      objectFit: 'cover',
      maxWidth: '100%',
      height: 'auto',
      width: 'auto',
      borderRadius: { xs: 1, tablet: '12px' },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

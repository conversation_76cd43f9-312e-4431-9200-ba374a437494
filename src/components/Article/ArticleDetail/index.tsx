import { Box, Chip, Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import type { IPickupArticleDetail } from 'hooks/types';
import { t } from 'i18n';
import { get, isEmpty } from 'lodash';
import Image from 'next/image';
import { Element, Link as ScrollLink } from 'react-scroll';
import { MomentFormat } from 'utils/constants';

import styles from './styles';

interface SubTitle {
  key: number;
  subTitle: string;
}

const ArticleDetail = ({
  data,
}: {
  data: Omit<IPickupArticleDetail, 'provider' | 'providers'>;
}) => {
  const { contents, title, leadParagraph } = data;

  const subTitle = (contents || []).reduce((arr: SubTitle[], cur, index) => {
    if (cur.subTitle && cur.subTitle !== '') {
      return arr.concat([
        {
          key: index,
          subTitle: cur.subTitle,
        },
      ]);
    }
    return arr;
  }, []);

  return (
    <Box>
      <Typography sx={styles.title} component="h1">
        {title}
      </Typography>
      <Box sx={styles.infoContainer}>
        <Stack
          direction="row"
          gap={{ xs: '4px', tablet: 1 }}
          flexWrap="wrap"
          flex={1}
        >
          {!isEmpty(data.attribute) &&
            data.attribute.map((field) => (
              <Chip
                className="tabletStyle"
                label={field.value}
                key={field._id}
              />
            ))}
        </Stack>
        <Typography sx={styles.postedDate}>
          {t('articleList.postedDate')}:{' '}
          {dayjs(data.updatedAt).format(MomentFormat.JP_YEAR_MONTH_DATE)}
        </Typography>
      </Box>
      <Box sx={styles.imageContainer}>
        <Image
          alt=""
          sizes="100vw"
          width={1}
          height={97 / 174}
          src={get(data, 'image.originUrl') || '/images/default-image.png'}
        />
      </Box>
      <Typography sx={styles.leadParagraph}>{leadParagraph}</Typography>
      {!isEmpty(subTitle) && (
        <Box sx={styles.subTitle}>
          <Stack
            component="ul"
            spacing={{ xs: 1, tablet: '6px' }}
            style={{ margin: '0px', paddingInlineStart: '20px' }}
          >
            {subTitle.map((item) => (
              <ScrollLink
                to={item.key.toString()}
                smooth
                offset={-96}
                key={item.key}
                className="sub-title"
              >
                <Box component="li" key={item.key} className="item-sub-title">
                  {item.subTitle}
                </Box>
              </ScrollLink>
            ))}
          </Stack>
        </Box>
      )}
      {contents && !isEmpty(contents) && (
        <Box mt={3}>
          {contents.map((item, index) => (
            <div key={index}>
              {get(item, 'subTitle') && (
                <Box mt={3}>
                  <Element name={index.toString()}>
                    <Typography
                      fontSize={{ xs: 18, tablet: 24 }}
                      fontWeight="bold"
                      color="heading"
                      component="h2"
                    >
                      {get(item, 'subTitle')}
                    </Typography>
                  </Element>
                </Box>
              )}
              {!isEmpty(get(item, 'subImage')) && (
                <Box sx={{ ...styles.imageContainer, mt: 2 }}>
                  {item.subImage && item.subImage.originUrl && (
                    <Image
                      alt=""
                      sizes="100vw"
                      width={1}
                      height={97 / 174}
                      src={item.subImage.originUrl}
                    />
                  )}
                </Box>
              )}
              <Box mt={{ xs: 2, tablet: 3 }}>
                <Typography
                  whiteSpace="normal"
                  component="div"
                  sx={styles.imageContent}
                  fontSize={{ xs: 14, tablet: 16 }}
                  dangerouslySetInnerHTML={{
                    __html: get(item, 'content', '---'),
                  }}
                />
              </Box>
            </div>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default ArticleDetail;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
  },
  sideMenu: {
    borderRadius: '10px',
    flex: '1 0 auto',
    maxWidth: '232px',
    bgcolor: 'white',
    mr: { tablet: 2 },
    display: { sl: 'block', xs: 'none' },
  },
  listItem: {
    p: 2,
  },
  link: {
    textDecoration: 'unset',
  },
  listItemButton: {
    p: 2,
    borderRadius: 1,
    '& svg': {
      color: 'text.primary',
    },
    '&.MuiListItemButton-root': {
      color: 'text.primary',
    },
    '&.Mui-selected': {
      backgroundColor: '#fdf6e2',
      borderRadius: 1,
      color: 'primary.main',
      '& svg': {
        color: 'primary.main',
      },
    },
    '& .MuiListItemText-root': {
      '& .MuiListItemText-primary': {
        fontWeight: 500,
        fontSize: 14,
      },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

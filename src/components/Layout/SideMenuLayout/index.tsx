import { Box, Container } from '@mui/material';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Link from 'components/Link';
import i18n from 'i18n';
import {
  AddEmailIcon,
  MyPageAppointmentIcon,
  MyPagePaymentIcon,
  MyPageProfileIcon,
  MyPageRecommendIcon,
  MyPageRecordIcon,
  MyPageShiftManagementIcon,
} from 'icons';
import { useRouter } from 'next/router';
import type { ReactNode } from 'react';
import { ROLES } from 'utils/constants';

import styles from './styles';

const lawyerMenus = [
  {
    path: '/lawyer/my-page/cases',
    label: '面談情報',
    icon: <MyPageAppointmentIcon />,
  },
  {
    path: '/lawyer/my-page/profile',
    label: 'プロフィール',
    icon: <MyPageProfileIcon />,
  },
  {
    path: '/lawyer/my-page/mail-setting',
    label: 'メール送信先設定',
    icon: <AddEmailIcon />,
  },
];

const counselorMenus = [
  {
    path: '/counselor/my-page/cases',
    label: '面談情報',
    icon: <MyPageAppointmentIcon />,
  },
  {
    path: '/counselor/my-page/shift',
    label: 'シフト設定',
    icon: <MyPageShiftManagementIcon />,
  },
  {
    path: '/counselor/my-page/profile',
    label: 'プロフィール',
    icon: <MyPageProfileIcon />,
  },
];

const customerMenus = [
  {
    path: '/customer/my-page/cases',
    label: 'mypage.cases',
    icon: <MyPageAppointmentIcon />,
  },
  {
    path: '/customer/my-page/consultation-record',
    label: 'mypage.consultation',
    icon: <MyPageRecordIcon />,
  },
  {
    path: '/customer/my-page/preference',
    label: 'mypage.recommend',
    icon: <MyPageRecommendIcon />,
  },
  {
    path: '/customer/my-page/profile',
    label: 'mypage.profile',
    icon: <MyPageProfileIcon />,
  },
  {
    path: '/customer/my-page/cards',
    label: 'mypage.card',
    icon: <MyPagePaymentIcon />,
  },
];
const SideMenuLayout = ({
  children,
  role = ROLES.CUSTOMER,
}: {
  children: ReactNode;
  role?: Exclude<ROLES, ROLES.GUEST>;
}) => {
  const { route } = useRouter();
  const { t } = i18n;
  const menus = {
    [ROLES.LAWYER]: lawyerMenus,
    [ROLES.CUSTOMER]: customerMenus,
    [ROLES.COUNSELOR]: counselorMenus,
  };
  return (
    <Container maxWidth="xl" disableGutters sx={styles.container}>
      <Box
        p={{ xs: 0, tablet: 2 }}
        display={{ sl: 'flex', xs: 'block' }}
        flexGrow={1}
      >
        <Box sx={styles.sideMenu}>
          <List component="nav" aria-label="side-menu" sx={styles.listItem}>
            {menus[role].map((menu) => (
              <Link href={menu.path} key={menu.path} sx={styles.link}>
                <ListItemButton
                  sx={styles.listItemButton}
                  selected={route.startsWith(menu.path)}
                >
                  <ListItemIcon sx={{ minWidth: 'unset', mr: 1 }}>
                    {menu.icon}
                  </ListItemIcon>
                  <ListItemText primary={t(menu.label)} />
                </ListItemButton>
              </Link>
            ))}
          </List>
        </Box>
        <Box flex={1}>{children}</Box>
      </Box>
    </Container>
  );
};

export default SideMenuLayout;

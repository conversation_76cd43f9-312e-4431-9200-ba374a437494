// import Modal from 'components/Modal';
import { Box, IconButton } from '@mui/material';
import NewHeader from 'components/NewHome/Header';
import { useScroll } from 'framer-motion';
import { OrangeArrowUp } from 'icons';
// import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import type { ReactNode } from 'react';
import { useEffect, useState } from 'react';

import Footer from './Footer';
import styles from './styles';

// const MobileContactInfo = dynamic(
//   () => import('components/Home/MobileContactInfo'),
//   { ssr: false },
// );

export default function Layout({
  children,
  showMedia = false,
  showScrollButton = true,
  showFooter = true,
}: {
  children: ReactNode;
  showMedia?: boolean;
  showScrollButton?: boolean;
  showFooter?: boolean;
}) {
  const { pathname } = useRouter();
  // const shouldShowMobileContact = ![
  //   '/contact-us',
  //   '/customer/messages/[[...roomId]]',
  //   '/lawyer/messages/[[...roomId]]',
  //   '/counselor/messages/[[...roomId]]',
  //   '/customer/booking/[lawyerId]',
  //   '/customer/counselor-booking/[counselorId]',
  // ].includes(pathname);
  const [opacity, setOpacity] = useState(0);
  const { scrollY } = useScroll();

  useEffect(() => {
    return scrollY.onChange((latest) => {
      if (latest > document.documentElement.clientHeight && showScrollButton) {
        setOpacity(1);
      } else setOpacity(0);
    });
  }, [scrollY, showScrollButton]);
  const isChatPage = [
    '/counselor/messages/[[...roomId]]',
    '/lawyer/messages/[[...roomId]]',
    '/customer/messages/[[...roomId]]',
  ].includes(pathname);
  return (
    <div style={{ position: 'relative' }}>
      <NewHeader />
      <Box
        component="main"
        sx={{
          ...styles.main,
          minHeight: {
            xs: !isChatPage ? `calc(100vh - 136px)` : 'calc(100vh - 68px)',
            tablet: !isChatPage
              ? `calc(100vh - ${showMedia ? '259px' : '163px'})`
              : 'calc(100vh - 98px)',
          },
        }}
      >
        {children}
      </Box>

      <IconButton
        sx={
          [
            styles.fabButton,
            {
              opacity,
              pointerEvents: opacity === 1 ? 'normal' : 'none',
              bottom: { xs: 160, tablet: 40 },
              transform: `scale(${opacity ? 1 : 0})`,
            },
          ] as never
        }
        color="primary"
        onClick={() =>
          window.scrollTo({
            top: 0,
            behavior: 'smooth',
          })
        }
      >
        <OrangeArrowUp />
      </IconButton>
      {/* {shouldShowMobileContact && <MobileContactInfo />} */}
      {showFooter && <Footer showMedia={showMedia} isChatPage={isChatPage} />}
    </div>
  );
}

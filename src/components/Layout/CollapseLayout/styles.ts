import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  content: {
    borderRadius: 1,
    border: (theme) => `1px solid ${theme.palette.neutral2}`,
  },
  collapseList: {
    borderTop: (theme) => `1px solid ${theme.palette.neutral2}`,
    bgcolor: 'backgroundColor',
    borderRadius: '0 0 8px 8px',
  },
  itemButton: {
    p: { xs: 2, tablet: 3 },
  },
  itemText: {
    my: 0,
  },
  textHeader: {
    color: 'heading',
    fontWeight: 'bold',
    fontSize: { xs: 14, tablet: 16 },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

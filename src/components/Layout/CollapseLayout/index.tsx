import {
  Box,
  Collapse,
  List,
  ListItemButton,
  ListItemText,
  Stack,
  Typography,
} from '@mui/material';
import { ArrowDownIcon, ArrowUpIcon } from 'icons';
import type { ReactNode } from 'react';
import { useState } from 'react';

import styles from './styles';

export interface CollapseProps {
  content: ReactNode;
  label: ReactNode;
  number: string;
}

const CollapseLayout = ({ content, label, number }: CollapseProps) => {
  const [open, setOpen] = useState(false);
  const handleClick = () => {
    setOpen(!open);
  };

  return (
    <Box sx={styles.content}>
      <List sx={{ py: 0 }}>
        <ListItemButton onClick={handleClick} sx={styles.itemButton}>
          <ListItemText
            primary={
              <Stack spacing={{ xs: 0, tablet: 1 }} direction="row">
                <Typography
                  sx={styles.textHeader}
                  maxWidth={{ xs: '28px', tablet: 32 }}
                  width="100%"
                >
                  {number}
                </Typography>
                <Typography sx={styles.textHeader}>{label}</Typography>
              </Stack>
            }
            sx={styles.itemText}
          />
          {open ? <ArrowUpIcon /> : <ArrowDownIcon />}
        </ListItemButton>
        <Collapse
          in={open}
          timeout="auto"
          unmountOnExit
          sx={styles.collapseList}
        >
          <List component="div" disablePadding sx={styles.itemButton}>
            <ListItemText primary={content} sx={styles.itemText} />
          </List>
        </Collapse>
      </List>
    </Box>
  );
};

export default CollapseLayout;

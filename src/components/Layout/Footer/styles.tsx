import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  footNavigation: {
    p: { xs: '16px 0px', tablet: '24px 16px 23px' },
    minHeight: { xs: 80, tablet: 67 },
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  link: {
    fontSize: 14,
    color: 'white',
    px: { xs: 2, tablet: 3 },
    position: 'relative',
    '@media (max-width: 375px)': {
      fontSize: 12,
    },
  },
  icon: {
    svg: { width: { xs: 40, tablet: 48 }, height: { xs: 40, tablet: 48 } },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

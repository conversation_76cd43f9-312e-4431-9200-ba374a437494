import { <PERSON>, Divider, Icon<PERSON>utton, Stack, Typography } from '@mui/material';
import MuiLink from 'components/Link';
import { FacebookIcon, InstagramIcon, TwitterIcon, YoutubeIcon } from 'icons';
import { FOOTER_ROUTES } from 'utils/constants';

import styles from './styles';

const FOOTER_ITEMS = [
  { url: 'https://twitter.com/Ricokatsu111', icon: <TwitterIcon /> },
  { url: 'https://instagram.com/ricokatsu_official', icon: <InstagramIcon /> },
  {
    url: 'https://www.youtube.com/channel/UCdSaQLt4rOmaM8dc2F6Gawg',
    icon: <YoutubeIcon />,
  },
  {
    url: 'https://www.facebook.com/people/リコ活/100090390049509',
    icon: <FacebookIcon />,
  },
];
export default function Footer({
  showMedia,
  isChatPage,
}: {
  showMedia: boolean;
  isChatPage?: boolean;
}) {
  return (
    <>
      {showMedia && (
        <Box p={{ xs: 1, tablet: 2 }} display="flex" justifyContent="center">
          <Stack direction="row" spacing={1}>
            {FOOTER_ITEMS.map((item) => (
              <IconButton
                key={item.url}
                sx={styles.icon}
                LinkComponent="a"
                target="_blank"
                rel="noreferrer"
                href={item.url}
              >
                {item.icon}
              </IconButton>
            ))}
          </Stack>
        </Box>
      )}
      {!isChatPage && (
        <Box bgcolor="orange" sx={styles.footNavigation}>
          <Stack
            direction="row"
            justifyContent="center"
            flexWrap="wrap"
            rowGap={1}
            sx={{
              '& hr:nth-of-type(3)': {
                display: { xs: 'none', fsf: 'block' },
              },
            }}
            divider={<Divider orientation="vertical" flexItem />}
          >
            {FOOTER_ROUTES.map((route) =>
              !route.newTab ? (
                <MuiLink
                  key={route.url}
                  href={route.url}
                  underline="hover"
                  color="white"
                  sx={{ lineHeight: '20px' }}
                >
                  <Typography sx={styles.link}>{route.text}</Typography>
                </MuiLink>
              ) : (
                <MuiLink
                  key={route.url}
                  href={route.url}
                  underline="hover"
                  color="white"
                  target="_blank"
                  rel="noreferrer"
                  sx={{ lineHeight: '20px' }}
                >
                  <Typography sx={styles.link}>{route.text}</Typography>
                </MuiLink>
              ),
            )}
          </Stack>
        </Box>
      )}
    </>
  );
}

import { Box, Container, Tab, Tabs, Typography } from '@mui/material';
import ActionSection from 'components/Article/ActionSection';
import Breadcrumbs from 'components/Breadcrumbs';
import Layout from 'components/Layout';
import i18n from 'i18n';
import { useRouter } from 'next/router';
import type { ReactElement, ReactNode } from 'react';

import styles from './styles';

const ArticleListLayout = ({ children }: { children: ReactNode }) => {
  const { replace, pathname } = useRouter();
  const { t } = i18n;
  return (
    <>
      <Container maxWidth="lg" disableGutters sx={styles.pageContainer}>
        <Box px={{ xs: 2, md: 0 }}>
          <Breadcrumbs />
        </Box>
        <Container maxWidth="md" disableGutters sx={styles.listContainer}>
          <Container maxWidth="md" disableGutters sx={styles.listContainer}>
            <Box
              p={{ xs: '8px 16px 0px 0px', tablet: '32px 16px 0px 16px' }}
              bgcolor="white"
              borderRadius={{ md: 2 }}
            >
              <Typography
                fontSize={{ xs: 24, tablet: 32 }}
                fontWeight="bold"
                color="heading"
                pl={2}
                component="h1"
              >
                {t('articleList.breadcrumb')}
              </Typography>
              <Tabs
                sx={{ minHeight: 'unset', mt: 1 }}
                value={pathname}
                onChange={(_, value) => replace(value)}
              >
                <Tab
                  label={t('home.article')}
                  value="/articles"
                  sx={{
                    fontWeight: 700,
                    fontSize: { xs: 16, tablet: 20 },
                  }}
                />
                <Tab
                  label={t('home.pickupArticle')}
                  value="/pickup-articles"
                  sx={{
                    fontWeight: 700,
                    fontSize: { xs: 16, tablet: 20 },
                  }}
                />
              </Tabs>
            </Box>
            {children}
          </Container>
        </Container>
      </Container>
      <Box mt={{ xs: 0, tablet: '54px' }}>
        <ActionSection />
      </Box>
    </>
  );
};

ArticleListLayout.getLayout = function getLayout(page: ReactElement) {
  return <Layout>{page}</Layout>;
};
export default ArticleListLayout;

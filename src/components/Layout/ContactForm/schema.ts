import { t } from 'i18n';
import { Regex } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  category: string().required(),
  name: string().required(),
  email: string()
    .required()
    .trim()
    .matches(Regex.EMAIL, t('validation.invalidField')),
  content: string()
    .required()
    .max(1000, t('validation.maxLength', { number: 1000 })),
});

export type ContactUsFormSchema = InferType<typeof schema>;
export default schema;

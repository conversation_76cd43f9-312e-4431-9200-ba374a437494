import { yupR<PERSON>olver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Stack } from '@mui/material';
import { Select, TextField } from 'components/Form';
import { t } from 'i18n';
import { useForm } from 'react-hook-form';
import { INQUIRY_CATEGORY } from 'utils/constants';
import Helper from 'utils/helpers';

import type { ContactUsFormSchema } from './schema';
import schema from './schema';

export interface ContactFormProps {
  onSubmit: (values: ContactUsFormSchema) => void;
  loading?: boolean;
}
const ContactForm = ({ onSubmit, loading }: ContactFormProps) => {
  const { control, handleSubmit } = useForm<ContactUsFormSchema>({
    mode: 'onTouched',
    resolver: yupResolver(schema),
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box mt={{ xs: 2, tablet: '40px' }}>
        <Stack spacing={{ xs: '20px', tablet: 2 }}>
          <Select
            label="お問い合わせカテゴリー"
            control={control}
            name="category"
            placeholder="カテゴリーを選択してください"
            data={Helper.convertObjectToOptions(INQUIRY_CATEGORY)}
          />
          <TextField
            label={t('contactUs.name')}
            control={control}
            name="name"
            placeholder={t('contactUs.placeholderName')}
          />
          <TextField
            label={t('contactUs.email')}
            control={control}
            name="email"
            placeholder={t('contactUs.placeholderEmail')}
          />
          <TextField
            label={t('contactUs.content')}
            placeholder={t('contactUs.placeholderContent')}
            name="content"
            control={control}
            maxLength={1000}
            multiline
            minRows={7}
          />
        </Stack>
        <Box display="flex" justifyContent="center" mt={4}>
          <LoadingButton
            color="secondary"
            type="submit"
            variant="contained"
            size="large"
            className="tabletStyle"
            fullWidth
            loading={loading}
            sx={{
              maxWidth: 368,
            }}
          >
            {t('contactUs.submit')}
          </LoadingButton>
        </Box>
      </Box>
    </form>
  );
};

export default ContactForm;

import {
  <PERSON>ge,
  Box,
  Button,
  Divider,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import type { IUserDoc } from 'features/chat/types';
import useGlobalState from 'hooks/useGlobalState';
import i18n from 'i18n';
import { ChatIcon, ContactIcon, LineIcon } from 'icons';
import { get } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import process from 'process';
import * as React from 'react';
import { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

const messageLink = {
  [ROLES.CUSTOMER]: '/customer/messages',
  [ROLES.COUNSELOR]: '/counselor/messages',
  [ROLES.LAWYER]: '/lawyer/messages',
};
const RightHeader = () => {
  const { t } = i18n;
  const { setLoginModal } = useGlobalState();
  const webCookie = Helper.getWebCookie();
  const role = get(webCookie, 'role');
  const { pathname } = useRouter();
  const { data: userDoc } = useQuery<IUserDoc>(['currentUser', 'userDoc'], {
    staleTime: Infinity,
    enabled: false,
  });

  // Get the appropriate my page link based on user role
  const getMyPageLink = () => {
    if (role === ROLES.CUSTOMER) {
      return '/customer/my-page';
    }
    if (role === ROLES.LAWYER) {
      return '/lawyer/my-page';
    }
    if (role === ROLES.COUNSELOR) {
      return '/counselor/my-page';
    }
    return '/customer/my-page'; // fallback
  };

  return (
    <Stack direction="row" alignItems="center" spacing={{ xs: 1, tablet: 2 }}>
      {!webCookie && (
        <>
          {pathname !== '/register' && (
            <Button
              size="large"
              variant="contained"
              color="yellow"
              sx={styles.headerButton}
              LinkComponent={Link}
              href="/register"
            >
              無料登録
            </Button>
          )}
          <Button
            size="large"
            variant="contained"
            color="silver"
            onClick={() => setLoginModal(true)}
            sx={styles.headerButton}
          >
            {t('header.login')}
          </Button>
          <Divider
            orientation="vertical"
            sx={{ height: '36px', display: { xs: 'none', md: 'block' } }}
          />
          <Box
            href={Helper.getLineUrl()}
            component={Link}
            target="_blank"
            display={{ xs: 'none', md: 'block' }}
          >
            <Box display="flex" justifyContent="center" gap="4px">
              <LineIcon />
              <Typography fontWeight={700} color="#06C755">
                LINEで無料相談
              </Typography>
            </Box>
            <Typography marginTop={1} fontSize={14} color="text.primary">
              リコ活スタッフへ相談 ＞
            </Typography>
          </Box>
        </>
      )}
      {webCookie && (
        <>
          <Button
            size="large"
            variant="contained"
            color="yellow"
            href={getMyPageLink()}
            LinkComponent={Link}
            sx={[styles.headerButton as never, { minWidth: 94 }]}
          >
            マイページ
          </Button>
          {![
            '/customer/messages/[[...roomId]]',
            '/lawyer/messages/[[...roomId]]',
            '/counselor/messages/[[...roomId]]',
          ].includes(pathname) && (
            <IconButton
              className="whiteOutlined"
              size="large"
              sx={styles.messageButton}
              LinkComponent={Link}
              href={messageLink[role as unknown as Exclude<ROLES, ROLES.GUEST>]}
            >
              <Badge
                variant="dot"
                color="error"
                sx={styles.messageBadge}
                invisible={!!((userDoc?.unreadRooms || []).length === 0)}
              >
                <ChatIcon />
              </Badge>
            </IconButton>
          )}
          <Divider
            orientation="vertical"
            sx={{ height: '36px', display: { xs: 'none', md: 'block' } }}
          />
          <Box
            href={Helper.getLineUrl()}
            component={Link}
            target="_blank"
            display={{ xs: 'none', md: 'block' }}
          >
            <Box display="flex" justifyContent="center" gap="4px">
              <LineIcon />
              <Typography fontWeight={700} color="#06C755">
                LINEで無料相談
              </Typography>
            </Box>
            <Typography marginTop={1} fontSize={14} color="text.primary">
              リコ活スタッフへ相談 ＞
            </Typography>
          </Box>

          {process.env.SHOW_OPERATOR_CONTACT === 'true' && (
            <>
              <Divider
                orientation="vertical"
                sx={{ height: '36px', display: { xs: 'none', md: 'block' } }}
              />
              <Box
                href={`tel:${process.env.OPERATOR_PHONE}`}
                component={Link}
                target="_blank"
                display={{ xs: 'none', md: 'block' }}
              >
                <Box display="flex" justifyContent="center" gap="4px">
                  <ContactIcon />
                  <Typography fontWeight={700} color="primary">
                    {process.env.OPERATOR_PHONE}
                  </Typography>
                </Box>
                <Typography marginTop={1} fontSize={14} color="text.primary">
                  受付時間 9:00〜18:00 (平日)
                </Typography>
              </Box>
            </>
          )}
        </>
      )}
    </Stack>
  );
};

export default RightHeader;

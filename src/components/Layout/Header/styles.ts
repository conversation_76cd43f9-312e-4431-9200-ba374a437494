import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  logoContainer: {
    display: 'flex',
    svg: {
      width: { xs: 101, tablet: 150 },
      height: { xs: 44, tablet: 66 },
    },
  },
  appBar: {
    px: 2,
    py: { xs: '12px', tablet: '16px' },
    boxShadow: 'none',
  },
  consultationButton: {
    width: '240px',
    display: {
      xs: 'none',
      xd: 'inline-flex',
    },
  },
  headerButton: {
    fontSize: 22,
    width: { xs: 'unset', tablet: 157 },
    height: { xs: 32, tablet: 56 },
    '@media (max-width: 768px)': {
      padding: '6px 12px',
      fontSize: 14,
    },
  },
  messageButton: {
    '@media (max-width: 768px)': {
      width: 4,
      height: 4,
      svg: {
        width: '20px',
        height: '20px',
      },
    },
  },
  messageBadge: {
    '.MuiBadge-dot': {
      width: '12px',
      height: '12px',
      borderRadius: '50%',
      transform: 'scale(1) translate(calc(50% - 1px), calc(-50% + 3px))',
      '@media (max-width: 768px)': {
        width: '8px',
        height: '8px',
        transform: 'scale(1) translate(calc(50% - 4px), calc(-50% + 3px))',
      },
    },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

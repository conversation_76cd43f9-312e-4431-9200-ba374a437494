import { Box, Container, Stack } from '@mui/material';
import AppBar from '@mui/material/AppBar';
import MuiLink from 'components/Link';
import { useFetchUser } from 'hooks';
import type { IDefaultUser } from 'hooks/useFetchUser';
import { LogoIcon } from 'icons';
import dynamic from 'next/dynamic';
import { ProviderType } from 'utils/constants';

import styles from './styles';

const RightHeader = dynamic(
  () => import('components/Layout/Header/RightHeader'),
  { ssr: false },
);

const Header = () => {
  const { data } = useFetchUser<IDefaultUser & { type: ProviderType }>({
    enabled: false,
  });
  const getHomeUrl = () => {
    if (data?.type === ProviderType.COUNSELOR) {
      return '/counselor/my-page';
    }
    if (data?.type === ProviderType.LAWYER) {
      return '/lawyer/my-page';
    }
    return '/';
  };

  return (
    <AppBar component="nav" position="sticky" color="white" sx={styles.appBar}>
      <Container maxWidth="lg" disableGutters>
        <Box display="flex" justifyContent="space-between">
          <Stack direction="row" gap={{ xs: 1, tablet: 2 }} alignItems="center">
            <MuiLink href={getHomeUrl()} aria-label="home">
              <Box sx={styles.logoContainer}>
                <LogoIcon />
              </Box>
            </MuiLink>
          </Stack>
          <RightHeader />
        </Box>
      </Container>
    </AppBar>
  );
};

export default Header;

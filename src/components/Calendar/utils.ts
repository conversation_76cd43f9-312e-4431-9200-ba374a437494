/* eslint-disable no-plusplus */
import dayjs from 'dayjs';

type DateData = { data: string[]; index: number };
export enum SlotStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
}
export interface EventItem {
  /** Unique ID for the event. */
  // id: string;
  /** Start date of the event. (ISOString) */
  start: string;
  /** End date of the event. (ISOString) */
  end: string;
  /** Title of the event */
  title?: string;
  /** Background color of the event */
  color?: string;
  /** Container style of the event */
  // containerStyle?: StyleProp<ViewStyle>;
  [key: string]: any;
}

export const DEFAULT_PROPS = {
  VIEW_MODE: 'week' as const,
  FIRST_DAY: 1,
  MIN_DATE: dayjs().subtract(1, 'y').format('YYYY-MM-DD'),
  MAX_DATE: dayjs().add(1, 'y').format('YYYY-MM-DD'),
  INITIAL_DATE: dayjs().format('YYYY-MM-DD'),
  START: 0,
  END: 24,
  TIME_INTERVAL: 60,
  INIT_TIME_INTERVAL_HEIGHT: 60,
  MIN_TIME_INTERVAL_HEIGHT: 29,
  MAX_TIME_INTERVAL_HEIGHT: 116,
  CELL_BORDER_COLOR: '#E8E9ED',
  PRIMARY_COLOR: '#1973E7',
  CREATE_ITEM_BACKGROUND_COLOR: 'rgba(25, 115, 231, 0.1)',
  SECONDARY_COLOR: '#5F6369',
  WHITE_COLOR: '#FFFFFF',
  HOUR_WIDTH: 53,
  DAY_BAR_HEIGHT: 60,
  SPACE_CONTENT: 16,
  DRAG_CREATE_INTERVAL: 60,
  DRAG_STEP: 10,
  UNAVAILABLE_BACKGROUND_COLOR: '#F5F5F5',
  RIGHT_EDGE_SPACING: 1,
  OVERLAP_EVENTS_SPACING: 1,
  BLACK_COLOR: '#000000',
  EVENT_ANIMATED_DURATION: 150,
  NOW_INDICATOR_INTERVAL: 1000,
  NAVIGATION_DELAY: 1000,
  EDIT_SAVE_MODE: 'manual' as const,
};

export const SECONDS_IN_DAY = 86400;

export const calculateDates = (
  initialFirstDay: number,
  minDateStr: string,
  maxDateStr: string,
  initialDateStr: string,
  tzOffset: number,
) => {
  const day: DateData = { data: [], index: -1 };
  const week: DateData = { data: [], index: -1 };
  const threeDays: DateData = { data: [], index: -1 };
  const workWeek: DateData = { data: [], index: -1 };

  const initialDate = dayjs(initialDateStr).add(tzOffset, 'm');
  const minDate = dayjs(minDateStr);
  const maxDate = dayjs(maxDateStr);
  const minDateUnix = minDate.unix();
  const maxDateUnix = maxDate.unix();
  const minWeekDay = minDate.day();
  const maxWeekDay = maxDate.day();

  const fDow = (7 + initialFirstDay) % 7;
  const diffBefore = (minWeekDay + 7 - fDow) % 7;

  const minWeekDateUnix = minDateUnix - diffBefore * SECONDS_IN_DAY;
  let minWorkWorkDateUnix = minWeekDateUnix;
  if (diffBefore === 5) {
    minWorkWorkDateUnix = minDateUnix + 2 * SECONDS_IN_DAY;
  } else if (diffBefore === 6) {
    minWorkWorkDateUnix = minDateUnix + SECONDS_IN_DAY;
  }

  const lDow = (fDow + 6) % 7;
  const diffAfter = (lDow + 7 - maxWeekDay) % 7;
  const maxWeekDateUnix = maxDateUnix + diffAfter * SECONDS_IN_DAY;

  const totalDays = (maxWeekDateUnix - minWeekDateUnix) / SECONDS_IN_DAY + 1;
  let startWorkWeekDate = minWorkWorkDateUnix;
  let startWeekDate = minWeekDateUnix;
  let startThreeDays = minDateUnix;
  let startDay = minDateUnix;
  for (let dayIndex = 0; dayIndex < totalDays; dayIndex++) {
    const currentUnix = minWeekDateUnix + dayIndex * SECONDS_IN_DAY;
    const dateFromUnix = dayjs.unix(currentUnix);
    const dateStr = dateFromUnix.format('YYYY-MM-DD');
    if (startDay === currentUnix) {
      if (currentUnix <= maxDateUnix) {
        day.data.push(dateStr);
      }
      startDay = currentUnix + SECONDS_IN_DAY;
    }
    if (startWorkWeekDate === currentUnix) {
      workWeek.data.push(dateStr);
      startWorkWeekDate = currentUnix + 7 * SECONDS_IN_DAY;
    }
    if (startWeekDate === currentUnix) {
      week.data.push(dateStr);
      startWeekDate = currentUnix + 7 * SECONDS_IN_DAY;
    }
    if (startThreeDays === currentUnix && startThreeDays <= maxDateUnix) {
      threeDays.data.push(dateStr);
      startThreeDays = currentUnix + 3 * SECONDS_IN_DAY;
    }
    if (dateFromUnix.isSame(initialDate, 'day')) {
      day.index = day.data.length - 1;
      threeDays.index = threeDays.data.length - 1;
      week.index = week.data.length - 1;
      workWeek.index = workWeek.data.length - 1;
    }
  }
  return { day, week, threeDays, workWeek };
};

export const calculateHours = (
  start: number,
  end: number,
  step: number,
  hourFormat?: string,
) => {
  const hours: { text: string; hourNumber: number }[] = [];
  let tempStart = start;
  while (tempStart < end) {
    const roundHour = Math.floor(tempStart);
    const minutes = (tempStart - roundHour) * 60;
    const rMinutes = Math.round(minutes);
    const hourStr = `0${roundHour}`.slice(-2);
    const minuteStr = `0${rMinutes}`.slice(-2);
    let time = `${hourStr}:${minuteStr}`;
    if (hourFormat) {
      time = dayjs(`1970/1/1 ${hourStr}:${minuteStr}`, 'YYYY/M/D HH:mm').format(
        hourFormat,
      );
    }

    hours.push({
      text: time,
      hourNumber: tempStart,
    });
    tempStart += step / 60;
  }
  return hours;
};

export const groupEventsByDate = <T>(
  // eslint-disable-next-line @typescript-eslint/default-param-last
  events: (T & EventItem)[] = [],
  tzOffset: number,
) => {
  const groupedEvents: Record<string, (T & EventItem)[]> = {};
  events.forEach((event) => {
    const startEvent = dayjs(event.start).add(tzOffset, 'm').startOf('d');
    const endEvent = dayjs(event.end).add(tzOffset, 'm').startOf('d');
    const diffDays = endEvent.diff(startEvent, 'd');
    for (let i = 0; i <= diffDays; i++) {
      const dateStr = startEvent.add(i, 'd').format('YYYY-MM-DD');
      const prevEvents = groupedEvents[dateStr] || [];
      groupedEvents[dateStr] = [...prevEvents, event];
    }
  });
  return groupedEvents;
};

const hasCollision = (a: EventItem, b: EventItem) => {
  return a.end > b.start && a.start < b.end;
};

const calcColumnSpan = (
  event: EventItem,
  columnIndex: number,
  columns: EventItem[][],
) => {
  let colSpan = 1;
  for (let i = columnIndex + 1; i < columns.length; i++) {
    const column = columns[i]!;
    const foundCollision = column.find((ev) => hasCollision(event, ev));
    if (foundCollision) {
      return colSpan;
    }
    colSpan++;
  }
  return colSpan;
};

const buildEvent = (
  event: EventItem,
  left: number,
  width: number,
  options: PopulateOptions,
): PackedEvent => {
  const eventStart = dayjs(event.start).add(options.tzOffset, 'm');
  const eventEnd = dayjs(event.end).add(options.tzOffset, 'm');
  const timeToHour = eventStart.hour() + eventStart.minute() / 60;
  let start = timeToHour - options.startHour;
  const diffHour = eventEnd.diff(eventStart, 'm') / 60;
  const isSameDate = eventStart.isSame(eventEnd, 'd');
  if (!isSameDate) {
    const currentDate = dayjs(options.startDate).add(options.dayIndex, 'd');
    const diffCurrent = eventStart.diff(currentDate, 'm') / 60;
    if (diffCurrent < 0) {
      start = 0 + diffCurrent - options.startHour;
    }
  }

  return {
    ...event,
    startHour: start,
    duration: diffHour,
    left,
    width,
  };
};

const packOverlappingEventGroup = (
  columns: EventItem[][],
  calculatedEvents: PackedEvent[],
  populateOptions: PopulateOptions,
) => {
  const { columnWidth, rightEdgeSpacing, overlapEventsSpacing } =
    populateOptions;

  columns.forEach((column, columnIndex) => {
    column.forEach((event) => {
      const totalWidth = columnWidth - rightEdgeSpacing;
      const columnSpan = calcColumnSpan(event, columnIndex, columns);
      const eventLeft = (columnIndex / columns.length) * totalWidth;

      let eventWidth = totalWidth * (columnSpan / columns.length);
      if (columnIndex + columnSpan <= columns.length - 1) {
        eventWidth -= overlapEventsSpacing;
      }

      calculatedEvents.push({
        ...buildEvent(event, eventLeft, eventWidth, populateOptions),
        columnIndex,
        columns: columns.length,
      });
    });
  });
};

type PopulateOptions = {
  columnWidth: number;
  startHour: number;
  dayIndex: number;
  startDate: string;
  overlapEventsSpacing: number;
  rightEdgeSpacing: number;
  tzOffset: number;
};

export interface PackedEvent extends EventItem {
  left: number;
  startHour: number;
  width: number;
  duration: number;
  leftByIndex?: number;
}

export const populateEvents = <TData>(
  events: (TData & EventItem)[],
  options: PopulateOptions,
) => {
  let lastEnd: string | null = null;
  let columns: (TData & EventItem)[][] = [];
  const calculatedEvents: (TData & PackedEvent)[] = [];
  const cloneEvents = [...events];
  const sortedEvents = cloneEvents.sort((a, b) => {
    if (a.start < b.start) {
      return -1;
    }
    if (a.start > b.start) {
      return 1;
    }
    if (a.end < b.end) {
      return -1;
    }
    if (a.end > b.end) {
      return 1;
    }
    return 0;
  });
  sortedEvents.forEach((ev) => {
    if (lastEnd !== null && ev.start >= lastEnd) {
      packOverlappingEventGroup(columns, calculatedEvents, options);
      columns = [];
      lastEnd = null;
    }

    let placed = false;
    for (let i = 0; i < columns.length; i++) {
      const col = columns[i]!;
      if (!hasCollision(col[col.length - 1]!, ev)) {
        col.push(ev);
        placed = true;
        break;
      }
    }

    if (!placed) {
      columns.push([ev]);
    }

    if (lastEnd === null || ev.end > lastEnd) {
      lastEnd = ev.end;
    }
  });

  if (columns.length > 0) {
    packOverlappingEventGroup(columns, calculatedEvents, options);
  }

  return calculatedEvents;
};

interface DivideEventsProps<TData> {
  events?: {
    [date: string]: (TData & EventItem)[];
  };
  startDate: string;
  columns: number;
  columnWidth: number;
  startHour: number;
  overlapEventsSpacing: number;
  rightEdgeSpacing: number;
  tzOffset: number;
}

export const divideEventsByColumns = <TData>(
  props: DivideEventsProps<TData>,
) => {
  const { events = {}, startDate, columns, ...other } = props;
  const eventsByColumns: (TData & EventItem)[][] = [];
  const startUnix = dayjs(startDate).unix();
  for (let i = 0; i < columns; i++) {
    const currentUnix = startUnix + i * SECONDS_IN_DAY;
    const dateStr = dayjs.unix(currentUnix).format('YYYY-MM-DD');
    let eventsInDate: (TData & EventItem)[] = [];
    const eventInDate = events[dateStr];
    if (eventInDate) {
      eventsInDate = eventInDate;
    }
    eventsByColumns[i] = eventsInDate;
  }

  return eventsByColumns.map((event, index) =>
    populateEvents<TData>(event, {
      ...other,
      dayIndex: index,
      startDate,
    }),
  );
};

export const getWeekDates = (date: string) => {
  const startOfWeek = dayjs(date).startOf('week').add(1, 'day');
  return [...Array(7).keys()].map((index) =>
    startOfWeek.add(index, 'day').toISOString(),
  );
};

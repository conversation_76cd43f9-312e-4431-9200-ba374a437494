import {
  ButtonBase,
  ClickAwayListener,
  Tooltip,
  Typography,
} from '@mui/material';
import type { ReactNode } from 'react';
import React, { memo } from 'react';

import type { PackedEvent } from './utils';

export interface EventIemProps<TEventData extends PackedEvent> {
  data: TEventData;
  onClick?: (item: TEventData) => void;
  eventTooltip?: (event: TEventData) => ReactNode;
  columnOrder?: number;
}
const EventItem = <TEventData extends PackedEvent>({
  data,
  onClick,
  eventTooltip,
  columnOrder,
}: EventIemProps<TEventData>) => {
  const isYesterDayEvent = data.startHour < 0;
  const [open, setOpen] = React.useState(false);

  return (
    <ClickAwayListener onClickAway={() => setOpen(false)}>
      <Tooltip
        open={open}
        placement={columnOrder && columnOrder < 3 ? 'right' : 'left'}
        PopperProps={{
          disablePortal: true,
        }}
        slotProps={{
          tooltip: {
            sx: {
              borderRadius: '12px',
              border: '1px solid #e4eaed',
              maxWidth: 'unset',
              bgcolor: 'white',
              overflow: 'hidden',
              p: 0,
            },
          },
        }}
        title={(eventTooltip && eventTooltip(data)) || data.title}
      >
        <ButtonBase
          onClick={() => {
            if (onClick) {
              onClick(data);
            }
            setOpen(!open);
          }}
          sx={{
            p: '4px',
            position: 'absolute',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            overflow: 'hidden',
            border: data.border,
            borderRadius: isYesterDayEvent ? 0 : '4px',
            bgcolor: data.bgcolor,
            opacity: data.opacity,
            height: `calc(${(data.duration / 24) * 100}% - 4px)`,
            justifyContent: !isYesterDayEvent ? 'start' : 'end',
            left: `calc(100% * ${data.left} + ${
              data.columnIndex === 0 ? 2 : 0
            }px)`,
            width: `calc(100% * ${data.width} - 2px - ${
              (data.columnIndex + data.columnIndex === 0 ? 1 : 0) * 2
            }px)`,
            top: `calc(${(data.startHour / 24) * 100}% + ${
              data.startHour >= 0 ? 2 : 0
            }px)`,
          }}
          key={data.id}
        >
          {data.title && (
            <Typography
              className={`line-clamp ${
                data.duration !== 0.5 ? 'two-line' : ''
              }`}
              textAlign="start"
              fontSize={12}
              fontWeight={500}
            >
              {data.title}
            </Typography>
          )}
          {data.content && (
            <Typography
              className={`line-clamp ${
                data.duration !== 0.5 ? 'two-line' : ''
              }`}
              textAlign="start"
              fontSize={10}
            >
              {data.content}
            </Typography>
          )}
        </ButtonBase>
      </Tooltip>
    </ClickAwayListener>
  );
};

const areEqual = <T extends PackedEvent>(
  prevProps: EventIemProps<T>,
  nextProps: EventIemProps<T>,
) => {
  return JSON.stringify(prevProps) === JSON.stringify(nextProps);
};

export default memo(EventItem, areEqual) as typeof EventItem;

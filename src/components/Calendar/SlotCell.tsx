import { ButtonBase } from '@mui/material';
import dayjs from 'dayjs';
import { memo } from 'react';

import styles from './styles';

export interface SlotCellProps {
  onClick?: (timeSlot: string, status: string) => void;
  timeSlot: string;
  status: string;
}
const SlotCell = ({ onClick, status, timeSlot }: SlotCellProps) => {
  const isDisabledSlot =
    dayjs(timeSlot) < dayjs() ||
    dayjs().startOf('d').add(42, 'day') <= dayjs(timeSlot);

  return (
    <ButtonBase
      sx={{
        ...styles.slotCell,
        background: isDisabledSlot
          ? 'repeating-linear-gradient( -20deg, #d2dce1, #d2dce1 0px, #FFF 2px, #fff 8px )'
          : 'none',
      }}
      className={`slot-cell ${status === 'CLOSED' ? 'off-slot' : ''}`}
      disabled={isDisabledSlot}
      onClick={() => onClick && onClick(timeSlot, status)}
    />
  );
};

export default memo(SlotCell);

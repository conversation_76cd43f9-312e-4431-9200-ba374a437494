import {
  <PERSON>,
  Button,
  IconButton,
  <PERSON>ack,
  Toolt<PERSON>,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  CollapseIcon,
  ExpandIcon,
  InfoIcon,
} from 'icons';
import type { ReactNode } from 'react';

const Toolbar = ({
  startDate,
  onNavigate,
  extraRender,
  onTooltipClick,
  isFullScreen,
  onExpandClick,
}: {
  startDate: string;
  onNavigate: (date: string) => void;
  extraRender: ReactNode;
  onTooltipClick: () => void;
  isFullScreen?: boolean;
  onExpandClick: () => void;
}) => {
  const startDateDayjs = dayjs(startDate);

  return (
    <Box display="flex" justifyContent="space-between">
      <Box display="flex" alignItems="center">
        <Box mr="12px">{extraRender}</Box>
        <Box mr="12px">
          <Button
            variant="whiteOutlined"
            size="small"
            fullWidth
            onClick={() =>
              onNavigate(dayjs().startOf('week').add(1, 'day').toISOString())
            }
          >
            今日
          </Button>
        </Box>
        <Box display="flex" gap="4px" mr="12px">
          <Box>
            <IconButton
              sx={{ m: '-7px' }}
              disabled={new Date(startDate) <= new Date()}
              onClick={() =>
                onNavigate(startDateDayjs.subtract(7, 'day').toISOString())
              }
            >
              <ArrowLeftIcon />
            </IconButton>
          </Box>
          <Box>
            <IconButton
              sx={{ m: '-7px' }}
              disabled={
                startDateDayjs.add(13, 'd').diff(dayjs().add(42, 'd'), 'd') >= 0
              }
              onClick={() =>
                onNavigate(startDateDayjs.add(7, 'day').toISOString())
              }
            >
              <ArrowRightIcon />
            </IconButton>
          </Box>
        </Box>
        <Typography fontSize={24} fontWeight={700} color="heading">
          {startDateDayjs
            .startOf('week')
            .add(1, 'day')
            .format('YYYY年MM月DD日')}{' '}
          - {startDateDayjs.endOf('week').add(1, 'day').format('MM月DD日')}
        </Typography>
        <Tooltip
          arrow
          title=""
          placement="right"
          slotProps={{
            arrow: {
              sx: { color: '#fdf6e2' },
            },
          }}
        >
          <IconButton
            size="small"
            sx={{
              svg: { color: 'icon' },
            }}
            onClick={onTooltipClick}
          >
            <InfoIcon />
          </IconButton>
        </Tooltip>
      </Box>
      <Stack
        direction="row"
        columnGap={2}
        justifyContent="center"
        alignItems="center"
        flexWrap="wrap"
      >
        <Stack gap="5px" direction="row" alignItems="center">
          <Box
            width="13px"
            height="13px"
            border="1px solid #d2dce1"
            borderRadius="3px"
          />
          <Typography fontSize={12}>新規</Typography>
        </Stack>
        <Stack gap="5px" direction="row" alignItems="center">
          <Box
            width="13px"
            height="13px"
            borderRadius="3px"
            bgcolor="#fff4c7"
          />
          <Typography fontSize={12}>面談リクエスト</Typography>
        </Stack>
        <Stack gap="5px" direction="row" alignItems="center">
          <Box
            width="13px"
            height="13px"
            bgcolor="#e0eef5"
            borderRadius="3px"
          />
          <Typography fontSize={12}>面談実施</Typography>
        </Stack>
        <Stack gap="5px" direction="row" alignItems="center">
          <Box
            width="13px"
            height="13px"
            bgcolor="#dcf4e4"
            borderRadius="3px"
          />
          <Typography fontSize={12}>面談完了</Typography>
        </Stack>
        <IconButton size="small" sx={{ ml: '-5px' }} onClick={onExpandClick}>
          {isFullScreen ? <CollapseIcon /> : <ExpandIcon />}
        </IconButton>
      </Stack>
    </Box>
  );
};

export default Toolbar;

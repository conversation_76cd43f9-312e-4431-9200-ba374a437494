import { Box, ButtonBase, Typography } from '@mui/material';
import dayjs from 'dayjs';
import type { ReactNode } from 'react';
import { memo, useEffect, useMemo, useRef } from 'react';
import { WORK_TIMES } from 'utils/constants';

import EventItem from './EventItem';
import CalendarHeader from './Header';
import SlotCell from './SlotCell';
import styles from './styles';
import type { EventItem as IEventItem } from './utils';
import {
  divideEventsByColumns,
  getWeekDates,
  groupEventsByDate,
  SlotStatus,
} from './utils';

export interface ICalendar<T = unknown> {
  startDate: string;
  closeSlots?: string[];
  isLoading: boolean;
  events: T[];
  timeColumnWidth?: number;
  onSlotSelect?: (slotTime: string, status: string) => void;
  onDateSelect?: (date: string) => void;
  onTimeSelect?: (time: string) => void;
  onEventSelect?: (event: T) => void;
  eventTooltip?: (e: T) => ReactNode;
}
const Calendar = <TData extends IEventItem>({
  startDate = dayjs().toISOString(),
  closeSlots = [],
  isLoading,
  events,
  timeColumnWidth = 88,
  onSlotSelect,
  onDateSelect,
  onTimeSelect,
  onEventSelect,
  eventTooltip,
}: ICalendar<TData>) => {
  const groupByDate = groupEventsByDate<TData>(events, 0);
  const divinedEvents = divideEventsByColumns<TData>({
    events: groupByDate,
    startDate: dayjs(startDate).startOf('week').add(1, 'day').toISOString(),
    columns: 7,
    columnWidth: 1,
    startHour: 0,
    overlapEventsSpacing: 0,
    rightEdgeSpacing: 0,
    tzOffset: 0,
  });
  const allWeekDates = useMemo(() => getWeekDates(startDate), [startDate]);
  const scrollToRef = useRef<null | HTMLDivElement>(null);

  useEffect(() => {
    if (scrollToRef.current) {
      scrollToRef.current.scrollIntoView();
    }
  }, []);

  return (
    <Box position="relative">
      <CalendarHeader
        isLoading={isLoading}
        allWeekDates={allWeekDates}
        onDateSelect={onDateSelect}
        timeColumnWidth={timeColumnWidth}
      />
      <Box
        display="flex"
        flex="1 0 0%"
        alignItems="flex-start"
        width="100%"
        position="relative"
        overflow="hidden"
      >
        <Box
          flex="none"
          display="flex"
          flexDirection="column"
          minHeight="100%"
          whiteSpace="nowrap"
          minWidth={timeColumnWidth}
        >
          {WORK_TIMES.map((time) => (
            <ButtonBase
              key={time}
              id={time}
              onClick={() => {
                if (onTimeSelect) {
                  onTimeSelect(time);
                }
              }}
              sx={styles.workTimeCell}
            >
              <Typography
                fontWeight={500}
                color="heading"
                fontSize={14}
                textAlign="center"
                ref={time === '06:30' ? scrollToRef : null}
              >
                {time}
              </Typography>
            </ButtonBase>
          ))}
        </Box>

        {allWeekDates.map((day, index) => (
          <Box key={day} sx={styles.slotColumn}>
            {WORK_TIMES.map((time) => {
              const selectedTime = dayjs(time, 'HH:mm');
              const date = dayjs(day)
                .set('hour', selectedTime.get('hour'))
                .set('minute', selectedTime.get('minute'));
              const dateString = date.toISOString();

              return (
                <SlotCell
                  timeSlot={dateString}
                  key={dateString}
                  onClick={onSlotSelect}
                  status={
                    closeSlots.includes(dateString)
                      ? SlotStatus.CLOSED
                      : SlotStatus.OPEN
                  }
                />
              );
            })}

            {(divinedEvents[index] || []).map((event) => {
              return (
                <EventItem
                  columnOrder={index}
                  onClick={onEventSelect}
                  data={event}
                  key={event.id}
                  eventTooltip={eventTooltip}
                />
              );
            })}
          </Box>
        ))}
      </Box>
    </Box>
  );
};

const areEqual = <T extends unknown>(
  prevProps: ICalendar<T>,
  nextProps: ICalendar<T>,
) => {
  return JSON.stringify(prevProps) === JSON.stringify(nextProps);
};

export default memo(Calendar, areEqual) as typeof Calendar;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  headerContainer: {
    display: 'flex',
    flex: '0 0 auto',
    flexDirection: 'row',
    position: 'sticky',
    width: '100%',
    bgcolor: 'white',
    top: 0,
    left: 0,
    boxShadow: '0px 8px 16px -12px rgb(210, 220, 225)',
    zIndex: 1501,
  },
  timeGutter: {
    height: 48,
    borderRadius: '8px 0px 0px',
    border: '1px solid #d2dce1',
    p: '14px 16px',
  },
  gutterText: {
    fontWeight: 500,
    fontSize: 14,
    color: 'heading',
    textAlign: 'center',
  },
  headerDateButton: {
    flexDirection: 'column',
    flex: '1 0 0%',
    border: '1px solid #d2dce1',
    borderLeftWidth: 0,
    p: '5px 8px',
    '&:hover': {
      border: '1px solid #FCAB28',
      ml: '-1px',
      transition: '0.2s ease',
      bgcolor: '#fdf6e2',
      '.calendar-day-text': {
        color: 'primary.main',
      },
      '.calendar-date-text-wrapper': {
        bgcolor: 'latte',
      },
      '.calendar-date-text': {
        color: 'primary.main',
      },
    },
    '&:last-child': {
      borderRadius: '0px 8px 0px 0px',
    },
  },
  headerDayText: {
    fontSize: 10,
    lineHeight: '12px',
    textAlign: 'center',
    fontWeight: 500,
  },
  headerDateText: {
    fontSize: 14,
    lineHeight: '20px',
    fontWeight: 500,
    textAlign: 'center',
    width: 20,
  },
  workTimeCell: {
    flexFlow: 'column nowrap',
    flex: 1,
    display: 'flex',
    border: '1px solid #d2dce1',
    minHeight: 50,
    justifyContent: 'center',
    borderTopWidth: 0,
    '&:hover': {
      bgcolor: 'latte',
      border: '1px solid #FCAB28',
      '& p': {
        color: 'primary.main',
      },
    },
    '&:last-child': {
      borderRadius: '0px 0px 0px 8px',
    },
  },
  slotColumn: {
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100%',
    width: '100%',
    position: 'relative',
    '&:last-child': {
      button: {
        '&.slot-cell:last-child': {
          borderRadius: '0px 0px 8px',
        },
      },
    },
  },
  slotCell: {
    flex: 1,
    border: '1px solid #d2dce1',
    borderLeftWidth: 0,
    borderTopWidth: 0,
    minHeight: 50,
    display: 'flex',
    flexFlow: 'column nowrap',
    '&.off-slot': {
      bgcolor: '#edf1f3',
    },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

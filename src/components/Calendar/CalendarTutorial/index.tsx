import {
  Box,
  Button,
  Dialog,
  DialogContent,
  IconButton,
  Stack,
  SvgIcon,
  Typography,
} from '@mui/material';
import useCarousel from 'hooks/useCarousel';
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  BookingNewCaseIcon,
  CloseIcon,
  FailedIcon,
  ScheduleOffIcon,
  SuccessIcon,
  WarningIcon,
} from 'icons';
import Image from 'next/image';

import styles from './styles';

const images = [
  {
    title: 'スケジュールの設定方法について',
    subTitle: '(1)空き状況を変更したい場合',
    image: '/images/tutorial1.webp',
    content: (
      <>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          gap="2px"
        >
          <Typography fontSize={14}>欠勤の場合は枠をタップして</Typography>
          <BookingNewCaseIcon />
          <Typography fontSize={14}>→</Typography>
          <ScheduleOffIcon />
        </Box>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          gap="2px"
          mb={3}
        >
          <Typography fontSize={14}>出勤の場合は枠をタップして</Typography>
          <ScheduleOffIcon />
          <Typography fontSize={14}>→</Typography>
          <BookingNewCaseIcon />
        </Box>
      </>
    ),
  },
  {
    title: 'スケジュールの設定方法について',
    image: '/images/tutorial2.webp',
    subTitle: '(2) 1枠ずつ空き状況を変更したい場合',
    content: (
      <Typography fontSize={14} mt={1} textAlign="center" mb={3}>
        {`空き状況を変更したい枠をタップでかんたんに変更ができます
          ※スケジュールは1枠が30分となります`}
      </Typography>
    ),
  },
  {
    title: 'スケジュールの設定方法について',
    image: '/images/tutorial3.webp',
    subTitle: '(3) 特定の時間の空き状況をまとめて変更したい場合',
    content: (
      <Typography fontSize={14} mt={1} textAlign="center" mb={3}>
        {`空き状況を変更したい時間をタップで
          その時間の空き状況をまとめて変更できます`}
      </Typography>
    ),
  },
  {
    title: 'スケジュールの設定方法について',
    image: '/images/tutorial4.webp',
    subTitle: '(4) 特定の日付けの空き状況をまとめて変更したい場合',
    content: (
      <Typography fontSize={14} mt={1} textAlign="center" mb={3}>
        {`空き状況を変更したい日付をタップで
          その日の空き状況をまとめて変更できます`}
      </Typography>
    ),
  },
];
export const ICON = {
  success: <SuccessIcon />,
  warning: <FailedIcon />,
  error: <WarningIcon />,
};
export interface CalendarTutorialProps {
  open?: boolean;
  onClose?: () => void;
}
const CalendarTutorial = ({ open = false, onClose }: CalendarTutorialProps) => {
  const {
    viewportRef,
    prevBtnEnabled,
    scrollTo,
    nextBtnEnabled,
    selectedIndex,
    scrollSnaps,
    scrollPrev,
    scrollNext,
  } = useCarousel(images);
  return (
    <Dialog
      open={open}
      maxWidth="mw"
      fullWidth
      scroll="paper"
      PaperProps={{
        sx: styles.paper,
      }}
      onClose={onClose}
    >
      <IconButton
        onClick={onClose}
        sx={{ position: 'absolute', top: 9, right: 9 }}
      >
        <CloseIcon />
      </IconButton>
      <DialogContent className="confirm-dialog-content-container" sx={{ p: 0 }}>
        <Box overflow="hidden" ref={viewportRef}>
          <Box display="grid" gridAutoFlow="column" gridAutoColumns="100%">
            {images.map((image) => (
              <Box key={image.image} flex="0 0 100%">
                <Typography
                  fontSize={24}
                  mb={3}
                  fontWeight={700}
                  color="heading"
                  textAlign="center"
                >
                  {image.title}
                </Typography>
                <Typography
                  fontWeight={500}
                  color="heading"
                  mb={1}
                  textAlign="center"
                >
                  {image.subTitle}
                </Typography>
                {image.content}
                <Image
                  priority
                  width={1}
                  height={512 / 512}
                  style={{
                    width: '100%',
                    height: 'auto',
                    display: 'block',
                  }}
                  src={image.image}
                  alt=""
                  sizes="100vw"
                  key={image.image}
                />
              </Box>
            ))}
          </Box>
        </Box>
        <Box
          alignItems="center"
          gap={2}
          mt={2}
          display="flex"
          justifyContent="center"
        >
          <IconButton
            sx={{ p: '5px', border: 'solid 1px #d2dce1' }}
            onClick={scrollPrev}
            disabled={!prevBtnEnabled}
          >
            <SvgIcon fontSize="small" component={ArrowLeftIcon} />
          </IconButton>
          <Stack direction="row" gap={1}>
            {scrollSnaps.map((_, index) => (
              <Button
                sx={{
                  width: '8px',
                  height: '8px',
                  bgcolor: index === selectedIndex ? 'neutral7' : 'neutral2',
                  p: '4px',
                  minWidth: 'unset',
                  '&:hover': {
                    bgcolor: 'neutral2',
                  },
                }}
                key={index}
                onClick={() => scrollTo(index)}
              />
            ))}
          </Stack>
          <IconButton
            sx={{ p: '5px', border: 'solid 1px #d2dce1' }}
            onClick={scrollNext}
            disabled={!nextBtnEnabled}
          >
            <SvgIcon fontSize="small" component={ArrowRightIcon} />
          </IconButton>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default CalendarTutorial;

import { t } from 'i18n';
import type { AnyObjectSchema } from 'yup';
import { array, number, object, string } from 'yup';

const schema: AnyObjectSchema = object().shape({
  title: string()
    .required(t('validation.requiredField'))
    .trim(t('validation.notAllowedWhiteSpace'))
    .max(100, t('validation.maxLength', { number: 100 })),
  consultationField: array()
    .min(1, t('validation.requiredField'))
    .required(t('validation.requiredField')),
  customer: object({
    age: number()
      .typeError(t('validation.invalidField'))
      .required(t('validation.requiredField'))
      .max(200, t('validation.invalidField'))
      .min(18, t('validation.invalidField')),
    gender: string().required(t('validation.requiredField')),
  }),
  partner: object({
    age: number()
      .typeError(t('validation.invalidField'))
      .required(t('validation.requiredField'))
      .max(200, t('validation.invalidField'))
      .min(18, t('validation.invalidField')),
    gender: string().required(t('validation.requiredField')),
  }),
  background: string().required(t('validation.requiredField')),
  benefit: string().required(t('validation.requiredField')),
  content: string().required(t('validation.requiredField')),
  point: string().required(t('validation.requiredField')),
  children: string().required(t('validation.requiredField')),
});

export default schema;

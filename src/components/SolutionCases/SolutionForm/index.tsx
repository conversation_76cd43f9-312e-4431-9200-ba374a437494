import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Stack, Typography } from '@mui/material';
import { CheckBox, Radio, TextField } from 'components/Form';
import { useFetchList } from 'hooks';
import type { IListItem } from 'hooks/types';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import { omit } from 'lodash';
import resourceQuery from 'models/resource/query';
import type { ICreateSolution } from 'models/solution/interface';
import { useEffect } from 'react';
import { Gender, RadioOptions } from 'utils/constants';
import Helper from 'utils/helpers';

import schema from './schema';

const SolutionForm = ({
  defaultValues,
  onSubmit,
  loading,
  onCancel,
}: {
  defaultValues?: ICreateSolution;
  onSubmit: (values: ICreateSolution, isDirty: boolean) => void;
  loading?: boolean;
  onCancel: () => void;
}) => {
  const { list: consultationList } = useFetchList<IListItem>(
    resourceQuery.consultations,
  );
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useHookForm<ICreateSolution>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues,
  });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);
  return (
    <form onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}>
      <Stack spacing="28px">
        <TextField
          control={control}
          labelCol={4}
          name="title"
          label={t('solutionCase.title')}
          required
          placeholder={t('placeholder.title')}
          maxLength={100}
        />
        <CheckBox
          layout="horizontal"
          label={t('solutionCase.consultationField')}
          showSelectAll
          control={control}
          name="consultationField"
          required
          data={consultationList}
          labelCol={4}
        />
        <Box>
          <Typography fontWeight="bold">
            {t('solutionCase.customer')}
          </Typography>
          <Stack
            p="24px 24px 36px 24px"
            border="solid 1px #d2dce1"
            borderRadius={1}
            mt={2}
            spacing="28px"
          >
            <TextField
              control={control}
              name="customer.age"
              label={t('solutionCase.age')}
              placeholder={t('placeholder.input', {
                field: t('solutionCase.age'),
              })}
              required
              columns={28}
              labelCol={9}
              type="number"
              inputProps={{ min: 18, max: 200 }}
            />
            <Radio
              label={t('solutionCase.gender')}
              required
              name="customer.gender"
              columns={28}
              labelCol={9}
              control={control}
              data={Helper.convertObjectToOptions(Gender)}
            />
          </Stack>
          <Typography fontWeight="bold" mt={3}>
            {t('solutionCase.partner')}
          </Typography>
          <Stack
            p="24px 24px 36px 24px"
            border="solid 1px #d2dce1"
            borderRadius={1}
            mt={2}
            spacing="28px"
          >
            <TextField
              control={control}
              name="partner.age"
              label={t('solutionCase.age')}
              placeholder={t('placeholder.input', {
                field: t('solutionCase.age'),
              })}
              required
              columns={28}
              labelCol={9}
              type="number"
              inputProps={{ min: 18, max: 200 }}
            />
            <Radio
              label={t('solutionCase.gender')}
              name="partner.gender"
              required
              columns={28}
              labelCol={9}
              control={control}
              data={Helper.convertObjectToOptions(Gender)}
            />
          </Stack>
        </Box>
        <Radio
          name="children"
          label={t('solutionCase.kids')}
          required
          labelCol={4}
          control={control}
          data={Helper.convertObjectToOptions(omit(RadioOptions, ['other']))}
        />
        <Stack spacing={2}>
          <TextField
            labelCol={4}
            name="background"
            label={t('solutionCase.background')}
            placeholder={t('placeholder.input', {
              field: t('solutionCase.background'),
            })}
            control={control}
            multiline
            required
            minRows={7}
            maxLength={1000}
          />
          <TextField
            labelCol={4}
            name="content"
            label={t('solutionCase.contents')}
            placeholder={t('placeholder.input', {
              field: t('solutionCase.contents'),
            })}
            control={control}
            multiline
            required
            minRows={7}
            maxLength={1000}
          />
          <TextField
            labelCol={4}
            name="benefit"
            label={t('solutionCase.benefit')}
            placeholder={t('placeholder.input', {
              field: t('solutionCase.benefit'),
            })}
            control={control}
            multiline
            required
            minRows={7}
            maxLength={1000}
          />
          <TextField
            labelCol={4}
            name="point"
            label={t('solutionCase.point')}
            placeholder={t('placeholder.input', {
              field: t('solutionCase.point'),
            })}
            control={control}
            multiline
            required
            minRows={7}
            maxLength={1000}
          />
        </Stack>
      </Stack>
      <Stack
        bgcolor="white"
        direction="row"
        justifyContent="flex-end"
        spacing={1}
        mt={4}
      >
        <Button variant="outlined" onClick={onCancel}>
          {t('global.cancel')}
        </Button>
        <LoadingButton
          variant="contained"
          color="secondary"
          type="submit"
          sx={{ maxWidth: 112 }}
          fullWidth
          loading={loading}
        >
          {t('global.settle')}
        </LoadingButton>
      </Stack>
    </form>
  );
};

export default SolutionForm;

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Di<PERSON><PERSON>,
  <PERSON>rid,
  Icon<PERSON>utton,
  Stack,
  Typography,
} from '@mui/material';
import { AnimatePresence, motion } from 'framer-motion';
import type { IListItem } from 'hooks/types';
import i18n from 'i18n';
import { ArrowDownIcon, ArrowUpIcon, EditIcon, TrashIcon } from 'icons';
import type { ISolutionItem } from 'models/solution/interface';
import { memo, useState } from 'react';
import { Gender, RadioOptions } from 'utils/constants';

const SolutionCaseItem = ({
  data,
  onEdit,
  onDelete,
}: {
  data: ISolutionItem;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}) => {
  const { t } = i18n;
  const [seeMore, setSeeMore] = useState(false);

  return (
    <Box key={data._id} p="32px 32px 24px" bgcolor="white" borderRadius={2}>
      <motion.div layout key="title">
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography fontSize={24} fontWeight="bold" flex={1} color="heading">
            {data.title}
          </Typography>
          <Stack direction="row" spacing={1}>
            <Box>
              <IconButton
                sx={{ svg: { color: 'neutral7' } }}
                className="whiteOutlined"
                onClick={() => onDelete(data._id)}
              >
                <TrashIcon />
              </IconButton>
            </Box>
            <Box>
              <Button
                startIcon={<EditIcon />}
                variant="contained"
                color="secondary"
                onClick={() => onEdit(data._id)}
              >
                {t('global.edit')}
              </Button>
            </Box>
          </Stack>
        </Box>
        <Stack direction="row" gap={1} flexWrap="wrap" mt={2}>
          {data.consultationField.map((field: IListItem) => (
            <Chip label={field.value} key={field._id} />
          ))}
        </Stack>
        <Stack
          divider={<Divider />}
          borderRadius="12px"
          bgcolor="backgroundColor"
          mt={3}
          px={4}
        >
          <Grid container py={2} color="heading">
            <Grid item xs={4} fontWeight="bold">
              {t('solutionCase.customer')}
            </Grid>
            <Grid item xs={4}>
              {data.customer.age}
              {t('solutionCase.ageUnit')}
            </Grid>
            <Grid item xs={4}>
              {Gender[data.customer.gender]}
            </Grid>
          </Grid>
          <Grid container py={2} color="heading">
            <Grid item xs={4} fontWeight="bold">
              {t('solutionCase.partner')}
            </Grid>
            <Grid item xs={4}>
              {data.partner.age}
              {t('solutionCase.ageUnit')}
            </Grid>
            <Grid item xs={4}>
              {Gender[data.partner.gender]}
            </Grid>
          </Grid>
          <Grid container py={2} color="heading">
            <Grid item xs={4} fontWeight="bold">
              {t('solutionCase.kids')}
            </Grid>
            <Grid item xs={4}>
              {RadioOptions[data.children]}
            </Grid>
          </Grid>
        </Stack>
      </motion.div>
      <AnimatePresence>
        {seeMore && (
          <motion.div
            layout
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            key="content"
          >
            <Box px={4}>
              <Stack spacing={2} mt={3}>
                <div>
                  <Typography fontWeight="bold" color="heading">
                    {t('solutionCase.background')}
                  </Typography>
                  <Typography mt="4px">{data.background}</Typography>
                </div>
                <div>
                  <Typography fontWeight="bold" color="heading">
                    {t('solutionCase.contents')}
                  </Typography>
                  <Typography mt="4px">{data.content}</Typography>
                </div>
                <div>
                  <Typography fontWeight="bold" color="heading">
                    {t('solutionCase.benefit')}
                  </Typography>
                  <Typography mt="4px">{data.benefit}</Typography>
                </div>
                <div>
                  <Typography fontWeight="bold" color="heading">
                    {t('solutionCase.point')}
                  </Typography>
                  <Typography mt="4px">{data.point}</Typography>
                </div>
              </Stack>
            </Box>
          </motion.div>
        )}
      </AnimatePresence>
      <motion.div layout key="button">
        <Button
          endIcon={seeMore ? <ArrowUpIcon /> : <ArrowDownIcon />}
          sx={{ mt: 2, ml: -2 }}
          onClick={() => setSeeMore(!seeMore)}
        >
          {t('global.seeMore')}
        </Button>
      </motion.div>
    </Box>
  );
};

export default memo(SolutionCaseItem);

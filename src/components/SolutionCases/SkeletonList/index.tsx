import { Box, Skeleton, Stack } from '@mui/material';
import { times } from 'lodash';

const SkeletonList = () => {
  return (
    <Stack spacing={2}>
      {times(3).map((number) => (
        <Box p={4} bgcolor="white" borderRadius={2} key={number}>
          <Skeleton variant="text" sx={{ fontSize: '28px' }} width={200} />
          <Skeleton variant="text" sx={{ mt: 2 }} width={140} />
          <Skeleton
            variant="rounded"
            width="100%"
            height={170}
            sx={{ mt: 3 }}
          />
          <Skeleton variant="text" sx={{ mt: 3 }} width={80} />
        </Box>
      ))}
    </Stack>
  );
};

export default SkeletonList;

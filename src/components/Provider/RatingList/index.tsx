import StarIcon from '@mui/icons-material/Star';
import StarOutlineIcon from '@mui/icons-material/StarOutline';
import {
  Box,
  Button,
  Rating as RatingMui,
  Stack,
  Typography,
  useMediaQuery,
} from '@mui/material';
import Ratinglist from 'components/LawyerDetail/RatingList';
import BorderLabel from 'components/UI/BorderLabel';
import type { TReview } from 'hooks/types';
import useFetchListLoadMore from 'hooks/useFetchListLoadMore';
import i18n from 'i18n';
import { ArrowRightIcon } from 'icons';
import type { ICounselorItem, ILawyerItem } from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import { forwardRef } from 'react';
import Helper from 'utils/helpers';

import styles from './style';

const RatingListProvider = forwardRef(
  ({ detail }: { detail: ILawyerItem | ICounselorItem }, ref) => {
    const { t } = i18n;

    const isSmallMobile = useMediaQuery('(max-width:767px)');

    const {
      list: ratingList,
      handlerLoadMore,
      pagination,
      isLoading,
      isFetched,
    } = useFetchListLoadMore<TReview>({
      ...providerQuery.providerReviewsList(detail._id),
      nextLimit: 10,
      customParams: {
        page: 1,
        limit: 2,
        sort: 'createdAt.desc',
      },
    });

    return (
      <Box ref={ref} sx={styles.reviewList}>
        <BorderLabel>{t('lawyerProfile.rating')}</BorderLabel>
        <Box mt={4}>
          <Stack gap="4px" alignItems="center" direction="row">
            <Typography
              fontSize={{ xs: '24px', md: '28px' }}
              fontWeight="700"
              color="#464646"
            >
              {Helper.formatRating(detail.avgRating)}
            </Typography>
            <RatingMui
              emptyIcon={
                <StarOutlineIcon
                  fontSize={isSmallMobile ? 'medium' : 'large'}
                />
              }
              icon={<StarIcon fontSize={isSmallMobile ? 'medium' : 'large'} />}
              name="read-only"
              size="large"
              value={Helper.calculateRoundedRating(detail.avgRating)}
              readOnly
              precision={0.5}
            />
            <Typography color="#887F70" fontSize={{ xs: '18px', md: '24px' }}>
              ({detail.totalReview}件)
            </Typography>
          </Stack>
        </Box>
        <Box mt={2}>
          <Ratinglist
            data={ratingList}
            loading={isLoading}
            isFetched={isFetched}
          />
        </Box>
        {pagination?.hasMore && (
          <Box sx={styles.seeMore}>
            <Button
              sx={{ p: 0 }}
              color="primary"
              endIcon={<ArrowRightIcon />}
              onClick={handlerLoadMore}
            >
              {t('global.seeMore')}
            </Button>
          </Box>
        )}
      </Box>
    );
  },
);

RatingListProvider.displayName = 'RatingListProvider';

export default RatingListProvider;

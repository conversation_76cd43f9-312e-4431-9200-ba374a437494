import { Box, Container, Typography } from '@mui/material';
import { getSevenDatesFromNow } from 'components/TimeTable/utils';
import dayjs from 'dayjs';
import { t } from 'i18next';
import type { ICalendarDetail } from 'models/provider/interface';
import { useMemo } from 'react';
import {
  DATE_COLOR,
  DATE_COLOR_HEADING,
  MomentFormat,
  SlotStatus,
} from 'utils/constants';

import styles from './styles';

const Calendar = ({ calendarDetail }: { calendarDetail: ICalendarDetail }) => {
  const startTime = dayjs().startOf('d').toISOString();
  const allDatesFromNow = useMemo(
    () => getSevenDatesFromNow(startTime),
    [startTime],
  );
  return (
    <Box>
      <Typography
        color="heading"
        fontWeight="bold"
        fontSize={{ xs: 14, tablet: 16 }}
      >
        {t('calendar.title')}
      </Typography>
      <Typography
        color="hint"
        fontSize={{ xs: 12, tablet: 14 }}
        mt={{ xs: '4px', tablet: 1 }}
      >
        {t('calendar.description')}
      </Typography>
      <Container disableGutters sx={styles.container}>
        <Box display="flex" flexDirection="row">
          {allDatesFromNow.map((day, index) => {
            let borderRadiusBox;
            if (index === 0) {
              borderRadiusBox = '8px 0px 0px 0px';
            } else if (index === 6) {
              borderRadiusBox = '0px 8px 0px 0px';
            } else {
              borderRadiusBox = '0px';
            }

            const checkedCurrentDate = dayjs().isSame(dayjs(day), 'day');
            const colorDate = DATE_COLOR[dayjs(day).get('day')];
            const colorDateHeader = DATE_COLOR_HEADING[dayjs(day).get('day')];

            return (
              <Box
                key={day}
                sx={styles.boxSlot}
                border="1px solid #E4EAED"
                borderRadius={borderRadiusBox}
              >
                <Typography fontSize={10} textAlign="center" color={colorDate}>
                  {dayjs(day).format(MomentFormat.JP_DAY_NAME)}
                </Typography>
                <Box
                  sx={styles.currentDateHeading}
                  borderRadius={checkedCurrentDate ? '100%' : 0}
                  bgcolor={checkedCurrentDate ? 'primary.main' : 'white'}
                >
                  <Typography
                    fontSize={{ xs: 12, tablet: 14 }}
                    fontWeight={500}
                    textAlign="center"
                    color={checkedCurrentDate ? 'white' : colorDateHeader}
                  >
                    {dayjs(day).get('date')}
                  </Typography>
                </Box>
              </Box>
            );
          })}
        </Box>
        <Box display="flex" flexDirection="row">
          {Object.values(calendarDetail).map((item, index) => {
            let borderRadiusBox;
            if (index === 0) {
              borderRadiusBox = '0px 0px 0px 8px';
            } else if (index === 6) {
              borderRadiusBox = '0px 0px 8px 0px';
            } else {
              borderRadiusBox = '0px';
            }
            return (
              <Box
                key={index}
                bgcolor={
                  item.status !== SlotStatus.OPEN ? 'backgroundColor' : 'white'
                }
                sx={styles.boxSlot}
                border="1px solid #E4EAED"
                borderRadius={borderRadiusBox}
                borderTop="none"
              >
                {item.status === SlotStatus.OPEN && (
                  <Box sx={styles.slotOpen}></Box>
                )}
              </Box>
            );
          })}
        </Box>
      </Container>
    </Box>
  );
};
export default Calendar;

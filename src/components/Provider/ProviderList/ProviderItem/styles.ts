import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  introduction: {
    display: '-webkit-box',
    WebkitLineClamp: '2',
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    whiteSpace: 'pre-line',
    fontSize: { xs: 14, tablet: 16 },
    mt: { xs: '12px', tablet: 1 },
  },
  buttonWrapper: {
    '> *': {
      width: 'calc((100% - 8px)/2)',
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

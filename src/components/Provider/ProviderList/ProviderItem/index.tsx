import {
  <PERSON>,
  Button,
  Chip,
  Divider,
  Stack,
  SvgIcon,
  Typography,
} from '@mui/material';
import AvatarCarousel from 'components/UI/AvatarCarousel';
import ChipList from 'components/UI/ChipList';
import IconText from 'components/UI/IconText';
import Rating from 'components/UI/Rating';
import { useNavigationState } from 'context/NavigationContext';
import dayjs from 'dayjs';
import { t } from 'i18n';
import {
  ArrowRightIcon,
  BigArrowRightIcon,
  BlackPathLeft,
  BlackPathRight,
  CheckListIcon,
  MapIcon,
  MeetingNoteIcon,
  MyPageProfileIcon,
  OutlinedBuilding,
  QuoteIcon,
  StationIcon,
  WorkingHourIcon,
} from 'icons';
import { get } from 'lodash';
import type { ICounselorItem, ILawyerItem } from 'models/provider/interface';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  Gender,
  getProviderMenuText,
  PROVIDER_LIST_PATH,
  ProviderType,
} from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

const ProviderItem = ({ data }: { data: ILawyerItem | ICounselorItem }) => {
  const { push, query } = useRouter();
  const isLawyer = data.type === ProviderType.LAWYER;
  const isCounselor = data.type === ProviderType.COUNSELOR;
  const { catchphrase, introduction, freeConsultationMenu } = data;
  const address = isLawyer ? data.office.address : '';
  const stationToTheOffice = isLawyer && data.office.stationToTheOffice;
  const timeToTheOffice = isLawyer && data.office.timeToTheOffice;
  const businessHours = isLawyer && data.office.businessHours;
  const { setState } = useNavigationState();
  // Helper function to build detail URL with preserved query parameters
  const getDetailUrlWithParams = () => {
    const baseUrl = `${PROVIDER_LIST_PATH[data.type]}/${data._id}`;

    // Preserve all current query parameters
    const queryParams = new URLSearchParams();

    // Add all existing query parameters
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach((v) => queryParams.append(key, String(v)));
        } else {
          queryParams.append(key, String(value));
        }
      }
    });

    const queryString = queryParams.toString();
    return queryString ? `${baseUrl}?${queryString}` : baseUrl;
  };

  const handleContactLawyer = () => {
    const webCookie = Helper.getWebCookie();
    const webCookieByGuest = Helper.getWebCookieByGuest();
    const role = get(webCookie, 'role');
    const isDraftUser = get(webCookieByGuest, 'isDraftUser');
    if (isDraftUser || !role) {
      if (isLawyer) {
        push('/register');
      } else {
        setState({
          ...data,
        });

        // Filter out undefined values from query
        const cleanQuery = Object.entries({
          counselorId: data._id,
          duration: query?.duration,
          meetingType: query?.meetingType,
          startSlot: query?.startSlot,
          isFilterCounselor: query?.isFilterCounselor,
          date: query?.date,
          isVerifyEmail: !!isDraftUser,
        }).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== 'undefined' && value !== '') {
            acc[key] = value;
          }
          return acc;
        }, {} as Record<string, string | string[] | boolean>);

        push({
          pathname: '/guest/consultation-form',
          query: cleanQuery,
        });
      }
    } else if (isLawyer) {
      push(`/customer/consultation-form?lawyerId=${data._id}`);
    } else {
      // Filter out undefined values from query
      const cleanQuery = Object.entries({
        counselorId: data._id,
        duration: query?.duration,
        meetingType: query?.meetingType,
        startSlot: query?.startSlot,
      }).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== 'undefined' && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, string | string[]>);

      push({
        pathname: '/customer/consultation-form',
        query: cleanQuery,
      });
    }
  };

  const renderBasicInfo = () => {
    if (isCounselor) {
      const menus = data.menus || [];
      const MAX_LENGTH = 3;
      const menusShow =
        menus.length > MAX_LENGTH ? menus.slice(0, MAX_LENGTH) : menus;
      const numberMenuOverflow = menus.length - MAX_LENGTH;
      return (
        <Stack
          direction={{
            xs: 'column',
            tablet: 'row',
          }}
          gap={{ tablet: 3, xs: '6px' }}
          justifyContent={'space-between'}
        >
          <Stack spacing="6px">
            <IconText icon={MyPageProfileIcon}>
              {data?.birthday
                ? Math.floor(dayjs().diff(dayjs(data.birthday), 'years') / 10) *
                  10
                : '-'}
              代 /{data.gender ? Gender[data.gender] : '-'}
            </IconText>
            <IconText icon={OutlinedBuilding}>
              {data?.career
                ? t('counselorList.history', {
                    number: dayjs().diff(dayjs(data.career), 'years'),
                  })
                : '-'}
            </IconText>
            <IconText icon={CheckListIcon}>
              {data?.numberSolutionCases
                ? `カウンセリング総件数：約${data?.numberSolutionCases}件`
                : '-'}
            </IconText>
          </Stack>
          {menus.length > 0 && (
            <Stack gap={1} direction={'row'}>
              <MeetingNoteIcon />
              <Box
                sx={{
                  height: 'fit-content',
                  border: '1px solid #D2DCE1',
                  borderRadius: '6px',
                  overflow: 'hidden',
                }}
              >
                {menusShow.map((row, index) => {
                  const unitPrices = row.unitPrices?.[0];
                  const isOdd = index % 2 === 0;
                  if (unitPrices) {
                    return (
                      <Stack
                        direction={'row'}
                        key={row.title}
                        sx={{
                          backgroundColor: isOdd ? '#F7F8FA' : 'white',
                          borderBottom: '1px solid #D2DCE1',
                          '&:last-child': {
                            borderBottom: 0,
                          },
                        }}
                      >
                        <Box
                          sx={{
                            padding: '6px 12px',
                            borderRight: '1px solid #D2DCE1',
                          }}
                        >
                          <Typography
                            fontSize={{
                              xs: 12,
                              tablet: 12,
                            }}
                            fontWeight={500}
                          >
                            {getProviderMenuText(
                              row.meetingType as string,
                              unitPrices.duration,
                            )}
                          </Typography>
                        </Box>
                        <Box sx={{ padding: '6px 12px' }}>
                          <Typography
                            fontSize={{
                              xs: 12,
                              tablet: 12,
                            }}
                            fontWeight={500}
                          >
                            {`${Helper.addComma(unitPrices.price)}円`}
                          </Typography>
                        </Box>
                      </Stack>
                    );
                  }

                  return null;
                })}
                {numberMenuOverflow > 0 && (
                  <Stack direction={'row'}>
                    <Box
                      sx={{
                        padding: '6px 12px',
                      }}
                    >
                      <Typography
                        fontSize={{
                          xs: 12,
                          tablet: 12,
                        }}
                        fontWeight={500}
                      >
                        {`+その他${numberMenuOverflow}個のメニュー`}
                      </Typography>
                    </Box>
                  </Stack>
                )}
              </Box>
            </Stack>
          )}
        </Stack>
      );
    }
    return (
      <>
        <IconText icon={MapIcon}>
          {address
            ? t('lawyerList.address', {
                postCode: address?.postCode,
                address1: address?.address1?.value,
                address2: address?.address2,
                address3: address?.address3,
                address4: address?.address4,
              })
            : '-'}
        </IconText>
        <IconText icon={StationIcon}>
          {stationToTheOffice && timeToTheOffice
            ? t('lawyerList.access', {
                stationToTheOffice,
                timeToTheOffice,
              })
            : '-'}
        </IconText>
        <IconText icon={WorkingHourIcon}>{businessHours || '-'}</IconText>
      </>
    );
  };

  return (
    <Box
      p={{ xs: 2, tablet: 4 }}
      bgcolor="white"
      borderRadius={{ xs: 0, tablet: 2 }}
    >
      <Box display="flex">
        <Box display={{ xs: 'none', tablet: 'block' }}>
          <AvatarCarousel
            imageContainerProps={{
              width: { xs: 64, tablet: 152 },
              height: { xs: 64, tablet: 152 },
            }}
            imageProps={{
              sizes: '(min-width: 768px) 20vw, 18vw',
            }}
            images={data.images}
            redirect={getDetailUrlWithParams()}
          />
        </Box>
        <Link href={getDetailUrlWithParams()}>
          <Box
            display={{ xs: 'block', tablet: 'none' }}
            width={64}
            height={64}
            position="relative"
          >
            <Image
              style={{ borderRadius: '50%' }}
              alt=""
              fill
              sizes="(max-width: 888px) 17vw, 13vw"
              priority
              src={
                data.images && data.images[0]
                  ? data.images[0]?.originUrl
                  : '/images/default-avatar.png'
              }
            />
          </Box>
        </Link>
        <Box ml={{ xs: 2, tablet: 6 }}>
          <Box>
            {!isCounselor &&
              (data?.videoUrl?.value || data?.hasOnlineSupport) && (
                <Box
                  sx={{
                    marginBottom: { xs: '0', md: '5px' },
                    gap: '4px',
                    display: 'flex',
                  }}
                >
                  {data?.videoUrl?.value && (
                    <Chip
                      key={'lawyer video url'}
                      variant="orangeChip"
                      label="紹介動画あり"
                      component={'div'}
                    />
                  )}
                  {data?.hasOnlineSupport && (
                    <Chip
                      key={'lawyer support online'}
                      variant="orangeChip"
                      label="オンライン対応可"
                      component={'div'}
                    />
                  )}
                </Box>
              )}
            {isCounselor &&
              (data?.videoUrl?.value || data?.isAvailableToday) && (
                <Box
                  sx={{
                    marginBottom: { xs: '0', md: '5px' },
                    gap: '4px',
                    display: 'flex',
                  }}
                >
                  {data?.videoUrl?.value && (
                    <Chip
                      key={'counselor video url'}
                      variant="orangeChip"
                      label="紹介動画あり"
                      component={'div'}
                    />
                  )}
                  {data?.isAvailableToday && (
                    <Chip
                      key={'available today'}
                      variant="orangeChip"
                      label="本日相談可"
                      component={'div'}
                    />
                  )}
                </Box>
              )}
            <Link href={getDetailUrlWithParams()}>
              <Typography
                fontSize={{ xs: 20, tablet: 32 }}
                fontWeight="bold"
                color="heading"
                className="line-clamp"
                mt={{ xs: '6px', tablet: 0 }}
              >
                {(isCounselor && data?.nickname) || data.fullName}
              </Typography>
            </Link>
            {!(isCounselor && data?.nickname) && (
              <Typography
                fontWeight={500}
                fontSize={{ xs: 14, tablet: 16 }}
                color="text.primary"
                className="line-clamp"
              >
                {data.katakanaName}
              </Typography>
            )}

            {!isLawyer && (
              <Rating
                rate={{
                  avgRating: data.avgRating,
                  totalReview: data.totalReview,
                }}
                sx={{ marginTop: ' 4px' }}
                size="lg"
              />
            )}
          </Box>
          <Stack
            mt={2}
            sx={{ svg: { color: 'primary.main' } }}
            display={{ xs: 'none', tablet: 'block' }}
          >
            {isCounselor && data.certificate && data.certificate.length > 0 && (
              <ChipList
                data={data.certificate}
                chipProps={{ className: 'tabletStyle dark' }}
              />
            )}
            {data.consultationField && data.consultationField.length > 0 && (
              <ChipList
                data={data.consultationField}
                labelPath="value"
                stackProps={{ mt: 1 }}
              />
            )}
            <Stack gap="6px" mt={2}>
              {renderBasicInfo()}
            </Stack>
          </Stack>
        </Box>
      </Box>
      <Stack
        mt={2}
        sx={{ svg: { color: 'primary.main' } }}
        display={{ xs: 'block', tablet: 'none' }}
      >
        {isCounselor && data.certificate && data.certificate.length > 0 && (
          <ChipList
            data={data.certificate}
            chipProps={{ className: 'tabletStyle dark' }}
          />
        )}
        {data.consultationField && data.consultationField.length > 0 && (
          <ChipList
            data={data.consultationField}
            labelPath="value"
            stackProps={{ mt: 1 }}
          />
        )}
        <Stack gap="6px" mt={1}>
          {renderBasicInfo()}
        </Stack>
      </Stack>
      <Divider sx={{ display: { xs: 'block', tablet: 'none' }, mt: 2 }} />
      <Box mt={{ xs: 2, tablet: 3 }}>
        {catchphrase && (
          <Stack spacing={1} direction="row">
            <SvgIcon className="tabletStyle" component={QuoteIcon} />
            <Typography
              fontSize={{ tablet: 24 }}
              fontWeight="bold"
              color="primary"
              flex={1}
            >
              {catchphrase}
            </Typography>
          </Stack>
        )}
        {introduction && (
          <Typography sx={styles.introduction}>{introduction}</Typography>
        )}
        <Stack
          direction="row"
          spacing={{ xs: 1, tablet: 3 }}
          mt={{ xs: 2, tablet: 3 }}
          alignItems="flex-end"
          sx={styles.buttonWrapper}
        >
          <Button
            LinkComponent={Link}
            href={getDetailUrlWithParams()}
            size="large"
            variant="outlined"
            className="tabletStyle shadow"
            endIcon={<BigArrowRightIcon />}
          >
            詳細を見る
          </Button>
          <Box>
            {freeConsultationMenu && (
              <Box
                display="flex"
                gap={1}
                justifyContent="center"
                mb={{ xs: 0.5, tablet: 2 }}
                sx={{
                  svg: {
                    width: { xs: 11, tablet: 15 },
                    height: { xs: 15, tablet: 25 },
                  },
                }}
              >
                <BlackPathLeft />
                <Typography
                  fontSize={{ xs: 12, tablet: 18 }}
                  fontWeight={500}
                  color="#464646"
                >
                  {isLawyer ? '初回無料' : 'オンラインで気軽に'}
                </Typography>
                <BlackPathRight />
              </Box>
            )}
            <Button
              variant="contained"
              color="secondary"
              size="large"
              className="tabletStyle shadow"
              fullWidth
              onClick={handleContactLawyer}
              endIcon={<ArrowRightIcon width={32} height={32} color="white" />}
            >
              相談する
            </Button>
          </Box>
        </Stack>
      </Box>
    </Box>
  );
};

export default ProviderItem;

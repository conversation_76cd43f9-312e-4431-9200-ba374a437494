import { Box, Skeleton, Stack } from '@mui/material';

const ItemSkeleton = () => {
  return (
    <Box p={{ xs: 2, tablet: 4 }} bgcolor="white" borderRadius={2}>
      <Stack direction="row" display="flex" spacing={{ xs: 2, tablet: 7 }}>
        <Box>
          <Skeleton
            variant="circular"
            sx={{
              width: { xs: 64, tablet: 152 },
              height: { xs: 64, tablet: 152 },
            }}
          />
        </Box>
        <Box>
          <Skeleton
            sx={{
              width: { xs: 80, tablet: 160 },
              height: { xs: 20, tablet: 44 },
            }}
          />
          <Skeleton
            sx={{
              width: { xs: 80, tablet: 160 },
              height: { xs: 20, tablet: 44 },
            }}
          />
          <Stack
            mt={{ xs: 4, tablet: 2 }}
            spacing="6px"
            ml={{ xs: -10, tablet: 0 }}
          >
            <Skeleton
              sx={{
                width: { xs: 125, tablet: 250 },
                height: { xs: 20, tablet: 24 },
              }}
            />
            <Skeleton
              sx={{
                width: { xs: 125, tablet: 250 },
                height: { xs: 20, tablet: 24 },
              }}
            />
            <Skeleton
              sx={{
                height: { xs: 20, tablet: 24 },
              }}
            />
          </Stack>
        </Box>
      </Stack>
      <Box mt={{ xs: 2, tablet: 3 }}>
        <Stack direction="row" spacing={3}>
          <Skeleton
            sx={{
              height: { xs: 40, tablet: 56 },
            }}
            width="50%"
          />
          <Skeleton
            sx={{
              height: { xs: 40, tablet: 56 },
            }}
            width="50%"
          />
        </Stack>
      </Box>
    </Box>
  );
};

export default ItemSkeleton;

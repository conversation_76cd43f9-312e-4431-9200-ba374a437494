import { Box, Stack } from '@mui/material';
import ListPagination from 'components/CustomPagination/ListPagination';
import { isEmpty, times } from 'lodash';
import type { ICounselorItem, ILawyerItem } from 'models/provider/interface';

import ProviderItem from './ProviderItem';
import ItemSkeleton from './ProviderItem/Skeleton';

export interface ProviderListProps {
  list: (ILawyerItem | ICounselorItem)[];
  total: number;
  limit?: number;
  loading: boolean;
  showPagination?: boolean;
}
const ProviderList = ({
  list = [],
  total,
  loading,
  limit,
  showPagination = true,
}: ProviderListProps): JSX.Element => {
  return (
    <Box className="lawyer-list">
      <Stack spacing={{ xs: 1, tablet: 2 }}>
        {loading &&
          isEmpty(list) &&
          times(3).map((index) => <ItemSkeleton key={index} />)}

        {!loading &&
          !isEmpty(list) &&
          list.map((data) => {
            return <ProviderItem data={data} key={data._id} />;
          })}
      </Stack>
      {!isEmpty(list) && showPagination && (
        <Box display="flex" justifyContent="center" mt={{ xs: 2, tablet: 5 }}>
          <ListPagination total={total} limit={limit} />
        </Box>
      )}
    </Box>
  );
};

export default ProviderList;

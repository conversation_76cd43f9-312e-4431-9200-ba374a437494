import {
  Box,
  Button,
  Grid,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import FormControlLabel from '@mui/material/FormControlLabel';
import CheckboxBase from 'components/Form/CheckBox/CheckboxBase';
import type { IListItem } from 'hooks/types';
import useBreakpoint from 'hooks/useBreakpoint';
import i18n from 'i18n';
import { CloseIcon } from 'icons';
import { isEmpty } from 'lodash';
import type { ReactNode } from 'react';
import { memo, useEffect, useState } from 'react';

import styles from './styles';
// TODO: Reduce code
export interface ChecboxModalProps {
  open: boolean;
  ids: string[];
  entities: Record<string, IListItem>;
  defaultValues: string[];
  onSubmit: (value: string[]) => void;
  onClose: () => void;
  icon: ReactNode;
  title: string;
}
const CheckboxModal = ({
  open,
  ids = [],
  defaultValues = [],
  onSubmit,
  onClose,
  entities,
  icon,
  title,
}: ChecboxModalProps) => {
  const { t } = i18n;
  const isBreakpoint = useBreakpoint({});
  const [values, setValues] = useState(defaultValues);
  useEffect(() => {
    setValues(defaultValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(defaultValues)]);

  const renderFilterString = () => {
    if (isEmpty(values)) {
      return t('global.noSelected');
    }
    if (values.length === ids.length) {
      return t('global.selectAll');
    }
    return values.map((val) => entities[val]?.value).join(' • ');
  };
  if (!isBreakpoint)
    return (
      <div>
        <Dialog
          onClose={(_, reason) => {
            if (reason === 'backdropClick') {
              onClose();
            }
          }}
          open={open}
          aria-labelledby="checkbox-modal-title"
          aria-describedby="checkbox-modal-description"
          maxWidth="ncol"
        >
          <DialogTitle id="checkbox-modal-title" sx={styles.dialogTitle}>
            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
            >
              <Stack direction="row" spacing={1} alignItems="center">
                {icon}
                <Typography fontSize={18} fontWeight="bold">
                  {title} :
                </Typography>
              </Stack>
              <IconButton
                className="whiteOutlined"
                size="small"
                onClick={onClose}
                sx={{ svg: { color: 'neutral7' } }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
            <Typography mt="12px" sx={styles.filterText}>
              {renderFilterString()}
            </Typography>
          </DialogTitle>
          <DialogContent sx={styles.dialogContent}>
            <DialogContentText id="checkbox-modal-description">
              <FormControlLabel
                sx={styles.formLabel}
                onChange={(_, checked) => {
                  if (checked) {
                    setValues(ids);
                  } else setValues([]);
                }}
                control={
                  <CheckboxBase
                    sx={styles.checkbox}
                    checked={values.length === ids.length}
                  />
                }
                label={t('global.selectAll')}
              />
              <Grid container>
                {ids.map((id) => (
                  <Grid item xs={6} sm={4} key={id}>
                    <FormControlLabel
                      sx={styles.formLabel}
                      control={
                        <CheckboxBase
                          sx={styles.checkbox}
                          checked={values.includes(id)}
                        />
                      }
                      label={entities[id]?.value?.toString().split('（')[0]}
                      onChange={(_, checked) => {
                        if (checked) {
                          setValues((prevValues) =>
                            prevValues.concat(id as never),
                          );
                        } else {
                          setValues((prevValues) =>
                            prevValues.filter((item) => item !== id),
                          );
                        }
                      }}
                    />
                  </Grid>
                ))}
              </Grid>
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={styles.dialogActions}>
            <Button onClick={() => setValues([])} sx={styles.resetButton}>
              {t('global.reset')}
            </Button>
            <Stack spacing={1} direction="row">
              <Button variant="outlined" onClick={onClose} sx={{ width: 112 }}>
                {t('global.cancel')}
              </Button>
              <Button
                variant="contained"
                color="secondary"
                className="icon-button"
                onClick={() => onSubmit(values)}
                fullWidth
                sx={{ width: 112, svg: { color: 'heading' } }}
              >
                {t('global.settle')}
              </Button>
            </Stack>
          </DialogActions>
        </Dialog>
      </div>
    );
  return (
    <Dialog
      onClose={(_, reason) => {
        if (reason === 'backdropClick') {
          onClose();
        }
      }}
      sx={styles.dialog}
      fullScreen
      open={open}
      aria-labelledby="checkbox-modal-title"
      aria-describedby="checkbox-modal-description"
      maxWidth="ncol"
    >
      <DialogTitle id="checkbox-modal-title" sx={styles.dialogTitleSection}>
        <Box position="relative">
          <IconButton
            size="small"
            sx={{
              position: 'absolute',
              top: -5,
              left: -5,
              svg: { width: 28, height: 28, color: 'primary.main' },
            }}
            onClick={onClose}
          >
            <CloseIcon />
          </IconButton>
          <Typography
            fontSize={18}
            fontWeight={500}
            color="heading"
            textAlign="center"
            lineHeight="28px"
          >
            {title}
          </Typography>
          <Button
            color="primary"
            size="small"
            onClick={() => setValues([])}
            sx={{ fontSize: 14, position: 'absolute', right: -5, top: -1 }}
          >
            {t('global.reset')}
          </Button>
        </Box>
      </DialogTitle>
      <DialogContent className="dialog-content" sx={{ p: '0px' }}>
        <DialogContentText
          sx={{
            padding: '6px 16px',
            backgroundColor: 'white',
            my: '8px',
          }}
          component="div"
          id="checkbox-modal-description"
        >
          <FormControlLabel
            sx={styles.formLabel}
            onChange={(_, checked) => {
              if (checked) {
                setValues(ids);
              } else setValues([]);
            }}
            control={
              <CheckboxBase
                sx={styles.checkbox}
                iconClassName="tabletStyle"
                checked={values.length === ids.length}
              />
            }
            label={t('global.selectAll')}
          />
          <Grid container>
            {ids.map((id) => (
              <Grid item xs={6} sm={4} key={id}>
                <FormControlLabel
                  sx={styles.formLabel}
                  control={
                    <CheckboxBase
                      sx={styles.checkbox}
                      iconClassName="tabletStyle"
                      checked={values.includes(id)}
                    />
                  }
                  label={entities[id]?.value?.toString().split('（')[0]}
                  onChange={(_, checked) => {
                    if (checked) {
                      setValues((prevValues) => prevValues.concat(id as never));
                    } else {
                      setValues((prevValues) =>
                        prevValues.filter((item) => item !== id),
                      );
                    }
                  }}
                />
              </Grid>
            ))}
          </Grid>
        </DialogContentText>
      </DialogContent>
      <DialogActions sx={{ p: 2, bgcolor: 'white' }}>
        <Stack spacing={1} direction="row" width={1}>
          <Button
            variant="outlined"
            color="primary"
            onClick={onClose}
            fullWidth
          >
            {t('global.cancel')}
          </Button>
          <Button
            color="secondary"
            variant="contained"
            type="submit"
            fullWidth
            sx={{ fontWeight: 'bold' }}
            onClick={() => onSubmit(values)}
          >
            {t('global.settle')}
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default memo(CheckboxModal);

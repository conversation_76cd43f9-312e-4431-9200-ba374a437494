import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  resetButton: {
    ml: -1,
  },
  dialogTitle: { p: '32px 32px 24px' },
  dialogContent: { p: '0px 32px 24px' },
  dialogActions: {
    p: '8px 32px 32px',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterText: {
    display: '-webkit-box',
    WebkitLineClamp: '1',
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    flex: 1,
    whiteSpace: 'normal',
  },
  dialog: {
    '.MuiPaper-root': {
      width: 'unset',
      borderRadius: 0,
      bgcolor: 'backgroundColor',
    },
  },
  dialogTitleSection: {
    p: '14px 16px',
    bgcolor: 'white',
  },
  backButton: {
    position: 'absolute',
    p: 0,
    svg: {
      width: 28,
      height: 28,
    },
  },
  formLabel: {
    whiteSpace: 'pre-line',
    alignItems: 'flex-start',
  },
  checkbox: {
    p: {
      xs: '10px',
      tablet: 1,
    },
    '+ .MuiTypography-root': {
      fontSize: { xs: 14, tablet: 16 },
      lineHeight: { xs: '20px', tablet: '24px' },
      marginTop: { xs: '10px', tablet: 1 },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import { Box } from '@mui/material';
import { PlayIcon } from 'icons';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import Helper from 'utils/helpers';

import styles from './styles';

const VideoThumbnail = ({ videoUrl }: { videoUrl: string }) => {
  const [thumbnail, setThumbnail] = useState(
    Helper.getVideoThumnail(videoUrl) || '/images/default-image.png',
  );

  useEffect(() => {
    setThumbnail(
      Helper.getVideoThumnail(videoUrl) || '/images/default-image.png',
    );
  }, [videoUrl]);

  return (
    <a href={Helper.formatUrl(videoUrl)} target="_blank" rel="noreferrer">
      <Box overflow="hidden" position="relative" sx={styles.uploadVideo}>
        <Image
          className="pointer"
          alt=""
          width={1}
          height={463 / 824}
          onError={() => {
            const youtubeId = Helper.getYouTubeVideoIdFromUrl(videoUrl);
            setThumbnail(`http://i3.ytimg.com/vi/${youtubeId}/hqdefault.jpg`);
          }}
          src={thumbnail}
          sizes="100vw"
          style={{
            width: '100%',
            height: 'auto',
          }}
        />
        <Box className="pointer" position="absolute" sx={styles.playIcon}>
          <PlayIcon />
        </Box>
      </Box>
    </a>
  );
};

export default VideoThumbnail;

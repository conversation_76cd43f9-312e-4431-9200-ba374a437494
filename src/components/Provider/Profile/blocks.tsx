import { Box, Chip, Stack, Typography } from '@mui/material';
import type { I<PERSON>lockField } from 'components/UI/ViewEditBlock';
import dayjs from 'dayjs';
import type { CounselorData, LawyerData } from 'hooks/useFetchUser/types';
import { t } from 'i18n';
import {
  BankIcon,
  BuildingIcon,
  DivorceIcon,
  ProfileIcon,
  PropertyIcon,
} from 'icons';
import { isUndefined, orderBy } from 'lodash';
import type { BankInformation } from 'models/provider/interface';
import type { ReactNode } from 'react';
import type {
  GenderType,
  RadioOptionsType,
  YesNoOptionsType,
} from 'utils/constants';
import {
  Gender,
  MomentFormat,
  RadioOptions,
  YES_NO_OPTIONS,
} from 'utils/constants';
import Helper from 'utils/helpers';

export interface IBlock<T = unknown> {
  title: string;
  icon: ReactNode;
  fields: IBlockField<T>[];
}
export const profileBlock: {
  title: string;
  icon: ReactNode;
  fields: IBlockField<LawyerData>[];
} = {
  title: 'ご相談者のプロフィール',
  icon: <ProfileIcon />,
  fields: [
    {
      label: t('updateCustomerProfile.fullName'),
      path: 'fullName',
    },
    {
      label: t('updateCustomerProfile.katakanaName'),
      path: 'katakanaName',
    },
    {
      label: t('updateCustomerProfile.email'),
      path: 'email',
    },
    {
      label: t('updateCustomerProfile.gender'),
      path: 'gender',
      renderValue: (value) => Gender[value as GenderType],
    },
    {
      label: t('updateCustomerProfile.phone'),
      path: 'phone',
    },
    {
      label: t('updateCustomerProfile.address'),
      path: 'currentAddress.address1.value',
    },
    {
      label: t('updateCustomerProfile.age'),
      path: 'age.value',
    },
    {
      label: t('updateCustomerProfile.job'),
      path: 'job',
    },
    {
      label: t('updateCustomerProfile.annualIncome'),
      path: 'annualIncome',
    },
  ],
};
export const divorceBlock: IBlock = {
  title: t('consultationRecord.divorceBackground'),
  icon: <DivorceIcon />,
  fields: [
    {
      label: 'consultationRecord.youDivorce',
      path: 'backgroundOfDivorce.youDivorce',
      renderValue: (value: YesNoOptionsType) => YES_NO_OPTIONS[value],
    },
    {
      label: 'consultationRecord.partnerDivorce',
      path: 'backgroundOfDivorce.partnerDivorce',
      renderValue: (value: YesNoOptionsType) =>
        YES_NO_OPTIONS[value as YesNoOptionsType],
    },
    {
      label: 'consultationRecord.reason',
      path: 'backgroundOfDivorce.reason',
      renderValue: (
        value: {
          value: string;
          text: string;
          extraData?: string;
        }[],
      ) => {
        if (value && value.length > 0) {
          return (
            <Stack spacing={1}>
              {orderBy(value, (item) => Number(item.value.slice(15))).map(
                (i) => (
                  <Stack direction="row" key={i.value} spacing={1}>
                    <Box>
                      <Chip label={i.text} />
                    </Box>
                    <Typography>{i.extraData}</Typography>
                  </Stack>
                ),
              )}
            </Stack>
          );
        }
        return null;
      },
    },
    {
      label: 'その他ご相談したい内容をご記入下さい',
      path: 'backgroundOfDivorce.additionalTopics',
    },
  ],
};

export const partnerBlock: IBlock = {
  title: t('consultationRecord.partnerInformation'),
  icon: <ProfileIcon />,
  fields: [
    {
      label: '年齢',
      path: 'partner.age.value',
    },
    {
      label: '職業',
      path: 'partner.job',
    },
    {
      label: 'consultationRecord.annualIncome',
      path: 'partner.annualIncome',
    },
  ],
};

export const marriageBlock: IBlock = {
  title: t('consultationRecord.marriageInformation'),
  icon: <BuildingIcon />,
  fields: [
    {
      label: 'カウンセラー歴',
      path: 'marriageInformation.marriedDate',
    },
    {
      label: 'カウンセリング総件数',
      path: 'marriageInformation.isSeparated',
    },
    {
      label: 'consultationRecord.separationDate',
      path: 'marriageInformation.separationDate',
      renderValue: (value) =>
        value && dayjs(value).format(MomentFormat.JP_YEAR_MONTH_DATE),
    },
  ],
};

export const propertyBlock: IBlock = {
  title: t('consultationRecord.propertyInformation'),
  icon: <PropertyIcon />,
  fields: [
    {
      label: 'consultationRecord.firstValueOfRealEstate',
      path: 'propertyInformation.firstValueOfRealEstate',
      renderValue: (value) => !isUndefined(value) && `${value}万円`,
    },
    {
      label: 'consultationRecord.currentValueOfRealEstate',
      path: 'propertyInformation.currentValueOfRealEstate',
      renderValue: (value) => !isUndefined(value) && `${value}万円`,
    },
    {
      label: 'consultationRecord.loan',
      path: 'propertyInformation.loan',
      renderValue: (value) => !isUndefined(value) && `${value}万円`,
    },
    {
      label: 'consultationRecord.depositAndSaving',
      path: 'propertyInformation.depositAndSaving',
      renderValue: (value) => !isUndefined(value) && `${value}円`,
    },
    {
      label: 'consultationRecord.depositAndSavingOfPartner',
      path: 'propertyInformation.depositAndSavingOfPartner',
      renderValue: (value) => !isUndefined(value) && `${value}円`,
    },
    {
      label: 'consultationRecord.lifeInsurance',
      path: 'propertyInformation.lifeInsurance',
      renderValue: (value: RadioOptionsType) => RadioOptions[value],
    },
    {
      label: 'consultationRecord.typeOfPensionOfYourself',
      path: 'propertyInformation.typeOfPensionOfYourself',
    },
    {
      label: 'consultationRecord.typeOfPensionOfPartner',
      path: 'propertyInformation.typeOfPensionOfPartner',
    },
    {
      label: 'consultationRecord.otherProperties',
      path: 'propertyInformation.otherProperties',
    },
    {
      label: 'consultationRecord.supplementaryInfo',
      path: 'propertyInformation.supplementaryInfo',
    },
  ],
};

export const careerBlock: IBlock<CounselorData> = {
  title: '経歴・得意分野',
  icon: <BuildingIcon />,
  fields: [
    {
      label: 'カウンセラー歴',
      path: 'career',
      renderValue: (_, data) =>
        data?.career &&
        `${Math.abs(dayjs(data.career).diff(dayjs(), 'years'))}年`,
    },
    {
      label: 'カウンセリング総件数',
      path: 'numberSolutionCases',
      renderValue: (_, data) =>
        (data?.numberSolutionCases || data?.numberSolutionCases === 0) &&
        `約${Helper.addComma(data.numberSolutionCases)}件`,
    },
    {
      label: '資格',
      path: 'certificate',
      renderValue: (_, data) =>
        data?.certificate?.length ? (
          <Stack direction="row" gap={1} flexWrap="wrap">
            {data?.certificate?.map((cert) => (
              <Chip className="dark" key={cert} label={cert} />
            ))}
          </Stack>
        ) : null,
    },
    {
      label: '得意分野',
      path: 'consultationField',
      renderValue: (_, data) =>
        data?.consultationField?.length ? (
          <Stack direction="row" gap={1} flexWrap="wrap">
            {data?.consultationField?.map((field) => (
              <Chip key={field._id} label={field.value} />
            ))}
          </Stack>
        ) : null,
    },
  ],
};

export const bankBlock: IBlock<BankInformation> = {
  title: '銀行情報',
  icon: <BankIcon />,
  fields: [
    {
      label: t('bankInformation.financialName'),
      path: 'bankName',
    },
    {
      label: t('bankInformation.branchName'),
      path: 'branchName',
    },
    {
      label: t('bankInformation.accountType'),
      path: 'accountType',
      renderValue: (_, data) =>
        data?.accountType?.labelJP ? data?.accountType.labelJP : '',
    },
    {
      label: t('bankInformation.accountNumber'),
      path: 'accountNumber',
    },
    {
      label: t('bankInformation.accountName'),
      path: 'holderName',
    },
  ],
};

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { <PERSON><PERSON>, Stack } from '@mui/material';
import { Select, TextField } from 'components/Form';
import AutoCompleteSelect from 'components/Form/AutoCompleteSelect';
import { useDeepCompareEffect, useFetchList } from 'hooks';
import useGlobalState from 'hooks/useGlobalState';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import type { BankInformation } from 'models/provider/interface';
import type { IBank, IBranches } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import { useCallback } from 'react';
import type { DeepPartial } from 'react-hook-form';
import { BANK_ACCOUNT_TYPE, BANK_ACCOUNT_TYPE_TEXT } from 'utils/constants';
import Helper from 'utils/helpers';

import type { BankFormValues } from './schema';
import schema from './schema';

export interface BankFormProps {
  onSubmit: (values: BankInformation, isDirty: boolean) => void;
  loading?: boolean;
  onCancel: (isDirty: boolean) => void;
  defaultValues: DeepPartial<BankFormValues>;
}
const BankForm = ({
  onSubmit,
  loading,
  onCancel,
  defaultValues,
}: BankFormProps) => {
  const {
    control,
    handleSubmit,
    formState: { isDirty },
    reset,
    watch,
    setValue,
  } = useHookForm<BankFormValues>({
    mode: 'onTouched',
    resolver: yupResolver(schema),
    defaultValues,
  });
  const watchBankField = watch('bankId');
  const watchBranchField = watch('branchId');

  const { setConfirmModal } = useGlobalState();

  const { list: bankList } = useFetchList<IBank & { value: string }>(
    resourceQuery.banks,
  );
  const { list: branchList, isLoading: loadingBranch } = useFetchList<
    IBranches & { value: string }
  >({
    ...resourceQuery.branchesByBank(watchBankField || ''),
    enabled: !!watchBankField,
  });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  const submitBankForm = (values: BankFormValues) => {
    const chooseBank = bankList.find((bank) => bank._id === values.bankId) || {
      bankCode: '',
      bankName: '',
    };
    const chooseBranch = branchList.find(
      (branch) => branch._id === values.branchId,
    ) || {
      branchCode: '',
      branchName: '',
    };
    onSubmit(
      {
        bankId: values.bankId,
        ...chooseBank,
        branchId: values.branchId,
        ...chooseBranch,
        accountNumber: values.accountNumber,
        holderName: values.holderName,
        accountType: {
          key:
            BANK_ACCOUNT_TYPE[values.accountType]?.key ||
            BANK_ACCOUNT_TYPE.STANDARD?.key ||
            '',
          labelJP:
            BANK_ACCOUNT_TYPE[values.accountType]?.labelJP ||
            BANK_ACCOUNT_TYPE.STANDARD?.labelJP ||
            '',
          labelEN:
            BANK_ACCOUNT_TYPE[values.accountType]?.labelEN ||
            BANK_ACCOUNT_TYPE.STANDARD?.labelEN ||
            '',
        },
      },
      isDirty,
    );
  };

  const handleCancel = useCallback(() => {
    if (isDirty) {
      setConfirmModal({
        onConfirm: () => {
          onCancel(false);
        },
        title: 'discardInput.title',
        content: 'discardInput.message',
      });
    } else onCancel(isDirty);
  }, [isDirty, onCancel, setConfirmModal]);

  return (
    <form onSubmit={handleSubmit((values) => submitBankForm(values))}>
      <Stack spacing={2} mt="19px">
        <AutoCompleteSelect
          labelCol={5}
          columns={15}
          name="bankId"
          label={t('bankInformation.financialName')}
          placeholder="選択してください"
          data={bankList}
          control={control}
          required
          onChange={() => {
            if (watchBranchField) {
              setValue('branchId', '');
            }
          }}
        />
        <AutoCompleteSelect
          labelCol={5}
          columns={15}
          name="branchId"
          label={t('bankInformation.branchName')}
          placeholder="選択してください"
          data={branchList}
          control={control}
          required
          disabled={!watchBankField || !branchList || loadingBranch}
        />
        <Select
          labelCol={5}
          columns={15}
          name="accountType"
          label={t('bankInformation.accountType')}
          placeholder="選択してください"
          data={Helper.convertObjectToOptions(BANK_ACCOUNT_TYPE_TEXT)}
          control={control}
          required
        />
        <TextField
          labelCol={5}
          columns={15}
          maxLength={7}
          control={control}
          required
          name="accountNumber"
          label={t('bankInformation.accountNumber')}
          placeholder={'(例：0123456)'}
        />
        <TextField
          labelCol={5}
          columns={15}
          maxLength={50}
          control={control}
          required
          name="holderName"
          label={t('bankInformation.accountName')}
          placeholder={'ヤマダタロウ'}
        />
      </Stack>
      <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
        <Button variant="outlined" onClick={handleCancel}>
          {t('global.cancel')}
        </Button>
        <LoadingButton
          color="secondary"
          variant="contained"
          fullWidth
          loading={loading}
          type="submit"
          sx={{ maxWidth: 112 }}
        >
          {t('global.settle')}
        </LoadingButton>
      </Stack>
    </form>
  );
};

export default BankForm;

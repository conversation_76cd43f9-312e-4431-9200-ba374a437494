import { t } from 'i18n';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  bankId: string()
    .trim()
    .max(50, t('validation.maxLength', { number: 50 }))
    .required(),
  branchId: string()
    .trim()
    .max(50, t('validation.maxLength', { number: 50 }))
    .required(),
  accountType: string().trim().required(),
  accountNumber: string()
    .required()
    .matches(/^\d+$/, t('validation.invalidField'))
    .trim(t('validation.notAllowedWhiteSpace')),
  holderName: string()
    .required()
    .matches(/^[ァ-ヶー0-9A-Za-z().\-/\u0020]*$/, t('validation.invalidField'))
    .trim(t('validation.notAllowedWhiteSpace')),
});

export type BankFormValues = InferType<typeof schema>;
export default schema;

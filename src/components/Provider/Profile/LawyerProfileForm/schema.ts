import dayjs from 'dayjs';
import i18n from 'i18n';
import { Regex } from 'utils/constants';
import type { InferType } from 'yup';
import { array, object, string } from 'yup';

const schema = object({
  lastName: string()
    .required(i18n.t('validation.requiredField'))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
  firstName: string()
    .required(i18n.t('validation.requiredField'))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
  lastKatakanaName: string()
    .required(i18n.t('validation.requiredField'))
    .trim(i18n.t('validation.notAllowedWhiteSpace'))
    .matches(Regex.KATAKANA, i18n.t('validation.invalidField')),
  firstKatakanaName: string()
    .required(i18n.t('validation.requiredField'))
    .trim(i18n.t('validation.notAllowedWhiteSpace'))
    .matches(Regex.KATAKANA, i18n.t('validation.invalidField')),
  gender: string().required(i18n.t('validation.requiredField')),
  hasOnlineSupport: string().required(i18n.t('validation.requiredField')),
  birthday: string()
    .nullable()
    .required(i18n.t('validation.requiredField'))
    .test('validBirthday', i18n.t('validation.invalidField'), (value) => {
      const eighteenYearsAgo = dayjs().subtract(18, 'years');
      if (!eighteenYearsAgo.isAfter(dayjs(value))) {
        return false;
      }
      return true;
    }),
  images: array()
    .min(1, i18n.t('validation.requiredField'))
    .required(i18n.t('validation.requiredField')),
  videoUrl: string().test(
    'validUrl',
    i18n.t('validation.invalidUrl'),
    (value) => {
      if (value) {
        return Regex.URL.test(value);
      }
      return true;
    },
  ),
  introduction: string().max(
    400,
    i18n.t('validation.maxLength', { number: 400 }),
  ),
  catchphrase: string()
    .required(i18n.t('validation.requiredField'))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
});

export type LawyerProfileFormValues = InferType<typeof schema>;

export default schema;

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Grid, Stack } from '@mui/material';
import { DatePicker, Radio, TextField, Upload } from 'components/Form';
import Label from 'components/Form/Label';
import dayjs from 'dayjs';
import { useDeepCompareEffect } from 'hooks';
import type { LawyerData } from 'hooks/useFetchUser/types';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import { Gender, RadioYesNoOptions } from 'utils/constants';
import Helper from 'utils/helpers';

import type { LawyerProfileFormValues } from './schema';
import schema from './schema';

export interface LawyerProfileFormProps {
  onSubmit: (values: LawyerProfileFormValues, isDirty: boolean) => void;
  loading?: boolean;
  onCancel: (isDirty: boolean) => void;
  defaultValues: Partial<LawyerProfileFormValues>;
  data?: LawyerData;
}
const LawyerProfileForm = ({
  onSubmit,
  loading,
  onCancel,
  defaultValues,
  data,
}: LawyerProfileFormProps) => {
  const {
    control,
    handleSubmit,
    formState: { isDirty },
    reset,
  } = useHookForm<LawyerProfileFormValues>({
    mode: 'onTouched',
    resolver: yupResolver(schema),
    defaultValues,
  });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  return (
    <form onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}>
      <Upload
        name="images"
        control={control}
        helperText={t('lawyerProfile.uploadImages', { number: 3 })}
      />
      <Stack spacing={2} mt="19px">
        <Grid container columnSpacing={4} rowSpacing={2} columns={15}>
          <Label
            label={t('updateCustomerProfile.fullName')}
            required
            labelCol={5}
          />
          <Grid item xs={12} tablet={10} mt={0}>
            <Stack spacing={2} direction="row">
              <Box maxWidth="calc((100% - 16px)/2)">
                <TextField
                  labelCol={0}
                  control={control}
                  name="lastName"
                  maxLength={10}
                  placeholder={t('lawyerProfile.lastName')}
                />
              </Box>
              <Box maxWidth="calc((100% - 16px)/2)">
                <TextField
                  labelCol={0}
                  control={control}
                  name="firstName"
                  maxLength={10}
                  placeholder={t('lawyerProfile.firstName')}
                />
              </Box>
            </Stack>
          </Grid>
          <Label
            label={t('updateCustomerProfile.katakanaName')}
            required
            labelCol={5}
          />
          <Grid item xs={12} tablet={10} mt={0}>
            <Stack spacing={2} direction="row">
              <Box maxWidth="calc((100% - 16px)/2)">
                <TextField
                  labelCol={0}
                  control={control}
                  name="lastKatakanaName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.lastKanaName')}
                />
              </Box>
              <Box maxWidth="calc((100% - 16px)/2)">
                <TextField
                  labelCol={0}
                  control={control}
                  name="firstKatakanaName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.firstKanaName')}
                />
              </Box>
            </Stack>
          </Grid>
          <Label
            label={t('updateCustomerProfile.email')}
            labelCol={5}
            required
          />
          <Grid item xs={12} tablet={10} mt="12px">
            {data?.email}
          </Grid>
        </Grid>

        <Radio
          label={t('lawyerProfile.gender')}
          required
          name="gender"
          control={control}
          data={Helper.convertObjectToOptions(Gender)}
          labelCol={5}
          columns={15}
        />
        <DatePicker
          name="birthday"
          required
          control={control}
          columns={15}
          label={t('lawyerProfile.birthday')}
          labelCol={5}
          maxDate={dayjs().subtract(18, 'y')}
          defaultCalendarMonth={dayjs('1980', 'YYYY')}
        />
        <TextField
          labelCol={5}
          columns={15}
          control={control}
          name="videoUrl"
          label={t('lawyerProfile.videoUrl')}
          placeholder={t('placeholder.url')}
        />

        <TextField
          labelCol={5}
          columns={15}
          maxLength={30}
          control={control}
          required
          name="catchphrase"
          label={t('lawyerProfile.catchphrase')}
          placeholder={t('placeholder.input', {
            field: t('lawyerProfile.catchphrase'),
          })}
        />
        <Radio
          label={t('lawyerProfile.supportOnline')}
          required
          name="hasOnlineSupport"
          control={control}
          data={Helper.convertObjectToOptions(RadioYesNoOptions)}
          labelCol={5}
          columns={15}
        />
        <TextField
          labelCol={5}
          columns={15}
          minRows={7}
          multiline
          maxLength={400}
          control={control}
          name="introduction"
          label={t('lawyerProfile.introduction')}
          placeholder={t('placeholder.introduction')}
        />
      </Stack>
      <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
        <Button variant="outlined" onClick={() => onCancel(isDirty)}>
          {t('global.cancel')}
        </Button>
        <LoadingButton
          color="secondary"
          variant="contained"
          fullWidth
          loading={loading}
          type="submit"
          sx={{ maxWidth: 112 }}
        >
          {t('global.settle')}
        </LoadingButton>
      </Stack>
    </form>
  );
};

export default LawyerProfileForm;

import dayjs from 'dayjs';
import { t } from 'i18n';
import { Regex } from 'utils/constants';
import type { InferType } from 'yup';
import { array, number, object, string } from 'yup';

const schema = object({
  lastName: string().required().trim(t('validation.notAllowedWhiteSpace')),
  firstName: string().required().trim(t('validation.notAllowedWhiteSpace')),
  lastKatakanaName: string()
    .required()
    .trim(t('validation.notAllowedWhiteSpace'))
    .matches(Regex.KATAKANA, t('validation.invalidField')),
  firstKatakanaName: string()
    .required()
    .trim(t('validation.notAllowedWhiteSpace'))
    .matches(Regex.KATAKANA, t('validation.invalidField')),
  nickname: string().max(10, t('validation.maxLength', { number: 10 })),
  gender: string().required(),
  birthday: string()
    .nullable()
    .required()
    .test('validBirthday', t('validation.invalidField'), (value) => {
      const eighteenYearsAgo = dayjs().subtract(18, 'years');
      if (!eighteenYearsAgo.isAfter(dayjs(value))) {
        return false;
      }
      return true;
    }),
  images: array().min(1, t('validation.requiredField')).required(),
  videoUrl: string().test('validUrl', t('validation.invalidUrl'), (value) => {
    if (value) {
      return Regex.URL.test(value);
    }
    return true;
  }),
  introduction: string().max(400, t('validation.maxLength', { number: 400 })),
  catchphrase: string().required().trim(t('validation.notAllowedWhiteSpace')),
  extendIntroduction: object({
    hobbies: string().max(400, t('validation.maxLength', { number: 100 })),
    solutionCases: string().max(
      400,
      t('validation.maxLength', { number: 400 }),
    ),
    favoriteSubject: string().max(
      400,
      t('validation.maxLength', { number: 400 }),
    ),
    importantInCounseling: string().max(
      400,
      t('validation.maxLength', { number: 400 }),
    ),
  }),
  meetingStyle: object({
    speakingRate: number().required(),
    communicationRate: number().required(),
    adviceRate: number().required(),
  }),
});

export type CounselorProfileFormValues = InferType<typeof schema>;

export default schema;

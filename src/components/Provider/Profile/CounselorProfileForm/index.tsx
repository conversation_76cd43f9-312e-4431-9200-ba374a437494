import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Grid, Stack } from '@mui/material';
import { DatePicker, Radio, TextField, Upload } from 'components/Form';
import Label from 'components/Form/Label';
import LevelRating from 'components/Form/LevelRating';
import dayjs from 'dayjs';
import { useDeepCompareEffect } from 'hooks';
import type { CounselorData } from 'hooks/useFetchUser/types';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import type { DeepPartial } from 'react-hook-form';
import { Gender } from 'utils/constants';
import Helper from 'utils/helpers';

import type { CounselorProfileFormValues } from './schema';
import schema from './schema';

export interface CounselorProfileFormProps {
  onSubmit: (values: CounselorProfileFormValues, isDirty: boolean) => void;
  loading?: boolean;
  onCancel: (isDirty: boolean) => void;
  defaultValues: DeepPartial<CounselorProfileFormValues>;
  data?: CounselorData;
}
const CounselorProfileForm = ({
  onSubmit,
  loading,
  onCancel,
  defaultValues,
  data,
}: CounselorProfileFormProps) => {
  const {
    control,
    handleSubmit,
    formState: { isDirty },
    reset,
  } = useHookForm<CounselorProfileFormValues>({
    mode: 'onTouched',
    resolver: yupResolver(schema),
    defaultValues,
  });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  return (
    <form onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}>
      <Upload
        name="images"
        control={control}
        helperText={t('lawyerProfile.uploadImages', { number: 3 })}
      />
      <Stack spacing={2} mt="19px">
        <Grid container columnSpacing={4} rowSpacing={2} columns={15}>
          <Label
            label={t('updateCustomerProfile.fullName')}
            required
            labelCol={5}
          />
          <Grid item xs={12} tablet={10} mt={0}>
            <Stack spacing={2} direction="row">
              <Box maxWidth="calc((100% - 16px)/2)">
                <TextField
                  labelCol={0}
                  control={control}
                  name="lastName"
                  maxLength={10}
                  placeholder={t('lawyerProfile.lastName')}
                />
              </Box>
              <Box maxWidth="calc((100% - 16px)/2)">
                <TextField
                  labelCol={0}
                  control={control}
                  name="firstName"
                  maxLength={10}
                  placeholder={t('lawyerProfile.firstName')}
                />
              </Box>
            </Stack>
          </Grid>
          <Label
            label={t('updateCustomerProfile.katakanaName')}
            required
            labelCol={5}
          />
          <Grid item xs={12} tablet={10} mt={0}>
            <Stack spacing={2} direction="row">
              <Box maxWidth="calc((100% - 16px)/2)">
                <TextField
                  labelCol={0}
                  control={control}
                  name="lastKatakanaName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.lastKanaName')}
                />
              </Box>
              <Box maxWidth="calc((100% - 16px)/2)">
                <TextField
                  labelCol={0}
                  control={control}
                  name="firstKatakanaName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.firstKanaName')}
                />
              </Box>
            </Stack>
          </Grid>
        </Grid>
        <TextField
          labelCol={5}
          columns={15}
          control={control}
          name="nickname"
          label="ニックネーム"
          placeholder="ニックネーム"
          maxLength={10}
        />
        <Box>
          <Grid container columnSpacing={4} rowSpacing={2} columns={15}>
            <Label
              label={t('updateCustomerProfile.email')}
              labelCol={5}
              required
            />
            <Grid item xs={12} tablet={10} mt="12px">
              {data?.email}
            </Grid>
          </Grid>
        </Box>
        <Radio
          label={t('lawyerProfile.gender')}
          required
          name="gender"
          control={control}
          data={Helper.convertObjectToOptions(Gender)}
          labelCol={5}
          columns={15}
        />
        <DatePicker
          name="birthday"
          required
          control={control}
          columns={15}
          label={t('lawyerProfile.birthday')}
          labelCol={5}
          maxDate={dayjs().subtract(18, 'y')}
          defaultCalendarMonth={dayjs('1980', 'YYYY')}
        />
        <TextField
          labelCol={5}
          columns={15}
          control={control}
          name="videoUrl"
          label={t('lawyerProfile.videoUrl')}
          placeholder={t('placeholder.url')}
        />
        <Box>
          <Grid container columnSpacing={4} rowSpacing={2} columns={15}>
            <Label paddingTop={3} label="タイプ表" labelCol={5} required />
            <Grid item xs={12} tablet={10} mt="12px">
              <Stack
                p={3}
                gap={2}
                borderRadius="12px"
                border="1px solid rgb(237, 241, 243);"
              >
                <LevelRating
                  name="meetingStyle.speakingRate"
                  control={control}
                  label="話し方"
                  minText="穏やか"
                  maxText="テンポが速い"
                />
                <LevelRating
                  name="meetingStyle.communicationRate"
                  control={control}
                  label="言葉づかい"
                  minText="丁寧"
                  maxText="フレンドリー"
                />
                <LevelRating
                  name="meetingStyle.adviceRate"
                  control={control}
                  label="アドバイス"
                  minText="ソフト"
                  maxText="ストレート"
                />
              </Stack>
            </Grid>
          </Grid>
        </Box>
        <TextField
          labelCol={5}
          columns={15}
          maxLength={30}
          control={control}
          required
          name="catchphrase"
          label={t('lawyerProfile.catchphrase')}
          placeholder={t('placeholder.input', {
            field: t('lawyerProfile.catchphrase'),
          })}
        />
        <TextField
          labelCol={5}
          columns={15}
          minRows={7}
          multiline
          maxLength={400}
          control={control}
          name="introduction"
          label={t('lawyerProfile.introduction')}
          placeholder={t('placeholder.introduction')}
        />
        <TextField
          labelCol={5}
          columns={15}
          minRows={7}
          multiline
          maxLength={400}
          control={control}
          name="extendIntroduction.favoriteSubject"
          label="得意な内容・項目"
          placeholder="得意な内容・項目を記入してください"
        />
        <TextField
          labelCol={5}
          columns={15}
          minRows={7}
          multiline
          maxLength={400}
          control={control}
          name="extendIntroduction.importantInCounseling"
          label="大切にしていること"
          placeholder="大切にしていることを記入してください"
        />
        <TextField
          labelCol={5}
          columns={15}
          minRows={7}
          multiline
          maxLength={400}
          control={control}
          name="extendIntroduction.solutionCases"
          label="過去の実例"
          placeholder="過去の実例を記入してください"
        />
        <TextField
          labelCol={5}
          columns={15}
          minRows={7}
          multiline
          maxLength={100}
          control={control}
          name="extendIntroduction.hobbies"
          label="趣味"
          placeholder="趣味を記入してください"
        />
      </Stack>
      <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
        <Button variant="outlined" onClick={() => onCancel(isDirty)}>
          {t('global.cancel')}
        </Button>
        <LoadingButton
          color="secondary"
          variant="contained"
          fullWidth
          loading={loading}
          type="submit"
          sx={{ maxWidth: 112 }}
        >
          {t('global.settle')}
        </LoadingButton>
      </Stack>
    </form>
  );
};

export default CounselorProfileForm;

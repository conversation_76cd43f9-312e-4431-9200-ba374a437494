import {
  <PERSON>,
  Button,
  Chip,
  Grid,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material';
import BaseLevelRating from 'components/Form/LevelRating/BaseLevelRating';
import VideoThumbnail from 'components/Provider/VideoThumbnail';
import AvatarCarousel from 'components/UI/AvatarCarousel';
import IconText from 'components/UI/IconText';
import dayjs from 'dayjs';
import type { CounselorData, LawyerData } from 'hooks/useFetchUser/types';
import { t } from 'i18n';
import { CakeIcon, EditIcon, GenderIcon, MailIcon, QuoteIcon } from 'icons';
import { Gender, MomentFormat, ROLES } from 'utils/constants';

const ProfileDetail = ({
  data,
  onEdit,
}: {
  data?: LawyerData | CounselorData;
  onEdit: () => void;
}) => {
  const isCounselor = data?.type === ROLES.COUNSELOR;
  return (
    <Box className="lawyer-profile-detail">
      <Box display="flex" justifyContent="space-between">
        <Stack direction="row" justifyContent="space-between" spacing={6}>
          <AvatarCarousel images={data?.images || []} />
          <Box>
            {/* Lawyer setting Support Online */}
            {!isCounselor && data?.hasOnlineSupport && (
              <Box
                sx={{
                  marginBottom: { xs: '4px', tablet: '5px' },
                  gap: '4px',
                  width: 'auto',
                }}
              >
                <Chip
                  key={'support online'}
                  variant="orangeChip"
                  label="オンライン対応可"
                  component={'div'}
                />
              </Box>
            )}
            <Typography fontSize={32} color="heading" fontWeight="bold">
              {data?.fullName || <Skeleton width={200} />}
            </Typography>
            <Typography fontWeight={500} mt="2px">
              {data?.katakanaName || <Skeleton width={200} />}
            </Typography>
            {isCounselor && data?.nickname && (
              <Typography fontWeight={500}>{data?.nickname}</Typography>
            )}
            <Stack
              spacing="6px"
              mt={2}
              sx={{
                '.icon-text': {
                  svg: {
                    color: 'primary.main',
                  },
                },
              }}
            >
              <IconText icon={MailIcon}>
                {data?.email || <Skeleton width={200} />}
              </IconText>
              <IconText icon={CakeIcon}>
                {data?.birthday
                  ? dayjs(data?.birthday).format(
                      MomentFormat.JP_YEAR_MONTH_DATE,
                    )
                  : '-'}
              </IconText>
              <IconText icon={GenderIcon}>
                {data?.gender ? Gender[data?.gender] : '-'}
              </IconText>
            </Stack>
          </Box>
        </Stack>
        <Box>
          <Button
            color="secondary"
            variant="contained"
            onClick={onEdit}
            startIcon={<EditIcon />}
          >
            {t('global.edit')}
          </Button>
        </Box>
      </Box>
      {isCounselor && data?.meetingStyle && (
        <Box mt={3}>
          <Grid container spacing="30px" direction="row">
            <Grid item xs={4}>
              <BaseLevelRating
                label="話し方"
                value={data?.meetingStyle?.speakingRate}
                minText="穏やか"
                maxText="テンポが速い"
              />
            </Grid>
            <Grid item xs={4}>
              <BaseLevelRating
                label="言葉づかい"
                value={data?.meetingStyle?.communicationRate}
                minText="丁寧"
                maxText="フレンドリー"
              />
            </Grid>
            <Grid item xs={4}>
              <BaseLevelRating
                label="アドバイス"
                value={data?.meetingStyle?.adviceRate}
                minText="ソフト"
                maxText="ストレート"
              />
            </Grid>
          </Grid>
        </Box>
      )}
      {data?.catchphrase && (
        <Stack direction="row" spacing={1} mt={isCounselor ? 4 : 3}>
          <QuoteIcon />
          <Typography fontSize={24} fontWeight="bold" color="primary" flex={1}>
            {data.catchphrase}
          </Typography>
        </Stack>
      )}
      {data?.introduction && (
        <Typography mt="20px">{data?.introduction}</Typography>
      )}
      {data?.videoUrl?.value && (
        <Box mt={3}>
          <VideoThumbnail videoUrl={data.videoUrl.value} />
        </Box>
      )}
      {isCounselor && data?.extendIntroduction?.favoriteSubject && (
        <>
          <Typography mt={2} fontWeight="bold">
            得意な内容・項目
          </Typography>
          <Typography mt="4px">
            {data.extendIntroduction.favoriteSubject}
          </Typography>
        </>
      )}
      {isCounselor && data?.extendIntroduction?.importantInCounseling && (
        <>
          <Typography mt={2} fontWeight="bold">
            大切にしていること
          </Typography>
          <Typography mt="4px">
            {data.extendIntroduction.importantInCounseling}
          </Typography>
        </>
      )}
      {isCounselor && data?.extendIntroduction?.solutionCases && (
        <>
          <Typography mt={2} fontWeight="bold">
            過去の実例
          </Typography>
          <Typography mt="4px">
            {data.extendIntroduction.solutionCases}
          </Typography>
        </>
      )}
      {isCounselor && data?.extendIntroduction?.hobbies && (
        <>
          <Typography mt={2} fontWeight="bold">
            趣味
          </Typography>
          <Typography mt="4px">{data.extendIntroduction.hobbies}</Typography>
        </>
      )}
    </Box>
  );
};

export default ProfileDetail;

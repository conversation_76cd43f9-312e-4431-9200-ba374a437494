import { Box, Typography } from '@mui/material';
import VisibleMotion from 'components/Animation/VisibleMotion';
import DivorceInfoForm from 'components/Lawyer/DivorceInfoForm';
import type { DivorceFormValues } from 'components/Lawyer/DivorceInfoForm/schema';
import OfficeInfoForm from 'components/Lawyer/OfficeInfoForm';
import ProfileDetail from 'components/Provider/Profile/BasicProfileView';
import LawyerProfileForm from 'components/Provider/Profile/LawyerProfileForm';
import ViewEditBlock from 'components/UI/ViewEditBlock';
import { useFetchUser } from 'hooks';
import type { DivorcePayload } from 'hooks/lawyer/useProfile/types';
import type { CounselorData, LawyerData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18next';
import { get, pick } from 'lodash';
import type {
  BankInformation,
  CounselorJobPayload,
  JobPayload,
} from 'models/provider/interface';
import providerQuery from 'models/provider/query';
import { useCallback, useMemo, useState } from 'react';
import { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

import BankForm from './BankForm';
import { bankBlock, careerBlock, divorceBlock, profileBlock } from './blocks';
import CounselorJobForm from './CounselorJobForm';
import CounselorProfileForm from './CounselorProfileForm';
import type { CounselorProfileFormValues } from './CounselorProfileForm/schema';
import type { LawyerProfileFormValues } from './LawyerProfileForm/schema';

const BasicInfoView = () => {
  const { data: currentUser, refetch } = useFetchUser<
    LawyerData | CounselorData
  >({
    enabled: true,
  });
  const attribute = get(currentUser, 'attribute', []);
  const consultationField = get(currentUser, 'consultationField', []);

  const [editForm, setEditForm] = useState<string[]>([]);

  const { mutateAsync: updateJob, isLoading: isUpdatingJob } = useMutate<
    CounselorJobPayload | JobPayload
  >(providerQuery.updateJob);
  const { mutateAsync: updateDivorce, isLoading: isUpdatingDivorce } =
    useMutate(providerQuery.updateDivorce);

  const { mutateAsync: updateProfile, isLoading: isUpdatingProfile } =
    useMutate<LawyerProfileFormValues | CounselorProfileFormValues>(
      providerQuery.updateProfile,
    );

  const { mutateAsync: updateBank, isLoading: isUpdatingBank } = useMutate(
    providerQuery.updateBank,
  );

  const handleEditForm = useCallback((form: string) => {
    setEditForm((forms) => forms.concat(form));
  }, []);

  const { setConfirmModal } = useGlobalState();

  const closeForm = useCallback((formName: string) => {
    setEditForm((forms) => forms.filter((form) => form !== formName));
  }, []);

  const handleCancelEdit = useCallback(
    (isDirty: boolean, formName: string) => {
      if (isDirty) {
        setConfirmModal({
          onConfirm: () => closeForm(formName),
          title: 'discardInput.title',
          content: 'discardInput.message',
        });
      } else {
        closeForm(formName);
      }
    },
    [closeForm, setConfirmModal],
  );

  const handleUpdateProfile = <T extends ROLES>(
    values: T extends ROLES.LAWYER
      ? LawyerProfileFormValues
      : CounselorProfileFormValues,
    isDirty: boolean,
  ) => {
    if (isDirty) {
      const params = {
        ...values,
        images: values.images.map((image) => image.key),
      };
      updateProfile(params, {
        onSuccess: () => {
          refetch();
          closeForm('profile-form');
        },
      });
    } else {
      closeForm('profile-form');
    }
  };

  const handleUpdateJob = <T extends ROLES>(
    values: T extends ROLES.COUNSELOR ? CounselorJobPayload : JobPayload,
    isDirty: boolean,
  ) => {
    if (isDirty) {
      updateJob(values, {
        onSuccess: () => {
          refetch();
          closeForm('job-form');
        },
      });
    } else {
      closeForm('job-form');
    }
  };

  const handleUpdateDivorce = (values: DivorcePayload, isDirty: boolean) => {
    if (isDirty) {
      updateDivorce(values, {
        onSuccess: () => {
          refetch();
          closeForm('divorce-form');
        },
      });
    } else {
      closeForm('divorce-form');
    }
  };

  const handleUpdateBank = <T extends ROLES>(
    values: T extends ROLES.COUNSELOR ? BankInformation : {},
    isDirty: boolean,
  ) => {
    if (isDirty) {
      updateBank(values, {
        onSuccess: () => {
          refetch();
          closeForm('bank-form');
        },
      });
    } else {
      closeForm('bank-form');
    }
  };

  const profileDefaultValues = useMemo(
    () => ({
      ...pick(currentUser, [
        'images',
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'birthday',
        'catchphrase',
        'introduction',
      ]),
      videoUrl: currentUser?.videoUrl?.value,
    }),
    [currentUser],
  );

  const counselorProfileDefaultValues = useMemo(
    () => ({
      ...pick(currentUser, [
        'images',
        'firstName',
        'lastName',
        'firstKatakanaName',
        'lastKatakanaName',
        'gender',
        'birthday',
        'catchphrase',
        'introduction',
        'extendIntroduction',
        'nickname',
        'meetingStyle',
      ]),
      videoUrl: currentUser?.videoUrl?.value,
    }),
    [currentUser],
  );

  const counselorJobDefaultValues = useMemo(
    () => ({
      ...pick(currentUser, [
        'career',
        'numberSolutionCases',
        'consultationField',
      ]),
      consultationField: (currentUser?.consultationField || []).map(
        (field) => field._id,
      ),
      certificate:
        currentUser?.type === ROLES.COUNSELOR
          ? (currentUser?.certificate || []).map((cer) => ({ value: cer }))
          : [],
    }),
    [currentUser],
  );

  const divorceDefaultValues = useMemo(
    () => pick(currentUser, ['children', 'marriageHistory', 'divorceHistory']),
    [currentUser],
  ) as DivorceFormValues;

  const jobDefaultValues = useMemo(
    () => ({
      ...pick(currentUser, ['barAssociation']),
      consultationField: consultationField.map((field) => field._id),
      attribute: attribute.map((field) => field._id),
    }),
    [attribute, consultationField, currentUser],
  ) as JobPayload;

  const counselorBankDefaultValues = useMemo(() => {
    return currentUser && 'bankInfo' in currentUser && currentUser.bankInfo
      ? {
          ...pick(currentUser.bankInfo, [
            'bankId',
            'branchId',
            'accountNumber',
            'holderName',
            'accountType',
          ]),
          accountType:
            currentUser.bankInfo.accountType?.key.toLocaleUpperCase() ||
            undefined,
        }
      : {};
  }, [currentUser]);

  return (
    <Box>
      <Box maxWidth="md" p={4} bgcolor="white" borderRadius={2} mt={2}>
        {!editForm.includes('profile-form') ? (
          <VisibleMotion key="profile-detail">
            <ProfileDetail
              data={currentUser}
              onEdit={() => handleEditForm('profile-form')}
            />
          </VisibleMotion>
        ) : (
          <VisibleMotion key="profile-form">
            {currentUser?.type === ROLES.LAWYER ? (
              <LawyerProfileForm
                data={currentUser}
                onSubmit={(values, isDirty) =>
                  handleUpdateProfile<ROLES.LAWYER>(values, isDirty)
                }
                loading={isUpdatingProfile}
                defaultValues={profileDefaultValues}
                onCancel={(isDirty) =>
                  handleCancelEdit(isDirty, 'profile-form')
                }
              />
            ) : (
              <CounselorProfileForm
                data={currentUser}
                onSubmit={(values, isDirty) =>
                  handleUpdateProfile<ROLES.COUNSELOR>(values, isDirty)
                }
                loading={isUpdatingProfile}
                defaultValues={counselorProfileDefaultValues}
                onCancel={(isDirty) =>
                  handleCancelEdit(isDirty, 'profile-form')
                }
              />
            )}
          </VisibleMotion>
        )}
      </Box>
      {currentUser?.type === ROLES.LAWYER && (
        <Box>
          <ViewEditBlock
            editMode={editForm.includes('job-form')}
            onEdit={() => handleEditForm('job-form')}
            detail={currentUser}
            block={profileBlock}
            formId="job-form"
          >
            <OfficeInfoForm
              loading={isUpdatingJob}
              defaultValues={jobDefaultValues}
              onSubmit={handleUpdateJob}
              detail={{ fullName: currentUser?.office.fullName || '' }}
              onCancel={(isDirty) => handleCancelEdit(isDirty, 'job-form')}
            />
          </ViewEditBlock>
          <ViewEditBlock
            editMode={editForm.includes('divorce-form')}
            onEdit={() => handleEditForm('divorce-form')}
            detail={currentUser}
            block={divorceBlock}
            formId="divorce-form"
            detailRender={
              Helper.checkIsEmptyObject(
                pick(currentUser, [
                  'children',
                  'divorceHistory',
                  'marriageHistory',
                ]),
              ) ? (
                <Typography color="hint">{t('global.hasNoInfo')}</Typography>
              ) : null
            }
          >
            <DivorceInfoForm
              loading={isUpdatingDivorce}
              defaultValues={divorceDefaultValues}
              onSubmit={handleUpdateDivorce}
              onCancel={(isDirty) => handleCancelEdit(isDirty, 'divorce-form')}
            />
          </ViewEditBlock>
        </Box>
      )}
      {currentUser?.type === ROLES.COUNSELOR && (
        <ViewEditBlock
          editMode={editForm.includes('job-form')}
          onEdit={() => handleEditForm('job-form')}
          detail={currentUser}
          block={careerBlock}
          formId="job-form"
        >
          <CounselorJobForm
            loading={isUpdatingJob}
            defaultValues={counselorJobDefaultValues}
            onSubmit={(values, isDirty) =>
              handleUpdateJob(
                {
                  ...values,
                  consultationField: (values.consultationField || []).map(
                    (field) => field || '',
                  ),
                  certificate: (values.certificate || []).map(
                    (cer) => cer.value || '',
                  ),
                },
                isDirty,
              )
            }
            onCancel={(isDirty) => handleCancelEdit(isDirty, 'job-form')}
          />
        </ViewEditBlock>
      )}
      {currentUser?.type === ROLES.COUNSELOR && (
        <ViewEditBlock
          editMode={editForm.includes('bank-form')}
          onEdit={() => handleEditForm('bank-form')}
          detail={currentUser.bankInfo}
          block={bankBlock}
          formId="bank-form"
        >
          <BankForm
            loading={isUpdatingBank}
            defaultValues={counselorBankDefaultValues}
            onSubmit={(values, isDirty) => handleUpdateBank(values, isDirty)}
            onCancel={(isDirty) => handleCancelEdit(isDirty, 'bank-form')}
          />
        </ViewEditBlock>
      )}
    </Box>
  );
};

export default BasicInfoView;

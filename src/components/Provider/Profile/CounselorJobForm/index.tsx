import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Grid,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import { CheckBox, DatePicker, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import NumberField from 'components/Form/NumberField';
import dayjs from 'dayjs';
import { useDeepCompareEffect, useFetchList } from 'hooks';
import type { IListItem } from 'hooks/types';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import { AddIcon, TrashIcon } from 'icons';
import resourceQuery from 'models/resource/query';
import type { DeepPartial } from 'react-hook-form';
import { useFieldArray } from 'react-hook-form';

import type { CounselorJobFormValues } from './schema';
import schema from './schema';

export interface CounselorJobFormProps {
  onSubmit: (values: CounselorJobFormValues, isDirty: boolean) => void;
  loading?: boolean;
  onCancel: (isDirty: boolean) => void;
  defaultValues: DeepPartial<CounselorJobFormValues>;
}
const CounselorJobForm = ({
  onSubmit,
  loading,
  onCancel,
  defaultValues,
}: CounselorJobFormProps) => {
  const {
    control,
    handleSubmit,
    formState: { isDirty },
    reset,
    clearErrors,
  } = useHookForm<CounselorJobFormValues>({
    mode: 'onTouched',
    resolver: yupResolver(schema),
    defaultValues,
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'certificate',
  });
  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  const { list: consultationList } = useFetchList<IListItem>({
    ...resourceQuery.consultations,
    customParams: {
      ...resourceQuery.consultations.customParams,
      providerType: 'COUNSELOR',
    },
  });

  return (
    <form onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}>
      <Stack spacing={2} mt="19px">
        <DatePicker
          labelCol={5}
          columns={15}
          control={control}
          name="career"
          label="カウンセラー歴"
          required
          maxDate={dayjs()}
        />
        <NumberField
          labelCol={5}
          columns={15}
          max={999999999}
          control={control}
          name="numberSolutionCases"
          label="カウンセリング総件数"
          placeholder="これまでのカウンセリング件数を入力"
          leftAdorment={<Typography mr={1}>約</Typography>}
          adornment={<Typography ml={1}>件</Typography>}
        />

        <Box>
          <Grid container columnSpacing={4} rowSpacing={2} columns={15}>
            <Label label="資格" labelCol={5} />
            <Grid item xs={12} tablet={10} mt={0}>
              <Stack
                p={3}
                gap={1}
                borderRadius="12px"
                border="1px solid rgb(237, 241, 243)"
              >
                {fields.map((item, index) => (
                  <Box
                    display="flex"
                    sx={{ '.text-field-container': { flex: 1 } }}
                    key={item.id}
                  >
                    <TextField
                      labelCol={0}
                      maxLength={30}
                      control={control}
                      required
                      name={`certificate.${index}.value`}
                      placeholder={t('placeholder.input', {
                        field: '資格',
                      })}
                    />
                    <Box mt="4px">
                      <IconButton
                        onClick={() => {
                          remove(index);
                          clearErrors(`certificate.${index}.value`);
                        }}
                        className="whiteOutlined"
                        sx={{ ml: 1, svg: { color: 'neutral7' } }}
                      >
                        <TrashIcon />
                      </IconButton>
                    </Box>
                  </Box>
                ))}
                <Button
                  size="small"
                  onClick={() => append({ value: '' })}
                  sx={{ m: '-4px -5px', width: 'fit-content' }}
                  className="tabletStyle"
                  startIcon={<AddIcon />}
                  color="primary"
                  disabled={fields.length === 5}
                >
                  追加
                </Button>
              </Stack>
            </Grid>
          </Grid>
        </Box>
        <CheckBox
          labelCol={5}
          columns={15}
          control={control}
          data={consultationList}
          name="consultationField"
          label="得意分野"
          showSelectAll
          optionContainerProps={{
            tablet: 4,
          }}
        />
      </Stack>
      <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
        <Button variant="outlined" onClick={() => onCancel(isDirty)}>
          {t('global.cancel')}
        </Button>
        <LoadingButton
          color="secondary"
          variant="contained"
          fullWidth
          loading={loading}
          type="submit"
          sx={{ maxWidth: 112 }}
        >
          {t('global.settle')}
        </LoadingButton>
      </Stack>
    </form>
  );
};

export default CounselorJobForm;

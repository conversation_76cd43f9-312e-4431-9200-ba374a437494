import { isNaN } from 'lodash';
import type { InferType } from 'yup';
import { array, number, object, string } from 'yup';

const schema = object({
  certificate: array().of(
    object({
      value: string().required(),
    }),
  ),
  career: string().nullable().required(),
  numberSolutionCases: number().transform((value) =>
    isNaN(value) ? undefined : value,
  ),
  consultationField: array().of(string()),
});

export type CounselorJobFormValues = InferType<typeof schema>;

export default schema;

import dayjs from 'dayjs';
import { t } from 'i18n';
import type { InferType } from 'yup';
import { boolean, object, string } from 'yup';

const daySchema = object({
  active: boolean().default(false).required(),
  startTime: string()
    .nullable()
    .test('validDay', t('validation.invalidField'), (value, ctx) => {
      if (value) {
        const minutePart = value.split(':')[1];
        if (minutePart && !['00', '30'].includes(minutePart)) {
          return false;
        }
      }
      if (
        ctx.parent.endTime &&
        value &&
        dayjs(ctx.parent.endTime, 'HH:mm').diff(
          dayjs(value, 'HH:mm'),
          'minutes',
        ) <= 0
      ) {
        return false;
      }
      return true;
    })
    .when('active', {
      is: true,
      then: string().nullable().required(),
      otherwise: string().nullable(),
    }),
  endTime: string()
    .nullable()
    .required()
    .test('validDay', t('validation.invalidField'), (value, ctx) => {
      if (value) {
        const minutePart = value.split(':')[1];
        if (minutePart && !['00', '30'].includes(minutePart)) {
          return false;
        }
      }
      if (
        ctx.parent.startTime &&
        value &&
        dayjs(value, 'HH:mm').diff(
          dayjs(ctx.parent.startTime, 'HH:mm'),
          'minutes',
        ) <= 0
      ) {
        return false;
      }
      return true;
    })
    .when('active', {
      is: true,
      then: string().nullable().required(),
      otherwise: string().nullable(),
    }),
}).required();

const schema = object({
  sunday: daySchema,
  monday: daySchema,
  tuesday: daySchema,
  wednesday: daySchema,
  thursday: daySchema,
  friday: daySchema,
  saturday: daySchema,
});

export type WorkDaysType = InferType<typeof schema>;
export default schema;

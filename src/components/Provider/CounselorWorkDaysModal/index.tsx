import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Stack, Typography } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import ConfirmModal from 'components/ConfirmModal/modal';
import { Select } from 'components/Form';
import SwitchField from 'components/Form/SwitchField';
import { useFetchUser } from 'hooks';
import type { CounselorData } from 'hooks/useFetchUser/types';
import useMutate from 'hooks/useMutate';
import type { UpdateWorkDaysPayload } from 'models/shift/interface';
import shiftQuery from 'models/shift/query';
import { useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { WORK_TIMES } from 'utils/constants';

import type { WorkDaysType } from './schema';
import schema from './schema';

const TIME_OPTIONS = WORK_TIMES.map((time) => ({ _id: time, value: time }));
export type WorkDaysModalProps = {
  open: boolean;
  setOpen: () => void;
};

const workDays = [
  { id: 'monday', label: '月曜日' },
  { id: 'tuesday', label: '火曜日' },
  { id: 'wednesday', label: '水曜日' },
  { id: 'thursday', label: '木曜日' },
  { id: 'friday', label: '金曜日' },
  { id: 'saturday', label: '土曜日' },
  { id: 'sunday', label: '日曜日' },
] as const;

const WorkDaysModal = ({ open, setOpen }: WorkDaysModalProps) => {
  const queryClient = useQueryClient();
  const { data: currentUser, refetch: refetchUser } =
    useFetchUser<CounselorData>({ enabled: false });

  const { mutateAsync: updateWorkDays, isLoading: isUpdating } =
    useMutate<UpdateWorkDaysPayload>(shiftQuery.updateWorkDays);

  const {
    control,
    handleSubmit,
    reset,
    trigger,
    formState: { isDirty },
    setValue,
  } = useForm<WorkDaysType>({
    resolver: yupResolver(schema),
    mode: 'onChange',
    defaultValues: workDays.reduce(
      (prev, cur) =>
        Object.assign(prev, {
          [cur.id]: {
            active: true,
            startTime: '08:00',
            endTime: '19:00',
          },
        }),
      {},
    ),
  });

  useEffect(() => {
    if (currentUser?.weeklySchedule) {
      reset(currentUser?.weeklySchedule);
    }
  }, [currentUser?.weeklySchedule, reset]);

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  const watchDayActives = useWatch({
    control,
    name: workDays.map((day) => `${day.id}.active` as const),
  });

  const handleUpdateWorkDays = (values: WorkDaysType) => {
    if (isDirty) {
      updateWorkDays(
        { weeklySchedule: values },
        {
          onSuccess: () => {
            refetchUser();
            setOpen();
            setTimeout(() => {
              queryClient.refetchQueries({
                queryKey: ['currentUser', 'calendars'],
              });
            }, 1200);
          },
        },
      );
    } else {
      setOpen();
    }
  };

  return (
    <ConfirmModal
      open={open}
      onCancel={setOpen}
      title="営業時間設定"
      confirmText="確定"
      dialogProps={{
        sx: {
          zIndex: 1502,
        },
      }}
      confirmButtonProps={{
        form: 'work-days-form',
        type: 'submit',
        loading: isUpdating,
      }}
    >
      <Box>
        <Typography fontSize={14} textAlign="center">
          営業日時を設定してください
        </Typography>
        <Stack
          gap={1}
          mt={3}
          component="form"
          id="work-days-form"
          onSubmit={handleSubmit(handleUpdateWorkDays)}
        >
          {workDays.map((day, index) => (
            <Box display="flex" key={day.id}>
              <Typography
                fontWeight={700}
                mr={2}
                flex={1}
                minWidth={48}
                mt="12px"
              >
                {day.label}
              </Typography>
              <Select
                control={control}
                name={`${day.id}.startTime`}
                labelCol={0}
                hideError
                disabled={!watchDayActives[index]}
                handleChange={() => trigger(`${day.id}.endTime`)}
                allowClear={false}
                data={TIME_OPTIONS}
                containerProps={{ width: 1 }}
                MenuProps={{
                  sx: { zIndex: 1503 },
                }}
                IconComponent={(iconProps) => {
                  return (
                    <img
                      {...iconProps}
                      alt="arrow-down"
                      src="/icons/clock.svg"
                      style={{ transform: 'unset' }}
                    />
                  );
                }}
              />
              <Typography mx={1} minWidth={16} mt="12px">
                to
              </Typography>
              <Select
                control={control}
                name={`${day.id}.endTime`}
                labelCol={0}
                disabled={!watchDayActives[index]}
                handleChange={() => trigger(`${day.id}.startTime`)}
                hideError
                allowClear={false}
                data={TIME_OPTIONS}
                containerProps={{ width: 1 }}
                IconComponent={(iconProps) => {
                  return (
                    <img
                      {...iconProps}
                      alt="arrow-down"
                      src="/icons/clock.svg"
                      style={{ transform: 'unset' }}
                    />
                  );
                }}
                MenuProps={{
                  sx: { zIndex: 1503 },
                }}
              />
              <Box mt="12px" ml={2}>
                <SwitchField
                  name={`${day.id}.active`}
                  control={control}
                  onChange={async (_, checked) => {
                    const fieldError = await trigger([
                      `${day.id}.startTime`,
                      `${day.id}.endTime`,
                    ]);
                    if (fieldError) {
                      setValue(`${day.id}.active`, checked, {
                        shouldDirty: true,
                      });
                    }
                  }}
                />
              </Box>
            </Box>
          ))}
        </Stack>
      </Box>
    </ConfirmModal>
  );
};

export default WorkDaysModal;

import { Stack } from '@mui/material';
import { Radio } from 'components/Form';
import i18n from 'i18n';
import type { Control } from 'react-hook-form';
import { RadioOptions } from 'utils/constants';
import Helper from 'utils/helpers';

import type { DivorceFormValues } from './schema';

export interface FieldsProps {
  control: Control<DivorceFormValues>;
}
const Fields = ({ control }: FieldsProps) => {
  const { t } = i18n;
  return (
    <Stack spacing="28px">
      <Radio
        name="marriageHistory"
        label={t('lawyerProfile.marriageHistory')}
        labelCol={5}
        columns={15}
        control={control}
        data={Helper.convertObjectToOptions(RadioOptions)}
      />
      <Radio
        name="divorceHistory"
        label={t('lawyerProfile.divorceHistory')}
        labelCol={5}
        columns={15}
        control={control}
        data={Helper.convertObjectToOptions(RadioOptions)}
      />
      <Radio
        name="children"
        label={t('lawyerProfile.children')}
        labelCol={5}
        columns={15}
        control={control}
        data={Helper.convertObjectToOptions(RadioOptions)}
      />
    </Stack>
  );
};

export default Fields;

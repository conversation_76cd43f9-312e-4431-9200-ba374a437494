import { RadioOptionsType } from 'utils/constants';
import type { InferType } from 'yup';
import { mixed, object } from 'yup';

const schema = object({
  children: mixed<RadioOptionsType>().oneOf(Object.values(RadioOptionsType)),
  divorceHistory: mixed<RadioOptionsType>().oneOf(
    Object.values(RadioOptionsType),
  ),
  marriageHistory: mixed<RadioOptionsType>().oneOf(
    Object.values(RadioOptionsType),
  ),
});

export type DivorceFormValues = InferType<typeof schema>;
export default schema;

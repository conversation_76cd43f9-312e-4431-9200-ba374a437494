import { LoadingButton } from '@mui/lab';
import { Button, Stack } from '@mui/material';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import { useEffect } from 'react';
import type { DeepPartial } from 'react-hook-form';

import Fields from './fields';
import type { DivorceFormValues } from './schema';

export interface DivorceInfoFormProps {
  onSubmit: (values: DivorceFormValues, isDirty: boolean) => void;
  onCancel: (isDirty: boolean) => void;
  loading?: boolean;
  defaultValues: DeepPartial<DivorceFormValues>;
}
const DivorceInfoForm = ({
  onSubmit,
  onCancel,
  defaultValues,
  loading,
}: DivorceInfoFormProps) => {
  const { t } = i18n;

  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useHookForm<DivorceFormValues>({
    mode: 'onTouched',
  });

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const handleSubmitForm = (values: DivorceFormValues) => {
    onSubmit(values, isDirty);
  };
  return (
    <form onSubmit={handleSubmit(handleSubmitForm)}>
      <Fields control={control} />
      <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
        <Button variant="outlined" onClick={() => onCancel(isDirty)}>
          {t('global.cancel')}
        </Button>
        <LoadingButton
          color="secondary"
          variant="contained"
          fullWidth
          loading={loading}
          type="submit"
          sx={{ maxWidth: 112 }}
        >
          {t('global.settle')}
        </LoadingButton>
      </Stack>
    </form>
  );
};

export default DivorceInfoForm;

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Button, Stack } from '@mui/material';
import { TextField } from 'components/Form';
import { t } from 'i18n';
import type { IAddPrivateNote } from 'models/case/interface';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { object, string } from 'yup';

export interface PrivateNoteFormProps {
  onSubmit: (value: Omit<IAddPrivateNote, 'caseId'>, isDirty: boolean) => void;
  onCancel: () => void;
  defaultValues?: Omit<IAddPrivateNote, 'caseId'>;
  loading: boolean;
}
const PrivateNoteForm = ({
  onSubmit,
  onCancel,
  defaultValues,
  loading,
}: PrivateNoteFormProps) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useForm<Omit<IAddPrivateNote, 'bookingId'>>({
    mode: 'onTouched',
    defaultValues,
    resolver: yupResolver(
      object({
        note: string().max(6000, t('validation.maxLength', { number: 6000 })),
      }),
    ),
  });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  return (
    <div>
      <form onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}>
        <Stack spacing={2}>
          <TextField
            labelCol={0}
            name="privateNote"
            placeholder="運営への連絡事項等がございましたらこちらにご記載ください"
            required
            control={control}
            multiline
            minRows={5}
            maxLength={6000}
          />
        </Stack>
        <Stack direction="row" spacing={1} justifyContent="flex-end" mt={2}>
          <Button
            sx={{ minWidth: '112px' }}
            variant="outlined"
            size="small"
            onClick={onCancel}
          >
            {t('global.cancel')}
          </Button>
          <LoadingButton
            variant="contained"
            color="secondary"
            size="small"
            loading={loading}
            sx={{ minWidth: '112px' }}
            type="submit"
          >
            {t('global.settle')}
          </LoadingButton>
        </Stack>
      </form>
    </div>
  );
};

export default PrivateNoteForm;

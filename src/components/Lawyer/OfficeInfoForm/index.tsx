import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Button, Stack } from '@mui/material';
import type { JobPayload } from 'hooks/lawyer/useProfile/types';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import { useEffect } from 'react';

import Fields from './fields';
import schema from './schema';

export interface OfficeInfoFormProps {
  onSubmit: (values: JobPayload, isDirty: boolean) => void;
  onCancel: (isDirty: boolean) => void;
  loading?: boolean;
  defaultValues: JobPayload;
  detail: {
    fullName: string;
  };
}
const OfficeInfoForm = ({
  onSubmit,
  onCancel,
  loading,
  defaultValues,
  detail,
}: OfficeInfoFormProps) => {
  const { t } = i18n;

  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useHookForm<JobPayload>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
  });

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const handleSubmitForm = (values: JobPayload) => {
    onSubmit(values, isDirty);
  };
  return (
    <form onSubmit={handleSubmit(handleSubmitForm)}>
      <Fields control={control} detail={detail} />
      <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
        <Button variant="outlined" onClick={() => onCancel(isDirty)}>
          {t('global.cancel')}
        </Button>
        <LoadingButton
          color="secondary"
          variant="contained"
          fullWidth
          loading={loading}
          type="submit"
          sx={{ maxWidth: 112 }}
        >
          {t('global.settle')}
        </LoadingButton>
      </Stack>
    </form>
  );
};

export default OfficeInfoForm;

import { Grid, Stack } from '@mui/material';
import { CheckBox, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import { useFetchList } from 'hooks';
import type { JobPayload } from 'hooks/lawyer/useProfile/types';
import type { IListItem } from 'hooks/types';
import { t } from 'i18n';
import resourceQuery from 'models/resource/query';
import type { Control } from 'react-hook-form';

export interface FieldsProps {
  control: Control<JobPayload>;
  detail: { fullName: string };
}
const Fields = ({ control, detail }: FieldsProps) => {
  const { list: attributeList } = useFetchList<IListItem>(
    resourceQuery.attributes,
  );
  const { list: consultationList } = useFetchList<IListItem>(
    resourceQuery.consultations,
  );

  return (
    <Stack spacing="28px">
      <Grid container columns={15} columnSpacing={4} rowSpacing={2}>
        <Label label={t('lawyerProfile.officeName')} labelCol={5} required />
        <Grid item xs={12} tablet={10} mt="12px">
          {detail.fullName}
        </Grid>
      </Grid>
      <TextField
        columns={15}
        labelCol={5}
        control={control}
        required
        name="barAssociation"
        label={t('lawyerProfile.association')}
        placeholder={t('placeholder.barAssociation')}
        maxLength={30}
      />

      <CheckBox
        columns={15}
        labelCol={5}
        layout="horizontal"
        control={control}
        name="consultationField"
        label={t('lawyerProfile.focusArea')}
        showSelectAll
        required
        data={consultationList}
      />

      <CheckBox
        columns={15}
        labelCol={5}
        layout="horizontal"
        control={control}
        name="attribute"
        required
        label={t('lawyerProfile.attribute')}
        showSelectAll
        data={attributeList}
      />
    </Stack>
  );
};

export default Fields;

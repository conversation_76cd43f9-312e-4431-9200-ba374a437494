import i18n from 'i18n';
import type { AnyObjectSchema } from 'yup';
import { array, object, string } from 'yup';

const schema: AnyObjectSchema = object().shape({
  barAssociation: string()
    .required(i18n.t('validation.requiredField'))
    .trim(i18n.t('validation.notAllowedWhiteSpace'))
    .max(30, i18n.t('validation.maxLength', { number: 30 })),
  consultationField: array()
    .min(1, i18n.t('validation.requiredField'))
    .required(i18n.t('validation.requiredField')),
  attribute: array()
    .min(1, i18n.t('validation.requiredField'))
    .required(i18n.t('validation.requiredField')),
});

export default schema;

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Container, Stack } from '@mui/material';
import { TextField } from 'components/Form';
import useGlobalState from 'hooks/useGlobalState';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import type { ICreateConsultation } from 'models/consultation/interface';
import { useCallback, useEffect } from 'react';

import schema from './schema';

export interface ConsultationMenuFormProps {
  onSubmit: (value: ICreateConsultation, isDirty: boolean) => void;
  onCancel: () => void;
  defaultValues?: ICreateConsultation;
  loading: boolean;
}
const ConsultationMenuForm = ({
  onSubmit,
  onCancel,
  defaultValues,
  loading,
}: ConsultationMenuFormProps) => {
  const { setConfirmModal } = useGlobalState();
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useHookForm<ICreateConsultation>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues,
  });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  const handleCancel = useCallback(() => {
    if (isDirty) {
      setConfirmModal({
        onConfirm: () => {
          onCancel();
        },
        title: 'discardInput.title',
        content: 'discardInput.message',
      });
    } else onCancel();
  }, [isDirty, onCancel, setConfirmModal]);

  return (
    <div>
      <Container maxWidth="md" disableGutters>
        <Box p={4} bgcolor="white" borderRadius={2} mt={2}>
          <form onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}>
            <Stack spacing={2}>
              <TextField
                name="title"
                label={t('consultationMenu.title')}
                placeholder={t('placeholder.menuTitle')}
                required
                control={control}
                multiline
                minRows={7}
                maxLength={300}
                columns={28}
                labelCol={9}
              />
              <TextField
                name="content"
                label={t('consultationMenu.content')}
                placeholder={t('placeholder.menuContent')}
                control={control}
                multiline
                minRows={7}
                maxLength={1000}
                columns={28}
                labelCol={9}
              />
            </Stack>
            <Stack
              bgcolor="white"
              direction="row"
              justifyContent="flex-end"
              spacing={1}
              mt={4}
            >
              <Button variant="outlined" onClick={handleCancel}>
                {t('global.cancel')}
              </Button>
              <LoadingButton
                variant="contained"
                color="secondary"
                type="submit"
                sx={{ maxWidth: 112 }}
                fullWidth
                loading={loading}
              >
                {t('global.settle')}
              </LoadingButton>
            </Stack>
          </form>
        </Box>
      </Container>
    </div>
  );
};

export default ConsultationMenuForm;

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Grid, IconButton, Stack } from '@mui/material';
import { TextField } from 'components/Form';
import { useDeepCompareEffect } from 'hooks';
import i18n from 'i18n';
import { TrashIcon } from 'icons';
import { useFieldArray, useForm } from 'react-hook-form';

import type { AdditionalEmailFormValues } from './schema';
import schema from './schema';

export interface AdditionalEmailFormProps {
  onSubmit: (values: AdditionalEmailFormValues, isDirty: boolean) => void;
  onCancel: (isDirty: boolean) => void;
  loading?: boolean;
  defaultValues: AdditionalEmailFormValues;
}
const AdditionalEmailForm = ({
  onSubmit,
  onCancel,
  loading,
  defaultValues,
}: AdditionalEmailFormProps) => {
  const { t } = i18n;

  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
    clearErrors,
  } = useForm<AdditionalEmailFormValues>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'additionalEmail',
  });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const handleSubmitForm = (values: AdditionalEmailFormValues) => {
    onSubmit(values, isDirty);
  };
  return (
    <form onSubmit={handleSubmit(handleSubmitForm)}>
      <Stack spacing="28px">
        {fields.map((item, index) => (
          <Box
            display="flex"
            sx={{ '.text-field-container': { flex: 1 } }}
            key={item.id}
          >
            <TextField
              labelCol={5}
              columns={15}
              label={`メールアドレス (${index + 1})`}
              control={control}
              name={`additionalEmail.${index}.value`}
              placeholder="メールアドレス"
              adornment={
                fields.length > 1 ? (
                  <IconButton
                    onClick={() => {
                      remove(index);
                      clearErrors(`additionalEmail.${index}.value`);
                    }}
                    className="whiteOutlined"
                    sx={{ ml: 2, svg: { color: 'neutral7' } }}
                  >
                    <TrashIcon />
                  </IconButton>
                ) : null
              }
            />
          </Box>
        ))}
      </Stack>
      <Stack>
        <Grid container columns={15} columnSpacing={4} mt={3}>
          <Grid item xs={5}></Grid>
          <Grid item xs={10}>
            <Button
              variant="outlined"
              color="primary"
              fullWidth
              onClick={() => append({ value: '' })}
              sx={{
                height: '48px',
                border: 'dashed 1px #EBC35A',
                borderRadius: '4px',
                transition: 'all 0.2s ease-in-out',
                maxWidth: fields.length > 1 ? 'calc(100% - 56px)' : '100%',
              }}
            >
              + 追加
            </Button>
          </Grid>
        </Grid>
      </Stack>
      <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
        <Button variant="outlined" onClick={() => onCancel(isDirty)}>
          {t('global.cancel')}
        </Button>
        <LoadingButton
          color="secondary"
          variant="contained"
          fullWidth
          loading={loading}
          type="submit"
          sx={{ maxWidth: 112 }}
        >
          {t('global.settle')}
        </LoadingButton>
      </Stack>
    </form>
  );
};

export default AdditionalEmailForm;

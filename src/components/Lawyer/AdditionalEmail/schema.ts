import { t } from 'i18n';
import { Regex } from 'utils/constants';
import type { InferType, TestContext } from 'yup';
import { array, object, string } from 'yup';

const schema = object({
  additionalEmail: array()
    .of(
      object({
        value: string()
          .matches(Regex.EMAIL, {
            message: t('validation.invalidField'),
            excludeEmptyString: true,
          })
          .test(
            'Not allowed',
            'メールアドレスはすでに登録されています',
            (_, ctx) => {
              const { email, additionalEmail } = (
                ctx as TestContext & { from: any[]; options: { index: number } }
              ).from[1].value;
              const { index } = ctx.options as { index: number };
              const invalidIndex = (additionalEmail || []).reduce(
                (prev: number[], current: { value: string }, idx: number) => {
                  if (current.value === email) {
                    return prev.concat(idx);
                  }
                  return prev;
                },
                [],
              );
              if (invalidIndex.includes(index)) {
                return false;
              }
              return true;
            },
          ),
      }),
    )
    .required(),
  email: string(),
});

export type AdditionalEmailFormValues = InferType<typeof schema>;
export default schema;

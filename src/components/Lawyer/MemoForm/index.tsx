import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { But<PERSON>, Stack } from '@mui/material';
import { TextField } from 'components/Form';
import { t } from 'i18n';
import type { IAddBookingMemo } from 'models/booking/interface';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { object, string } from 'yup';

export interface MemoFormProps {
  onSubmit: (
    value: Omit<IAddBookingMemo, 'bookingId'>,
    isDirty: boolean,
  ) => void;
  onCancel: () => void;
  defaultValues?: Omit<IAddBookingMemo, 'bookingId'>;
  loading: boolean;
}
const MemoForm = ({
  onSubmit,
  onCancel,
  defaultValues,
  loading,
}: MemoFormProps) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useForm<Omit<IAddBookingMemo, 'bookingId'>>({
    mode: 'onTouched',
    defaultValues,
    resolver: yupResolver(
      object({
        note: string().max(6000, t('validation.maxLength', { number: 6000 })),
      }),
    ),
  });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  return (
    <div>
      <form onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}>
        <Stack spacing={2}>
          <TextField
            labelCol={0}
            name="note"
            placeholder={t('placeholder.input', {
              field: t('caseDetail.memo'),
            })}
            required
            control={control}
            multiline
            minRows={5}
            maxLength={6000}
          />
        </Stack>
        <Stack direction="row" spacing={1} justifyContent="flex-end" mt={2}>
          <Button
            sx={{ minWidth: '112px' }}
            variant="outlined"
            size="small"
            onClick={onCancel}
          >
            {t('global.cancel')}
          </Button>
          <LoadingButton
            variant="contained"
            color="secondary"
            size="small"
            loading={loading}
            sx={{ minWidth: '112px' }}
            type="submit"
          >
            {t('global.settle')}
          </LoadingButton>
        </Stack>
      </form>
    </div>
  );
};

export default MemoForm;

import { Box, Skeleton } from '@mui/material';

import styles from './styles';

const SkeletonItem = () => {
  return (
    <Box sx={styles.lawyerItem}>
      <Box display="flex" justifyContent="center">
        <Skeleton
          variant="circular"
          sx={{
            width: { xs: 64, tablet: 136 },
            height: { xs: 64, tablet: 136 },
          }}
        />
      </Box>
      <Box sx={styles.infoContainer}>
        <Skeleton
          variant="text"
          sx={{ fontSize: { xs: 10, tablet: 12 }, mt: { tablet: 5 } }}
        />
        <Skeleton variant="text" sx={{ fontSize: { tablet: 24 } }} />
        <Skeleton variant="text" sx={{ fontSize: { xs: 12, tablet: 16 } }} />
      </Box>
    </Box>
  );
};

export default SkeletonItem;

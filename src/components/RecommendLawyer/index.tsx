import { <PERSON>, But<PERSON>, Stack, Tab, Tabs, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import AvatarCarousel from 'components/UI/AvatarCarousel';
import Rating from 'components/UI/Rating';
import useBreakpoint from 'hooks/useBreakpoint';
import i18n from 'i18n';
import { RecommendIcon } from 'icons';
import { isEmpty, times } from 'lodash';
import type { IRecommendListItem } from 'models/provider/interface';
import Link from 'next/link';
import { useState } from 'react';
import Slider from 'react-slick';
import { PROVIDER_LIST_PATH, ProviderType } from 'utils/constants';

import SkeletonItem from './SekeletonItem';
import styles from './styles';

export interface IRecommendLawyer {
  data: IRecommendListItem[];
  recommendCounselors: IRecommendListItem[];
  displayRows?: number;
  tabletSlidesToShow?: number;
  loading?: boolean;
  showButton?: boolean;
}
const RecommendLawyer = ({
  data = [],
  recommendCounselors = [],
  displayRows = 1,
  tabletSlidesToShow = 2,
  loading,
  showButton,
}: IRecommendLawyer) => {
  const { t } = i18n;
  const theme = useTheme();
  const [selectedTab, setSelectedTab] = useState<ProviderType>(
    ProviderType.LAWYER,
  );
  const isBreakpoint = useBreakpoint({});
  const settings = {
    infinite: false,
    slidesToShow: 4,
    rows: 1,
    variableWidth: false,
    className: `list-slider row-${displayRows}`,
    responsive: [
      {
        breakpoint: theme.breakpoints.values.sl,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: theme.breakpoints.values.tablet,
        settings: {
          slidesToShow: tabletSlidesToShow,
          rows: displayRows,
        },
      },
    ],
  };

  return (
    <Box>
      <Box sx={styles.container}>
        <Stack
          direction="row"
          spacing={1}
          alignItems="center"
          sx={{
            svg: {
              width: { xs: 28, tablet: 32 },
              height: { xs: 28, tablet: 32 },
            },
          }}
        >
          <RecommendIcon />
          <Typography
            fontSize={{ xs: 18, tablet: 24 }}
            fontWeight="bold"
            color="heading"
          >
            おすすめの専門家に相談する
          </Typography>
        </Stack>
        <Tabs
          variant="fullWidth"
          value={selectedTab}
          sx={{
            minHeight: 'unset',
            mt: { xs: '4px', tablet: 2 },
            ml: { xs: -2, tablet: 0 },
          }}
          onChange={(_, value) => setSelectedTab(value)}
        >
          <Tab label="弁護士" value={ProviderType.LAWYER} sx={styles.tab} />
          {recommendCounselors.length > 0 && (
            <Tab
              label="カウンセラー"
              value={ProviderType.COUNSELOR}
              sx={styles.tab}
            />
          )}
        </Tabs>
        <Box
          sx={[
            styles.slider,
            {
              '& .slick-slider > .slick-list > .slick-track > .slick-slide>div':
                {
                  height: {
                    xs: displayRows === 1 ? '100%' : 'auto',
                    tablet: '100%',
                  },
                },
            },
          ]}
        >
          <Slider {...settings}>
            {loading &&
              isEmpty(data) &&
              times(4).map((number) => <SkeletonItem key={number} />)}
            {!loading &&
              (selectedTab === ProviderType.LAWYER ? data : recommendCounselors)
                .slice(0, 4)
                .map((provider) => (
                  <Link
                    href={`${PROVIDER_LIST_PATH[selectedTab]}/${provider._id}`}
                    key={provider._id}
                    legacyBehavior
                  >
                    <Box sx={styles.lawyerItem} className="scale pointer">
                      <Box display="flex" justifyContent="center">
                        <Box>
                          <AvatarCarousel
                            images={provider.images}
                            imageContainerProps={{
                              width: { xs: 64, tablet: 152 },
                              height: { xs: 64, tablet: 152 },
                            }}
                            carouselProps={{
                              maxWidth: { xs: 64, tablet: 152 },
                            }}
                            imageProps={{
                              sizes: '(min-width: 768px) 21vw, 18vw',
                            }}
                            navContainerProps={{
                              display: {
                                xs: 'none',
                                tablet:
                                  provider.images.length > 1 ? 'flex' : 'none',
                              },
                            }}
                          />
                        </Box>
                      </Box>
                      <Box sx={styles.infoContainer}>
                        <Typography
                          fontSize={{ xs: 10, tablet: 12 }}
                          fontWeight="bold"
                          color="heading"
                        >
                          {selectedTab === ProviderType.LAWYER
                            ? t('home.lawyer')
                            : 'カウンセラー'}
                        </Typography>
                        <Typography
                          fontSize={{ tablet: 24 }}
                          fontWeight="bold"
                          color="heading"
                          className="line-clamp"
                        >
                          {provider?.nickname || provider.fullName}
                        </Typography>
                        {!provider?.nickname && (
                          <Typography
                            fontSize={{ xs: 12, tablet: 16 }}
                            fontWeight={500}
                            className="line-clamp"
                          >
                            {provider.katakanaName}
                          </Typography>
                        )}
                        {selectedTab !== ProviderType.LAWYER && (
                          <Rating
                            size="lg"
                            rate={{
                              avgRating: provider.avgRating,
                              totalReview: provider.totalReview,
                            }}
                            sx={{ marginTop: ' 4px', justifyContent: 'center' }}
                          />
                        )}
                      </Box>
                    </Box>
                  </Link>
                ))}
          </Slider>
        </Box>
        {showButton && !isBreakpoint && (
          <Stack
            direction="row"
            px={2}
            justifyContent="center"
            spacing={3}
            mt="20px"
            mb="12px"
          >
            <Link href="/" legacyBehavior>
              <Button
                variant="outlined"
                fullWidth
                size="large"
                sx={{ maxWidth: 240 }}
                className="tabletStyle shadow"
              >
                {t('global.backToHome')}
              </Button>
            </Link>
            <Link
              href={PROVIDER_LIST_PATH[selectedTab]}
              passHref
              legacyBehavior
            >
              <Button
                variant="contained"
                size="large"
                className="shadow"
                color="secondary"
                fullWidth
                sx={{ maxWidth: 240 }}
              >
                {selectedTab === ProviderType.LAWYER
                  ? '弁護士'
                  : 'カウンセラー'}
                を探す
              </Button>
            </Link>
          </Stack>
        )}
      </Box>
      {isBreakpoint && showButton && (
        <Box mt={2} display="flex" justifyContent="center">
          <Stack direction="column" spacing={1}>
            <Link
              href={PROVIDER_LIST_PATH[selectedTab]}
              passHref
              legacyBehavior
            >
              <Button
                variant="contained"
                size="medium"
                className="shadow"
                color="secondary"
                fullWidth
                sx={{ minWidth: 311 }}
              >
                {selectedTab === ProviderType.LAWYER
                  ? '専門家'
                  : 'カウンセラー'}
                を探す
              </Button>
            </Link>
            <Link href="/" legacyBehavior>
              <Button
                variant="outlined"
                fullWidth
                size="medium"
                sx={{ minWidth: 311 }}
              >
                {t('global.backToHome')}
              </Button>
            </Link>
          </Stack>
        </Box>
      )}
    </Box>
  );
};

export default RecommendLawyer;

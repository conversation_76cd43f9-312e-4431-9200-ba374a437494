import type { Theme } from '@mui/material';

const styles = {
  container: {
    p: { xs: '16px 0px 12px 16px', tablet: '32px 32px 20px' },
    borderRadius: { tablet: 2 },
    bgcolor: 'white',
  },
  slider: (theme: Theme) => ({
    m: { xs: '12px 0px 0px -16px', tablet: '20px -12px 0px -12px' },
    '& .list-slider': {
      overflow: 'hidden',
      '& .slick-slide': {
        height: 'auto',
        '>div': { padding: { tablet: '12px' } },
      },
      '& .slick-track': {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        alignItems: 'stretch',
      },
    },

    [theme.breakpoints.down('tablet')]: {
      '.row-2': {
        '.slick-slide': {
          '&:first-of-type': {
            pl: '12px',
          },
          '&:last-child': {
            pr: '12px',
          },
          '>div': {
            p: '4px',
          },
        },
      },
      '.row-1': {
        '.slick-track': {
          gap: 2,
        },
        '.slick-slide:not(:first-of-type)': {
          marginLeft: -1,
        },
        '.slick-slide>div': {
          py: '4px',
        },
      },
    },
  }),
  lawyerItem: (theme: Theme) => ({
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    borderRadius: { xs: 1, tablet: 2 },
    border: {
      xs: `1.5px solid ${theme.palette.neutral4}`,
      tablet: `2px solid ${theme.palette.neutral4}`,
    },
    p: { xs: '14.5px 6.5px', tablet: '30px 14px' },
  }),
  infoContainer: {
    mt: { xs: 1, tablet: 2 },
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    textAlign: 'center',
  },
  icon: (theme: Theme) => ({
    svg: { width: 32, height: 32 },
    [theme.breakpoints.down('tablet')]: {
      svg: {
        width: 28,
        height: 28,
      },
    },
  }),
  iconMobile: {
    svg: { width: 28, height: 28 },
  },
  tab: {
    minHeight: { xs: 'auto', tablet: 48 },
    padding: '12px',
    fontWeight: 700,
    fontSize: { xs: 16, tablet: 20 },
    '&:not(.Mui-selected)': {
      borderBottom: (theme: Theme) => `1px solid ${theme.palette.placeholder}`,
    },
  },
} as const;

export default styles;

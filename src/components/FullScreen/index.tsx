import { DialogActions, DialogContent, Fade } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import type { TransitionProps } from '@mui/material/transitions';
import type { ReactElement, Ref } from 'react';
import { forwardRef } from 'react';

const Transition = forwardRef(function Transition(
  props: TransitionProps & {
    children: ReactElement;
  },
  ref: Ref<unknown>,
) {
  return <Fade ref={ref} {...props} />;
});

const FullScreenDialog = ({
  open,
  onClose,
  children,
}: {
  open: boolean;
  children: JSX.Element;
  onClose: () => void;
}) => {
  if (open) {
    return (
      <Dialog
        fullScreen
        open={open}
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 0,
          },
        }}
        onClose={onClose}
        TransitionComponent={Transition}
        sx={{
          borderRadius: 0,
        }}
      >
        <DialogContent sx={{ p: '32px 32px 0px' }}>{children}</DialogContent>
        <DialogActions sx={{ p: 2, boxShadow: '0 -8px 16px -12px #d2dce1' }} />
      </Dialog>
    );
  }
  return children;
};

export default FullScreenDialog;

import type { ChipProps, StackProps } from '@mui/material';
import { Chip, Stack } from '@mui/material';
import { motion } from 'framer-motion';
import type { DeepKeys } from 'hooks/types';
import { get } from 'lodash';
import type { ReactNode } from 'react';
import { memo, useState } from 'react';
import TruncateMarkup from 'react-truncate-markup';

export interface ChipListProps<TData> {
  data: TData;
  labelPath?: DeepKeys<TData>;
  maxLines?: number;
  stackProps?: StackProps;
  chipProps?: Omit<
    ChipProps,
    'onDrag' | 'onDragEnd' | 'onDragStart' | 'onAnimationStart'
  >;
}
const ChipList = <TData extends unknown[]>({
  data,
  labelPath,
  maxLines = 1,
  chipProps,
  stackProps,
}: ChipListProps<TData>) => {
  const [lines, setLines] = useState(1);
  const tagLeftEllipsis = (node: ReactNode) => {
    const tagRendered = get(node, 'props.children') || [];

    return (
      <Chip
        onClick={maxLines > 1 ? () => setLines(maxLines + 1) : undefined}
        className="tabletStyle"
        label={`+${(data || []).length - tagRendered.length}`}
        {...chipProps}
      />
    );
  };
  return (
    <TruncateMarkup lines={lines} ellipsis={tagLeftEllipsis}>
      <Stack
        direction="row"
        gap="4px"
        flexWrap="wrap"
        style={{ display: 'flex' }}
        {...stackProps}
      >
        {(data || []).map((item, index) => (
          <TruncateMarkup.Atom key={index}>
            <Chip
              key={index}
              className="tabletStyle"
              label={labelPath ? get(item, labelPath) : (item as string)}
              component={motion.div}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              {...chipProps}
            />
          </TruncateMarkup.Atom>
        ))}
      </Stack>
    </TruncateMarkup>
  );
};

const areEqual = <T extends unknown>(
  prevProps: ChipListProps<T>,
  nextProps: ChipListProps<T>,
) => {
  return JSON.stringify(prevProps) === JSON.stringify(nextProps);
};

export default memo(ChipList, areEqual);

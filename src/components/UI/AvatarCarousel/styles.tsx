import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  carousel: {
    overflow: 'unset',
    '& .indicator-container': {
      height: { xs: 28, tablet: 32 },
      mt: 1,
      '& .MuiSvgIcon-root': {
        p: '1.5px',
        width: '15px',
        height: '15px',
      },
    },
  },
  navButton: {
    position: 'absolute',
    overflow: 'unset',
    bottom: 0,
    transform: 'translate(0px, calc(100% + 8px))',
    p: { xs: '4px', tablet: '5px' },
    width: { xs: '28px', tablet: '32px' },

    svg: {
      width: { xs: 18, tablet: 20 },
      height: { xs: 18, tablet: 20 },
    },
    border: (theme) => `1px solid ${theme.palette.neutral4}`,
    '&:hover': {
      color: 'white',
    },
  },
  image: {
    borderRadius: '50%',
    width: '100%',
    height: '100%',
  },
  amblaContainer: {
    display: 'flex',
    userSelect: 'none',
    transform: 'unset',
    WebkitTouchCallout: 'none',
    KhtmlUserSelect: 'none',
    WebkitTapHighlightColor: 'transparent',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import type { BoxProps } from '@mui/material';
import { Box, Button, IconButton, Stack, SvgIcon } from '@mui/material';
import useEmblaCarousel from 'embla-carousel-react';
import { ArrowLeftIcon, ArrowRightIcon } from 'icons';
import Image from 'next/image';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useState } from 'react';
import type { UrlObject } from 'url';

import styles from './styles';

export interface AvatarCarouselProps {
  images?: {
    key: string;
    originUrl: string;
  }[];
  redirect?: string | UrlObject;
  navContainerProps?: BoxProps;
  imageContainerProps?: BoxProps;
  imageProps?: any;
  carouselProps?: BoxProps;
}
const AvatarCarousel = ({
  images = [{ originUrl: '/images/default-avatar.png', key: 'defaultImage' }],
  redirect,
  navContainerProps,
  imageContainerProps,
  imageProps,
  carouselProps,
}: AvatarCarouselProps) => {
  const displayImages = useMemo(
    () =>
      images.length === 0
        ? [{ originUrl: '/images/default-avatar.png', key: 'defaultImage' }]
        : images,
    [images],
  );
  const [viewportRef, embla] = useEmblaCarousel({ loop: false });
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const scrollPrev = useCallback(
    (e: any) => {
      e.preventDefault();
      if (embla) {
        embla.scrollPrev();
      }
    },
    [embla],
  );
  const scrollNext = useCallback(
    (e: any) => {
      e.preventDefault();
      if (embla) {
        embla.scrollNext();
      }
    },
    [embla],
  );
  const scrollTo = useCallback(
    (index: number) => embla && embla.scrollTo(index),
    [embla],
  );

  const onSelect = useCallback(() => {
    if (!embla) return;
    setSelectedIndex(embla.selectedScrollSnap());
    setPrevBtnEnabled(embla.canScrollPrev());
    setNextBtnEnabled(embla.canScrollNext());
  }, [embla]);

  useEffect(() => {
    if (!embla) return;
    embla.on('select', onSelect);
    embla.on('reInit', onSelect);
    onSelect();
    setScrollSnaps(embla.scrollSnapList());
  }, [embla, onSelect]);

  useEffect(() => {
    if (embla) {
      embla.reInit();
      setScrollSnaps(embla.scrollSnapList());
    }
  }, [displayImages, embla]);

  return (
    <Box position="relative" maxWidth={152} margin="0 auto" {...carouselProps}>
      <Box overflow="hidden" width={1} ref={viewportRef}>
        <Box sx={styles.amblaContainer}>
          {displayImages.map((image) => {
            if (redirect) {
              return (
                <Link href={redirect} key={image.key}>
                  <Box
                    width={152}
                    height={152}
                    position="relative"
                    {...imageContainerProps}
                  >
                    <Image
                      priority
                      fill
                      src={image.originUrl || '/images/default-avatar.png'}
                      alt="avatar"
                      style={styles.image as never}
                      {...imageProps}
                    />
                  </Box>
                </Link>
              );
            }
            return (
              <Box key={image.key}>
                <Box
                  width={152}
                  height={152}
                  position="relative"
                  {...imageContainerProps}
                >
                  <Image
                    priority
                    fill
                    src={image.originUrl || '/images/default-avatar.png'}
                    alt="avatar"
                    style={styles.image as never}
                    {...imageProps}
                  />
                </Box>
              </Box>
            );
          })}
        </Box>
      </Box>
      <Box
        justifyContent="space-between"
        alignItems="center"
        mt={1}
        display={images.length > 1 ? 'flex' : 'none'}
        {...navContainerProps}
      >
        <IconButton
          sx={{
            p: '5px',
            border: 'solid 1px #d2dce1',
            width: { xs: 28, tablet: 32 },
            height: { xs: 28, tablet: 32 },
          }}
          onClick={scrollPrev}
          disabled={!prevBtnEnabled}
        >
          <SvgIcon fontSize="small" component={ArrowLeftIcon} />
        </IconButton>
        <Stack
          direction="row"
          gap={{ xs: '4px', tablet: 1 }}
          mx={{ xs: '12px', tablet: 0 }}
        >
          {scrollSnaps.map((_, index) => (
            <Button
              sx={{
                width: '8px',
                height: '8px',
                bgcolor: index === selectedIndex ? 'neutral7' : 'neutral2',
                p: '4px',
                minWidth: 'unset',
                '&:hover': {
                  bgcolor: 'neutral2',
                },
              }}
              key={index}
              onClick={() => scrollTo(index)}
            />
          ))}
        </Stack>
        <IconButton
          sx={{
            p: '5px',
            border: 'solid 1px #d2dce1',
            width: { xs: 28, tablet: 32 },
            height: { xs: 28, tablet: 32 },
          }}
          onClick={scrollNext}
          disabled={!nextBtnEnabled}
        >
          <SvgIcon fontSize="small" component={ArrowRightIcon} />
        </IconButton>
      </Box>
    </Box>
  );
};

export default AvatarCarousel;

import type { MenuProps, SvgIconProps, TypographyProps } from '@mui/material';
import { Menu, MenuItem, SvgIcon, Typography } from '@mui/material';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import type { ElementType } from 'react';

import styles from './styles';

dayjs.extend(isSameOrAfter);

export interface DropdonwMenuProps extends MenuProps {
  onClose: () => void;
  menus: {
    icon?: ElementType;
    label: string;
    key: string;
    disabled?: boolean;
    iconProps?: SvgIconProps;
    labelProps?: TypographyProps;
  }[];
  onMenuItemClick?: (key: string) => void;
}
const DropdownMenu = ({
  onClose,
  onMenuItemClick,
  menus = [],
  ...props
}: DropdonwMenuProps) => {
  const handleClose = () => {
    onClose();
  };

  return (
    <Menu
      sx={styles.dropdownButton}
      id="more-button"
      onClose={handleClose}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      PaperProps={{
        sx: { px: 1 },
      }}
      {...props}
    >
      {menus.map((menu) => (
        <MenuItem
          key={menu.key}
          sx={{
            p: { xs: '4px', tablet: '10px 8px' },
            borderRadius: 1,
            minHeight: { xs: '20px', tablet: '40px' },
          }}
          onClick={() => onMenuItemClick && onMenuItemClick(menu.key)}
          disabled={menu.disabled}
        >
          {menu.icon && (
            <SvgIcon
              sx={{ mr: '4px' }}
              component={menu.icon}
              fontSize="small"
              {...menu.iconProps}
            />
          )}
          <Typography
            ml={{ xs: 0, tablet: '4px' }}
            color="heading"
            fontSize={14}
            fontWeight={500}
            {...menu.labelProps}
          >
            {menu.label}
          </Typography>
        </MenuItem>
      ))}
    </Menu>
  );
};

export default DropdownMenu;

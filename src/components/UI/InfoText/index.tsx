import type { StackProps } from '@mui/material';
import { Stack, Typography } from '@mui/material';
import { InfoIcon } from 'icons';
import type { ReactNode } from 'react';

export interface IInfoText extends StackProps {
  text: string;
  icon?: ReactNode;
}
const InfoText = ({ text, icon = <InfoIcon />, ...stackProps }: IInfoText) => {
  return (
    <Stack
      mb={{ xs: 2, tablet: 3 }}
      direction="row"
      alignItems="center"
      p={1}
      spacing={1}
      borderRadius={{ xs: '4px', tablet: '6px' }}
      bgcolor="divine"
      sx={{
        svg: {
          color: 'icon',
          width: { xs: 16, tablet: 20 },
          height: { xs: 16, tablet: 20 },
        },
      }}
      {...stackProps}
    >
      {icon}
      <Typography
        fontSize={{ xs: 12, tablet: 14 }}
        fontWeight={500}
        color="neutral6"
      >
        {text}
      </Typography>
    </Stack>
  );
};

export default InfoText;

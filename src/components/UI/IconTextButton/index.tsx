/* eslint-disable react/display-name */
import type { ButtonProps } from '@mui/material';
import { Box, Button, Typography } from '@mui/material';
import type { SxProps, Theme } from '@mui/material/styles';
import type { ReactNode } from 'react';
import { forwardRef } from 'react';

import styles from './styles';

export interface IconTextButtonProps extends ButtonProps {
  text: string;
  middleText?: string;
  endText?: string;
  subText?: string;
  textAlign?: any;
  icon: ReactNode;
  containerStyle?: SxProps<Theme>;
}
const IconTextButton = forwardRef<unknown, IconTextButtonProps>(
  (
    {
      text,
      subText,
      textAlign = 'start',
      icon,
      containerStyle = [],
      middleText,
      endText,
      ...props
    }: IconTextButtonProps,
    ref,
  ) => {
    return (
      <Button
        disableRipple
        startIcon={<Box sx={styles.startIcon}>{icon}</Box>}
        sx={[
          styles.button,
          { pr: 1 },
          ...(Array.isArray(containerStyle)
            ? containerStyle
            : [containerStyle]),
        ]}
        variant="contained"
        color="secondary"
        ref={ref as never}
        {...props}
      >
        <Box width={1}>
          {subText && (
            <Typography textAlign={textAlign} sx={styles.subText}>
              {subText}
            </Typography>
          )}
          <Typography sx={styles.text} textAlign={textAlign}>
            {text}
            {middleText && (
              <Typography sx={styles.middleText} component="span">
                {middleText}
              </Typography>
            )}
            {endText && (
              <Typography component="span" sx={styles.endText}>
                {endText}
              </Typography>
            )}
          </Typography>
        </Box>
      </Button>
    );
  },
);

export default IconTextButton;

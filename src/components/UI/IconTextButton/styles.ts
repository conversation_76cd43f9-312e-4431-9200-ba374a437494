import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  button: (theme) => ({
    borderRadius: '28px',
    py: '7px',
    pl: '8px',
    justifyContent: 'start',
    boxShadow: '0px 4px #e4dfdb',
    border: 'solid 2px #fff',
    '& .MuiButton-startIcon': {
      mr: { xs: '6px', tablet: 2 },
    },
    [theme.breakpoints.down('tablet')]: {
      py: '4px',
      pl: '4px',
      width: 188,
      '& .MuiButton-startIcon': {
        mr: '6px',
      },
    },
  }),
  startIcon: (theme) => ({
    display: 'flex',
    p: 1,
    bgcolor: 'white',
    borderRadius: '50%',
    height: '40px',
    svg: {
      width: 24,
      height: 24,
    },
    [theme.breakpoints.down('tablet')]: {
      p: '6px',
      height: '32px',
      svg: {
        width: 20,
        height: 20,
      },
    },
  }),
  text: {
    fontWeight: 'bold',
    fontSize: 14,
    color: 'heading',
    letterSpacing: { xs: '3.5px', tablet: '4px' },
    lineHeight: { xs: '20px', tablet: '24px' },
    mt: '1px',
    '@media (max-width: 375px)': {
      fontSize: 12,
    },
  },
  subText: {
    fontSize: { xs: 8, tablet: 12 },
    fontWeight: 'bold',
    lineHeight: { xs: '11px', tablet: '18px' },
    letterSpacing: '2px',
    color: 'white',
  },
  middleText: {
    fontWeight: 'bold',
    fontSize: { xs: 10, tablet: 12 },
    letterSpacing: { xs: '3.33px', tablet: '4px' },
  },
  endText: {
    fontWeight: 'bold',
    fontSize: { xs: 12, tablet: 14 },
    letterSpacing: { xs: '3.43px', tablet: '4px' },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

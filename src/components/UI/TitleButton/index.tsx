import type { LoadingButtonProps } from '@mui/lab';
import { LoadingButton } from '@mui/lab';
import { Box, Typography } from '@mui/material';
import { ArrowRightIcon, BlackPathLeft, BlackPathRight } from 'icons';

import styles from './styles';

const TitleButton = ({
  title,
  text,
  subText,
  loading,
  showArrow = true,
  ...props
}: LoadingButtonProps & {
  text: string;
  subText: string;
  showArrow?: boolean;
}) => {
  return (
    <Box width="fit-content">
      <Box
        display="flex"
        gap={1}
        justifyContent="center"
        sx={{
          svg: {
            width: { xs: 14, tablet: 15 },
            height: { xs: 22, tablet: 25 },
          },
        }}
      >
        <BlackPathLeft />
        <Typography fontSize={{ tablet: 18 }} fontWeight={500} color="#464646">
          {title}
        </Typography>
        <BlackPathRight />
      </Box>
      <LoadingButton
        variant="contained"
        color="yellow"
        sx={styles.button}
        endIcon={
          showArrow && <ArrowRightIcon width={32} height={32} color="white" />
        }
        onClick={() => {}}
        loading={loading}
        {...props}
      >
        {!loading && (
          <Box>
            <Typography
              fontSize={{ xs: 20, tablet: 24 }}
              fontWeight={700}
              color="#464646"
            >
              {text}
            </Typography>
            <Typography
              fontSize={{ tablet: 18 }}
              fontWeight={700}
              color="#fff"
              mt="2px"
            >
              {subText}
            </Typography>
          </Box>
        )}
      </LoadingButton>
    </Box>
  );
};

export default TitleButton;

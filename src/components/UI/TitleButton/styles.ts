import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  button: {
    minWidth: { xs: 327, tablet: 358 },
    minHeight: 93,
    boxShadow: { xs: '0px 4px #e4dfdb', tablet: '0px 5px #e4dfdb' },
    borderRadius: '45px',
    border: 'solid 2px #fff',
    padding: '13px',
    mt: { xs: '12px', tablet: 2 },
    '.MuiButton-endIcon': {
      position: 'absolute',
      margin: 0,
      right: '12px',
      // '@media (max-width: 768px)': {
      //   svg: {
      //     width: 24,
      //     height: 24,
      //   },
      // },
    },
    '@media (max-width: 768px)': {
      padding: '12px 12px 11px',
      minHeight: 76,
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

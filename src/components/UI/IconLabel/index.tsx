import type { StackProps } from '@mui/material';
import { Stack, SvgIcon, Typography } from '@mui/material';
import type { ElementType } from 'react';

const IconLabel = ({
  icon,
  children,
  containerProps,
}: {
  children: any;
  icon: ElementType<any>;
  containerProps?: StackProps;
}) => {
  return (
    <Stack spacing={1} direction="row" alignItems="center" {...containerProps}>
      <SvgIcon fontSize="large" className="tabletStyle" component={icon} />
      <Typography
        flex={1}
        fontSize={{ xs: 20, tablet: 24 }}
        fontWeight="bold"
        color="heading"
        className="icon-label__label"
      >
        {children}
      </Typography>
    </Stack>
  );
};

export default IconLabel;

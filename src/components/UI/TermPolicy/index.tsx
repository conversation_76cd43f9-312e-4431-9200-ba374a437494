import { FormControlLabel, Typography } from '@mui/material';
import CheckboxBase from 'components/Form/CheckBox/CheckboxBase';
import type { LinkProps } from 'components/Link';
import MuiLink from 'components/Link';

import styles from './styles';

const TermPolicy = ({
  checked,
  onChange,
  linkProps,
}: {
  checked: boolean;
  onChange: (data: boolean) => void;
  linkProps?: Partial<LinkProps>;
}) => {
  return (
    <FormControlLabel
      className="term-policy-container"
      sx={styles.agreement}
      control={
        <CheckboxBase
          iconClassName="tabletStyle"
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
        />
      }
      label={
        <Typography fontWeight={500} fontSize={{ xs: 14, tablet: 16 }}>
          <MuiLink
            target="_blank"
            rel="noreferrer"
            href="/terms"
            underline="hover"
            fontSize={14}
            {...linkProps}
          >
            利用規約
          </MuiLink>
          <Typography fontSize={14} component="span">
            と
          </Typography>
          <MuiLink
            target="_blank"
            rel="noreferrer"
            href="/policy"
            underline="hover"
            fontSize={14}
            {...linkProps}
          >
            プライバシーポリシー
          </MuiLink>
          <Typography fontSize={14} component="span">
            に同意する
          </Typography>
        </Typography>
      }
    />
  );
};

export default TermPolicy;

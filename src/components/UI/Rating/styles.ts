import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  average: {
    fontSize: { xs: '12px', md: '14px' },
    lineHeight: { xs: '16px', md: '20px' },
    fontWeight: 700,
    color: '#464646',
    flexShrink: 0,
  },
  averageLG: {
    fontSize: { xs: '14px', md: '16px' },
    lineHeight: { xs: '20px', md: '24px' },
    fontWeight: 700,
    color: '#464646',
    flexShrink: 0,
  },
  total: {
    fontSize: { xs: '12px', md: '14px' },
    lineHeight: { xs: '16px', md: '20px' },
    fontWeight: 400,
    color: '#887F70',
  },
  totalLG: {
    fontSize: { xs: '14px', md: '16px' },
    lineHeight: { xs: '20px', md: '24px' },
    fontWeight: 400,
    color: '#887F70',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

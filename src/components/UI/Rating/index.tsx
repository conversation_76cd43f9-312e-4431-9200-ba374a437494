import type { SxProps, Theme } from '@mui/material';
import { Stack, Typography } from '@mui/material';
import { StarFilledLightIcon, StarFilledLightLGIcon } from 'icons';
import React, { useMemo } from 'react';
import Helper from 'utils/helpers';

import styles from './styles';
//

export default function Rating({
  rate,
  sx,
  size = 'md',
}: {
  rate: { avgRating: number; totalReview: number };
  sx?: SxProps<Theme> | undefined;
  size?: 'md' | 'lg';
}) {
  const avgRating = Helper.formatRating(rate.avgRating);
  const com = useMemo(() => {
    switch (size) {
      case 'lg':
        return (
          <Stack
            direction="row"
            spacing="2px"
            alignItems="center"
            flexWrap="wrap"
            sx={sx}
          >
            <StarFilledLightLGIcon />
            <Typography sx={styles.averageLG}>{avgRating}</Typography>
            <Typography sx={styles.totalLG}>({rate.totalReview}件)</Typography>
          </Stack>
        );
      case 'md':
        return (
          <Stack
            direction="row"
            spacing="2px"
            alignItems="center"
            flexWrap="wrap"
            sx={sx}
          >
            <StarFilledLightIcon />
            <Typography sx={styles.average}>{avgRating}</Typography>
            <Typography sx={styles.total}>({rate.totalReview}件)</Typography>
          </Stack>
        );
      default:
        return null;
    }
  }, [avgRating, rate.totalReview, size, sx]);

  return <>{com}</>;
}

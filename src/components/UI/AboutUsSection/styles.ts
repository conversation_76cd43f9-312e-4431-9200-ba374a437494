import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  contentText: {
    fontSize: { xs: 14, tablet: 18 },
    fontWeight: 'bold',
    color: 'heading',
  },
  container: (theme: Theme) => ({
    pb: 6,
    pt: 10,
    [theme.breakpoints.down('tablet')]: {
      py: 3,
    },
  }),
  image: {
    flex: 1,
    alignSelf: 'flex-end',
    ml: 0,
    position: 'relative',
    maxWidth: 560,
    overflow: 'hidden',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

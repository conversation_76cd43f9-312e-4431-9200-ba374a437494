import { Box, Container, Typography } from '@mui/material';
import FadeMotion, { fadeInUp } from 'components/Animation/FadeMotion';
import { motion } from 'framer-motion';
import { t } from 'i18n';
import Image from 'next/image';

import styles from './styles';

const AboutUsSection = () => {
  return (
    <Box bgcolor="primary.main">
      <FadeMotion>
        <Container
          maxWidth="mw"
          sx={{ px: { xs: 2, tablet: 0 } }}
          disableGutters
        >
          <Box sx={styles.container} flex={1}>
            <motion.span variants={fadeInUp}>
              <Typography
                fontSize={{ xs: 24, tablet: 40 }}
                fontWeight="bold"
                color="white"
              >
                {t('aboutUs.header')}
              </Typography>
            </motion.span>
            <motion.span variants={fadeInUp}>
              <Typography sx={styles.contentText} mt={{ xs: 3, tablet: 4 }}>
                {t('aboutUs.contentText1')}
              </Typography>
              <Typography
                sx={styles.contentText}
                whiteSpace={{ xs: 'pre-line', tablet: 'normal' }}
              >
                {t('aboutUs.contentText2Sub1')}
              </Typography>
              <Typography sx={styles.contentText}>
                {t('aboutUs.contentText2Sub2')}
              </Typography>
              <Typography sx={styles.contentText}>
                {t('aboutUs.contentText3Sub1')}
              </Typography>
              <Typography
                sx={styles.contentText}
                whiteSpace={{ xs: 'pre-line', tablet: 'normal' }}
              >
                {t('aboutUs.contentText3Sub2')}
              </Typography>
            </motion.span>
          </Box>
          <Box sx={styles.image}>
            <motion.div variants={fadeInUp}>
              <Image
                src="/images/footer-image.webp"
                alt=""
                width={1}
                height={449 / 560}
                sizes="100vw"
                style={{
                  width: '100%',
                  height: 'auto',
                  display: 'block',
                }}
              />
            </motion.div>
          </Box>
        </Container>
      </FadeMotion>
    </Box>
  );
};

export default AboutUsSection;

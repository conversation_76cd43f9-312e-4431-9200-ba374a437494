const styles = {
  container: {
    borderRadius: { tablet: 2 },
    bgcolor: 'white',
  },
  divider: {
    width: { xs: 'calc(100% - 16px)', tablet: 'calc(100% - 48px)' },
    margin: '0 auto',
  },
  listItemIcon: {
    minWidth: 'unset',
    mr: { xs: '12px', tablet: 2 },
    svg: {
      width: { xs: 20, tablet: 24 },
      height: { xs: 20, tablet: 24 },
    },
  },
  listItemText: {
    color: 'heading',
    fontWeight: 500,
    fontSize: { xs: 14, tablet: 16 },
    my: 0,
  },
  listItemButton: {
    p: { xs: 2, tablet: 3 },
    my: 0,
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      bgcolor: 'divine',
      borderRadius: { xs: 1, tablet: 2 },
    },
    svg: {
      color: 'primary.main',
      width: { xs: 20, tablet: 24 },
      height: { xs: 20, tablet: 24 },
    },
  },
} as const;

export default styles;

import {
  Box,
  Divider,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Stack,
} from '@mui/material';
import { ArrowRightIcon } from 'icons';
import Link from 'next/link';
import type { ReactNode } from 'react';

import styles from './styles';

export interface IMyPageMenu {
  path: string;
  icon?: ReactNode;
  label: string;
}
const MyPageMenu = ({ menu }: { menu: IMyPageMenu[] }) => {
  return (
    <Box sx={styles.container} className="mypage-menu">
      <List
        component="nav"
        aria-label="menu-settings"
        sx={{ p: { xs: '0px 8px', tablet: 1 } }}
      >
        <Stack divider={<Divider sx={styles.divider} />}>
          {menu.map((menuItem) => (
            <Link href={menuItem.path} key={menuItem.path} legacyBehavior>
              <ListItemButton sx={styles.listItemButton}>
                {menuItem.icon && (
                  <ListItemIcon sx={styles.listItemIcon}>
                    {menuItem.icon}
                  </ListItemIcon>
                )}
                <ListItemText
                  disableTypography
                  primary={menuItem.label}
                  sx={styles.listItemText}
                />
                <ArrowRightIcon />
              </ListItemButton>
            </Link>
          ))}
        </Stack>
      </List>
    </Box>
  );
};

export default MyPageMenu;

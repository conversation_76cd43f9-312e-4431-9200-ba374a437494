import { Box, Stack, Typography } from '@mui/material';

const BorderLabel = ({ children }: { children: any }) => {
  return (
    <Stack direction="row" spacing={{ xs: 1, tablet: 2 }}>
      <Box
        bgcolor="primary.main"
        width={{ xs: '2px', tablet: '4px' }}
        borderRadius="2px"
      />
      <Typography
        fontSize={{ xs: 18, tablet: 24 }}
        fontWeight="bold"
        lineHeight={{ xs: '28px', tablet: '36px' }}
        color="heading"
      >
        {children}
      </Typography>
    </Stack>
  );
};

export default BorderLabel;

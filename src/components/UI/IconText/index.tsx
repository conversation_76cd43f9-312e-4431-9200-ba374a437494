import { Stack, SvgIcon, Typography } from '@mui/material';
import type { ElementType } from 'react';

import styles from './styles';

const IconText = ({
  children,
  icon,
}: {
  children: any;
  icon: ElementType<any>;
}) => {
  return (
    <Stack
      direction="row"
      spacing={1}
      sx={styles.container}
      className="icon-text"
    >
      <SvgIcon className="tabletStyle" component={icon} />
      <Typography className="icon-text__text" sx={styles.text}>
        {children}
      </Typography>
    </Stack>
  );
};

export default IconText;

import type { IconButtonProps } from '@mui/material';
import { IconButton } from '@mui/material';
import { BackIcon } from 'icons';
import { useRouter } from 'next/router';
import type { ReactNode } from 'react';

export interface IBackButton extends IconButtonProps {
  icon?: ReactNode;
  url?: string;
}
const BackButton = ({
  icon = <BackIcon />,
  size = 'large',
  url,
  ...props
}: IBackButton) => {
  const { back, push } = useRouter();
  return (
    <IconButton
      size={size}
      sx={{ p: '4px' }}
      onClick={() => {
        if (url) {
          push(url);
        } else back();
      }}
      {...props}
    >
      {icon}
    </IconButton>
  );
};

export default BackButton;

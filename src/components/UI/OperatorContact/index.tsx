import { Box, Button, Typography } from '@mui/material';
import { ContactIcon2, LineIcon2 } from 'icons';
import Link from 'next/link';
import process from 'process';
import { getLineUrl } from 'utils/getLineUrl';

interface OperatorContactProps {
  lineUrlType?: 'default' | 'verify-email';
}

const OperatorContact = ({ lineUrlType = 'default' }: OperatorContactProps) => {
  const getLineUrlForPage = () => {
    if (lineUrlType === 'verify-email') {
      return 'https://s.lmes.jp/landing-qr/2006801672-5wnY6OZr?uLand=OpDv7s';
    }
    return getLineUrl();
  };
  return (
    <>
      <Typography
        whiteSpace={{ xs: 'normal', tablet: 'pre-line' }}
        fontSize={{ xs: 12, tablet: 14 }}
        textAlign="center"
      >
        {`専門家選びにお悩みの方は、運営スタッフがサポートさせて頂きます。\nまずはお気軽にご相談ください。`}
      </Typography>
      <Box
        display={{ xs: 'block', tablet: 'flex' }}
        gap={3}
        maxWidth={624}
        margin="0 auto"
        sx={{ '&>*': { flex: 1 }, marginTop: { xs: 2, tablet: 3 } }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            LinkComponent={Link}
            href={getLineUrlForPage()}
            target="_blank"
            fullWidth
            startIcon={<LineIcon2 />}
            size="large"
            className="tabletStyle"
            sx={{
              backgroundColor: '#06C755',
              color: 'white',
              maxWidth: { tablet: 300 },
              '&:hover': {
                backgroundColor: '#06C755',
              },
            }}
            variant="contained"
          >
            LINEで無料相談
          </Button>
        </Box>
        {process.env.SHOW_OPERATOR_CONTACT === 'true' && (
          <Box marginTop={{ xs: 2, tablet: 0 }}>
            <Button
              fullWidth
              size="large"
              LinkComponent={Link}
              target="_blank"
              href={`tel:${process.env.OPERATOR_PHONE}`}
              sx={{
                display: 'block',
                padding: 1,
                marginTop: 1,
                backgroundColor: '#F6F8F9',
              }}
              variant="whiteOutlined"
            >
              <Box display="flex" gap="6px" justifyContent="center">
                <ContactIcon2 />
                <Typography fontSize={14} color="#F49B15" fontWeight={700}>
                  070-4075-4201
                </Typography>
              </Box>
              <Typography
                fontSize={12}
                marginTop="2px"
                lineHeight="16px"
                color="text.primary"
                textAlign="center"
              >
                受付時間 9:00〜18:00 (平日)
              </Typography>
            </Button>
          </Box>
        )}
      </Box>
    </>
  );
};

export default OperatorContact;

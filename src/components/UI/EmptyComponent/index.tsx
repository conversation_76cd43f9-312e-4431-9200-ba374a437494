import { Box, Typography } from '@mui/material';
import { LetterIcon } from 'icons';
import type { ReactNode } from 'react';
import React from 'react';

const EmptyComponent = ({
  icon = <LetterIcon />,
  text,
}: {
  icon?: ReactNode;
  text?: string | ReactNode;
}) => {
  return (
    <Box
      minHeight="340px"
      bgcolor="white"
      borderRadius={2}
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      mt={2}
    >
      {icon}
      {text && (
        <Typography textAlign="center" component="div" mb="20px" mt={2}>
          {text}
        </Typography>
      )}
    </Box>
  );
};

export default EmptyComponent;

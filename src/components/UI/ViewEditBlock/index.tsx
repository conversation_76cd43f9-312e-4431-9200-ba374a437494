import { <PERSON>, Button, Grid, <PERSON>ack, Typography } from '@mui/material';
import VisibleMotion from 'components/Animation/VisibleMotion';
import i18n from 'i18n';
import { EditIcon, InfoIcon } from 'icons';
import { get, isEmpty } from 'lodash';
import type { ReactNode } from 'react';
import { Element } from 'react-scroll';

import styles from './styles';

type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`;
}[keyof ObjectType & (string | number)];

export interface IBlockField<TData = unknown> {
  label?: string;
  path?: TData extends unknown ? string : NestedKeyOf<TData & object>;
  renderValue?: (value: any, detail?: TData) => void;
}
interface IViewEditBlock<TData> {
  detail?: TData;
  block: {
    title: string;
    subTitle?: string;
    icon?: ReactNode;
    fields: I<PERSON><PERSON><PERSON>ield<TData>[];
  };
  editMode?: boolean;
  onEdit?: () => void;
  hideEditModeButton?: boolean;
  children?: ReactNode;
  showNoti?: boolean;
  formId: string;
  detailRender?: ReactNode;
}
const ViewEditBlock = <TData extends unknown>({
  detail,
  block,
  editMode,
  onEdit,
  hideEditModeButton,
  children,
  showNoti,
  formId,
  detailRender,
}: IViewEditBlock<TData>) => {
  const { t } = i18n;
  return (
    <Box maxWidth="md" sx={styles.viewEditContainer}>
      <Box display="flex" justifyContent="space-between">
        <Stack spacing={1} direction="row" sx={styles.titleWrapper}>
          {block.icon}
          <Element name={formId}>
            <Typography className="view-edit-title" sx={styles.title}>
              {t(block.title)}
            </Typography>
            {block.subTitle && (
              <Typography fontSize={14} color="hint" mt="2px">
                {block.subTitle}
              </Typography>
            )}
          </Element>
        </Stack>
        {!hideEditModeButton && (
          <Stack direction="row" spacing={1}>
            {!editMode && (
              <Box>
                <Button
                  color="secondary"
                  variant="contained"
                  className="tabletStyle"
                  onClick={onEdit}
                  startIcon={<EditIcon />}
                >
                  {t('global.edit')}
                </Button>
              </Box>
            )}
          </Stack>
        )}
      </Box>
      {showNoti && !editMode && (
        <Stack direction="row" spacing={1} sx={styles.changeContainer}>
          <InfoIcon />
          <Typography
            color="primary"
            fontWeight={500}
            fontSize={{ xs: 12, tablet: 14 }}
            flex={1}
          >
            {['expect-counselor-form', 'expect-lawyer-form'].includes(formId)
              ? 'おすすめ条件が変更されました'
              : t('consultationRecord.checkYourChange')}
          </Typography>
        </Stack>
      )}
      <Stack
        spacing={2}
        mt={hideEditModeButton ? 4 : { xs: '12px', tablet: '28px' }}
      >
        {!editMode ? (
          <VisibleMotion>
            {detailRender || (
              <Stack rowGap={2}>
                {block.fields.map((field) => {
                  const fieldValue =
                    field.path && (get(detail, field.path) as any);
                  const isEmptyValue =
                    !(fieldValue || fieldValue === 0) ||
                    (Array.isArray(fieldValue) && isEmpty(fieldValue));

                  const renderFieldValue =
                    detail &&
                    field.renderValue &&
                    field.renderValue(fieldValue, detail);
                  const isEmptyDisplay = !renderFieldValue && isEmptyValue;
                  const displayFieldValue = isEmptyDisplay
                    ? t('global.noInfo')
                    : fieldValue;

                  return (
                    <Grid
                      container
                      rowSpacing={{ xs: '4px', tablet: 1.5 }}
                      columnSpacing={4}
                      key={field.path || field.label}
                    >
                      <Grid
                        item
                        xs={12}
                        tablet={4}
                        fontWeight="bold"
                        fontSize={{ xs: 14, tablet: 16 }}
                      >
                        {field.label && t(field.label)}
                      </Grid>
                      <Grid
                        item
                        xs
                        tablet={8}
                        color={isEmptyDisplay ? 'hint' : 'text.primary'}
                        fontSize={{ xs: 14, tablet: 16 }}
                      >
                        {field.path && (renderFieldValue || displayFieldValue)}
                      </Grid>
                    </Grid>
                  );
                })}
              </Stack>
            )}
          </VisibleMotion>
        ) : (
          <VisibleMotion key={formId}>{children}</VisibleMotion>
        )}
      </Stack>
    </Box>
  );
};

export default ViewEditBlock;

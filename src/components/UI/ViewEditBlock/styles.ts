const styles = {
  viewEditContainer: {
    p: { xs: 2, tablet: 4 },
    m: { xs: '8px auto', tablet: '16px auto' },
    borderRadius: { tablet: '10px' },
    bgcolor: 'white',
  },
  titleWrapper: {
    alignItems: 'center',
    flex: 1,
    svg: {
      width: { xs: 28, tablet: 32 },
      height: { xs: 28, tablet: 32 },
    },
  },
  title: {
    fontSize: { xs: 18, tablet: 24 },
    fontWeight: 'bold',
    lineHeight: 'normal',
    color: 'heading',
  },
  changeContainer: {
    mt: '20px',
    bgcolor: '#fdf6e2',
    p: { xs: '4px 8px', tablet: 1 },
    borderRadius: { xs: '4px', tablet: '6px' },
    alignItems: 'center',
    svg: {
      color: 'primary.main',
      width: { xs: 16, tablet: 20 },
      height: { xs: 16, tablet: 20 },
    },
  },
} as const;

export default styles;

import { Box, Grid, Stack, Typography } from '@mui/material';
import { get, isEmpty } from 'lodash';
import { Fragment } from 'react';

export interface FieldType {
  label: string;
  path?: string;
  renderValue?: (value: any, detail: any) => void;
}
interface InfoBlockProps {
  detail?: unknown;
  title: string;
  fields: FieldType[];
}
const InfoBlock = ({ detail, title, fields }: InfoBlockProps) => {
  return (
    <Box
      p={4}
      maxWidth="md"
      width={1}
      borderRadius="10px"
      bgcolor="white"
      boxShadow={1}
    >
      <Stack direction="row" justifyContent="space-between">
        <Typography fontSize={24} fontWeight={500}>
          {title}
        </Typography>
      </Stack>
      <Stack spacing={2} mt={4}>
        <Grid container rowGap={1.5}>
          {fields.map((field: FieldType) => {
            const fieldValue = field.path && (get(detail, field.path) as any);
            const isEmptyValue =
              !(fieldValue || fieldValue === 0) ||
              (Array.isArray(fieldValue) && isEmpty(fieldValue));

            const renderFieldValue =
              field.renderValue && field.renderValue(fieldValue, detail);
            const isEmptyDisplay = !renderFieldValue && isEmptyValue;
            const displayFieldValue = isEmptyDisplay ? '情報なし' : fieldValue;
            return (
              <Fragment key={`${field.path}${field.label}`}>
                <Grid item xs={4} fontWeight="bold">
                  {field.label}
                </Grid>
                <Grid item xs={8}>
                  {field.path && (
                    <Typography
                      fontSize={16}
                      whiteSpace="pre-line"
                      color={isEmptyDisplay ? 'neutral4' : 'text.primary'}
                    >
                      {renderFieldValue || displayFieldValue}
                    </Typography>
                  )}
                </Grid>
              </Fragment>
            );
          })}
        </Grid>
      </Stack>
    </Box>
  );
};

export default InfoBlock;

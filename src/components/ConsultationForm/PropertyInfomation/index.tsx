import { LoadingButton } from '@mui/lab';
import { Button, Stack } from '@mui/material';
import type {
  ConsultationRecord,
  PropertyInformationPayload,
} from 'hooks/customer/useConsultationForm/types';
import useBreakpoint from 'hooks/useBreakpoint';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';

import EditDialog from '../EditDialog';
import Fields from './fields';

export interface PropertyInformationProps {
  record: ConsultationRecord;
  onSubmit: (values: PropertyInformationPayload, isDirty: boolean) => void;
  onCancel: (isDirty: boolean) => void;
  loading: boolean;
}
const PropertyInformation = ({
  onSubmit,
  onCancel,
  record,
  loading,
}: PropertyInformationProps) => {
  const { propertyInformation } = record;
  const {
    control,
    handleSubmit,
    formState: { isDirty },
  } = useHookForm<PropertyInformationPayload>({
    mode: 'onTouched',
    defaultValues: propertyInformation,
  });
  const isBreakpoint = useBreakpoint({});

  return (
    <form
      id="property-form"
      onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}
    >
      {!isBreakpoint ? (
        <>
          <Fields control={control} />
          <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
            <Button variant="outlined" onClick={() => onCancel(isDirty)}>
              {t('global.cancel')}
            </Button>
            <LoadingButton
              color="secondary"
              variant="contained"
              fullWidth
              loading={loading}
              type="submit"
              sx={{ maxWidth: 112 }}
            >
              {t('global.settle')}
            </LoadingButton>
          </Stack>
        </>
      ) : (
        <EditDialog
          loading={loading}
          title={t('consultationRecord.propertyInformation')}
          formName="property-form"
          onCancel={() => onCancel(isDirty)}
        >
          <Fields control={control} />
        </EditDialog>
      )}
    </form>
  );
};

export default PropertyInformation;

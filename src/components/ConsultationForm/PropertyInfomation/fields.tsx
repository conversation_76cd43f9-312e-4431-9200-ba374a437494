import { InputAdornment, Stack } from '@mui/material';
import { Radio, TextField } from 'components/Form';
import type { PropertyInformationPayload } from 'hooks/customer/useConsultationForm/types';
import i18n from 'i18n';
import { omit } from 'lodash';
import type { Control } from 'react-hook-form';
import { PENSION_TYPES, RadioOptions } from 'utils/constants';
import Helper from 'utils/helpers';

export interface PropertyInformationProps {
  control: Control<PropertyInformationPayload>;
}
const Fields = ({ control }: PropertyInformationProps) => {
  const { t } = i18n;
  return (
    <>
      <Stack spacing={2}>
        <TextField
          control={control}
          name="firstValueOfRealEstate"
          label={t('consultationRecord.firstValueOfRealEstate')}
          placeholder={t('placeholder.firstValueOfRealEstate')}
          type="number"
          labelCol={4}
          inputProps={{ min: 0, max: 1000000000000 }}
          endAdornment={
            <InputAdornment position="end" sx={{ mr: '12px', width: 44 }}>
              万円
            </InputAdornment>
          }
        />
        <TextField
          control={control}
          name="currentValueOfRealEstate"
          label={t('consultationRecord.currentValueOfRealEstate')}
          placeholder={t('placeholder.currentValueOfRealEstate')}
          type="number"
          labelCol={4}
          inputProps={{ min: 0, max: 1000000000000 }}
          endAdornment={
            <InputAdornment position="end" sx={{ mr: '12px', width: 44 }}>
              万円
            </InputAdornment>
          }
        />
        <TextField
          control={control}
          name="loan"
          label={t('consultationRecord.loan')}
          placeholder={t('placeholder.loan')}
          type="number"
          labelCol={4}
          inputProps={{ min: 0, max: 1000000000000 }}
          endAdornment={
            <InputAdornment position="end" sx={{ mr: '12px', width: 44 }}>
              万円
            </InputAdornment>
          }
        />
        <TextField
          control={control}
          name="depositAndSaving"
          placeholder={t('placeholder.depositAndSaving')}
          label={t('consultationRecord.depositAndSaving')}
          type="number"
          labelCol={4}
          inputProps={{ min: 0, max: 1000000000000 }}
          endAdornment={
            <InputAdornment position="end" sx={{ mr: '12px' }}>
              円
            </InputAdornment>
          }
        />
        <TextField
          control={control}
          name="depositAndSavingOfPartner"
          label={t('consultationRecord.depositAndSavingOfPartner')}
          placeholder={t('placeholder.depositAndSavingOfPartner')}
          type="number"
          labelCol={4}
          inputProps={{ min: 0, max: 1000000000000 }}
          endAdornment={
            <InputAdornment position="end" sx={{ mr: '12px' }}>
              円
            </InputAdornment>
          }
        />
        <Radio
          control={control}
          labelCol={4}
          name="lifeInsurance"
          label={t('consultationRecord.lifeInsurance')}
          data={Helper.convertObjectToOptions(omit(RadioOptions, 'other'))}
        />
        <Radio
          control={control}
          labelCol={4}
          name="typeOfPensionOfYourself"
          label={t('consultationRecord.typeOfPensionOfYourself')}
          data={PENSION_TYPES.map((item) => ({ _id: item, value: item }))}
        />
        <Radio
          control={control}
          labelCol={4}
          name="typeOfPensionOfPartner"
          label={t('consultationRecord.typeOfPensionOfPartner')}
          data={PENSION_TYPES.map((item) => ({ _id: item, value: item }))}
        />
        <TextField
          control={control}
          labelCol={4}
          name="otherProperties"
          label={t('consultationRecord.otherProperties')}
          placeholder={t('placeholder.otherProperties')}
          multiline
          minRows={5}
          maxLength={300}
        />
        <TextField
          control={control}
          labelCol={4}
          name="supplementaryInfo"
          label={t('consultationRecord.supplementaryInfo')}
          placeholder={t('placeholder.supplementaryInfo')}
          multiline
          minRows={5}
          maxLength={300}
        />
      </Stack>
    </>
  );
};

export default Fields;

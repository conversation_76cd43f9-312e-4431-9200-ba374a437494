import i18n, { t } from 'i18n';
import { Regex } from 'utils/constants';
import type { AnyObjectSchema } from 'yup';
import { object, string } from 'yup';

const schema: AnyObjectSchema = object().shape({
  age: string().required(i18n.t('validation.requiredField')),
  job: string()
    .max(30, i18n.t('validation.maxLength', { number: 30 }))
    .required(i18n.t('validation.requiredField'))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
  firstName: string()
    .required(t('validation.requiredField'))
    .max(10, t('validation.maxLength', { number: 10 }))
    .trim(t('validation.notAllowedWhiteSpace')),
  lastName: string()
    .max(10, t('validation.maxLength', { number: 10 }))
    .required(t('validation.requiredField'))
    .trim(t('validation.notAllowedWhiteSpace')),
  firstKatakanaName: string()
    .max(10, t('validation.maxLength', { number: 10 }))
    .required(t('validation.requiredField'))
    .trim(t('validation.notAllowedWhiteSpace'))
    .matches(Regex.KATAKANA, t('validation.invalidField')),
  lastKatakanaName: string()
    .max(10, t('validation.maxLength', { number: 10 }))
    .required(t('validation.requiredField'))
    .trim(t('validation.notAllowedWhiteSpace'))
    .matches(Regex.KATAKANA, t('validation.invalidField')),
});

export default schema;

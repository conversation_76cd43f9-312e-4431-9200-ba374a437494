import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { But<PERSON>, Stack } from '@mui/material';
import type {
  ConsultationRecord,
  PartnerInformationPayload,
} from 'hooks/customer/useConsultationForm/types';
import useBreakpoint from 'hooks/useBreakpoint';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import { useEffect, useMemo } from 'react';

import EditDialog from '../EditDialog';
import Fields from './fields';
import schema from './schema';

export interface PartnerInformationProps {
  onSubmit: (values: PartnerInformationPayload, isDirty: boolean) => void;
  onCancel: (isDirty: boolean) => void;
  loading: boolean;
  record: ConsultationRecord;
}

const PartnerInformation = ({
  onSubmit,
  onCancel,
  loading,
  record,
}: PartnerInformationProps) => {
  const isBreakpoint = useBreakpoint({});
  const { partner } = record;
  const defaultValues = useMemo(
    () =>
      partner && {
        ...partner,
        age: partner?.age?._id,
      },
    [partner],
  );
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useHookForm<PartnerInformationPayload>({
    mode: 'onTouched',
    defaultValues,
    resolver: yupResolver(schema),
  });
  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  return (
    <form
      id="partner-form"
      onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}
    >
      {!isBreakpoint ? (
        <>
          <Fields control={control} />
          <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
            <Button variant="outlined" onClick={() => onCancel(isDirty)}>
              {t('global.cancel')}
            </Button>
            <LoadingButton
              color="secondary"
              variant="contained"
              fullWidth
              type="submit"
              sx={{ maxWidth: 112 }}
              loading={loading}
            >
              {t('global.settle')}
            </LoadingButton>
          </Stack>
        </>
      ) : (
        <EditDialog
          formName="partner-form"
          loading={loading}
          title={t('consultationRecord.partnerInformation')}
          onCancel={() => onCancel(isDirty)}
        >
          <Fields control={control} />
        </EditDialog>
      )}
    </form>
  );
};

export default PartnerInformation;

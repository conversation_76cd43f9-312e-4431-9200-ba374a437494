import { Box, Grid, Stack } from '@mui/material';
import { Select, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import { useFetchList } from 'hooks';
import type { PartnerInformationPayload } from 'hooks/customer/useConsultationForm/types';
import { t } from 'i18n';
import type { IAge } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import type { Control } from 'react-hook-form';
import { ANNUAL_INCOME } from 'utils/constants';

const PartnerInformation = ({
  control,
}: {
  control: Control<PartnerInformationPayload, any>;
}) => {
  const { list: ageList } = useFetchList<IAge>(resourceQuery.ages);

  return (
    <Stack spacing={2}>
      <Stack rowGap={{ tablet: 2 }}>
        <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
          <Label label="お名前" required labelCol={4} />
          <Grid
            item
            xs={12}
            tablet={8}
            mt={0}
            fontSize={{ xs: 14, tablet: 16 }}
          >
            <Stack spacing={{ xs: 1, tablet: 2 }} direction="row">
              <Box width={1}>
                <TextField
                  labelCol={0}
                  control={control}
                  name="lastName"
                  maxLength={10}
                  placeholder="姓"
                />
              </Box>
              <Box width={1}>
                <TextField
                  labelCol={0}
                  control={control}
                  name="firstName"
                  maxLength={10}
                  placeholder="名"
                />
              </Box>
            </Stack>
          </Grid>
        </Grid>
        <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
          <Label label="お名前 (フリガナ)" required labelCol={4} />
          <Grid
            item
            xs={12}
            tablet={8}
            mt={0}
            fontSize={{ xs: 14, tablet: 16 }}
          >
            <Stack spacing={{ xs: 1, tablet: 2 }} direction="row">
              <Box width={1}>
                <TextField
                  labelCol={0}
                  control={control}
                  name="lastKatakanaName"
                  maxLength={10}
                  placeholder="姓（フリガナ）"
                />
              </Box>
              <Box width={1}>
                <TextField
                  labelCol={0}
                  control={control}
                  name="firstKatakanaName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.firstKanaName')}
                />
              </Box>
            </Stack>
          </Grid>
        </Grid>
      </Stack>
      <Select
        data={ageList}
        control={control}
        name="age"
        label={t('consultationRecord.age')}
        required
        labelCol={4}
        placeholder={t('placeholder.ageSelect')}
      />
      <TextField
        control={control}
        required
        name="job"
        label={t('consultationRecord.job')}
        placeholder={t('placeholder.job')}
        maxLength={30}
        labelCol={4}
      />
      <Select
        data={ANNUAL_INCOME.map((item) => ({ _id: item, value: item }))}
        control={control}
        name="annualIncome"
        label={t('consultationRecord.annualIncome')}
        placeholder={t('placeholder.annualIncome')}
        labelCol={4}
      />
    </Stack>
  );
};

export default PartnerInformation;

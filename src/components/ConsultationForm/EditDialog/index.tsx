import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Stack,
  Typography,
} from '@mui/material';
import BackButton from 'components/UI/BackButton';
import { t } from 'i18n';
import type { ReactNode } from 'react';

import styles from './styles';

interface IEditDialog {
  onCancel: () => void;
  formName: string;
  children: ReactNode;
  loading?: boolean;
  title?: string;
}
const EditDialog = ({
  onCancel,
  loading,
  formName,
  children,
  title,
}: IEditDialog) => {
  return (
    <Dialog
      fullScreen
      open={true}
      sx={styles.dialog}
      aria-labelledby="view-edit-dialog-title"
    >
      <DialogTitle sx={styles.dialogTitle}>
        <Box position="relative">
          <BackButton onClick={onCancel} sx={styles.backButton} />
          <Typography
            fontSize={18}
            fontWeight={500}
            color="heading"
            textAlign="center"
          >
            {title}
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent className="dialog-content" sx={{ p: '0px' }}>
        <DialogContentText
          sx={{
            padding: '16px',
            backgroundColor: 'white',
            my: '8px',
          }}
          component="div"
        >
          {children}
        </DialogContentText>
      </DialogContent>
      <DialogActions sx={{ p: 2, bgcolor: 'white' }}>
        <Stack spacing={1} direction="row" width={1}>
          <Button variant="outlined" onClick={onCancel} fullWidth>
            {t('global.cancel')}
          </Button>
          <LoadingButton
            color="secondary"
            variant="contained"
            type="submit"
            form={formName}
            fullWidth
            loading={loading}
            sx={{ fontWeight: 'bold' }}
          >
            {t('global.settle')}
          </LoadingButton>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default EditDialog;

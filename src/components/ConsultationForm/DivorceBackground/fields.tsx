import { Box, Grid, Stack } from '@mui/material';
import { CheckBox, Radio, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import { useFetchList } from 'hooks';
import i18n from 'i18n';
import type { ICause } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import { useEffect } from 'react';
import type { Control, UseFormSetValue } from 'react-hook-form';
import { useWatch } from 'react-hook-form';
import { YES_NO_OPTIONS } from 'utils/constants';
import Helper from 'utils/helpers';

interface DivorceBackgroundForm {
  youDivorce: string;
  partnerDivorce: string;
  reason: { check?: string; value: string[]; extraData: string };
  additionalTopics?: string;
}

const Fields = ({
  control,
  setValue,
}: {
  control: Control<DivorceBackgroundForm, any>;
  setValue: UseFormSetValue<DivorceBackgroundForm>;
}) => {
  const { t } = i18n;
  const reasonValue = useWatch({ control, name: 'reason.value' });
  const { list: causeList, entities: causeEntities } = useFetchList<ICause>(
    resourceQuery.divorceCauses,
  );

  const hasExtraId = (causeList || []).find((cause) => cause.hasExtra)?._id;

  useEffect(() => {
    if (hasExtraId && reasonValue && !reasonValue.includes(hasExtraId)) {
      setValue('reason.extraData', '');
    }
  }, [hasExtraId, reasonValue, setValue]);

  return (
    <Stack spacing={2}>
      <Radio
        data={Helper.convertObjectToOptions(YES_NO_OPTIONS)}
        control={control}
        labelCol={4}
        label={t('consultationRecord.youDivorce')}
        name="youDivorce"
        required
      />
      <Radio
        data={Helper.convertObjectToOptions(YES_NO_OPTIONS)}
        control={control}
        labelCol={4}
        label={t('consultationRecord.partnerDivorce')}
        name="partnerDivorce"
        required
      />
      <Box>
        <Grid mt={-2} columnSpacing={4} container columns={12}>
          <Label labelCol={4} label={t('consultationRecord.reason')} required />
          <Grid item xs={12} tablet={8}>
            <Box
              border="solid 1px #edf1f3"
              p={{ xs: 2, tablet: 3 }}
              borderRadius={1}
              ml={{ tablet: -2 }}
              mt={{ xs: '8px', tablet: '28px' }}
            >
              <CheckBox
                name="reason.value"
                layout="vertical"
                labelCol={0}
                data={causeList}
                control={control}
              />
              {(reasonValue || []).find(
                (reason) => causeEntities && causeEntities[reason]?.hasExtra,
              ) && (
                <Box ml={3}>
                  <TextField
                    labelCol={0}
                    placeholder={t('placeholder.reasonExtraData')}
                    control={control}
                    name="reason.extraData"
                    multiline
                    minRows={5}
                    maxLength={500}
                  />
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>
      <TextField
        control={control}
        labelCol={4}
        label="その他ご相談したい内容をご記入下さい"
        placeholder="その他ご相談したい内容をご記入ください"
        minRows={7}
        multiline
        name="additionalTopics"
        maxLength={500}
      />
    </Stack>
  );
};

export default Fields;

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { But<PERSON>, Stack } from '@mui/material';
import { useFetchList } from 'hooks';
import type {
  ConsultationRecord,
  DivorceBackgroundPayload,
} from 'hooks/customer/useConsultationForm/types';
import useBreakpoint from 'hooks/useBreakpoint';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import type { ICause } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import { useEffect, useMemo } from 'react';
import { get } from 'react-hook-form';

import EditDialog from '../EditDialog';
import Fields from './fields';
import schema from './schema';

interface DivorceBackgroundForm {
  youDivorce: string;
  partnerDivorce: string;
  reason: { check?: string; value: string[]; extraData: string };
  additionalTopics?: string;
}

interface DivorceBackgroundProps {
  record: ConsultationRecord;
  onSubmit: (values: DivorceBackgroundPayload, isDirty: boolean) => void;
  defaultValues?: DivorceBackgroundForm;
  onCancel: (isDirty: boolean) => void;
  loading: boolean;
}
const DivorceBackground = ({
  onSubmit,
  onCancel,
  loading,
  record,
}: DivorceBackgroundProps) => {
  const { t } = i18n;
  const backgroundReason = record.backgroundOfDivorce?.reason;

  const backgroundOfDivorce = get(record, 'backgroundOfDivorce');
  const defaultValues = useMemo(
    () =>
      backgroundOfDivorce && {
        ...backgroundOfDivorce,
        reason: {
          value: (backgroundReason || []).map(
            (reason: { value?: string; extraData?: string }) => reason.value,
          ),
          extraData: backgroundReason?.find((reason) => reason.extraData)
            ?.extraData,
        },
      },
    [backgroundOfDivorce, backgroundReason],
  );
  const { list: causeList } = useFetchList<ICause>(resourceQuery.divorceCauses);

  const hasExtraId = (causeList || []).find((cause) => cause.hasExtra)?._id;
  const isBreakpoint = useBreakpoint({});

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { isDirty },
  } = useHookForm<DivorceBackgroundForm>({
    resolver: yupResolver(schema(hasExtraId as string)),
    mode: 'onTouched',
    defaultValues,
  });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  const handleSubmitForm = (values: DivorceBackgroundForm) => {
    const extraOption = causeList.find((item) => item.hasExtra);
    const reason = values.reason.value.map((item) =>
      item === extraOption?._id
        ? { value: item, extraData: values.reason.extraData }
        : { value: item },
    );
    const payload = { ...values, reason };
    onSubmit(payload, isDirty);
  };

  return (
    <form id="divorce-form" onSubmit={handleSubmit(handleSubmitForm)}>
      {!isBreakpoint ? (
        <>
          <Fields control={control} setValue={setValue} />
          <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
            <Button variant="outlined" onClick={() => onCancel(isDirty)}>
              {t('global.cancel')}
            </Button>
            <LoadingButton
              color="secondary"
              variant="contained"
              fullWidth
              loading={loading}
              type="submit"
              sx={{ maxWidth: 112 }}
            >
              {t('global.settle')}
            </LoadingButton>
          </Stack>
        </>
      ) : (
        <EditDialog
          loading={loading}
          title={t('consultationRecord.divorceBackground')}
          formName="divorce-form"
          onCancel={() => onCancel(isDirty)}
        >
          <Fields control={control} setValue={setValue} />
        </EditDialog>
      )}
    </form>
  );
};

export default DivorceBackground;

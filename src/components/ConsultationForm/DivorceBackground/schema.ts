import i18n, { t } from 'i18n';
import type { AnyObjectSchema } from 'yup';
import { array, object, string } from 'yup';

const schema = (hasExtraId: string): AnyObjectSchema =>
  object().shape({
    youDivorce: string().required(i18n.t('validation.requiredField')),
    partnerDivorce: string().required(i18n.t('validation.requiredField')),
    reason: object({
      value: array()
        .min(1, i18n.t('validation.requiredField'))
        .required(i18n.t('validation.requiredField')),
    }),
    extraData: string().when('value', (reasonValue, reasonSchema) => {
      if ((reasonValue || []).includes(hasExtraId)) {
        return string().required(i18n.t('validation.requiredField'));
      }
      return reasonSchema;
    }),
    additionalTopics: string()
      .trim()
      .max(500, t('validation.maxLength', { number: 500 })),
  });

export default schema;

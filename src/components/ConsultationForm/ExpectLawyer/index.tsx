import { LoadingButton } from '@mui/lab';
import { But<PERSON>, Stack } from '@mui/material';
import type {
  ConsultationRecord,
  ExpectLawyerPayload,
} from 'hooks/customer/useConsultationForm/types';
import useBreakpoint from 'hooks/useBreakpoint';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import { useEffect, useMemo } from 'react';
import type { GenderType } from 'utils/constants';

import EditDialog from '../EditDialog';
import Fields from './fields';

export interface IExpectLawyerForm {
  lawyerGender: GenderType;
  lawyerAge: string;
  lawyerConsultation: string[];
}
export interface ExpectLawyerProps {
  onSubmit: (values: ExpectLawyerPayload, isDirty: boolean) => void;
  onCancel: (isDirty: boolean) => void;
  loading?: boolean;
  record: ConsultationRecord;
}
const ExpectLawyer = ({
  onSubmit,
  onCancel,
  record,
  loading,
}: ExpectLawyerProps) => {
  const { t } = i18n;
  const { expectLawyer } = record;
  const isBreakpoint = useBreakpoint({});

  const defaultValues = useMemo(
    () =>
      expectLawyer && {
        lawyerGender: expectLawyer?.gender,
        lawyerAge: expectLawyer?.age?._id,
        lawyerConsultation: expectLawyer?.consultation?.map(
          (field) => field._id,
        ),
      },
    [expectLawyer],
  );
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useHookForm<IExpectLawyerForm>({
    mode: 'onTouched',
    defaultValues,
  });

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const handleSubmitForm = (values: IExpectLawyerForm) => {
    onSubmit(
      {
        gender: values.lawyerGender,
        age: values.lawyerAge,
        consultation: values.lawyerConsultation,
      },
      isDirty,
    );
  };
  return (
    <form id="expect-lawyer-form" onSubmit={handleSubmit(handleSubmitForm)}>
      {!isBreakpoint ? (
        <>
          <Fields control={control} />
          <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
            <Button variant="outlined" onClick={() => onCancel(isDirty)}>
              {t('global.cancel')}
            </Button>
            <LoadingButton
              color="secondary"
              variant="contained"
              fullWidth
              loading={loading}
              type="submit"
              sx={{ maxWidth: 112 }}
            >
              {t('global.settle')}
            </LoadingButton>
          </Stack>
        </>
      ) : (
        <EditDialog
          formName="expect-lawyer-form"
          loading={loading}
          title={t('consultationRecord.expectLawyer')}
          onCancel={() => onCancel(isDirty)}
        >
          <Fields control={control} />
        </EditDialog>
      )}
    </form>
  );
};

export default ExpectLawyer;

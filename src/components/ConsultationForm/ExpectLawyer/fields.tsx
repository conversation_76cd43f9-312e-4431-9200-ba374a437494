import { Stack } from '@mui/material';
import { CheckBox, Radio, Select } from 'components/Form';
import { useFetchList } from 'hooks';
import type { IListItem } from 'hooks/types';
import { t } from 'i18n';
import type { IAge } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import type { Control } from 'react-hook-form';
import type { GenderType } from 'utils/constants';
import { PREFERENCE_GENDER } from 'utils/constants';
import Helper from 'utils/helpers';

export interface IExpectLawyerForm {
  lawyerGender: GenderType;
  lawyerAge: string;
  lawyerConsultation: string[];
}
export interface FieldsProps {
  control: Control<IExpectLawyerForm>;
  nested?: boolean;
}
const Fields = ({ control }: FieldsProps) => {
  const { list: ageList } = useFetchList<IAge>(resourceQuery.ages);
  const { list: consultationList } = useFetchList<IListItem>(
    resourceQuery.consultations,
  );
  return (
    <Stack spacing={{ xs: '20px', tablet: '28px' }}>
      <Radio
        control={control}
        labelCol={4}
        name="lawyerGender"
        label={t('consultationRecord.lawyerGender')}
        data={Helper.convertObjectToOptions(PREFERENCE_GENDER)}
      />
      <Select
        control={control}
        labelCol={4}
        name="lawyerAge"
        label={t('consultationRecord.lawyerAge')}
        placeholder={t('placeholder.lawyerAge')}
        data={ageList}
      />
      <CheckBox
        layout="horizontal"
        labelCol={4}
        control={control}
        name="lawyerConsultation"
        label={t('consultationRecord.consultation')}
        showSelectAll
        data={consultationList}
        showNothingSpecial
      />
    </Stack>
  );
};

export default Fields;

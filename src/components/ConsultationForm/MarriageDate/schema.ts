import dayjs from 'dayjs';
import { t } from 'i18n';
import type { AnyObjectSchema } from 'yup';
import { object, string } from 'yup';

const schema: AnyObjectSchema = object().shape({
  marriedDate: string()
    .nullable()
    .required(t('validation.requiredField'))
    .test('validDay', t('validation.invalidMarriedDate'), (value) => {
      if (!dayjs(value).isValid()) {
        return false;
      }
      return true;
    }),
  isSeparated: string().required(t('validation.requiredField')),
  separationDate: string()
    .nullable()
    .when('isSeparated', {
      is: 'yes',
      then: string()
        .nullable()
        .required(t('validation.requiredField'))
        .test('validDay', t('validation.invalidSeparationDate'), (value) => {
          if (!dayjs(value).isValid()) {
            return false;
          }
          return true;
        }),
    }),
});

export default schema;

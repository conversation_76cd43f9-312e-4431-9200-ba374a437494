import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { But<PERSON>, Stack } from '@mui/material';
import type {
  ConsultationRecord,
  MarriageInformationPayload,
} from 'hooks/customer/useConsultationForm/types';
import useBreakpoint from 'hooks/useBreakpoint';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import { useEffect } from 'react';

import EditDialog from '../EditDialog';
import Fields from './fields';
import schema from './schema';

export interface MarriageInformationProps {
  onSubmit: (values: MarriageInformationPayload, isDirty: boolean) => void;
  record: ConsultationRecord;
  onCancel: (isDirty: boolean) => void;
  loading: boolean;
}

const MarriageInformation = ({
  onSubmit,
  onCancel,
  loading,
  record,
}: MarriageInformationProps) => {
  const { marriageInformation } = record;
  const { t } = i18n;
  const {
    control,
    handleSubmit,
    setValue,
    clearErrors,
    reset,
    formState: { isDirty },
  } = useHookForm<MarriageInformationPayload>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues: marriageInformation,
  });
  const isBreakpoint = useBreakpoint({});

  useEffect(() => {
    reset(marriageInformation);
  }, [reset, marriageInformation]);

  return (
    <form
      id="marriage-form"
      onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}
    >
      {!isBreakpoint ? (
        <>
          <Fields
            control={control}
            clearErrors={clearErrors}
            setValue={setValue}
            defaultValues={marriageInformation}
          />
          <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
            <Button variant="outlined" onClick={() => onCancel(isDirty)}>
              {t('global.cancel')}
            </Button>
            <LoadingButton
              color="secondary"
              variant="contained"
              fullWidth
              type="submit"
              sx={{ maxWidth: 112 }}
              loading={loading}
            >
              {t('global.settle')}
            </LoadingButton>
          </Stack>
        </>
      ) : (
        <EditDialog
          formName="marriage-form"
          title={t('consultationRecord.marriageInformation')}
          loading={loading}
          onCancel={() => onCancel(isDirty)}
        >
          <Fields
            control={control}
            clearErrors={clearErrors}
            setValue={setValue}
            defaultValues={marriageInformation}
          />
        </EditDialog>
      )}
    </form>
  );
};

export default MarriageInformation;

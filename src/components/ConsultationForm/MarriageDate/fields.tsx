import { Stack } from '@mui/material';
import { DatePicker, Radio } from 'components/Form';
import dayjs from 'dayjs';
import type { MarriageInformationPayload } from 'hooks/customer/useConsultationForm/types';
import { useEffect } from 'react';
import type {
  Control,
  UseFormClearErrors,
  UseFormSetValue,
} from 'react-hook-form';
import { useWatch } from 'react-hook-form';
import { YesNoOptions } from 'utils/constants';
import Helper from 'utils/helpers';

export interface IFields {
  defaultValues?: MarriageInformationPayload;
  control: Control<MarriageInformationPayload>;
  setValue: UseFormSetValue<MarriageInformationPayload>;
  clearErrors: UseFormClearErrors<MarriageInformationPayload>;
}

const Fields = ({ setValue, clearErrors, control, defaultValues }: IFields) => {
  const isSeparated = useWatch({
    control,
    name: 'isSeparated',
    defaultValue: defaultValues?.isSeparated,
  });
  useEffect(() => {
    if (isSeparated === 'no') {
      clearErrors('separationDate');
      setValue('separationDate', '');
    }
  }, [clearErrors, isSeparated, setValue]);

  return (
    <Stack spacing={{ xs: '20px', tablet: 1 }}>
      <DatePicker
        labelCol={4}
        control={control}
        name="marriedDate"
        label="結婚日"
        maxDate={dayjs()}
        defaultCalendarMonth={dayjs('1980', 'YYYY')}
        required
      />
      <Radio
        labelCol={4}
        control={control}
        name="isSeparated"
        label="現在、ご夫婦は別居していますか？"
        required
        data={Helper.convertObjectToOptions(YesNoOptions)}
      />
      {isSeparated === 'yes' && (
        <DatePicker
          labelCol={4}
          control={control}
          name="separationDate"
          label="別居開始日"
          required
          defaultCalendarMonth={dayjs('2000', 'YYYY')}
        />
      )}
    </Stack>
  );
};

export default Fields;

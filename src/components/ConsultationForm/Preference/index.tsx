import { LoadingButton } from '@mui/lab';
import { <PERSON><PERSON>, Stack } from '@mui/material';
import type {
  ConsultationRecord,
  PreferencePayload,
} from 'hooks/customer/useConsultationForm/types';
import useBreakpoint from 'hooks/useBreakpoint';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import { useEffect, useMemo } from 'react';
import { ProviderType } from 'utils/constants';

import EditDialog from '../EditDialog';
import Fields from './fields';
import type { PreferenceFormValues } from './schema';

export interface ExpectLawyerProps {
  onSubmit: (values: PreferencePayload, isDirty: boolean) => void;
  onCancel: (isDirty: boolean) => void;
  loading?: boolean;
  record: ConsultationRecord;
  providerType: ProviderType;
}
const Preference = ({
  onSubmit,
  onCancel,
  record,
  loading,
  providerType,
}: ExpectLawyerProps) => {
  const { t } = i18n;
  const { expectLawyer, expectCounselor } = record;
  const isBreakpoint = useBreakpoint({});

  const defaultValues = useMemo(
    () =>
      providerType === ProviderType.LAWYER
        ? expectLawyer && {
            preferenceGender: expectLawyer?.gender,
            preferenceAge: expectLawyer?.age?._id,
            preferenceConsultation: expectLawyer.nothingSpecial
              ? ['NOTHING_SPECIAL']
              : expectLawyer?.consultation?.map((field) => field._id),
          }
        : expectCounselor && {
            preferenceGender: expectCounselor?.gender,
            preferenceAge: expectCounselor?.age?._id,
            preferenceConsultation: expectCounselor.nothingSpecial
              ? ['NOTHING_SPECIAL']
              : expectCounselor?.consultation?.map((field) => field._id),
          },
    [expectCounselor, expectLawyer, providerType],
  );
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useHookForm<PreferenceFormValues>({
    mode: 'onTouched',
    defaultValues,
  });

  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const handleSubmitForm = (values: PreferenceFormValues) => {
    onSubmit(
      {
        gender: values.preferenceGender,
        age: values.preferenceAge,
        consultation: values.preferenceConsultation,
      },
      isDirty,
    );
  };
  return (
    <form id="expect-lawyer-form" onSubmit={handleSubmit(handleSubmitForm)}>
      {!isBreakpoint ? (
        <>
          <Fields control={control} providerType={providerType} />
          <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
            <Button variant="outlined" onClick={() => onCancel(isDirty)}>
              {t('global.cancel')}
            </Button>
            <LoadingButton
              color="secondary"
              variant="contained"
              fullWidth
              loading={loading}
              type="submit"
              sx={{ maxWidth: 112 }}
            >
              {t('global.settle')}
            </LoadingButton>
          </Stack>
        </>
      ) : (
        <EditDialog
          formName="expect-lawyer-form"
          loading={loading}
          title={
            providerType === ProviderType.LAWYER
              ? t('consultationRecord.expectLawyer')
              : '希望するカウンセラー'
          }
          onCancel={() => onCancel(isDirty)}
        >
          <Fields control={control} providerType={providerType} />
        </EditDialog>
      )}
    </form>
  );
};

export default Preference;

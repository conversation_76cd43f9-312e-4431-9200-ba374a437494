import { GenderType } from 'utils/constants';
import type { InferType } from 'yup';
import { array, mixed, object, string } from 'yup';

const schema = object({
  preferenceGender: mixed<GenderType>().oneOf(Object.values(GenderType)),
  preferenceAge: string(),
  preferenceConsultation: array().of(string().required()),
});

export type PreferenceFormValues = InferType<typeof schema>;
export default schema;

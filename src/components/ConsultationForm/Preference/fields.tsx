import { Stack } from '@mui/material';
import { CheckBox, Radio, Select } from 'components/Form';
import { useFetchList } from 'hooks';
import type { IListItem } from 'hooks/types';
import { t } from 'i18n';
import type { IAge } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import type { Control } from 'react-hook-form';
import { PREFERENCE_GENDER, ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

import type { PreferenceFormValues } from './schema';

export interface FieldsProps {
  control: Control<PreferenceFormValues>;
  providerType: ProviderType;
}
const PreferenceFields = ({ control, providerType }: FieldsProps) => {
  const { list: ageList } = useFetchList<IAge>(resourceQuery.ages);
  const { list: consultationList } = useFetchList<IListItem>({
    ...resourceQuery.consultations,
    customParams: {
      ...resourceQuery.consultations.customParams,
      providerType: [providerType],
    },
  });
  const isLawyer = providerType === ProviderType.LAWYER;

  return (
    <Stack spacing={{ xs: '30px', tablet: '28px' }}>
      <Radio
        control={control}
        labelCol={4}
        name="preferenceGender"
        label={
          isLawyer ? t('consultationRecord.lawyerGender') : 'カウンセラーの性別'
        }
        data={Helper.convertObjectToOptions(PREFERENCE_GENDER)}
      />
      <Select
        control={control}
        labelCol={4}
        name="preferenceAge"
        label={
          isLawyer ? t('consultationRecord.lawyerAge') : 'カウンセラーの年齢'
        }
        placeholder={
          isLawyer ? t('placeholder.lawyerAge') : 'カウンセラーの年齢を選択'
        }
        data={ageList}
      />
      <CheckBox
        layout="horizontal"
        labelCol={4}
        control={control}
        name="preferenceConsultation"
        label={isLawyer ? t('consultationRecord.consultation') : '得意分野'}
        showSelectAll
        data={consultationList}
        showNothingSpecial
      />
    </Stack>
  );
};

export default PreferenceFields;

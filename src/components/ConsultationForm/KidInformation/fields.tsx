import { Box, Button, IconButton, Stack, Typography } from '@mui/material';
import { Radio, Select } from 'components/Form';
import { LayoutGroup, motion } from 'framer-motion';
import type { KidsFormPayload } from 'hooks/customer/useConsultationForm/types';
import i18n from 'i18n';
import { AddIcon, TrashIcon } from 'icons';
import { isEmpty, omit, times } from 'lodash';
import { useEffect } from 'react';
import type { Control, UseFormSetValue } from 'react-hook-form';
import { useFieldArray, useWatch } from 'react-hook-form';
import { Gender, RadioOptions, SchoolTypeOptions } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

export interface IFields {
  control: Control<KidsFormPayload>;
  setValue: UseFormSetValue<KidsFormPayload>;
}
const KidInformation = ({ control, setValue }: IFields) => {
  const { t } = i18n;
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'kids',
  });
  const hasKid = useWatch({
    control,
    name: 'hasKid',
  });

  useEffect(() => {
    if (hasKid === 'yes' && fields.length === 0) {
      append({ age: '' });
    }
    if (hasKid === 'no') {
      setValue('kids', []);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [append, hasKid, setValue]);

  return (
    <LayoutGroup>
      <Radio
        labelCol={4}
        control={control}
        name="hasKid"
        label={t('consultationRecord.kids')}
        helperText={t('consultationRecord.under18')}
        required
        data={Helper.convertObjectToOptions(omit(RadioOptions, ['other']))}
      />
      {!isEmpty(fields) && (
        <Typography
          fontWeight="bold"
          mt={{ xs: '20px', tablet: 0 }}
          fontSize={{ xs: 14, tablet: 16 }}
        >
          {t('consultationRecord.child')}:
        </Typography>
      )}

      {fields.map((_, index) => {
        return (
          <motion.div key={_.id} layout>
            <Box display="flex" alignItems="center" mt={{ xs: 1, tablet: 2 }}>
              <Box sx={styles.kidFormWrapper}>
                <Stack spacing={{ xs: '20px', tablet: 1 }}>
                  <Radio
                    labelCol={4}
                    control={control}
                    name={`kids.${index}.gender`}
                    label={t('consultationRecord.kidGender')}
                    data={Helper.convertObjectToOptions(Gender)}
                  />
                  <Select
                    data={times(18).map((i) => ({
                      _id: i,
                      value: t('consultationRecord.ageNumber', { number: i }),
                    }))}
                    labelCol={4}
                    control={control}
                    name={`kids.${index}.age`}
                    label={t('consultationRecord.kidAge')}
                    placeholder={t('placeholder.childAge')}
                  />
                  <Radio
                    labelCol={4}
                    control={control}
                    name={`kids.${index}.schoolType`}
                    label={t('consultationRecord.schoolType')}
                    data={Helper.convertObjectToOptions(SchoolTypeOptions)}
                  />
                </Stack>
              </Box>
              <Box ml={{ xs: 2, tablet: 3 }}>
                <IconButton
                  sx={{ svg: { color: 'neutral7' } }}
                  className="whiteOutlined tabletStyle"
                  disabled={fields.length === 1}
                  onClick={() => remove(index)}
                >
                  <TrashIcon />
                </IconButton>
              </Box>
            </Box>
          </motion.div>
        );
      })}
      {hasKid === 'yes' && (
        <motion.div key="add-button" layout>
          <Button
            variant="tertiary"
            onClick={() => append({ age: '' })}
            sx={styles.addButton}
            fullWidth
            className="tabletStyle"
            startIcon={<AddIcon />}
            color="primary"
          >
            {t('global.add')}
          </Button>
        </motion.div>
      )}
    </LayoutGroup>
  );
};

export default KidInformation;

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Button, Stack } from '@mui/material';
import type {
  ConsultationRecord,
  KidsFormPayload,
  KidsPayload,
} from 'hooks/customer/useConsultationForm/types';
import useBreakpoint from 'hooks/useBreakpoint';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import { isEmpty } from 'lodash';
import { useEffect, useMemo } from 'react';

import EditDialog from '../EditDialog';
import Fields from './fields';
import schema from './schema';

export interface KidInformationProps {
  onSubmit: (values: KidsPayload, isDirty: boolean) => void;
  record: ConsultationRecord;
  onCancel: (isDirty: boolean) => void;
  loading: boolean;
}
const KidInformation = ({
  onSubmit,
  onCancel,
  loading,
  record,
}: KidInformationProps) => {
  const { t } = i18n;
  const kids = record?.kids?.kids;
  const kidsDefaultOption = isEmpty(kids) ? 'no' : 'yes';
  const kidsCheck = kids ? kidsDefaultOption : undefined;

  const defaultValues = useMemo(
    () =>
      kids && {
        kids,
        hasKid: kidsCheck,
      },
    [kids, kidsCheck],
  );
  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { isDirty },
  } = useHookForm<KidsFormPayload>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues,
  });
  const isBreakpoint = useBreakpoint({});

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  return (
    <form
      id="kid-form"
      onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}
    >
      {!isBreakpoint ? (
        <>
          <Fields control={control} setValue={setValue} />
          <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
            <Button variant="outlined" onClick={() => onCancel(isDirty)}>
              {t('global.cancel')}
            </Button>
            <LoadingButton
              color="secondary"
              variant="contained"
              fullWidth
              type="submit"
              loading={loading}
              sx={{ maxWidth: 112 }}
            >
              {t('global.settle')}
            </LoadingButton>
          </Stack>
        </>
      ) : (
        <EditDialog
          formName="kid-form"
          loading={loading}
          title={t('consultationRecord.kidInformation')}
          onCancel={() => onCancel(isDirty)}
        >
          <Fields control={control} setValue={setValue} />
        </EditDialog>
      )}
    </form>
  );
};

export default KidInformation;

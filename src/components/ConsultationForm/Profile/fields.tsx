import { Box, Grid, Stack, Typography } from '@mui/material';
import { Radio, Select, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import { useFetchList } from 'hooks';
import type { IUpdateCustomerProfile } from 'hooks/useAuth/types';
import { t } from 'i18n';
import type { IAge, IPrefecture } from 'models/resource/interface';
import resourceQuery from 'models/resource/query';
import { memo } from 'react';
import type { Control } from 'react-hook-form';
import { ANNUAL_INCOME, Gender } from 'utils/constants';
import Helper from 'utils/helpers';

export interface IFields {
  currentUser: {
    email: string;
    fullName?: string;
    katakanaName?: string;
    phone?: string;
  };
  isLawyer?: boolean;
  control: Control<IUpdateCustomerProfile>;
  firstBooking?: boolean;
}
const Fields = ({ currentUser, control, firstBooking, isLawyer }: IFields) => {
  const { list: prefectureList } = useFetchList<IPrefecture>(
    resourceQuery.prefectures,
  );
  const { list: ageList } = useFetchList<IAge>(resourceQuery.ages);
  return (
    <Stack rowGap={{ xs: '20px', tablet: 2 }}>
      <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
        <Label
          label={t('updateCustomerProfile.fullName')}
          required
          labelCol={4}
        />
        <Grid
          item
          xs={12}
          tablet={8}
          mt={isLawyer ? '12px' : 0}
          fontSize={{ xs: 14, tablet: 16 }}
        >
          {isLawyer ? (
            currentUser.fullName
          ) : (
            <Stack spacing={{ xs: 1, tablet: 2 }} direction="row">
              <Box width={1}>
                <TextField
                  labelCol={0}
                  control={control}
                  name="lastName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.lastName')}
                />
              </Box>
              <Box width={1}>
                <TextField
                  labelCol={0}
                  control={control}
                  name="firstName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.firstName')}
                />
              </Box>
            </Stack>
          )}
        </Grid>
      </Grid>
      <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
        <Label
          label={t('updateCustomerProfile.katakanaName')}
          required
          labelCol={4}
        />
        <Grid
          item
          xs={12}
          tablet={8}
          mt={isLawyer ? '12px' : 0}
          fontSize={{ xs: 14, tablet: 16 }}
        >
          {isLawyer ? (
            currentUser.katakanaName
          ) : (
            <Stack spacing={{ xs: 1, tablet: 2 }} direction="row">
              <Box width={1}>
                <TextField
                  labelCol={0}
                  control={control}
                  name="lastKatakanaName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.lastKanaName')}
                />
              </Box>
              <Box width={1}>
                <TextField
                  labelCol={0}
                  control={control}
                  name="firstKatakanaName"
                  maxLength={10}
                  placeholder={t('updateCustomerProfile.firstKanaName')}
                />
              </Box>
            </Stack>
          )}
        </Grid>
      </Grid>
      <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
        <Label label={t('updateCustomerProfile.email')} labelCol={4} required />
        <Grid
          item
          xs={12}
          tablet={8}
          mt={{ xs: '10px', tablet: '12px' }}
          fontSize={{ xs: 14, tablet: 16 }}
        >
          {currentUser.email}
        </Grid>
      </Grid>
      <Grid container columnSpacing={4} rowSpacing={{ tablet: 2 }}>
        <Label label={t('updateCustomerProfile.phone')} labelCol={4} required />
        <Grid
          item
          xs={12}
          tablet={8}
          mt={isLawyer ? '12px' : 0}
          fontSize={{ xs: 14, tablet: 16 }}
        >
          {isLawyer ? (
            currentUser.phone || (
              <Typography color="hint" mt={1}>
                {t('global.noInfo')}
              </Typography>
            )
          ) : (
            <TextField
              labelCol={0}
              control={control}
              name="phone"
              placeholder={t('updateCustomerProfile.phone')}
              helperText={t('updateCustomerProfile.note')}
            />
          )}
        </Grid>
      </Grid>
      <Radio
        data={Helper.convertObjectToOptions(Gender)}
        control={control}
        label={t('updateCustomerProfile.gender')}
        name="gender"
        required
        labelCol={4}
        viewMode={isLawyer}
      />
      <Select
        data={prefectureList}
        control={control}
        label={t('updateCustomerProfile.address')}
        placeholder={t('updateCustomerProfile.selectPrefecture')}
        name="currentAddress1"
        required
        labelCol={4}
        viewMode={isLawyer}
      />
      {!firstBooking && (
        <>
          <Select
            data={ageList}
            control={control}
            label={t('updateCustomerProfile.age')}
            name="age"
            placeholder={t('placeholder.ageSelect')}
            required
            labelCol={4}
            viewMode={isLawyer}
          />
          <TextField
            control={control}
            label={t('updateCustomerProfile.job')}
            maxLength={30}
            name="job"
            labelCol={4}
            placeholder={t('placeholder.job')}
            required
          />
          <Select
            labelCol={4}
            data={ANNUAL_INCOME.map((i) => ({ _id: i, value: i }))}
            control={control}
            label={t('updateCustomerProfile.annualIncome')}
            placeholder={t('placeholder.annualIncome')}
            name="annualIncome"
          />
        </>
      )}
    </Stack>
  );
};

export default memo(Fields);

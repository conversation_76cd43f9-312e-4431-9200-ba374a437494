import { t } from 'i18n';
import { Regex } from 'utils/constants';
import type { AnyObjectSchema } from 'yup';
import { object, string } from 'yup';

const schema = (isLaywer?: boolean): AnyObjectSchema => {
  if (isLaywer) {
    return object().shape({
      job: string()
        .max(30, t('validation.maxLength', { number: 30 }))
        .required(t('validation.requiredField'))
        .trim(t('validation.notAllowedWhiteSpace')),
    });
  }
  return object().shape({
    firstName: string()
      .required(t('validation.requiredField'))
      .max(10, t('validation.maxLength', { number: 10 }))
      .trim(t('validation.notAllowedWhiteSpace')),
    lastName: string()
      .max(10, t('validation.maxLength', { number: 10 }))
      .required(t('validation.requiredField'))
      .trim(t('validation.notAllowedWhiteSpace')),
    firstKatakanaName: string()
      .max(10, t('validation.maxLength', { number: 10 }))
      .required(t('validation.requiredField'))
      .trim(t('validation.notAllowedWhiteSpace'))
      .matches(Regex.KATAKANA, t('validation.invalidField')),
    lastKatakanaName: string()
      .max(10, t('validation.maxLength', { number: 10 }))
      .required(t('validation.requiredField'))
      .trim(t('validation.notAllowedWhiteSpace'))
      .matches(Regex.KATAKANA, t('validation.invalidField')),
    currentAddress1: string().required(t('validation.requiredField')),
    gender: string().required(t('validation.requiredField')),
    age: string().required(t('validation.requiredField')),
    phone: string()
      .matches(Regex.PHONE, {
        message: t('validation.invalidPhoneNumber'),
        excludeEmptyString: true,
      })
      .required(),
    job: string()
      .max(30, t('validation.maxLength', { number: 30 }))
      .required(t('validation.requiredField'))
      .trim(t('validation.notAllowedWhiteSpace')),
  });
};

export default schema;

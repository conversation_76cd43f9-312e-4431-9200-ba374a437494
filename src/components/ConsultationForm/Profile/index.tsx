import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { <PERSON><PERSON>, Stack } from '@mui/material';
import type { IUpdateCustomerProfile } from 'hooks/useAuth/types';
import useBreakpoint from 'hooks/useBreakpoint';
import useHookForm from 'hooks/useHookForm';
import i18n from 'i18n';
import { useEffect } from 'react';

import EditDialog from '../EditDialog';
import Fields from './fields';
import schema from './schema';

interface IProfileForm {
  onSubmit: (values: IUpdateCustomerProfile, isDirty: boolean) => void;
  onCancel: (isDirty: boolean) => void;
  defaultValues: IUpdateCustomerProfile;
  currentUser: { email: string; isCompletedProfile?: boolean; phone?: string };
  isLawyer?: boolean;
  loading?: boolean;
}
const ProfileForm = ({
  onSubmit,
  onCancel,
  defaultValues,
  currentUser,
  loading,
  isLawyer,
}: IProfileForm) => {
  const { t } = i18n;
  const {
    control,
    handleSubmit,
    formState: { isDirty },
    reset,
    setValue,
  } = useHookForm<IUpdateCustomerProfile>({
    resolver: yupResolver(schema(isLawyer)),
    mode: 'onTouched',
    defaultValues,
  });

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  useEffect(() => {
    if (!currentUser?.phone && currentUser?.isCompletedProfile) {
      setValue('phone', '', { shouldTouch: true, shouldValidate: true });
    }
  }, [currentUser?.isCompletedProfile, currentUser?.phone, setValue]);

  const isBreakpoint = useBreakpoint({});
  return (
    <form
      id="profile-form"
      onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}
    >
      {!isBreakpoint ? (
        <>
          <Fields
            control={control}
            currentUser={currentUser}
            isLawyer={isLawyer}
          />
          <Stack direction="row" spacing={1} justifyContent="flex-end" mt={4}>
            <Button variant="outlined" onClick={() => onCancel(isDirty)}>
              {t('global.cancel')}
            </Button>
            <LoadingButton
              color="secondary"
              variant="contained"
              loading={loading}
              fullWidth
              type="submit"
              sx={{ maxWidth: 112 }}
            >
              {t('global.settle')}
            </LoadingButton>
          </Stack>
        </>
      ) : (
        <EditDialog
          loading={loading}
          formName="profile-form"
          title={t('updateCustomerProfile.yourProfile')}
          onCancel={() => onCancel(isDirty)}
        >
          <Fields
            control={control}
            currentUser={currentUser}
            isLawyer={isLawyer}
          />
        </EditDialog>
      )}
    </form>
  );
};

export default ProfileForm;

import useGlobalState from 'hooks/useGlobalState';
import { isEmpty } from 'lodash';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import type { MouseEventHandler } from 'react';
import { useEffect } from 'react';

import type { ConfirmModalProps } from './modal';

const ConfirmModal = dynamic(() => import('./modal'));

const ConfirmModalContainer = () => {
  const { pathname } = useRouter();
  const {
    openConfirmModal,
    confirmModal,
    toggleConfirmModal,
    setConfirmModal,
  } = useGlobalState();

  useEffect(() => {
    if (!isEmpty(confirmModal)) {
      const { closeOnNavigate = true } = confirmModal as ConfirmModalProps;
      if (closeOnNavigate) {
        toggleConfirmModal(false);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, toggleConfirmModal]);

  if (openConfirmModal) {
    const { onCancel, onConfirm, onClose } = confirmModal as ConfirmModalProps;
    const handleCancel = () => {
      if (onCancel) {
        onCancel();
      }
      toggleConfirmModal(false);
    };
    const handleConfirm: MouseEventHandler<HTMLButtonElement> = (e) => {
      if (e) {
        e.stopPropagation();
      }
      if (onConfirm) {
        onConfirm(e);
      }
      setTimeout(() => toggleConfirmModal(false), 0);
    };
    const handleOnClose = () => {
      if (onClose) {
        onClose();
        setConfirmModal({});
      }
    };
    return (
      <ConfirmModal
        {...(confirmModal as ConfirmModalProps)}
        open={openConfirmModal}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        onClose={handleOnClose}
      />
    );
  }
  return null;
};

export default ConfirmModalContainer;

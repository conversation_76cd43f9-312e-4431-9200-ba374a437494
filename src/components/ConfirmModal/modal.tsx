import type { LoadingButtonProps } from '@mui/lab/LoadingButton';
import LoadingButton from '@mui/lab/LoadingButton';
import type {
  Breakpoint,
  DialogActionsProps,
  DialogContentProps,
  DialogContentTextProps,
  DialogProps,
} from '@mui/material';
import {
  Box,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Stack,
  Typography,
} from '@mui/material';
import i18n from 'i18n';
import { FailedIcon, GrayInfoIcon, SuccessIcon, WarningIcon } from 'icons';
import type { MouseEventHandler, ReactNode } from 'react';

import styles from './styles';

export const ICON = {
  success: <SuccessIcon />,
  warning: <FailedIcon />,
  error: <WarningIcon />,
  info: <GrayInfoIcon />,
};
export interface ConfirmModalProps {
  open?: boolean;
  maxWidth?: false | Breakpoint;
  onClose?: () => void;
  onCancel?: () => void;
  onConfirm?: MouseEventHandler<HTMLButtonElement>;
  hideCancelButton?: boolean;
  dialogProps?: Omit<DialogProps, 'open'>;
  dialogContentProps?: DialogContentProps;
  dialogContentTextProps?: DialogContentTextProps;
  dialogActionsProps?: DialogActionsProps;
  confirmLoading?: boolean;
  content?: string | ReactNode;
  contentAlign?: 'right' | 'left' | 'inherit' | 'center' | 'justify';
  icon?: 'success' | 'error' | 'warning' | 'info';
  title?: string | ReactNode;
  confirmText?: string;
  cancelText?: string;
  hideActions?: boolean;
  closeOnNavigate?: boolean;
  buttonLayout?: string;
  confirmButtonProps?: LoadingButtonProps;
  cancelButtonProps?: LoadingButtonProps;
  children?: string | ReactNode;
}
const ConfirmModal = ({
  open = false,
  maxWidth = 'mw',
  onClose,
  onCancel,
  onConfirm,
  hideCancelButton = false,
  dialogProps,
  dialogContentProps,
  dialogContentTextProps,
  dialogActionsProps,
  confirmLoading = false,
  content,
  contentAlign = 'center',
  icon,
  title,
  confirmText = i18n.t('global.ok'),
  cancelText = i18n.t('global.cancel'),
  hideActions = false,
  buttonLayout = 'horizontal',
  confirmButtonProps,
  cancelButtonProps,
  children,
}: ConfirmModalProps) => {
  const { t } = i18n;

  return (
    <Dialog
      {...dialogProps}
      open={open}
      maxWidth={maxWidth}
      fullWidth
      scroll="paper"
      PaperProps={{
        sx: styles.paper,
      }}
      onClose={(_, reason) => {
        if (reason !== 'backdropClick') {
          if (onClose) {
            onClose();
          }
        }
      }}
    >
      <DialogContent
        className="confirm-dialog-content-container"
        {...dialogContentProps}
        sx={{ p: 0 }}
      >
        {title && (
          <Typography sx={styles.title}>
            {typeof title === 'string' ? t(title) : title}
          </Typography>
        )}
        {icon && (
          <Box sx={styles.icon} className="confirm-dialog_icon">
            {ICON[icon]}
          </Box>
        )}
        <DialogContentText
          fontSize={{ xs: 12, tablet: 14 }}
          textAlign={contentAlign}
          whiteSpace="pre-line"
          color="text.primary"
          className="confirm-dialog_content"
          {...dialogContentTextProps}
        >
          {typeof content === 'string' ? t(content) : content}
        </DialogContentText>
        {children}
      </DialogContent>
      {!hideActions && (
        <DialogActions
          className={`confirm-dialog_action ${buttonLayout}-button`}
          sx={styles.actionWrapper}
          {...dialogActionsProps}
        >
          {!hideCancelButton && (
            <LoadingButton
              variant="outlined"
              size="large"
              className="tabletStyle"
              onClick={onCancel}
              fullWidth
              sx={{ maxWidth: { tablet: '236px' } }}
              {...cancelButtonProps}
            >
              {cancelText}
            </LoadingButton>
          )}
          <LoadingButton
            fullWidth
            variant="contained"
            color="secondary"
            size="large"
            className="tabletStyle"
            loading={confirmLoading}
            sx={{
              maxWidth: { tablet: hideCancelButton ? '368px' : '236px' },
            }}
            onClick={onConfirm}
            loadingIndicator={
              <Stack direction="row" alignItems="center">
                <CircularProgress size={16} sx={{ mr: 1 }} />
                <Typography>{confirmText}</Typography>
              </Stack>
            }
            {...confirmButtonProps}
          >
            {confirmText}
          </LoadingButton>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default ConfirmModal;

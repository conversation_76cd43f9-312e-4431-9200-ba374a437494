/* eslint-disable react-hooks/exhaustive-deps */
import { Box, Button, Stack, Typography } from '@mui/material';
import { useNavigationState } from 'context/NavigationContext';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import i18n from 'i18n';
import { ArrowRightIcon, BlackPathLeft, BlackPathRight } from 'icons';
import get from 'lodash/get';
import type { ICounselorItem, ILawyerItem } from 'models/provider/interface';
import { useRouter } from 'next/router';
import { ProviderType } from 'utils/constants';
import Helper from 'utils/helpers';

const BookingButton = ({
  lawyerId,
  providerType = ProviderType.LAWYER,
  maxWidth = 358,
  freeConsultationMenu,
  detailProvider,
}: {
  lawyerId: string;
  providerType?: ProviderType;
  maxWidth?: any;
  freeConsultationMenu?: boolean;
  detailProvider?: ILawyerItem | ICounselorItem;
}) => {
  const isLawyer = providerType === ProviderType.LAWYER;
  const { state: counselorDetail, setState } = useNavigationState();
  const { t } = i18n;
  const { push, query } = useRouter();
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetail,
    enabled: false,
  });
  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;
  const { data: currentUser } = useFetchUser<CustomerData>({ enabled: false });
  const isCompletedFields =
    currentUser?.isCompletedProfile &&
    consultationDetail.backgroundOfDivorce &&
    consultationDetail.marriageInformation &&
    !Helper.checkIsEmptyObject(consultationDetail.expectLawyer);

  const handleClickContact = () => {
    const webCookie = Helper.getWebCookie();
    const webCookieByGuest = Helper.getWebCookieByGuest();
    const role = get(webCookie, 'role');
    const isDraftUser = get(webCookieByGuest, 'isDraftUser');

    // Helper function to filter out empty/undefined query parameters
    const getValidQueryParams = (baseQuery: Record<string, any>) => {
      const filteredQuery: Record<string, any> = {};
      Object.entries(baseQuery).forEach(([key, value]) => {
        // Special handling for meetingType - include even if it's a valid enum value
        if (
          key === 'meetingType' &&
          (value === 'SINGLE' || value === 'PARTNER')
        ) {
          filteredQuery[key] = value;
        } else if (
          value !== undefined &&
          value !== null &&
          value !== '' &&
          value !== 'undefined'
        ) {
          filteredQuery[key] = value;
        }
      });
      return filteredQuery;
    };

    // Get booking parameters from navigation state or URL parameters
    const getCurrentBookingParams = () => {
      // Check if parameters exist in current URL (not empty and not undefined)
      const hasUrlDuration =
        query?.duration && String(query.duration).trim() !== '';
      const hasUrlMeetingType =
        query?.meetingType && String(query.meetingType).trim() !== '';
      const hasUrlStartSlot =
        query?.startSlot && String(query.startSlot).trim() !== '';
      const hasUrlMenuId = query?.menuId && String(query.menuId).trim() !== '';

      return {
        // Always include these values if available (prioritize navigation state, then URL)
        duration:
          counselorDetail?.duration ||
          (hasUrlDuration ? query.duration : undefined),
        meetingType:
          counselorDetail?.meetingType ||
          (hasUrlMeetingType ? query.meetingType : undefined),
        startSlot:
          counselorDetail?.startSlot ||
          (hasUrlStartSlot ? query.startSlot : undefined),
        menuId:
          counselorDetail?.menuId || (hasUrlMenuId ? query.menuId : undefined),
        // expectDate is from navigation context (calendar selection)
        expectDate: counselorDetail?.expectDate,
      };
    };

    const bookingParams = getCurrentBookingParams();

    if (isDraftUser || !role) {
      if (isLawyer) {
        push('/register');
      } else {
        if (detailProvider)
          setState({
            ...detailProvider,
          });
        push({
          pathname: '/guest/consultation-form',
          query: getValidQueryParams({
            counselorId: query.counselorId,
            duration: bookingParams.duration,
            meetingType: bookingParams.meetingType,
            startSlot: bookingParams.startSlot,
            isFilterCounselor: '',
            date: bookingParams.expectDate,
            menuId: bookingParams.menuId,
            isVerifyEmail: !!isDraftUser,
          }),
        });
      }
    } else if (!isCompletedFields) {
      push(
        isLawyer
          ? `/customer/consultation-form?lawyerId=${lawyerId}`
          : {
              pathname: '/customer/consultation-form',
              query: getValidQueryParams({
                counselorId: query.counselorId,
                duration: bookingParams.duration,
                meetingType: bookingParams.meetingType,
                startSlot: bookingParams.startSlot,
                menuId: bookingParams.menuId,
              }),
            },
      );
    } else {
      push({
        pathname: isLawyer
          ? '/customer/booking/[lawyerId]'
          : '/customer/counselor-booking/[counselorId]',
        query: isLawyer
          ? { lawyerId }
          : getValidQueryParams({
              counselorId: lawyerId,
              duration: bookingParams.duration,
              meetingType: bookingParams.meetingType,
              startSlot: bookingParams.startSlot,
              menuId: bookingParams.menuId,
            }),
      });
    }
  };

  // Removed state clearing - this was interfering with Calendar state updates
  // useEffect(() => {
  //   setState({});
  // }, []);

  return (
    <Box
      display="flex"
      justifyContent="center"
      flexDirection="column"
      alignItems="center"
    >
      {freeConsultationMenu && (
        <Stack
          direction="row"
          spacing="4px"
          sx={{
            svg: {
              width: { xs: 11, tablet: 15 },
              height: { xs: 15, tablet: 25 },
            },
          }}
        >
          <BlackPathLeft />
          <Typography
            fontSize={{ xs: 12, tablet: 18 }}
            fontWeight={500}
            color="#464646"
          >
            {isLawyer ? '初回無料' : 'オンラインで気軽に'}
          </Typography>
          <BlackPathRight />
        </Stack>
      )}
      <Button
        variant="contained"
        color="secondary"
        fullWidth
        size="large"
        sx={{ maxWidth }}
        className="shadow tabletStyle"
        endIcon={<ArrowRightIcon width={32} height={32} color="white" />}
        onClick={() => handleClickContact()}
      >
        相談する
      </Button>
      <Typography
        mt={{ xs: '18px', tablet: 2 }}
        fontSize={{ xs: 10, tablet: 12 }}
      >
        {providerType === ProviderType.LAWYER
          ? t('lawyerProfile.checkPrice')
          : '*料金詳細は各カウンセラーの料金表をご確認ください'}
      </Typography>
    </Box>
  );
};

export default BookingButton;

/* eslint-disable no-nested-ternary */
import type { StackProps, SxProps, Theme } from '@mui/material';
import { Box, Stack, Typography } from '@mui/material';
import type { TReview } from 'hooks/types';
import { EmtyReview } from 'icons';
import { isEmpty } from 'lodash';

import RatingItem from './RatingItem';
import SkeletonList from './Skeleton';

export interface ISolutionList extends StackProps {
  data: TReview[];
  cellStyle?: SxProps<Theme>;
  loading?: boolean;
  isFetched?: boolean;
}
const Ratinglist = ({
  data = [],
  cellStyle = [],
  loading,
  isFetched,
  ...props
}: ISolutionList) => {
  if (isEmpty(data) && isFetched) {
    return (
      <Stack {...props}>
        <Box
          sx={{
            paddingY: '32px',
            display: 'flex',
            justifyContent: 'center',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <EmtyReview />
          <Typography
            sx={{
              fontSize: '16px',
              color: '#9E9B92',
            }}
          >
            評価がありません
          </Typography>
        </Box>
      </Stack>
    );
  }

  return (
    <Stack {...props}>
      <Stack direction="column" gap={2}>
        {data.map((item) => (
          <RatingItem data={item} key={item._id} sx={cellStyle} />
        ))}
        {loading && <SkeletonList />}
      </Stack>
    </Stack>
  );
};

export default Ratinglist;

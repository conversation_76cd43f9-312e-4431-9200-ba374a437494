import type { SxProps, Theme } from '@mui/material';
import {
  Avatar,
  Box,
  Grid,
  Rating,
  Typography,
  useMediaQuery,
} from '@mui/material';
import type { TReview } from 'hooks/types';
import i18n from 'i18n';
import ShowMoreText from 'react-show-more-text';
import type { GenderType } from 'utils/constants';
import { Gender } from 'utils/constants';

const RatingItem = ({ data, sx }: { data: TReview; sx?: SxProps<Theme> }) => {
  const isSmallMobile = useMediaQuery('(max-width:767px)');
  const { t } = i18n;

  const isGenderNotDelete = data.consumer.gender !== ('-' as GenderType);

  return (
    <Box
      sx={{
        border: '1px solid #CEDAE0',
        borderRadius: '12px',
        padding: { xs: 2, md: 3 },
        backgroundColor: '#F6F8F9',
        ...sx,
      }}
    >
      <Grid container spacing={{ xs: '12px', md: 2 }}>
        <Grid item xs={24}>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
          >
            <Box display="flex" alignItems="center" gap={{ xs: '12px', md: 2 }}>
              <Avatar
                src={'/images/default-avatar.png'}
                sx={{
                  width: { md: 64, xs: 44 },
                  height: { md: 64, xs: 44 },
                  border: '1px solid #DBE3E7',
                  backgroundColor: 'white',
                }}
              />
              <Typography
                fontSize={{ md: 20, xs: 16 }}
                variant="h6"
                fontWeight="bold"
                color="#625B50"
              >
                {data.consumer.age
                  ? `${data.consumer.age.min || '--'}代 / `
                  : ''}
                {isGenderNotDelete ? Gender[data.consumer.gender] : 'その他'}
              </Typography>
            </Box>

            <Rating
              value={data.point}
              readOnly
              precision={0.5}
              size={isSmallMobile ? 'small' : 'medium'}
            />
          </Box>
        </Grid>
        {data.comments && (
          <Grid
            item
            xs={24}
            sx={{ paddingTop: 0, fontSize: { md: 16, xs: 14 } }}
          >
            <ShowMoreText
              more={t('global.seeMore')}
              less={t('global.seeLess')}
              className="content-article"
              anchorClass="truncate-text__ellipsis"
              expanded={false}
              lines={3}
            >
              <Typography
                sx={{ color: '#887F70', lineHeight: 1.5 }}
                variant="body2"
                fontSize={{ md: 16, xs: 14 }}
              >
                {data.comments}
              </Typography>
            </ShowMoreText>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default RatingItem;

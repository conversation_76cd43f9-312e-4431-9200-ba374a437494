import { Box, Card, Skeleton, Stack } from '@mui/material';
import { times } from 'lodash';

const SkeletonList = () => {
  return (
    <>
      {times(2).map((number) => {
        return (
          <Card
            key={number}
            sx={{
              border: '1px solid #CEDAE0',
              borderRadius: '12px',
              padding: { xs: 2, md: 3 },
              backgroundColor: '#F6F8F9',
            }}
          >
            <Stack direction="row" spacing={2} flex={1}>
              <Skeleton width={48} height={48} variant="circular" />
              <Box flex={1}>
                <Skeleton variant="text" width={60} sx={{ fontSize: 24 }} />
              </Box>
            </Stack>
          </Card>
        );
      })}
    </>
  );
};

export default SkeletonList;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  order: {
    width: { xs: 40, tablet: 48 },
    height: { xs: 40, tablet: 48 },
    fontSize: { xs: 12, tablet: 16 },
    border: 'solid 1px',
    borderColor: 'neutral4',
    borderRadius: '50%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: 'neutral7',
    fontWeight: 500,
  },
  title: {
    fontSize: { xs: 14, tablet: 18 },
    fontWeight: { xs: 500, tablet: 'bold' },
    lineHeight: 1.56,
    color: 'heading',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

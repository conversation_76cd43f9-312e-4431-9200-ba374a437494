import type { SxProps, Theme } from '@mui/material';
import { Box, Chip, Stack, Typography } from '@mui/material';
import type { ISolution } from 'hooks/types';
import { get } from 'lodash';
import Link from 'next/link';
import type { ReactNode } from 'react';
import TruncateMarkup from 'react-truncate-markup';

import styles from './styles';

const SolutionItem = ({
  data,
  order,
  sx,
  lawyerId,
}: {
  data: ISolution;
  order: number;
  sx: SxProps<Theme>;
  lawyerId: string;
}) => {
  const tagLeftEllipsis = (node: ReactNode) => {
    const tagRendered = get(node, 'props.children') || [];

    return (
      <Chip
        className="tabletStyle"
        label={`+${(data.consultationField || []).length - tagRendered.length}`}
      />
    );
  };

  const href = {
    pathname: '/lawyers/[lawyerId]/solutions/[solutionId]',
    query: {
      lawyerId,
      solutionId: data._id,
    },
  };

  return (
    <Stack
      direction="row"
      spacing={{ xs: 1, tablet: 2 }}
      sx={sx}
      className="solution-item"
    >
      <Link href={href}>
        <Box sx={styles.order}>{order}</Box>
      </Link>
      <Box flex={1}>
        <Link href={href}>
          <Typography
            sx={styles.title}
            className="line-clamp two-line solution-title"
          >
            {data.title}
          </Typography>
        </Link>
        <Stack mt={1}>
          <TruncateMarkup lines={1} ellipsis={tagLeftEllipsis}>
            <Stack
              direction="row"
              spacing={{ xs: '4px', tablet: 1 }}
              flexWrap="wrap"
              style={{ display: 'flex' }}
            >
              {(data.consultationField || []).map((field) => (
                <TruncateMarkup.Atom key={field._id}>
                  <Chip className="tabletStyle" label={field.value} />
                </TruncateMarkup.Atom>
              ))}
            </Stack>
          </TruncateMarkup>
        </Stack>
      </Box>
    </Stack>
  );
};

export default SolutionItem;

import type { StackProps, SxProps, Theme } from '@mui/material';
import { Stack } from '@mui/material';
import type { ISolution } from 'hooks/types';
import { isEmpty } from 'lodash';

import SkeletonList from './Skeleton';
import SolutionItem from './SolutionItem';

export interface ISolutionList extends StackProps {
  data: ISolution[];
  cellStyle?: SxProps<Theme>;
  lawyerId: string;
  loading?: boolean;
  page?: string;
  limit?: string;
}
const SolutionList = ({
  data = [],
  cellStyle = [],
  lawyerId,
  loading,
  page = '1',
  limit = '10',
  ...props
}: ISolutionList) => {
  return (
    <Stack {...props}>
      {loading && isEmpty(data) && <SkeletonList />}
      {!loading &&
        data.map((item, index) => (
          <SolutionItem
            lawyerId={lawyerId}
            data={item}
            key={item._id}
            order={(Number(page) - 1) * Number(limit) + index + 1}
            sx={cellStyle}
          />
        ))}
    </Stack>
  );
};

export default SolutionList;

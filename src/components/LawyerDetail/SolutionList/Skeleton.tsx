import { Box, Skeleton, Stack } from '@mui/material';
import { times } from 'lodash';

const SkeletonList = () => {
  return (
    <>
      {times(5).map((number) => {
        return (
          <Stack direction="row" spacing={2} key={number} flex={1}>
            <Skeleton width={48} height={48} variant="circular" />
            <Box flex={1}>
              <Skeleton variant="text" sx={{ fontSize: 18 }} />
              <Skeleton variant="text" width={60} sx={{ fontSize: 24 }} />
            </Box>
          </Stack>
        );
      })}
    </>
  );
};

export default SkeletonList;

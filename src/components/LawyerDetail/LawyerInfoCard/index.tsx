import { Box, IconButton, Stack, Typography } from '@mui/material';
import BookingButton from 'components/LawyerDetail/BookingButton';
import IconText from 'components/UI/IconText';
import InfoText from 'components/UI/InfoText';
import dayjs from 'dayjs';
import type { ILawyerProfile } from 'hooks/types';
import { t } from 'i18n';
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  CheckListIcon,
  MapIcon,
  MyPageProfileIcon,
  OutlinedBuilding,
  WorkingHourIcon,
} from 'icons';
import { get } from 'lodash';
import type { ICounselorItem } from 'models/provider/interface';
import Image from 'next/image';
import Link from 'next/link';
import type { ReactNode } from 'react';
import Slider from 'react-slick';
import { Gender, PROVIDER_LIST_PATH, ProviderType } from 'utils/constants';

import styles from './styles';

const renderLawyerInfo = (
  data: ILawyerProfile | ICounselorItem,
  multiple?: boolean,
) => {
  const isLawyer = data.type === ProviderType.LAWYER;
  const address = isLawyer && data.office?.address;
  const businessHours = isLawyer && data.office?.businessHours;
  const displayName = (!isLawyer && data.nickname) || data.fullName;

  const renderOfficeInfo = () => {
    if (data.type === ProviderType.LAWYER) {
      return (
        <>
          <IconText icon={OutlinedBuilding}>
            {data.office.fullName || '-'}
          </IconText>
          <IconText icon={MapIcon}>
            {address
              ? t('lawyerList.address', {
                  postCode: address?.postCode,
                  address1: address?.address1?.value,
                  address2: address?.address2,
                  address3: address?.address3,
                  address4: address?.address4,
                })
              : '-'}
          </IconText>
          <IconText icon={WorkingHourIcon}>{businessHours || '-'}</IconText>
        </>
      );
    }
    return (
      <>
        <IconText icon={MyPageProfileIcon}>
          {data?.birthday
            ? Math.floor(dayjs().diff(dayjs(data.birthday), 'years') / 10) * 10
            : '-'}
          代 /{data.gender ? Gender[data.gender] : '-'}
        </IconText>
        <IconText icon={OutlinedBuilding}>
          {data?.career
            ? `カウンセラー歴：${dayjs().diff(dayjs(data.career), 'years')}年`
            : '-'}
        </IconText>
        <IconText icon={CheckListIcon}>
          {data?.numberSolutionCases
            ? `カウンセリング総件数：約${data?.numberSolutionCases}件`
            : '-'}
        </IconText>
      </>
    );
  };

  return (
    <Box
      className="lawyer-info-card__content"
      key={data._id}
      sx={{ mt: multiple ? 3 : 0 }}
    >
      {data.isPublic === false && (
        <InfoText
          justifyContent="center"
          text={t('lawyerProfile.inactiveLawyer')}
        />
      )}
      <Box sx={styles.avatarContainer}>
        <Link href={`${PROVIDER_LIST_PATH[data.type]}/${data._id}`}>
          <Image
            className="pointer"
            alt="avatar"
            src={
              get(data, 'images[0].originUrl') || '/images/default-avatar.png'
            }
            sizes="100vw"
            fill
            priority
          />
        </Link>
      </Box>
      <Typography sx={styles.association}>
        {isLawyer
          ? t('lawyerProfile.barAssociation', {
              association: data.barAssociation || '-',
            })
          : 'カウンセラー'}
      </Typography>
      <Link href={`${PROVIDER_LIST_PATH[data.type]}/${data._id}`}>
        <Typography
          fontWeight="bold"
          fontSize={{ xs: 20, tablet: 24 }}
          textAlign="center"
          color="heading"
          mt={{ xs: '4px', tablet: 0 }}
        >
          {displayName}
        </Typography>
      </Link>
      {!isLawyer && !data.nickname && (
        <Typography
          fontWeight={500}
          fontSize={{ xs: 14, tablet: 16 }}
          textAlign="center"
        >
          {data.katakanaName}
        </Typography>
      )}
      <Stack
        spacing="6px"
        sx={{
          ...styles.officeContainer,
          maxWidth: multiple ? '326px' : 'unset',
          svg: { color: 'primary.main' },
        }}
      >
        {renderOfficeInfo()}
      </Stack>
      {data.isPublic !== false && (
        <Box mt={{ xs: 2, tablet: '12px' }}>
          <BookingButton
            lawyerId={data._id}
            providerType={data.type}
            maxWidth={{ xs: 311, tablet: 239 }}
            freeConsultationMenu={data.freeConsultationMenu}
          />
        </Box>
      )}
    </Box>
  );
};

const ArrowLeftButton = ({ onClick }: { onClick?: () => void }) => {
  return (
    <IconButton size="small" sx={styles.arrowButton} onClick={onClick}>
      <ArrowLeftIcon />
    </IconButton>
  );
};
const ArrowRightButton = ({ onClick }: { onClick?: () => void }) => {
  return (
    <IconButton
      size="small"
      sx={{ ...styles.arrowButton, right: 0 }}
      onClick={onClick}
    >
      <ArrowRightIcon />
    </IconButton>
  );
};
const LawyerInfoCard = ({
  data,
}: {
  data: ILawyerProfile | ILawyerProfile[] | ICounselorItem;
}) => {
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: <ArrowRightButton />,
    prevArrow: <ArrowLeftButton />,
    appendDots: (dots: ReactNode) => {
      return (
        <Stack direction="row" gap={1} flexWrap="wrap" sx={styles.dotContainer}>
          {dots}
        </Stack>
      );
    },
    customPaging: () => {
      return <Box sx={styles.dot} />;
    },
  };
  if (Array.isArray(data)) {
    return (
      <Box sx={styles.container} className="lawyer-info-card">
        <Slider {...settings}>
          {data.map((item) => renderLawyerInfo(item, data.length > 1))}
        </Slider>
      </Box>
    );
  }
  return (
    <Box sx={styles.container} className="lawyer-info-card">
      {renderLawyerInfo(data)}
    </Box>
  );
};

export default LawyerInfoCard;

import type { Theme } from '@mui/material';

const styles = {
  container: {
    borderRadius: { tablet: 2 },
    p: { xs: 2, tablet: 4 },
    bgcolor: 'white',
  },
  avatarContainer: {
    width: { xs: 96, tablet: 120 },
    height: { xs: 96, tablet: 120 },
    position: 'relative',
    borderRadius: '50%',
    overflow: 'hidden',
    margin: '0 auto',
  },
  association: {
    fontSize: { xs: 10, tablet: 14 },
    fontWeight: 500,
    color: 'primary.main',
    textAlign: 'center',
    mt: { xs: 2, tablet: '12px' },
  },
  officeContainer: {
    width: 'fit-content',
    margin: { xs: '12px auto 0px', tablet: '16px auto 0px' },
  },
  arrowButton: {
    position: 'absolute',
    zIndex: 1,
    transform: 'translate(0px, 0px)',
    border: (theme: Theme) => `1px solid ${theme.palette.neutral4}`,
    top: { xs: '58px', tablet: '68px' },
    svg: {
      width: 20,
      height: 20,
    },
    p: '5px',
  },
  dotContainer: {
    '&.slick-dots': {
      display: 'flex',
      justifyContent: 'center',
      height: 'fit-content',
      top: 0,
      li: {
        height: '8px',
        width: '8px',
        margin: 0,
        '&.slick-active': {
          '> div': {
            bgcolor: 'neutral7',
          },
        },
      },
    },
  },
  dot: {
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    bgcolor: 'neutral2',
  },
} as const;

export default styles;

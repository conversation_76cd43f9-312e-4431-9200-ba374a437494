import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Stack, Typography, useMediaQuery } from '@mui/material';
import { Radio, Select, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import NumberField from 'components/Form/NumberField';
import { useDeepCompareEffect } from 'hooks';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import type { ICreateConsultationMenu } from 'models/consultation/interface';
import { ConsultationMenuMeetingType, LIST_DURATION } from 'utils/constants';
import Helper from 'utils/helpers';

import type { ConsultationMenuValues } from './schema';
import schema from './schema';

export interface ConsultationMenuFormProps {
  defaultValues?: ICreateConsultationMenu;
  onSubmit: (values: ConsultationMenuValues) => void;
}
const ConsultationMenuForm = ({
  defaultValues,
  onSubmit,
}: ConsultationMenuFormProps) => {
  const isSmallMobile = useMediaQuery('(max-width:767px)');
  const { control, handleSubmit, reset } = useHookForm<ConsultationMenuValues>({
    mode: 'all',
    resolver: yupResolver(schema),
    defaultValues,
  });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      id="counselor-consultation-menu-form"
    >
      <Stack spacing={2}>
        <TextField
          name="title"
          label={t('consultationMenu.title')}
          placeholder={t('placeholder.menuTitle')}
          required
          control={control}
          multiline
          minRows={7}
          maxLength={300}
          columns={28}
          labelCol={9}
        />
        <TextField
          name="content"
          label={t('consultationMenu.content')}
          placeholder={t('placeholder.menuContent')}
          control={control}
          multiline
          minRows={7}
          maxLength={1000}
          columns={28}
          labelCol={9}
        />
        <Select
          labelCol={4}
          columns={12}
          name="duration"
          label={t('confirmMeeting.duration')}
          placeholder="面談時間"
          data={Helper.convertObjectToOptions(LIST_DURATION)}
          control={control}
          required
          adornment={
            <Typography ml={1} color="text.primary">
              分
            </Typography>
          }
        />
        <NumberField
          label={t('confirmMeeting.price')}
          control={control}
          labelCol={4}
          name="price"
          min={0}
          max={999999999}
          required
          adornment={
            <Typography ml={1} color="text.primary">
              円
            </Typography>
          }
        />
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            flexDirection: isSmallMobile ? 'column' : 'row',
          }}
        >
          <Box sx={{ width: 'calc(calc(100% / 3) - 20px)' }}>
            <Label
              label={t('consultationMenu.meetingType')}
              required={true}
              paddingTop={1}
            />
          </Box>
          <Box
            sx={{
              paddingLeft: isSmallMobile ? 0 : '32px',
              paddingTop: isSmallMobile ? 0 : '8px',
              width: 'calc(100% - calc(100% / 3) + 20px)',
            }}
          >
            <Radio
              data={Helper.convertObjectToOptions(ConsultationMenuMeetingType)}
              control={control}
              name="meetingType"
              required
              columns={4}
              row={false}
              sx={{
                marginTop: 0,
              }}
            />
          </Box>
        </Box>
      </Stack>
    </form>
  );
};

export default ConsultationMenuForm;

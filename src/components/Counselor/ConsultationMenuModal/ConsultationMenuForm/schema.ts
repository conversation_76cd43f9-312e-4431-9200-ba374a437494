import { t } from 'i18n';
import { isNaN } from 'lodash';
import type { InferType } from 'yup';
import { number, object, string } from 'yup';

const schema = object({
  title: string()
    .required()
    .max(300, t('validation.maxLength', { number: 300 }))
    .trim(t('validation.notAllowedWhiteSpace')),
  content: string().max(1000, t('validation.maxLength', { number: 1000 })),
  duration: number()
    .transform((value) => {
      return isNaN(value) || !value ? undefined : value;
    })
    .required(),
  price: number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .max(999999999, t('validation.invalidField'))
    .required(),
  meetingType: string().required(t('validation.requiredField')),
});

export type ConsultationMenuValues = InferType<typeof schema>;

export default schema;

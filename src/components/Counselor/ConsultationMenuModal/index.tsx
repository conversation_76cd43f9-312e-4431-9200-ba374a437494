import { LoadingButton } from '@mui/lab';
import { Box, Button, DialogActions, Stack, Typography } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import type { ICreateConsultationMenu } from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import type { Dispatch, SetStateAction } from 'react';

import ConsultationMenuForm from './ConsultationMenuForm';
import type { ConsultationMenuValues } from './ConsultationMenuForm/schema';

export interface IConsultationMenuModal {
  defaultValues?: ICreateConsultationMenu;
  open: boolean;
  onClose: () => void;
  setAddMenuModal: Dispatch<SetStateAction<boolean>>;
  onSuccess: () => void;
  idEdit: string | undefined;
  setIdEdit: Dispatch<SetStateAction<string | undefined>>;
  setDefaultValues: Dispatch<SetStateAction<object>>;
}

const ConsultationMenuModal = ({
  open,
  onClose,
  defaultValues,
  setAddMenuModal,
  onSuccess,
  idEdit,
  setIdEdit,
  setDefaultValues,
}: IConsultationMenuModal) => {
  const { mutateAsync: createConsultationMenu, isLoading } =
    useMutate<ICreateConsultationMenu>(consultationQuery.create);
  const { mutateAsync: updateConsultation, isLoading: isUpdate } =
    useMutate<ICreateConsultationMenu>(
      consultationQuery.update(idEdit as string),
    );

  const handleSubmitForm = (values: ConsultationMenuValues) => {
    const { content, title, price, duration, meetingType } = values;
    const payload = {
      title,
      content,
      unitPrices: [{ price, duration }],
      meetingType,
    };
    if (idEdit) {
      updateConsultation(payload, {
        onSuccess: () => {
          setAddMenuModal(false);
          setIdEdit(undefined);
          setDefaultValues({});
          onSuccess();
        },
      });
    } else {
      createConsultationMenu(payload, {
        onSuccess: () => {
          setAddMenuModal(false);
          onSuccess();
        },
      });
    }
  };

  return (
    <Dialog
      open={open}
      aria-labelledby="consultation-menu-modal-title"
      maxWidth="mw"
      fullWidth
    >
      <DialogTitle
        id="consultation-menu-modal-title"
        sx={{ p: '32px 32px 24px' }}
      >
        <Typography
          fontSize={24}
          fontWeight="bold"
          color="heading"
          textAlign="center"
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: '2',
            WebkitBoxOrient: 'vertical',
          }}
        >
          {!idEdit ? '料金表を追加' : '料金表を編集'}
        </Typography>
      </DialogTitle>
      <DialogContent sx={{ p: '0px 32px 24px' }}>
        <Box display="grid">
          <ConsultationMenuForm
            defaultValues={defaultValues}
            onSubmit={handleSubmitForm}
          />
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          p: '16px 24px 24px',
        }}
      >
        <Stack
          direction="row"
          spacing={3}
          maxWidth={416}
          width="100%"
          margin="0px auto"
        >
          <Button variant="outlined" size="large" fullWidth onClick={onClose}>
            {t('global.cancel')}
          </Button>
          <LoadingButton
            variant="contained"
            color="secondary"
            size="large"
            fullWidth
            loading={isLoading || isUpdate}
            type="submit"
            form="counselor-consultation-menu-form"
          >
            {t('global.settle')}
          </LoadingButton>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default ConsultationMenuModal;

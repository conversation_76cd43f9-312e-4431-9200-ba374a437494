import { yupResolver } from '@hookform/resolvers/yup';
import {
  <PERSON><PERSON>,
  AppBar,
  Box,
  Button,
  Container,
  Stack,
  Toolbar,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { DatePicker, Radio, Select } from 'components/Form';
import TimeTable from 'components/TimeTable';
import dayjs from 'dayjs';
import { useFetchDetail, useFetchList } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ICanlendarSlot, IListItem } from 'hooks/types';
import { ArrowDownIcon } from 'icons';
import { isArray } from 'lodash';
import { IMeetingType } from 'models/consultation/interface';
import resourceQuery from 'models/resource/query';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { MEETING_TYPE_OPTIONS, SlotStatus } from 'utils/constants';

import CalendarHeader from './CalendarHeader';
import TimeTableExplan from './components/TimeTableExplan';
import ConsultationFieldDialog from './ConsultationFieldDialog';
import type { FilterFormValues } from './schema';
import schema from './schema';

function ToolbarBottom({
  onCancel,
  onOk,
  disabled = false,
}: {
  onOk?: () => void;
  onCancel?: () => void;
  disabled?: boolean;
}) {
  return (
    <AppBar position="fixed" color="white" sx={{ top: 'auto', bottom: 0 }}>
      <Toolbar
        sx={{
          minHeight: { xs: 72, tablet: 88 },
        }}
      >
        <Box
          display={'flex'}
          justifyContent={'center'}
          width={'100%'}
          gap={2}
          alignItems={'stretch'}
        >
          <Button
            variant="outlined"
            onClick={onCancel}
            fullWidth
            sx={{
              height: 'auto',
              borderRadius: '222px',
              p: {
                xs: '8px 16px',
                lg: '16px',
              },
              maxWidth: {
                xs: '100%',
                lg: 240,
              },
            }}
          >
            <Typography>キャンセル</Typography>
          </Button>
          <Button
            disabled={disabled}
            fullWidth
            onClick={onOk}
            sx={{
              p: {
                xs: '8px 16px',
                lg: '16px',
              },
              maxWidth: {
                xs: '100%',
                lg: 240,
              },
            }}
            variant="contained"
          >
            次へ
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
}

function handleFormattedDate(date: string) {
  const tokyoStartOfDay = dayjs.utc(date).tz('Asia/Tokyo').startOf('day');

  // Chuyển lại về UTC
  return tokyoStartOfDay.utc().toISOString();
}
export default function CounselorFilterPage() {
  const router = useRouter();
  const { query } = useRouter();
  const isMobile = useMediaQuery('(max-width: 768px)');
  const defaultValues: FilterFormValues = {
    date: new Date().toISOString(),
    consultationField: [],
    meetingType: IMeetingType.SINGLE,
    duration: 60,
  };
  const [open, setOpen] = React.useState(false);
  const [timeSelect, setTimeSelect] = React.useState('');
  const [filter, setFilter] = React.useState<FilterFormValues>(() => {
    return {
      ...defaultValues,
      date: handleFormattedDate(defaultValues.date!),
    };
  });
  const { list: consultationList } = useFetchList<IListItem>({
    ...resourceQuery.consultations,
    customParams: {
      ...resourceQuery.consultations.customParams,
      providerType: 'COUNSELOR',
    },
  });
  const { detail: calendars, refetch } = useFetchDetail<ICanlendarSlot[]>({
    ...apiQuery.calendars,
    queryKey: [...apiQuery.calendars.queryKey, JSON.stringify(filter)],
    staleTime: 0,
    customParams: {
      ...filter,
    },
  });
  const calendarsData = isArray(calendars) ? calendars : [];

  const slotItems =
    calendarsData?.flatMap((item) => {
      return item.slots || [];
    }) || [];
  const slotAvalabelItems = slotItems.filter((item) => {
    const startTime = dayjs(item.start).tz('Asia/Tokyo').toDate().getTime();
    const currentTime = dayjs().tz('Asia/Tokyo').toDate().getTime();
    const isAvailable = startTime >= currentTime;
    return isAvailable && item.status === SlotStatus.OPEN;
  });

  const allDates = calendarsData?.map((item) => {
    return dayjs.utc(item.dateValue).tz('Asia/Tokyo').format('YYYY-MM-DD');
  });
  const startDate = allDates && allDates.length ? (allDates[0] as string) : '';
  const endDate =
    allDates && allDates.length
      ? (allDates[allDates.length - 1] as string)
      : '';
  const { control, handleSubmit, watch, setValue } = useForm<FilterFormValues>({
    resolver: yupResolver(schema),
    defaultValues,
  });

  function submit(data: FilterFormValues) {
    const { date } = data;
    const formattedDateTime = handleFormattedDate(date || '');
    setTimeSelect('');
    setFilter((prev) => ({
      ...prev,
      date: formattedDateTime,
      consultationField: data.consultationField as string[],
      meetingType: data.meetingType,
      duration: data.duration,
    }));
    router.push({
      pathname: '/counselors/filter',
      query: {
        consultationField: data.consultationField as string[],
        meetingType: data.meetingType,
        duration: data.duration,
        date: formattedDateTime,
      },
    });
  }

  async function handleSubmitTimeSelect() {
    if (!timeSelect) {
      toast.error(
        '時間が選択されていません。ご希望の時間帯を選択してください。',
      );
      return;
    }

    const { data, isSuccess } = await refetch();

    if (!isSuccess) {
      toast.error(
        '選択された時間はご利用できません。時間帯を再度設定して、もう一度お試しください。',
      );
      return;
    }
    const start = timeSelect;
    const end = dayjs(timeSelect)
      .add(watch('duration') || 30, 'minute')
      .toDate()
      .toISOString();
    const slots = data.flatMap((item) => {
      return item.slots;
    });

    const isSlotStartAvalable = slots.some((slot) => {
      return slot.start === start && slot.status === 'OPEN';
    });
    const isSlotEndAvalable = slots.some((slot) => {
      return slot.end === end && slot.status === 'OPEN';
    });
    if (!isSlotStartAvalable || !isSlotEndAvalable) {
      toast.error(
        '選択された時間はご利用できません。時間帯を再度設定して、もう一度お試しください。',
      );
      return;
    }

    router.push({
      pathname: '/counselors',
      query: {
        consultationField: filter.consultationField as string[],
        meetingType: filter.meetingType,
        duration: filter.duration,
        startSlot: dayjs(start).toISOString(),
        isFilterCounselor: true,
        date: filter.date,
      },
    });
  }

  const watchConsultationField = watch('consultationField', []) as string[];
  const watchMeetingType = watch('meetingType');

  const valuesLabel = watchConsultationField.map((item) => {
    const label = consultationList
      .find((consultation) => consultation.value === item)
      ?.value?.replace(/\n/g, ' ');
    return label || item;
  });

  // Dynamic duration options based on meeting type
  const durationOptions = React.useMemo(() => {
    if (watchMeetingType === 'PARTNER') {
      // For 夫婦カウンセリング: only show 60, 90, 120 minutes
      return [
        {
          _id: 60,
          value: '夫婦カウンセリング（60分）※2回目以降の方のみ',
        },
        {
          _id: 90,
          value: '夫婦カウンセリング（90分）',
        },
        {
          _id: 120,
          value: '夫婦カウンセリング（120分）',
        },
      ];
    }

    // For 個人カウンセリング: show all options (30, 60, 90, 120)
    return [
      {
        _id: 30,
        value: 'クイック相談（30分）',
      },
      {
        _id: 60,
        value: 'カウンセリング（60分）',
      },
      {
        _id: 90,
        value: 'カウンセリング（90分）',
      },
      {
        _id: 120,
        value: 'カウンセリング（120分）',
      },
    ];
  }, [watchMeetingType]);

  // Auto-adjust duration if current selection becomes invalid
  React.useEffect(() => {
    const currentDuration = watch('duration');
    const validDurationIds = durationOptions.map((option) =>
      Number(option._id),
    );

    if (currentDuration && !validDurationIds.includes(currentDuration)) {
      // If current duration is invalid for the selected meeting type, set to 60 (first valid option for couple counseling)
      setValue('duration', 60);
    }
  }, [watchMeetingType, durationOptions, watch, setValue]);

  // Reset form to default conditions
  const handleReset = () => {
    const defaultFormValues: FilterFormValues = {
      date: new Date().toISOString(),
      consultationField: [],
      meetingType: IMeetingType.SINGLE,
      duration: 60,
    };

    // Reset form values
    setValue('date', defaultFormValues.date);
    setValue('consultationField', defaultFormValues.consultationField);
    setValue('meetingType', defaultFormValues.meetingType);
    setValue('duration', defaultFormValues.duration);

    // Reset filter state
    setFilter({
      ...defaultFormValues,
      date: handleFormattedDate(defaultFormValues.date!),
    });

    // Clear time selection
    setTimeSelect('');
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    if (query.date) {
      setValue(
        'date',
        dayjs
          .utc(query.date as string)
          .tz('Asia/Tokyo')
          .format('YYYY-MM-DD HH:mm:ss'),
        {
          shouldDirty: true,
        },
      );
      setFilter((prev) => ({
        ...prev,
        date: query.date as string,
      }));
    }
    if (query.consultationField) {
      if (isArray(query.consultationField)) {
        setValue('consultationField', query.consultationField as string[], {
          shouldDirty: true,
        });
        setFilter((prev) => ({
          ...prev,
          consultationField: query.consultationField as string[],
        }));
      } else {
        setValue('consultationField', [query.consultationField as string], {
          shouldDirty: true,
        });
        setFilter((prev) => ({
          ...prev,
          consultationField: [query.consultationField as string],
        }));
      }
    }
    if (query.meetingType) {
      setValue('meetingType', query.meetingType as string, {
        shouldDirty: true,
      });
      setFilter((prev) => ({
        ...prev,
        meetingType: query.meetingType as string,
      }));
    }
    if (query.duration) {
      setValue('duration', Number(query.duration), { shouldDirty: true });
      setFilter((prev) => ({
        ...prev,
        duration: Number(query.duration),
      }));
    }
  }, [
    query.consultationField,
    query.date,
    query.duration,
    query.meetingType,
    setValue,
  ]);

  return (
    <Box mt={{ xs: 2, tablet: 4 }} mb={{ xs: 2, tablet: 14 }}>
      <Container maxWidth="xtablet" disableGutters>
        <Box
          bgcolor={'white'}
          borderRadius={{
            tablet: 2,
            xs: 0,
          }}
          p={{
            tablet: 4,
            xs: 2,
          }}
        >
          <Typography
            fontWeight={700}
            fontSize={{
              xs: 18,
              tablet: 32,
            }}
            color="heading"
            textAlign={'center'}
            mb={{
              xs: 3,
              tablet: 5,
            }}
          >
            検索条件の設定
          </Typography>
          <Box
            border={'1px solid #DBE3E7'}
            bgcolor={'white'}
            borderRadius={{
              xs: '12px',
              tablet: 2,
            }}
            p={{
              xs: 2,
              tablet: 3,
            }}
          >
            <form onSubmit={handleSubmit(submit)}>
              <Stack
                spacing={{
                  xs: 3,
                  tablet: 4,
                }}
              >
                {/* min date is today and max date Maximum 6 weeks to the future */}
                <DatePicker
                  labelCol={12}
                  label="ご希望の相談日を選択してください"
                  name="date"
                  control={control}
                  inputFormat="YYYY年MM月DD日 (ddd)"
                  minDate={new Date()}
                  maxDate={
                    new Date(new Date().setDate(new Date().getDate() + 41))
                  }
                />
                <Stack spacing={1}>
                  <Typography fontSize={16} fontWeight={700}>
                    相談テーマを選択してください
                  </Typography>
                  <Stack
                    onClick={() => {
                      setOpen(true);
                    }}
                    direction="row"
                    justifyContent={'space-between'}
                    alignItems={'center'}
                    sx={{
                      borderRadius: 1,
                      cursor: 'pointer',
                      padding: '12px',
                      paddingBottom: '16px',
                      backgroundColor: '#F6F8F9',
                    }}
                  >
                    <Typography fontSize={16} fontWeight={400}>
                      {valuesLabel.length > 0
                        ? `${valuesLabel.join(' • ')}`
                        : '選択していません'}
                    </Typography>

                    <ArrowDownIcon />
                  </Stack>
                </Stack>
                {open && (
                  <ConsultationFieldDialog
                    value={watchConsultationField}
                    consultationList={consultationList}
                    open={open}
                    onClose={() => setOpen(false)}
                    onClear={() => {
                      setValue('consultationField', watchConsultationField);
                    }}
                    onAgree={(values) => {
                      setValue('consultationField', values, {
                        shouldDirty: true,
                      });
                      setOpen(false);
                    }}
                    onCancel={() => {
                      setOpen(false);
                    }}
                  />
                )}

                <Radio
                  control={control}
                  name="meetingType"
                  label="相談タイプを選択してください"
                  labelCol={12}
                  data={MEETING_TYPE_OPTIONS}
                />

                <Stack spacing={1}>
                  <Typography
                    fontSize={{
                      xs: 14,
                      tablet: 16,
                    }}
                    fontWeight={700}
                  >
                    相談時間を選択してください
                  </Typography>
                  <Typography fontSize={12}>
                    ＊当日の延長も可能です。カウンセラーに直接ご相談ください。
                  </Typography>
                  <Select
                    allowClear={false}
                    labelCol={12}
                    name="duration"
                    label=""
                    placeholder="面談時間"
                    data={durationOptions}
                    control={control}
                    required
                  />
                </Stack>
                <Box display="flex" justifyContent="center">
                  <Button
                    variant="contained"
                    type="submit"
                    fullWidth
                    sx={{
                      maxWidth: {
                        xs: '100%',
                        lg: 300,
                      },
                      backgroundColor: '#FFCD00',
                      color: '#464646',
                      '&:hover': {
                        backgroundColor: '#FFCD00',
                      },
                    }}
                  >
                    予約可能な日時を表示
                  </Button>
                </Box>
              </Stack>
            </form>
          </Box>

          <Box mt={4}>
            <Typography
              fontSize={{
                tablet: 16,
                xs: 14,
              }}
              fontWeight={700}
              mb={{
                tablet: 2,
                xs: 3,
              }}
            >
              ご希望の時間帯を選択してください
            </Typography>
            {slotAvalabelItems.length === 0 && (
              <Alert
                severity="warning"
                sx={{
                  mb: 3,
                  mt: -1,
                  display: 'flex',
                  justifyContent: 'center',
                  fontWeight: 500,
                }}
              >
                条件に該当する空き時間がありません。条件を変更してもう一度お試しください
              </Alert>
            )}
            <Box
              sx={{
                position: 'sticky',
                top: {
                  xs: '60px',
                  lg: '98px',
                },
                zIndex: 10,
                backgroundColor: 'white',
                borderBottom: '1px solid #DBE3E7',
              }}
            >
              <Typography
                fontSize={{
                  tablet: 18,
                  xs: 14,
                }}
                fontWeight={700}
                mb={2}
                color="heading"
                textAlign={'center'}
              >
                {startDate
                  ? dayjs(new Date(startDate)).format('YYYY年MM月DD日(ddd)')
                  : ''}{' '}
                -
                {endDate
                  ? dayjs(new Date(endDate)).format('YYYY年MM月DD日(ddd)')
                  : ''}
              </Typography>
              <TimeTableExplan />
              <CalendarHeader
                allWeekDates={allDates}
                isLoading={false}
                timeColumnWidth={!isMobile ? 35 + 18 : 40}
              />
            </Box>
            <TimeTable
              hourExtra={1}
              maxEndTime={100}
              border="2px solid #54585A"
              spacing={{
                lg: '18px',
                xs: '10px',
              }}
              isConvert={true}
              data={slotItems}
              duration={watch('duration') || 30}
              onChange={(data) => {
                setTimeSelect(data);
              }}
              allDates={allDates}
              startTime={
                allDates && allDates.length ? (allDates[0] as string) : ''
              }
              value={timeSelect}
              closeStatus={[SlotStatus.CLOSED_BY_PROVIDER]}
              openStatus={[SlotStatus.OPEN]}
              disabledFutureMinutes={0}
              enableSkipSlot={true}
            />
          </Box>
        </Box>
      </Container>

      {timeSelect && (
        <ToolbarBottom
          onOk={handleSubmitTimeSelect}
          disabled={slotItems.length === 0}
          onCancel={handleReset}
        />
      )}
    </Box>
  );
}

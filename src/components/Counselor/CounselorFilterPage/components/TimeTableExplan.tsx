import { Box, Stack, Typography } from '@mui/material';

import styles from '../styles';

export default function TimeTableExplan() {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      mb={{ xs: 2, tablet: 4 }}
    >
      <Stack spacing={2} direction="row">
        <Stack spacing="4px" direction="row" sx={styles.titleText}>
          <Box
            borderRadius="100%"
            width="16px"
            height="16px"
            bgcolor="#edf1f3"
          ></Box>
          <Typography fontSize={12}>{'空きなし'}</Typography>
        </Stack>
        <Stack spacing="4px" direction="row" sx={styles.titleText}>
          <Box
            width="16px"
            height="16px"
            border="2px solid #54585A"
            borderRadius="100%"
          ></Box>
          <Typography fontSize={12}>{'空きあり'}</Typography>
        </Stack>
        <Stack spacing="4px" direction="row" sx={styles.titleText}>
          <Box
            width="16px"
            height="16px"
            bgcolor="#F6AD3C"
            borderRadius="100%"
          ></Box>
          <Typography fontSize={12}>{'現在の予約'}</Typography>
        </Stack>
      </Stack>
    </Box>
  );
}

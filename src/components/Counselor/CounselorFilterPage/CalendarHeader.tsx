import { Box, ButtonBase, LinearProgress, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { memo } from 'react';

import styles from './styles';

export interface CalendarHeaderProps {
  allWeekDates: string[];
  isLoading: boolean;
  timeColumnWidth?: number;
  onDateSelect?: (date: string) => void;
}

const DATE_COLOR: Record<number, string> = {
  0: 'error',
  6: 'steelBlue',
  5: 'heading',
  4: 'heading',
  3: 'heading',
  2: 'heading',
  1: 'heading',
};
const CalendarHeader = ({
  allWeekDates,
  isLoading,
  timeColumnWidth = 88,
}: CalendarHeaderProps) => {
  return (
    <Box className="calendar-header" sx={styles.headerContainer}>
      <Box sx={styles.timeGutter} minWidth={timeColumnWidth}>
        {/* <Typography sx={styles.gutterText}>日時</Typography> */}
      </Box>
      <Box flex={1} position="relative">
        <Box display="flex" flexDirection="row">
          {allWeekDates.map((dateInWeek) => {
            const date = new Date(dateInWeek);
            const isToDay =
              new Date().toDateString() === new Date(date).toDateString();
            const isPastDate =
              dayjs(dateInWeek) < dayjs().startOf('d') ||
              dayjs(dateInWeek) >= dayjs().startOf('d').add(42, 'd');
            const dateColor = DATE_COLOR[date.getDay()];
            return (
              <ButtonBase
                key={dateInWeek}
                disabled={isPastDate}
                sx={styles.headerDateButton}
              >
                <Typography
                  sx={styles.headerDayText}
                  color={dateColor}
                  className="calendar-day-text"
                >
                  {dayjs(date).format('ddd')}
                </Typography>
                <Box
                  p="1px 2px 3px"
                  borderRadius="50%"
                  className="calendar-date-text-wrapper"
                  sx={{ bgcolor: isToDay ? '#FCAB28' : 'white' }}
                >
                  <Typography
                    sx={styles.headerDateText}
                    color={isToDay ? 'white' : dateColor}
                    className="calendar-date-text"
                  >
                    {date.getDate()}
                  </Typography>
                </Box>
              </ButtonBase>
            );
          })}
        </Box>
      </Box>
      {isLoading && (
        <LinearProgress
          sx={{ position: 'absolute', width: '100%', top: 44, zIndex: 1 }}
        />
      )}
    </Box>
  );
};

export default memo(CalendarHeader);

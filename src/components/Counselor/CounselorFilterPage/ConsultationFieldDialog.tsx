import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import { CheckBox } from 'components/Form';
import type { IListItem } from 'hooks/types';
import { CloseIcon, MenuIcon } from 'icons';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

export default function ConsultationFieldDialog({
  open,
  value,
  onClose,
  consultationList,
  onClear,
  onAgree,
  onCancel,
}: {
  value: string[];
  open: boolean;
  onClose: () => void;
  onClear: () => void;
  onAgree: (values: string[]) => void;
  onCancel: () => void;
  consultationList: IListItem[];
}) {
  const { control, watch, reset } = useForm({
    defaultValues: {
      consultationField: value,
    },
  });

  useEffect(() => {
    if (open) {
      reset({
        consultationField: value,
      });
    }
  }, [open, reset, value]);

  const values = watch('consultationField');

  const valuesLabel = values.map((item) => {
    const label = consultationList
      .find((consultation) => consultation.value === item)
      ?.value?.replace(/\n/g, ' ');
    return label || item;
  });

  return (
    <Dialog open={open} onClose={onClose} maxWidth="tablet">
      <DialogTitle sx={{ paddingBottom: 0 }}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          sx={{
            width: '100%',
          }}
        >
          <Typography
            fontSize={16}
            fontWeight={700}
            display={'flex'}
            alignItems={'center'}
            gap={1}
          >
            <MenuIcon /> 得意分野:
          </Typography>
          <IconButton
            onClick={onClose}
            sx={{
              border: '1px solid #E0E0E0',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent>
        <Typography mb={4}>
          {valuesLabel.length > 0
            ? `${valuesLabel.join(' • ')}`
            : '選択していない'}
        </Typography>
        <CheckBox
          labelCol={12}
          control={control}
          data={consultationList.map((item) => ({
            _id: item.value,
            value: item.value,
          }))}
          name="consultationField"
          label=""
          showSelectAll
          optionContainerProps={{
            tablet: 4,
          }}
        />
      </DialogContent>
      <DialogActions
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          padding: '24px',
        }}
      >
        <Button onClick={onClear}>
          <span>リセット</span>
        </Button>

        <Stack direction={'row'} spacing={2}>
          <Button onClick={onCancel} variant="outlined">
            キャンセル
          </Button>
          <Button
            onClick={() => {
              onAgree(values);
            }}
            variant="contained"
          >
            確定
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
}

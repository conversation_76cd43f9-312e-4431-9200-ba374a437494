import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  pageContainer: {
    paddingBottom: { xs: 2, tablet: 4 },
  },
  listContainer: {
    mt: { xs: 1, tablet: 2 },
    '.lawyer-list': {
      mt: 2,
    },
  },
  filterTitle: {
    fontWeight: 'bold',
    color: 'heading',
    fontSize: { xs: 14, tablet: 16 },
  },
  filterText: {
    display: '-webkit-box',
    WebkitLineClamp: '1',
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    flex: 1,
    fontSize: { xs: 14, tablet: 16 },
    mt: { xs: '4px', tablet: 0 },
    ml: { tablet: 1 },
    whiteSpace: 'normal',
  },
  filterRow: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    p: { xs: 1, tablet: 3 },
    cursor: 'pointer',
    '&:hover': {
      bgcolor: 'divine',
      borderRadius: { xs: 1, tablet: 2 },
    },
    '& svg': {
      color: 'primary.main',
    },
  },
  filterDivider: {
    bgcolor: 'divine',
    height: '1px',
    width: 'calc(100% - 48px)',
    mx: 3,
  },
  titleText: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContainer: {
    display: 'flex',
    flex: '0 0 auto',
    flexDirection: 'row',
    position: 'sticky',
    width: '100%',
    bgcolor: 'white',
    top: 0,
    left: 0,
    boxShadow: '0px 8px 16px -12px rgb(210, 220, 225)',
    zIndex: 1000,
  },
  headerDateButton: {
    flexDirection: 'column',
    flex: '1 0 0%',
    // border: '1px solid #d2dce1',
    borderLeftWidth: 0,
    p: '5px 8px',
    // '&:hover': {
    //   border: '1px solid #FCAB28',
    //   ml: '-1px',
    //   transition: '0.2s ease',
    //   bgcolor: '#fdf6e2',
    //   '.calendar-day-text': {
    //     color: 'primary.main',
    //   },
    //   '.calendar-date-text-wrapper': {
    //     bgcolor: 'latte',
    //   },
    //   '.calendar-date-text': {
    //     color: 'primary.main',
    //   },
    // },
    '&:last-child': {
      borderRadius: '0px 8px 0px 0px',
    },
  },
  headerDayText: {
    fontSize: 10,
    lineHeight: '12px',
    textAlign: 'center',
    fontWeight: 500,
  },
  headerDateText: {
    fontSize: 14,
    lineHeight: '20px',
    fontWeight: 500,
    textAlign: 'center',
    width: 20,
  },
} as Record<string, SxProps<Theme>>;

export default styles;

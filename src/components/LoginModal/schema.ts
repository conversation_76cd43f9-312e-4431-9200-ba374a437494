import { t } from 'i18n';
import { Regex } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  email: string()
    .required()
    .trim()
    .matches(Regex.EMAIL, t('validation.invalidField'))
    .lowercase(),
  password: string().required(),
});

export type LoginFormValues = InferType<typeof schema>;
export default schema;

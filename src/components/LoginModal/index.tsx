import useGlobalState from 'hooks/useGlobalState';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import type { ROLES } from 'utils/constants';

const LoginModal = dynamic(() => import('./modal'));

const LoginModalContainer = () => {
  const { asPath, replace, query, pathname } = useRouter();
  const { loginModal, setLoginModal } = useGlobalState();

  useEffect(() => {
    if (asPath.startsWith('/login')) {
      setLoginModal(true);
    }
  }, [asPath, replace, setLoginModal]);

  useEffect(() => {
    setLoginModal(false);
  }, [pathname, setLoginModal]);

  if (loginModal) {
    return <LoginModal defaultRole={query.role as ROLES} />;
  }
  return null;
};

export default LoginModalContainer;

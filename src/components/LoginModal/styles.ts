import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  dialogTitle: { p: { xs: 2, tablet: '30px 32px 24px' }, letterSpacing: '2px' },
  dialogContent: {
    p: { xs: '0px 16px 24px', tablet: '0px 32px 40px' },
  },
  tab: {
    minHeight: { xs: 'auto', tablet: 48 },
    padding: '12px',
    fontSize: { xs: 12, tablet: 14 },
    '&:not(.Mui-selected)': {
      borderBottom: (theme) => `1px solid ${theme.palette.placeholder}`,
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

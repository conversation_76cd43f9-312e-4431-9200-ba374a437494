import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Divider,
  Stack,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import variants from 'components/Animation/variants';
import { TextField } from 'components/Form';
import MuiLink from 'components/Link';
import { AnimatePresence, motion } from 'framer-motion';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { LoginPayload } from 'hooks/useAuth/types';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import i18n from 'i18n';
import { BlackPathLeft, BlackPathRight } from 'icons';
import type { LoginResponse } from 'models/auth/interface';
import authQuery from 'models/auth/query';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { NON_LOGIN_ROUTE, ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

import type { LoginFormValues } from './schema';
import schema from './schema';
import styles from './styles';

const LoginModal = ({ defaultRole }: { defaultRole?: ROLES }) => {
  const { loginModal, setLoginModal, authChannel } = useGlobalState();
  const [enabledUser, setEnabledUser] = useState(false);
  const [role, setRole] = useState(ROLES.CUSTOMER);
  useFetchUser({ enabled: enabledUser });
  useFetchDetail({
    ...apiQuery.consultationDetail,
    enabled: enabledUser,
    staleTime: Infinity,
  });

  // Get email from loginModal if available
  const defaultEmail =
    typeof loginModal === 'object' && loginModal.email ? loginModal.email : '';

  const { mutateAsync: login, isLoading: isLoggingIn } = useMutate<
    LoginFormValues,
    LoginResponse
  >(authQuery.login(role));
  const { t } = i18n;
  const { replace, pathname, asPath, reload } = useRouter();

  useEffect(() => {
    if (defaultRole) {
      setRole(defaultRole);
    }
  }, [defaultRole]);

  const handleLogin = (values: LoginFormValues) => {
    login(values, {
      onSuccess: (data, variables) => {
        const { token } = data.token;
        const tokenByGuest = Helper.getWebCookieByGuest();
        if (tokenByGuest) Helper.removeWebCookieByGuest();
        if (role === ROLES.CUSTOMER) {
          Helper.setToken({ token, role, email: variables.email });
          authChannel?.postMessage('logIn');
          if (asPath === '/' || asPath.startsWith('/guest')) {
            reload();
          } else {
            setEnabledUser(true);
            if (
              NON_LOGIN_ROUTE.includes(pathname) ||
              asPath.startsWith('/login')
            ) {
              replace('/');
            }
            setTimeout(() => setLoginModal(false));
          }
        } else if (data.provider?.type) {
          Helper.setToken({
            token,
            role: data.provider.type,
            email: variables.email,
          });
          authChannel?.postMessage('logIn');

          // Redirect to appropriate my-page based on provider type
          const redirectUrl =
            data.provider.type === ROLES.LAWYER
              ? '/lawyer/my-page'
              : '/counselor/my-page';

          setLoginModal(false);
          replace(redirectUrl);
        }
      },
    });
  };
  const { control, handleSubmit } = useForm<LoginPayload>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues: {
      email: defaultEmail,
    },
  });

  return (
    <Dialog
      onClose={(_, reason) => {
        if (reason === 'backdropClick') {
          setLoginModal(false);
        }
        if (asPath.startsWith('/login')) {
          replace('/');
        }
      }}
      open={!!loginModal}
      aria-labelledby="login-modal-title"
      aria-describedby="login-modal-description"
      maxWidth="fsf"
      fullWidth
    >
      <DialogTitle id="login-modal-title" sx={styles.dialogTitle}>
        <Typography
          fontSize={{ xs: 24, tablet: 32 }}
          fontWeight="bold"
          color="primary.main"
          textAlign="center"
        >
          {t('login.title')}
        </Typography>
        <Box className="logo-divider" mt={2}></Box>
      </DialogTitle>
      <DialogContent sx={styles.dialogContent}>
        <DialogContentText id="login-modal-description" component="div">
          <Box>
            <Tabs
              variant="fullWidth"
              value={role}
              sx={{ minHeight: 'unset' }}
              onChange={(_, value) => setRole(value)}
            >
              <Tab
                label={t('login.customer')}
                value={ROLES.CUSTOMER}
                sx={styles.tab}
              />
              <Tab
                label={t('login.lawyer')}
                value={ROLES.LAWYER}
                sx={styles.tab}
              />
            </Tabs>
            <form onSubmit={handleSubmit(handleLogin)}>
              <Stack spacing={2} mt={{ xs: 2, tablet: 5 }}>
                <TextField
                  labelCol={12}
                  name="email"
                  control={control}
                  label={t('login.email')}
                  placeholder={t('placeholder.email')}
                />
                <TextField
                  labelCol={12}
                  name="password"
                  control={control}
                  label={t('login.password')}
                  type="password"
                  placeholder={t('placeholder.password')}
                  extraLabel={
                    <MuiLink
                      href={{
                        pathname: '/forgot-password',
                        query: { role },
                      }}
                      underline="hover"
                    >
                      <Typography
                        fontSize={14}
                        fontWeight={500}
                        textAlign="right"
                      >
                        {t('login.forgotPassword')}
                      </Typography>
                    </MuiLink>
                  }
                />
              </Stack>
              <LoadingButton
                loading={isLoggingIn}
                variant="contained"
                color="yellow"
                size="large"
                type="submit"
                fullWidth
                className="tabletStyle shadow"
                sx={{ mt: 5 }}
              >
                {t('login.login')}
              </LoadingButton>
              <AnimatePresence mode="wait">
                {role === ROLES.CUSTOMER && (
                  <Box component={motion.div} {...variants.loginModal}>
                    <Divider
                      sx={{
                        mt: '36px',
                        borderBottomWidth: 1,
                        borderColor: '#dbe3e7',
                      }}
                    />

                    <Box
                      display="flex"
                      gap={1}
                      justifyContent="center"
                      mt={4}
                      mb={2}
                      sx={{
                        svg: {
                          width: { xs: 14, tablet: 15 },
                          height: { xs: 22, tablet: 25 },
                        },
                      }}
                    >
                      <BlackPathLeft />
                      <Typography
                        fontSize={{ tablet: 18 }}
                        fontWeight={500}
                        color="#464646"
                      >
                        登録されていない方は
                      </Typography>
                      <BlackPathRight />
                    </Box>
                    <Button
                      href="/register"
                      variant="outlined"
                      color="primary"
                      type="submit"
                      fullWidth
                      size="large"
                      className="shadow tabletStyle"
                      LinkComponent={Link}
                      onClick={() => setLoginModal(false)}
                      sx={{ marginBottom: '5px' }}
                    >
                      無料登録・無料相談
                    </Button>
                  </Box>
                )}
              </AnimatePresence>
            </form>
          </Box>
        </DialogContentText>
      </DialogContent>
    </Dialog>
  );
};

export default LoginModal;

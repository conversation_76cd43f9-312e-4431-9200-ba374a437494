/* eslint-disable react-hooks/exhaustive-deps */
import { auth } from 'features/chat/firebase';
import useAuthSignOut from 'features/chat/hooks/useAuthSignOut';
import useOnAuthStateChanged from 'features/chat/hooks/useOnAuthStateChanged';
import useSubscribeFirebaseUser from 'features/chat/hooks/useSubscribeFirebaseUser';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import useGlobalState from 'hooks/useGlobalState';
import { useRouter } from 'next/router';
import type { ReactNode } from 'react';
import { useEffect } from 'react';
import type { ROLES } from 'utils/constants';
import getRedirecttUrl from 'utils/getRedirectUrl';
import Helper from 'utils/helpers';

const DataProvider = ({ children }: { children: ReactNode }) => {
  const { authChannel } = useGlobalState();
  const { mutateAsync: signOutFirebase } = useAuthSignOut(auth);
  const { reload } = useRouter();
  const { data: currentUser } = useFetchUser({
    enabled: true,
  });
  const role = Helper.getUserRole() as ROLES;
  useSubscribeFirebaseUser({ firebaseUserId: currentUser?.firebaseUserId });

  useOnAuthStateChanged();
  useEffect(() => {
    if (currentUser?._id) {
      Helper.setEventTrackingUserId({
        userId: currentUser._id,
        userRole: role,
      });
    } else if (!currentUser?._id) {
      Helper.setEventTrackingUserId({
        userId: undefined,
        userRole: undefined,
      });
    }
  }, [currentUser?._id, role]);

  useFetchDetail({
    ...apiQuery.consultationDetail,
    enabled: Helper.isCustomer(),
    staleTime: Infinity,
  });

  const { pathname, replace } = useRouter();
  const redirectUrl = getRedirecttUrl({ role, nextUrl: pathname });

  useEffect(() => {
    if (redirectUrl) {
      replace(redirectUrl);
    }
  }, [redirectUrl, replace]);

  useEffect(() => {
    if (authChannel) {
      authChannel.onmessage = (e) => {
        if (e.data === 'logOut') {
          Helper.removeWebCookie();
          signOutFirebase();
          setTimeout(async () => reload());
        }
        if (e.data === 'logIn') {
          reload();
        }
      };
    }
  }, []);

  return <>{children}</>;
};

export default DataProvider;

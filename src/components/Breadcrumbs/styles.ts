import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  breadcrumb: {
    fontSize: '12px',
    '.MuiBreadcrumbs-separator': {
      mx: { xs: '4px', tablet: 1 },
      fontSize: { xs: 10, tablet: 12 },
    },
    '& li': {
      alignSelf: 'end',
      '&:last-child': {
        flex: 1,
      },
    },
  },
  home: {
    fontSize: { xs: 10, tablet: 12 },
    display: 'flex',
    alignItems: 'center',
  },
  activeItem: {
    color: 'text.primary',
    fontSize: { xs: 10, tablet: 12 },
  },
  inActiveItem: {
    color: 'placeholder',
    fontSize: { xs: 10, tablet: 12 },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import {
  Box,
  Breadcrumbs as MiuBreadcrumb,
  Skeleton,
  Typography,
} from '@mui/material';
import { t } from 'i18n';
import { HomeIcon } from 'icons';
import { isUndefined } from 'lodash';
import Link from 'next/link';
import router, { useRouter } from 'next/router';
import type { ReactNode } from 'react';
import React, { useEffect, useState } from 'react';

import styles from './styles';

const defaultBreadcrumbName: Record<string, string | ReactNode> = {
  '/lawyers': '弁護士を探す',
  '/counselors': 'カウンセラーを探す',
  '/articles': t('breadcrumb.articles'),
  '/pickup-articles': t('breadcrumb.articles'),
  '/contact-us': t('breadcrumb.contactUs'),
  '/company-info': t('breadcrumb.companyInfo'),
  '/faq': t('breadcrumb.faq'),
  '/policy': t('breadcrumb.policy'),
  '/terms': t('breadcrumb.terms'),
  // CUSTOMER BREADCRUMB
  '/customer/booking': t('breadcrumb.lawyers'),
  '/customer/my-page': t('breadcrumb.myPage'),
  '/customer/my-page/cases': t('breadcrumb.mypageCases'),
  '/customer/my-page/profile': t('breadcrumb.myPageProfile'),
  '/customer/my-page/consultation-record': t('breadcrumb.myPageConsultation'),
  '/customer/my-page/preference': t('breadcrumb.myPageExpectLawyer'),
  '/customer/my-page/cards': t('breadcrumb.card'),
  '/customer/counselor-booking': t('breadcrumb.counselors'),
  // LAWYER BREADCRUMB
  '/lawyer/my-page/cases/[caseId]/create': t('caseDetail.createMeeting'),
  '/lawyer/my-page': t('breadcrumb.myPage'),
  '/lawyer/my-page/profile': 'プロフィール',
  '/lawyers/[lawyerId]/solutions': t('breadcrumb.solution'),
  '/lawyer/my-page/cases': t('breadcrumb.cases'),
  '/lawyer/my-page/profile/add-solution': t('breadcrumb.solution'),
  '/lawyer/my-page/profile/edit-solution/[solutionId]': t(
    'breadcrumb.solution',
  ),
  '/lawyer/my-page/profile/add-consultation': t('breadcrumb.consultationMenu'),
  '/lawyer/my-page/profile/edit-consultation/[menuId]': t(
    'breadcrumb.consultationMenu',
  ),
  '/lawyer/my-page/mail-setting': 'メール送信先設定',
  // COUNSELOR BREADCRUM
  '/counselor/my-page': t('breadcrumb.myPage'),
  '/counselor/my-page/profile': 'プロフィール',
  '/counselor/my-page/cases': t('breadcrumb.cases'),
  '/counselor/my-page/shift': 'シフト設定',
  '/counselor/my-page/cases/[caseId]/create': t('caseDetail.createMeeting'),
  // GUEST BREADCRUM
  '/guest/counselor-booking': t('breadcrumb.counselors'),
};

const defaultBreadcrumbPath: Record<string, string> = {
  '/customer/booking': '/lawyers',
  '/customer/counselor-booking': '/counselors',
  '/guest/counselor-booking': '/counselors',
};

const convertBreadcrumb = (
  title: string | ReactNode,
  transformLabel?: Record<string, string | ReactNode>,
  breadcrumb?: Breadcrumb,
): React.ReactNode => {
  if (breadcrumb && defaultBreadcrumbName[breadcrumb.pathname]) {
    return defaultBreadcrumbName[breadcrumb.pathname];
  }
  if (breadcrumb && transformLabel && transformLabel[breadcrumb.pathname]) {
    return transformLabel[breadcrumb.pathname];
  }
  return title;
};

export interface Breadcrumb {
  breadcrumb: string;
  breadcrumbId: string;
  href: string;
  pathname: string;
}

export interface BreadcrumbsProps {
  omitRootLabel?: boolean;
  transformLabel?: Record<string, string | ReactNode>;
  omitIndexList?: Array<number> | undefined;
  containerStyle?: any | null;
}

const Breadcrumbs = ({
  omitRootLabel,
  transformLabel,
  omitIndexList,
  containerStyle,
}: BreadcrumbsProps) => {
  const { isReady, query, asPath, pathname } = useRouter();
  const [breadcrumbs, setBreadcrumbs] = useState<Array<Breadcrumb> | null>(
    null,
  );

  useEffect(() => {
    if (isReady) {
      const queryObject = Object.keys(query).reduce(
        (obj: Record<string, string>, current) => {
          const object = { ...obj };
          object[current] = query[current] as string;
          return object;
        },
        {},
      );
      const linkPath = asPath.split('/');
      const linkPathname = pathname.split('/');
      linkPath.shift();
      linkPathname.shift();

      const breadcrumbArray = linkPathname.map((path, i) => {
        return {
          breadcrumbId: path,
          breadcrumb: queryObject[path] || path,
          pathname: `/${linkPathname.slice(0, i + 1).join('/')}`,
          href: `/${linkPath.slice(0, i + 1).join('/')}`,
        };
      });
      setBreadcrumbs(breadcrumbArray);
    }
  }, [asPath, isReady, pathname, query]);

  return (
    <Box>
      <MiuBreadcrumb
        sx={{ ...styles.breadcrumb, ...containerStyle }}
        aria-label="breadcrumbs"
      >
        {!omitRootLabel && (
          <Link
            href={
              ['/lawyer/my-page'].includes(pathname) ? '/lawyer/my-page' : '/'
            }
          >
            <Typography sx={styles.home}>
              {convertBreadcrumb(<HomeIcon />, transformLabel)}
            </Typography>
          </Link>
        )}
        {!breadcrumbs && (
          <Skeleton
            variant="text"
            sx={{ fontSize: { xs: 10, tablet: 12 } }}
            width={100}
          />
        )}
        {breadcrumbs &&
          breadcrumbs.length >= 1 &&
          breadcrumbs.map((breadcrumb, i) => {
            if (
              !breadcrumb ||
              breadcrumb.breadcrumb.length === 0 ||
              (omitIndexList &&
                !isUndefined(omitIndexList.find((value) => value === i)))
            ) {
              return null;
            }
            if (breadcrumb.pathname === router.pathname) {
              return (
                <Typography
                  sx={styles.inActiveItem}
                  key={breadcrumb.breadcrumbId}
                  className="line-clamp"
                >
                  {convertBreadcrumb(
                    breadcrumb.breadcrumb,
                    transformLabel,
                    breadcrumb,
                  )}
                </Typography>
              );
            }
            return (
              <Link
                key={breadcrumb.breadcrumbId}
                href={{
                  pathname:
                    defaultBreadcrumbPath[breadcrumb.href] || breadcrumb.href,
                }}
              >
                <Typography sx={styles.activeItem} className="line-clamp">
                  {convertBreadcrumb(
                    breadcrumb.breadcrumb,
                    transformLabel,
                    breadcrumb,
                  )}
                </Typography>
              </Link>
            );
          })}
      </MiuBreadcrumb>
    </Box>
  );
};

export default Breadcrumbs;

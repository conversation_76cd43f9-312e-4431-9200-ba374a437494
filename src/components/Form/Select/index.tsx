import type { BoxProps } from '@mui/material';
import { Box, FormControl, Grid, IconButton, Typography } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import type { SelectChangeEvent, SelectProps } from '@mui/material/Select';
import MuiSelect from '@mui/material/Select';
import i18n from 'i18n';
import { CloseIcon } from 'icons';
import { isEmpty } from 'lodash';
import type { ReactNode } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from '../styles';

export interface IOption {
  _id: string | number;
  value: string | number;
  icon?: JSX.Element;
}
interface SelectFieldProps<TFormValues extends FieldValues> {
  label?: string;
  required?: boolean;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  maxLength?: number;
  data: { _id: string | number; value: string | number; icon?: JSX.Element }[];
  labelCol?: number;
  columns?: number;
  viewMode?: boolean;
  extraLabel?: string | ReactNode;
  labelflexDirection?: BoxProps['flexDirection'];
  helperText?: ReactNode;
  placeholder?: string;
  hideError?: boolean;
  containerProps?: BoxProps;
  allowClear?: boolean;
  handleChange?: () => void;
  adornment?: string | ReactNode;
}

const Select = <TFormValues extends FieldValues>({
  label,
  data = [],
  required,
  maxLength,
  control,
  name,
  placeholder,
  labelCol = 3,
  columns = 12,
  viewMode,
  extraLabel,
  helperText,
  hideError = false,
  allowClear = true,
  handleChange,
  containerProps,
  adornment,
  labelflexDirection,
  ...props
}: SelectFieldProps<TFormValues> & SelectProps) => {
  const { t } = i18n;
  const {
    field: { value = '', onChange, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const isHorizontalLayout = labelCol < columns;

  const handleChangeValue = (e: SelectChangeEvent<unknown>) => {
    onChange(e);
    if (handleChange) {
      handleChange();
    }
  };
  return (
    <Box {...containerProps}>
      <Grid
        container
        columns={columns}
        columnSpacing={isHorizontalLayout ? 4 : 0}
      >
        {label && (
          <Label
            flexDirection={labelflexDirection}
            label={label}
            labelCol={labelCol}
            required={required}
            extraLabel={extraLabel}
          />
        )}
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
          component="span"
        >
          {!viewMode ? (
            <FormControl fullWidth sx={{ justifyContent: 'center' }}>
              <Box display="flex" alignItems="center">
                <MuiSelect
                  className="tabletStyle"
                  sx={styles.input}
                  IconComponent={(iconProps) => {
                    if (allowClear && !isEmpty(value?.toString())) {
                      return (
                        <IconButton
                          sx={styles.clearButton}
                          onClick={() => {
                            onChange('');
                            if (handleChange) {
                              handleChange();
                            }
                          }}
                        >
                          <CloseIcon />
                        </IconButton>
                      );
                    }
                    return (
                      <img
                        {...iconProps}
                        alt="arrow-down"
                        src="/icons/arrow-down.svg"
                      />
                    );
                  }}
                  onChange={handleChangeValue}
                  {...otherField}
                  {...props}
                  value={value}
                  error={!!error}
                  displayEmpty
                  renderValue={
                    value !== ''
                      ? undefined
                      : () => <Box sx={styles.placeholder}>{placeholder}</Box>
                  }
                  inputProps={{ maxLength }}
                  MenuProps={{
                    PaperProps: {
                      sx: {
                        px: 1,
                        maxWidth: { tablet: 510 },
                      },
                    },
                    ...props.MenuProps,
                  }}
                >
                  {isEmpty(data) && (
                    <MenuItem key="none" value="" disabled={required}>
                      <Box sx={styles.placeholder}>{placeholder}</Box>
                    </MenuItem>
                  )}
                  {data.map((item) => (
                    <MenuItem
                      key={item._id}
                      value={item._id}
                      sx={styles.selectMenuItem}
                    >
                      {item.icon ? (
                        <Box display="flex">
                          <Box
                            mr={1}
                            alignSelf="center"
                            sx={{
                              width: 'fit-content',
                              svg: {
                                display: 'block',
                                maxHeight: { xs: '20px', tablet: '24px' },
                                maxWidth: { xs: '30px', tablet: '36px' },
                              },
                            }}
                          >
                            {item.icon}
                          </Box>

                          <Typography>{item.value}</Typography>
                        </Box>
                      ) : (
                        item.value
                      )}
                    </MenuItem>
                  ))}
                </MuiSelect>
                {adornment}
              </Box>
            </FormControl>
          ) : (
            <Typography color={value ? 'text.primary' : 'hint'} mt="12px">
              {data.find((item) => item._id === value)?.value ||
                t('global.noInfo')}
            </Typography>
          )}
        </Grid>
        {!viewMode && !hideError && (
          <>
            <Grid item xs={columns} tablet={labelCol} />
            <Grid
              item
              xs={columns}
              tablet={labelCol < columns ? columns - labelCol : columns}
            >
              <HelperText error={error?.message} helperText={helperText} />
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
};

export default Select;

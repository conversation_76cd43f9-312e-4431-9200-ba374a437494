import type { BoxProps } from '@mui/material';
import { Box, Grid, Typography } from '@mui/material';
import type { ReactNode } from 'react';

import styles from './styles';

interface LabelProps {
  label: string | ReactNode;
  required?: boolean;
  labelCol?: number;
  extraLabel?: string | ReactNode;
  paddingTop?: string | number;
  flexDirection?: BoxProps['flexDirection'];
}

const Label = ({
  label,
  required,
  labelCol = 4,
  extraLabel,
  paddingTop = '12px',
  flexDirection = 'row',
}: LabelProps) => {
  const isHorizontalLayout = labelCol < 12;
  return (
    <Grid item xs={12} tablet={labelCol}>
      <Box
        display="flex"
        flexDirection={flexDirection}
        justifyContent="space-between"
        pt={{ tablet: isHorizontalLayout ? paddingTop : 0 }}
        mb={{ xs: 1, tablet: paddingTop }}
      >
        <Box
          display="flex"
          alignItems="center"
          flex={1}
          justifyContent={{
            tablet: isHorizontalLayout ? 'space-between' : 'unset',
          }}
        >
          <Typography sx={styles.labelRoot} className="field-label">
            {label}
          </Typography>
          {required && <Box sx={styles.required}>必須</Box>}
        </Box>
        {extraLabel && <Box>{extraLabel}</Box>}
      </Box>
    </Grid>
  );
};

export default Label;

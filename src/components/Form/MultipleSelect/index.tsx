import {
  Box,
  FormControl,
  Grid,
  MenuItem,
  Select as MuiSelect,
  Stack,
  Typography,
} from '@mui/material';
import type { SelectChangeEvent, SelectProps } from '@mui/material/Select';
import type { IListItem } from 'hooks/types';
import { find, get } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import styles from '../styles';

interface SelectFieldProps<TFormValues extends FieldValues> {
  label?: string;
  required?: boolean;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  maxLength?: number;
  data: IListItem[];
  labelCol?: number;
  columns?: number;
  placeholder: string;
}

const MultipleSelect = <TFormValues extends FieldValues>({
  label,
  data = [],
  required,
  maxLength,
  control,
  name,
  placeholder,
  columns = 12,
  labelCol = 3,
  ...props
}: SelectFieldProps<TFormValues> & SelectProps) => {
  const {
    field: { value = [], onChange, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleChange = (event: SelectChangeEvent<typeof value>) => {
    const eventValue = get(event, 'target.value');
    onChange(
      typeof eventValue === 'string' ? eventValue.split(',') : eventValue,
    );
  };

  return (
    <div>
      <Grid container columnSpacing={4}>
        {label && (
          <Grid item xs={12} tablet={labelCol}>
            <Stack direction="row">
              <Typography sx={styles.labelRoot}>
                <strong>{label}</strong>
              </Typography>
              {required && (
                <Box display="flex" alignItems="center">
                  <Box sx={styles.required}>必須</Box>
                </Box>
              )}
            </Stack>
          </Grid>
        )}
        <Grid
          item
          xs={12}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
        >
          <FormControl fullWidth sx={{ justifyContent: 'center' }}>
            <MuiSelect
              sx={styles.input}
              {...props}
              {...otherField}
              defaultValue={value}
              value={value}
              error={!!error}
              displayEmpty
              multiple
              onChange={handleChange}
              inputProps={{ maxLength }}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected
                    .map((item) => find(data, (i) => i._id === item)?.value)
                    .join('、')}
                </Box>
              )}
              IconComponent={(iconProps) => {
                return (
                  <img
                    {...iconProps}
                    alt="arrow-down"
                    src="/icons/arrow-down.svg"
                  />
                );
              }}
            >
              <MenuItem key="none" value="" disabled>
                <Box sx={styles.placeholder}>{placeholder}</Box>
              </MenuItem>
              {data.map((item) => (
                <MenuItem key={item._id} value={item._id}>
                  {item.value}
                </MenuItem>
              ))}
            </MuiSelect>
          </FormControl>
        </Grid>
        <Grid item xs={12} tablet={labelCol} />
        <Grid
          item
          xs={12}
          tablet={labelCol < columns ? columns - labelCol : columns}
        >
          <HelperText error={error?.message} />
        </Grid>
      </Grid>
    </div>
  );
};

export default MultipleSelect;

import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Grid,
  IconButton,
  MenuItem,
  Select,
  Stack,
  SvgIcon,
  TextField,
  Typography,
} from '@mui/material';
import type { DateTimePickerProps as MuiDateTimePickerProps } from '@mui/x-date-pickers/DateTimePicker';
import { DesktopDateTimePicker as MuiDateTimePicker } from '@mui/x-date-pickers/DesktopDateTimePicker';
import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import useBreakpoint from 'hooks/useBreakpoint';
import { t } from 'i18n';
import { CalendarIcon, CloseIcon } from 'icons';
import { isEmpty, omit } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { MomentFormat } from 'utils/constants';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from '../styles';

const HOUR_RANGE = [
  '09',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
];

const MINUTE_RANGE = ['00', '15', '30', '45'];
interface DateTimePickerProps<TFormValues extends FieldValues>
  extends Omit<
    MuiDateTimePickerProps<Date | Dayjs, Date | Dayjs>,
    'onChange' | 'renderInput' | 'value'
  > {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: string;
  required?: boolean;
  labelCol?: number;
  columns?: number;
  placeholder?: string;
  onSave?: (date: string) => void;
}

const DatePicker = <TFormValues extends FieldValues>({
  label,
  required,
  control,
  name,
  labelCol = 3,
  columns = 12,
  inputFormat = MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
  placeholder,
  minDate,
  onSave,
  ...props
}: DateTimePickerProps<TFormValues>) => {
  const {
    field: { value = null, onBlur, onChange, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const ref = useRef<HTMLInputElement>();
  const [open, setOpen] = useState(false);
  const [dateValue, setDateValue] = useState<Dayjs | null>(null);
  const [hour, setHour] = useState<string>();
  const [minute, setMinute] = useState<string>();
  const isBreakpoint = useBreakpoint({});
  const isHorizontalLayout = labelCol < 12;

  useEffect(() => {
    if (value) {
      setDateValue(dayjs(value));
      setHour(dayjs(value).format('HH'));
      setMinute(dayjs(value).format('mm'));
    } else {
      setDateValue(null);
    }
  }, [value]);

  useEffect(() => {
    if (open) {
      ref?.current?.focus();
    }
  }, [dateValue, open]);

  const handleSetFieldValue = () => {
    onBlur();
    setOpen(false);
    if (dateValue && hour && minute) {
      const dateTimeValue = dateValue
        .set('hour', Number(hour))
        .set('minute', Number(minute))
        .toISOString();
      if (onSave) {
        onSave(dateTimeValue);
      }
      onChange(dateTimeValue);
    }
  };

  const inputProps = {
    ...(!isEmpty(value)
      ? {
          InputProps: {
            endAdornment: (
              <IconButton
                sx={styles.clearButton}
                onClick={(e) => {
                  e.stopPropagation();
                  onChange(null);
                }}
              >
                <CloseIcon />
              </IconButton>
            ),
          },
        }
      : {}),
  };

  return (
    <div className="date-time-picker">
      <Grid
        container
        columnSpacing={isHorizontalLayout ? 4 : 0}
        columns={columns}
      >
        {label && (
          <Label label={label} labelCol={labelCol} required={required} />
        )}
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
        >
          <MuiDateTimePicker
            {...props}
            {...otherField}
            value={value}
            onChange={onChange}
            readOnly
            inputFormat={inputFormat}
            disableMaskedInput
            components={{
              OpenPickerIcon: () => (
                <SvgIcon component={CalendarIcon} className="tabletStyle" />
              ),
            }}
            renderInput={(params) => {
              return (
                <TextField
                  sx={[styles.input, styles.datepicker] as never}
                  {...omit(params, ['onKeyUp', 'onKeyDown'])}
                  {...params}
                  size={isBreakpoint ? 'small' : 'medium'}
                  margin="none"
                  error={!!error}
                  onClick={() => {
                    setOpen(true);
                  }}
                  inputProps={{
                    ...params.inputProps,
                    placeholder: placeholder || params.inputProps?.placeholder,
                  }}
                  {...inputProps}
                />
              );
            }}
          />
        </Grid>
        <Grid item xs={columns} tablet={labelCol} />
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
        >
          <HelperText error={error?.message} />
        </Grid>
      </Grid>
      <Dialog
        onClose={(_, reason) => {
          if (reason === 'backdropClick') {
            // onClose();
          }
        }}
        open={open}
        aria-labelledby="timepicker-modal-title"
        aria-describedby="timepicker-modal-description"
        maxWidth="fcol"
      >
        <DialogContent sx={{ p: 2 }}>
          <Box>
            <StaticDatePicker
              displayStaticWrapperAs="desktop"
              value={dateValue}
              minDate={minDate}
              onChange={(newValue) => {
                if (dayjs.isDayjs(newValue)) {
                  if (hour && minute) {
                    const combinedTime = newValue
                      .set('hour', Number(hour))
                      .set('minute', Number(minute));
                    setDateValue(combinedTime);
                  }
                  setDateValue(newValue);
                }
              }}
              renderInput={(params) => <TextField {...params} />}
            />
            <Box sx={{ width: 1, maxWidth: 280, margin: '16px auto' }}>
              <Box display="flex" gap={1} alignItems="center">
                <Select
                  displayEmpty
                  renderValue={
                    (hour || '') !== ''
                      ? undefined
                      : () => <Box sx={styles.placeholder}>hh</Box>
                  }
                  value={hour || ''}
                  disabled={!dateValue}
                  placeholder="hh"
                  sx={{ width: '100%' }}
                  onChange={(selectedValue) => {
                    if (selectedValue.target.value) {
                      setHour(selectedValue.target.value);
                    }
                  }}
                >
                  {HOUR_RANGE.map((timeOpt) => (
                    <MenuItem key={timeOpt} value={timeOpt}>
                      {timeOpt}
                    </MenuItem>
                  ))}
                </Select>
                時
                <Select
                  displayEmpty
                  renderValue={
                    (minute || '') !== ''
                      ? undefined
                      : () => <Box sx={styles.placeholder}>mm</Box>
                  }
                  value={minute || ''}
                  disabled={!dateValue}
                  placeholder="hh"
                  sx={{ width: '100%', ml: 1 }}
                  onChange={(selectedValue) => {
                    if (selectedValue.target.value) {
                      setMinute(selectedValue.target.value);
                    }
                  }}
                >
                  {MINUTE_RANGE.map((timeOpt) => (
                    <MenuItem key={timeOpt} value={timeOpt}>
                      {timeOpt}
                    </MenuItem>
                  ))}
                </Select>
                分
              </Box>
              <Typography fontSize={12} color="#b0aca2" mt="12px">
                個別の営業時間をご確認ください
              </Typography>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: '0px 16px 16px' }}>
          <Stack spacing={1} direction="row">
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                onBlur();
                setOpen(false);
              }}
              sx={{ width: 112 }}
            >
              {t('global.cancel')}
            </Button>
            <Button
              variant="contained"
              color="secondary"
              className="icon-button"
              onClick={handleSetFieldValue}
              fullWidth
              disabled={!hour || !minute || !dateValue?.isValid()}
              sx={{ width: 112, svg: { color: 'white' } }}
            >
              保存
            </Button>
          </Stack>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default DatePicker;

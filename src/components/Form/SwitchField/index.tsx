import type { BoxProps, SwitchProps } from '@mui/material';
import { Box, Switch } from '@mui/material';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

interface RatingInputProps<TFormValues extends FieldValues>
  extends SwitchProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  containerProps?: BoxProps;
}

const SwitchField = <TFormValues extends FieldValues>({
  control,
  name,
  containerProps,
  ...props
}: RatingInputProps<TFormValues>) => {
  const {
    field: { value, onChange, onBlur },
  } = useController({
    name,
    control,
  });

  return (
    <Box {...containerProps}>
      <Switch
        name={name}
        checked={value}
        onChange={onChange}
        onBlur={onBlur}
        {...props}
      />
    </Box>
  );
};

export default SwitchField;

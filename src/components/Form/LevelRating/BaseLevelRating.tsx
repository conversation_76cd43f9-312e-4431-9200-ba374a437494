import type { TypographyProps } from '@mui/material';
import { Box, ButtonBase, Stack, Typography } from '@mui/material';

export interface BaseLevelRatingProps {
  value: number;
  max?: number;
  onChange?: (value: number) => void;
  minText?: string;
  maxText?: string;
  label?: string;
  disabled?: boolean;
  labelProps?: TypographyProps;
}
const BaseLevelRating = ({
  value,
  max = 5,
  onChange,
  minText,
  maxText,
  label,
  disabled,
  labelProps,
}: BaseLevelRatingProps) => {
  return (
    <Box>
      {label && (
        <Typography fontWeight={700} mb="4px" {...labelProps}>
          {label}
        </Typography>
      )}
      <Stack direction="row">
        {Array.from(Array(max).keys()).map((number) => {
          let borderRadius = '0px';
          if (number === 0) {
            borderRadius = '4px 0px 0px 4px';
          } else if (number === 4) {
            borderRadius = '0px 4px 4px 0px';
          }

          return (
            <ButtonBase
              disabled={disabled}
              key={number}
              onClick={() => onChange && onChange(number + 1)}
              sx={{
                width: `calc(100%/${max})`,
                border: '2px solid rgb(255, 255, 255)',
                height: 16,
                bgcolor:
                  value === number + 1
                    ? 'rgb(246, 173, 60)'
                    : 'rgb(228, 234, 237)',
                borderRadius: { borderRadius },
              }}
            />
          );
        })}
      </Stack>
      <Box display="flex" justifyContent="space-between" mt="2px">
        <Typography fontSize={12}>{minText}</Typography>
        <Typography fontSize={12}>{maxText}</Typography>
      </Box>
    </Box>
  );
};

export default BaseLevelRating;

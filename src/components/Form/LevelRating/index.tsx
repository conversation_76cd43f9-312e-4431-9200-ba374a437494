import type { BoxProps, RatingProps } from '@mui/material';
import { Box } from '@mui/material';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import BaseLevelRating from './BaseLevelRating';

interface LevelRatingProps<TFormValues extends FieldValues>
  extends RatingProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  containerProps?: BoxProps;
  label?: string;
  minText?: string;
  maxText?: string;
  helperText?: string;
}

const LevelRating = <TFormValues extends FieldValues>({
  control,
  name,
  label,
  minText,
  maxText,
  containerProps,
  helperText,
}: LevelRatingProps<TFormValues>) => {
  const {
    field: { value = null, onChange },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <Box {...containerProps}>
      <BaseLevelRating
        value={value || 0}
        onChange={onChange}
        minText={minText}
        maxText={maxText}
        label={label}
      />
      <HelperText error={error?.message} helperText={helperText} />
    </Box>
  );
};

export default LevelRating;

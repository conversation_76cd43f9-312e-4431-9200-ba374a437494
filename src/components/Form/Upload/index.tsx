import {
  <PERSON><PERSON>,
  Box,
  CircularProgress,
  Icon<PERSON>utton,
  Stack,
  Typography,
} from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { motion } from 'framer-motion';
import { AddIcon, TrashIcon } from 'icons';
import { set } from 'lodash';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import type { ChangeEvent } from 'react';
import { useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import api from 'utils/api';
import Helper from 'utils/helpers';

import HelperText from '../HelperText';
import styles from '../styles';

const ImageCrop = dynamic(() => import('./ImageCrop'));

interface UploadFieldProps<TFormValues extends FieldValues> {
  label?: string;
  required?: boolean;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  maxItems?: number;
  helperText?: string;
}

const readFile = (file: File) =>
  new Promise((resolve) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => resolve(reader.result), false);
    reader.readAsDataURL(file);
  });

const UploadAvatar = <TFormValues extends FieldValues>({
  name,
  control,
  maxItems = 3,
  label,
  required,
  helperText,
}: UploadFieldProps<TFormValues>) => {
  const {
    field: { value = [], onChange },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const [imageSrc, setImageSrc] = useState<{
    base64: string;
    fileName: string;
    contentType: string;
  } | null>();
  const [loading, setLoading] = useState(false);

  const { mutateAsync: uploadImage } = useMutation(
    async (params: { file: File; isPublic: boolean }) => {
      const { data } = await api.get(
        '/media/common/signedUrlForPuttingObject',
        {
          params: {
            isPublic: params.isPublic,
            fileName: params.file.name,
            contentType: params.file.type,
          },
        },
      );
      const urlObj = new URL(data.url);
      const url = `${urlObj.origin}${urlObj.pathname}`;

      await axios.put(data.url, params.file, {
        headers: {
          'Content-Type': params.file.type,
        },
      });
      return { ...data, originUrl: url };
    },
    {
      mutationKey: ['signedUrl'],
    },
  );
  const onFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (file) {
        if (!Helper.checkValidImage(file)) {
          return;
        }
        const imageDataUrl = await readFile(file);
        setImageSrc({
          base64: imageDataUrl as string,
          fileName: file.name,
          contentType: file.type,
        });
      }
    }
    set(e, 'target.value', null);
  };

  const handleRemoveImage = (image: { key: string; originUrl: string }) => {
    const newValue = value.filter(
      (item: { key: string; url: string }) => item.key !== image.key,
    );
    onChange(newValue);
  };
  const handleClose = () => {
    setImageSrc(null);
  };

  const handleSave = ({ file }: { file: File; originUrl: string }) => {
    try {
      setImageSrc(null);
      setLoading(true);
      uploadImage(
        { file, isPublic: true },
        {
          onSuccess: (data) => {
            onChange(value.concat(data));
          },
          onSettled: () => setLoading(false),
        },
      );
    } catch {
      setLoading(false);
    }
  };

  return (
    <div>
      {label && (
        <Typography sx={{ ...styles.labelRoot, mb: 3 }}>
          <strong>{label}</strong>
          {required && (
            <Box component="span" sx={styles.required}>
              必須
            </Box>
          )}
        </Typography>
      )}
      <Stack direction="row" spacing={2}>
        {value.map((image: { key: string; originUrl: string }) => (
          <motion.div
            key={image.key}
            layout
            transition={{
              type: 'spring',
              stiffness: 350,
              damping: 25,
            }}
          >
            <Box key={image.key} sx={styles.imageContainer}>
              <Image
                src={image.originUrl}
                alt=""
                fill
                sizes="100vw"
                style={{ borderRadius: '50%' }}
              />
              <Box sx={styles.overlayContainer}>
                <Box className="overlay" />
                <IconButton
                  onClick={() => handleRemoveImage(image)}
                  size="small"
                  disabled={loading}
                  className="delete-btn"
                >
                  {loading ? <CircularProgress size="small" /> : <TrashIcon />}
                </IconButton>
              </Box>
            </Box>
          </motion.div>
        ))}
        {value.length < maxItems && (
          <motion.div
            key="add"
            layout
            transition={{
              type: 'spring',
              stiffness: 350,
              damping: 25,
            }}
          >
            <label htmlFor="avatar" style={{ position: 'relative' }}>
              <Avatar
                alt="avatar"
                sx={{
                  width: 136,
                  height: 136,
                  cursor: 'pointer',
                  bgcolor: 'backgroundColor',
                  boxShadow:
                    error?.message && !loading
                      ? '0 0 0 1px #db5a42, inset 0 0 0 1px #db5a42, inset 0 0 0 3px #fff'
                      : 'unset',
                  svg: {
                    color: '#A8B0B4',
                    width: 32,
                    height: 32,
                  },
                }}
              >
                {<AddIcon />}
              </Avatar>
              {loading && (
                <CircularProgress
                  thickness={1}
                  color="primary"
                  size={136}
                  sx={{
                    position: 'absolute',
                    top: 0,
                  }}
                />
              )}
            </label>
            <Box sx={{ display: 'none' }}>
              <input
                type="file"
                id="avatar"
                name="avatar"
                disabled={loading}
                onChange={onFileChange}
                accept="image/*"
              />
            </Box>
          </motion.div>
        )}
        {!!imageSrc && (
          <ImageCrop
            image={imageSrc}
            handleClose={handleClose}
            width={300}
            height={300}
            borderRadius={300}
            onSave={handleSave}
          />
        )}
      </Stack>
      <Box mt="21px">
        <HelperText helperText={helperText} error={error?.message} />
      </Box>
    </div>
  );
};

export default UploadAvatar;

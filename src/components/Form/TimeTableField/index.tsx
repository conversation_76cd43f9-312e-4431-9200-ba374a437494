import type { OutlinedInputProps } from '@mui/material';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  LinearProgress,
  OutlinedInput,
  Stack,
} from '@mui/material';
import TimeTable from 'components/TimeTable';
import Toolbar from 'components/TimeTable/Toolbar';
import type { ISlotItem } from 'components/TimeTable/utils';
import dayjs from 'dayjs';
import { t } from 'i18n';
import { CalendarIcon, CloseIcon } from 'icons';
import { useCallback, useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { MomentFormat, SlotStatus } from 'utils/constants';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from '../styles';

interface TimeTableFieldProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: string;
  required?: boolean;
  labelCol?: number;
  columns?: number;
  placeholder?: string;
  data: ISlotItem[];
  startTime: string;
  duration: number;
  inputFormat?: string;
  onNavigate: (date: string) => void;
  loading?: boolean;
  isHasBookingSlot?: boolean;
  disabledFutureMinutes?: number;
}

const TimeTableField = <TFormValues extends FieldValues>({
  label,
  required,
  control,
  name,
  labelCol = 3,
  columns = 12,
  inputFormat = MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
  data,
  startTime,
  duration,
  onNavigate,
  loading,
  isHasBookingSlot,
  disabledFutureMinutes = 0,
  ...props
}: TimeTableFieldProps<TFormValues> & OutlinedInputProps) => {
  const {
    field: { value = '', onBlur, onChange, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const [open, setOpen] = useState(false);
  const [dateValue, setDateValue] = useState<string>();
  const isHorizontalLayout = labelCol < 12;

  const handleSetFieldValue = () => {
    onBlur();
    setOpen(false);
    if (dateValue) {
      onChange(dateValue);
    }
  };

  const handleChange = useCallback((timeSlot: string) => {
    setDateValue(timeSlot);
  }, []);

  return (
    <div className="date-time-picker">
      <Grid
        container
        columnSpacing={isHorizontalLayout ? 4 : 0}
        columns={columns}
      >
        {label && (
          <Label label={label} labelCol={labelCol} required={required} />
        )}
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
        >
          <OutlinedInput
            className="tabletStyle"
            sx={styles.input}
            size="medium"
            {...props}
            {...otherField}
            value={value ? dayjs(value).format(inputFormat) : ''}
            onChange={onChange}
            readOnly
            error={!!error}
            endAdornment={
              value && !props.disabled ? (
                <IconButton
                  sx={styles.clearButton}
                  onClick={(e) => {
                    e.stopPropagation();
                    onChange('');
                  }}
                >
                  <CloseIcon />
                </IconButton>
              ) : (
                <IconButton disabled={props.disabled} sx={styles.clearButton}>
                  <CalendarIcon />
                </IconButton>
              )
            }
            onClick={() => {
              setOpen(true);
            }}
          />
        </Grid>
        <Grid item xs={columns} tablet={labelCol} />
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
        >
          <HelperText error={error?.message} />
        </Grid>
      </Grid>
      <Dialog
        open={open}
        aria-labelledby="choose-time-modal-title"
        maxWidth="mw"
        fullWidth
      >
        <DialogTitle
          id="choose-time-modal-title"
          sx={styles.dialogTitle}
          position="relative"
        >
          <Box
            overflow="auto"
            pb="6px"
            sx={{ scrollbarGutter: 'stable' }}
            boxShadow="0 8px 16px -12px #d2dce1"
            zIndex={1}
            position="relative"
          >
            <Toolbar startTime={startTime} onNavigate={onNavigate} />
          </Box>
          {loading && (
            <LinearProgress
              sx={{
                position: 'absolute',
                width: 'calc(100%)',
                zIndex: 1,
                bottom: -4,
                left: 0,
              }}
            />
          )}
        </DialogTitle>
        <DialogContent
          sx={{
            p: { xs: '0px 16px 0px', tablet: '0px 32px 24px' },
            position: 'relative',
          }}
        >
          <TimeTable
            data={data}
            duration={duration}
            onChange={handleChange}
            startTime={startTime}
            value={value}
            closeStatus={[SlotStatus.CLOSED_BY_PROVIDER]}
            openStatus={[SlotStatus.OPEN_HAS_MEETING, SlotStatus.OPEN]}
            decidedStatus={[SlotStatus.CLOSED_BY_MEETING]}
            isHasBookingSlot={isHasBookingSlot}
            disabledFutureMinutes={disabledFutureMinutes}
            enableSkipSlot={true}
          />
        </DialogContent>
        <DialogActions sx={{ p: { xs: '16px', tablet: '16px 24px 24px' } }}>
          <Stack
            spacing={{ xs: '7px', tablet: 2 }}
            maxWidth={416}
            width="100%"
            margin="0px auto"
            direction="row"
          >
            <Button
              variant="outlined"
              size="large"
              className="tabletStyle"
              onClick={() => {
                onBlur();
                setOpen(false);
              }}
              fullWidth
            >
              {t('global.cancel')}
            </Button>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              className="tabletStyle"
              fullWidth
              onClick={handleSetFieldValue}
              disabled={dateValue === ''}
            >
              {t('global.saving')}
            </Button>
          </Stack>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default TimeTableField;

import type { OutlinedInputProps } from '@mui/material';
import { Box, Grid, LinearProgress } from '@mui/material';
import TimeTable from 'components/TimeTable';
import type { ISlotItem } from 'components/TimeTable/utils';
import { useCallback } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { SlotStatus } from 'utils/constants';

import HelperText from '../HelperText';
import Label from '../Label';
import Toolbar from './Toolbar';

interface TimeTableFieldProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: string;
  required?: boolean;
  labelCol?: number;
  columns?: number;
  placeholder?: string;
  data: ISlotItem[];
  startTime: string;
  duration: number;
  onNavigate: (date: string) => void;
  loading?: boolean;
  isHasBookingSlot?: boolean;
  disabledFutureMinutes?: number;
}

const TimeTableFieldWithoutDialog = <TFormValues extends FieldValues>({
  label,
  required,
  control,
  name,
  labelCol = 3,
  columns = 12,
  data,
  startTime,
  duration,
  onNavigate,
  loading,
  isHasBookingSlot,
  disabledFutureMinutes = 0,
  ...props
}: TimeTableFieldProps<TFormValues> &
  OutlinedInputProps & {
    onChangeValue?: (value: string) => void;
  }) => {
  const {
    field: { value = '', onChange },
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const isHorizontalLayout = labelCol < 12;

  const handleChange = useCallback(
    (timeSlot: string) => {
      const {
        onChangeValue,
      }: Partial<
        | {
            onChangeValue: (value: string) => void;
          }
        | undefined
      > = props || {};

      onChange(timeSlot);
      if (onChangeValue) onChangeValue(timeSlot as string);
    },
    [onChange, props],
  );

  return (
    <div className="date-time-picker">
      <Grid
        container
        columnSpacing={isHorizontalLayout ? 4 : 0}
        columns={columns}
      >
        {label && (
          <Label label={label} labelCol={labelCol} required={required} />
        )}
        <Grid item xs={columns} tablet={labelCol} />
      </Grid>

      <Box
        pb="6px"
        // sx={{ scrollbarGutter: 'stable' }}
        boxShadow="0 8px 16px -12px #d2dce1"
        pr={0}
        zIndex={2}
        position="sticky"
        top={{
          xs: '60px',
          lg: '98px',
        }}
        bgcolor="white"
      >
        <Toolbar startTime={startTime} onNavigate={onNavigate} />
      </Box>
      {loading && (
        <LinearProgress
          sx={{
            position: 'absolute',
            width: 'calc(100%)',
            zIndex: 1,
            bottom: -4,
            left: 0,
          }}
        />
      )}
      <TimeTable
        data={data}
        duration={duration}
        onChange={handleChange}
        startTime={startTime}
        value={value}
        closeStatus={[SlotStatus.CLOSED_BY_PROVIDER]}
        openStatus={[SlotStatus.OPEN_HAS_MEETING, SlotStatus.OPEN]}
        decidedStatus={[SlotStatus.CLOSED_BY_MEETING]}
        isHasBookingSlot={isHasBookingSlot}
        disabledFutureMinutes={disabledFutureMinutes}
        enableSkipSlot={true}
      />
      <Grid
        item
        xs={columns}
        tablet={labelCol < columns ? columns - labelCol : columns}
      >
        <HelperText error={error?.message} />
      </Grid>
    </div>
  );
};

export default TimeTableFieldWithoutDialog;

import { Box, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { memo, useMemo } from 'react';
import { DATE_COLOR, DATE_COLOR_HEADING, MomentFormat } from 'utils/constants';

import { getSevenDatesFromNow } from './utils';

const DateHeader = ({ startTime }: { startTime: string }) => {
  const allDatesFromNow = useMemo(
    () => getSevenDatesFromNow(startTime),
    [startTime],
  );
  return (
    <Box display="flex" flexDirection="row" ml="52px" mt={{ xs: 2, tablet: 3 }}>
      {allDatesFromNow.map((day) => {
        const checkedCurrentDate = dayjs().isSame(dayjs(day), 'day');
        const colorDate = DATE_COLOR[dayjs(day).get('day')];
        const colorDateHeader = DATE_COLOR_HEADING[dayjs(day).get('day')];

        return (
          <Box
            key={day}
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            flex="1 0 0%"
          >
            <Typography fontSize={10} textAlign="center" color={colorDate}>
              {dayjs(day).format(MomentFormat.JP_DAY_NAME)}
            </Typography>
            <Box
              width={24}
              height={24}
              display="flex"
              justifyContent="center"
              alignItems="center"
              borderRadius={checkedCurrentDate ? '100%' : 0}
              bgcolor={checkedCurrentDate ? 'primary.main' : 'white'}
            >
              <Typography
                fontSize={{ xs: 12, tablet: 14 }}
                fontWeight={500}
                textAlign="center"
                color={checkedCurrentDate ? 'white' : colorDateHeader}
              >
                {dayjs(day).get('date')}
              </Typography>
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};

export default memo(DateHeader);

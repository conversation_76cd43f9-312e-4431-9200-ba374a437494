import type { OutlinedInputProps } from '@mui/material';
import {
  Box,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  OutlinedInput,
  SvgIcon,
} from '@mui/material';
import { VisibleIcon, VisibleOffIcon } from 'icons';
import { invoke } from 'lodash';
import type { ReactNode } from 'react';
import { useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from '../styles';

interface TextInputProps<TFormValues extends FieldValues>
  extends OutlinedInputProps {
  label?: string;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  maxLength?: number;
  required?: boolean;
  labelCol?: number;
  columns?: number;
  fixedHelperText?: boolean;
  extraLabel?: string | ReactNode;
  helperText?: string;
  hideError?: boolean;
  adornment?: string | ReactNode;
  onBlur?: () => void;
}

const TextField = <TFormValues extends FieldValues>({
  label,
  maxLength,
  required,
  control,
  name,
  columns = 12,
  labelCol = 3,
  extraLabel,
  helperText,
  type,
  hideError = false,
  adornment,
  ...props
}: TextInputProps<TFormValues>) => {
  const {
    field: { value = '', onBlur, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const isHorizontalLayout = labelCol < 12;
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.preventDefault();
  };

  const handleBlur = () => {
    onBlur();
    if (props.onBlur) {
      props.onBlur();
    }
  };

  const renderEndAdornment = () => {
    if (type === 'password') {
      return (
        <InputAdornment position="end" sx={styles.adornmentPassword}>
          <IconButton
            aria-label="toggle-password"
            onClick={handleClickShowPassword}
            onMouseDown={handleMouseDownPassword}
          >
            <SvgIcon
              className="tabletStyle"
              component={showPassword ? VisibleOffIcon : VisibleIcon}
            />
          </IconButton>
        </InputAdornment>
      );
    }
    return props.endAdornment;
  };

  return (
    <div className="text-field-container">
      <Grid
        container
        columnSpacing={isHorizontalLayout ? 4 : 0}
        columns={columns}
      >
        {label && (
          <Label
            label={label}
            labelCol={labelCol}
            required={required}
            extraLabel={extraLabel}
          />
        )}
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
        >
          <FormControl fullWidth sx={{ justifyContent: 'center' }}>
            <Box display="flex" alignItems="center">
              <OutlinedInput
                className="tabletStyle"
                sx={styles.input}
                onWheel={(event) => {
                  invoke(event, 'target.blur');
                }}
                {...props}
                {...otherField}
                onBlur={handleBlur}
                type={
                  // eslint-disable-next-line no-nested-ternary
                  type === 'password'
                    ? !showPassword
                      ? 'password'
                      : 'text'
                    : type
                }
                value={value}
                error={!!error}
                inputProps={{ maxLength, ...props.inputProps }}
                onKeyPress={(event) => {
                  if (type === 'number' && !/[0-9]/.test(event?.key)) {
                    event.preventDefault();
                  }
                }}
                endAdornment={renderEndAdornment()}
              />
              {adornment}
            </Box>
          </FormControl>
        </Grid>
        {!hideError && (
          <>
            <Grid
              item
              xs={columns}
              tablet={labelCol}
              sx={{ display: { xs: 'none', tablet: 'block' } }}
            />
            <Grid
              item
              xs={columns}
              tablet={labelCol < columns ? columns - labelCol : columns}
            >
              <HelperText
                error={error?.message}
                value={value}
                maxLength={maxLength}
                helperText={helperText}
              />
            </Grid>
          </>
        )}
      </Grid>
    </div>
  );
};

export default TextField;

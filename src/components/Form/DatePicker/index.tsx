import { Box, Grid, SvgIcon, TextField } from '@mui/material';
import type { DesktopDatePickerProps } from '@mui/x-date-pickers/DesktopDatePicker';
import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import useBreakpoint from 'hooks/useBreakpoint';
import { CalendarIcon } from 'icons';
import { omit } from 'lodash';
import type { ReactNode } from 'react';
import { useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { MomentFormat } from 'utils/constants';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from '../styles';

interface DatePickerProps<TFormValues extends FieldValues>
  extends Omit<
    DesktopDatePickerProps<Date | Dayjs, Date | Dayjs>,
    'onChange' | 'renderInput' | 'value'
  > {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  required?: boolean;
  labelCol?: number;
  columns?: number;
  placeholder?: string;
  label?: string;
  extraLabel?: string | ReactNode;
  onValueChange?: () => void;
}

const DatePicker = <TFormValues extends FieldValues>({
  label,
  required,
  control,
  name,
  labelCol = 3,
  columns = 12,
  placeholder,
  inputFormat = MomentFormat.JP_YEAR_MONTH_DATE,
  extraLabel,
  onValueChange,
  ...props
}: DatePickerProps<TFormValues>) => {
  const isHorizontalLayout = labelCol < 12;
  const isBreakpoint = useBreakpoint({});
  const {
    field: { value = null, onChange, onBlur, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const [open, setOpen] = useState(false);

  return (
    <div>
      <Grid
        container
        columnSpacing={isHorizontalLayout ? 4 : 0}
        columns={columns}
      >
        {label && (
          <Label
            label={label}
            labelCol={labelCol}
            required={required}
            extraLabel={extraLabel}
          />
        )}
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
        >
          <Box display="flex" alignItems="center" width={1}>
            <DesktopDatePicker
              {...props}
              {...otherField}
              open={open}
              onChange={(dateValue) => {
                setOpen(false);
                if (dayjs.isDayjs(dateValue) && dayjs(dateValue).isValid()) {
                  onChange(dateValue.toISOString());
                } else {
                  onChange(dateValue);
                }
                if (onValueChange) {
                  onValueChange();
                }
              }}
              onClose={() => {
                onBlur();
                setOpen(false);
              }}
              value={value}
              disableHighlightToday
              inputFormat={inputFormat}
              components={{
                OpenPickerIcon: () => (
                  <SvgIcon component={CalendarIcon} className="tabletStyle" />
                ),
              }}
              renderInput={(params) => {
                return (
                  <TextField
                    sx={[styles.input, styles.datepicker] as never}
                    {...omit(params, ['onKeyUp', 'onKeyDown'])}
                    error={!!error}
                    // TODO: Replace by className
                    size={isBreakpoint ? 'small' : 'medium'}
                    margin="none"
                    onClick={() => setOpen(true)}
                    inputProps={{
                      ...params.inputProps,
                      readOnly: true,
                      placeholder:
                        placeholder || params.inputProps?.placeholder,
                    }}
                  />
                );
              }}
            />
          </Box>
        </Grid>
        <Grid item xs={columns} tablet={labelCol} />
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
        >
          <HelperText error={error?.message} />
        </Grid>
      </Grid>
    </div>
  );
};

export default DatePicker;

import type { AutocompleteProps, BoxProps } from '@mui/material';
import { Box, FormControl, Grid, TextField, Typography } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import MenuItem from '@mui/material/MenuItem';
import i18n from 'i18n';
import type { ReactNode } from 'react';
import React from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from '../styles';

export interface IOption {
  _id: string | number;
  value: string | number;
  icon?: JSX.Element;
}

interface SelectFieldProps<TFormValues extends FieldValues> {
  label?: string;
  required?: boolean;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  maxLength?: number;
  data: IOption[];
  labelCol?: number;
  columns?: number;
  viewMode?: boolean;
  extraLabel?: string | ReactNode;
  helperText?: ReactNode;
  placeholder?: string;
  hideError?: boolean;
  containerProps?: BoxProps;
  allowClear?: boolean;
  adornment?: string | ReactNode;
}

const AutoCompleteSelect = <TFormValues extends FieldValues>({
  label,
  data = [],
  required,
  control,
  name,
  placeholder,
  labelCol = 3,
  columns = 12,
  viewMode,
  extraLabel,
  helperText,
  hideError = false,
  containerProps,
  ...props
}: SelectFieldProps<TFormValues> &
  Partial<AutocompleteProps<IOption, false, false, false>>) => {
  const { t } = i18n;
  const {
    field: { value = '', onChange, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const isHorizontalLayout = labelCol < columns;

  return (
    <Box {...containerProps}>
      <Grid
        container
        columns={columns}
        columnSpacing={isHorizontalLayout ? 4 : 0}
      >
        {label && (
          <Label
            label={label}
            labelCol={labelCol}
            required={required}
            extraLabel={extraLabel}
          />
        )}
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
          component="span"
          style={{ width: '100%' }}
        >
          {!viewMode ? (
            <FormControl fullWidth sx={{ justifyContent: 'center' }}>
              <Box display="flex" alignItems="center">
                <Autocomplete
                  options={data}
                  sx={{
                    width: '100%',
                  }}
                  noOptionsText={'..'}
                  {...otherField}
                  {...props}
                  getOptionLabel={(option: IOption) => option.value.toString()}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder={placeholder}
                      error={!!error}
                    />
                  )}
                  onChange={(_e, values, _reason) => {
                    if (values !== null) {
                      onChange(values._id);
                    }
                    if (values === null) {
                      onChange('');
                    }
                  }}
                  clearOnEscape
                  value={data.find((item) => item._id === value) || null}
                  renderOption={(_props, option: IOption) => (
                    <MenuItem
                      key={option._id}
                      value={option._id}
                      sx={styles.selectMenuItem}
                      {..._props}
                    >
                      {option.icon ? (
                        <Box display="flex">
                          <Box
                            mr={1}
                            alignSelf="center"
                            sx={{
                              width: 'fit-content',
                              svg: {
                                display: 'block',
                                maxHeight: { xs: '20px', tablet: '24px' },
                                maxWidth: { xs: '30px', tablet: '36px' },
                              },
                            }}
                          >
                            {option.icon}
                          </Box>
                          <Typography>{option.value}</Typography>
                        </Box>
                      ) : (
                        option.value
                      )}
                    </MenuItem>
                  )}
                />
              </Box>
            </FormControl>
          ) : (
            <Typography color={value ? 'text.primary' : 'hint'} mt="12px">
              {data.find((item) => item._id === value)?.value ||
                t('global.noInfo')}
            </Typography>
          )}
        </Grid>
        {!viewMode && !hideError && (
          <>
            <Grid item xs={columns} tablet={labelCol} />
            <Grid
              item
              xs={columns}
              tablet={labelCol < columns ? columns - labelCol : columns}
            >
              <HelperText error={error?.message} helperText={helperText} />
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
};

export default AutoCompleteSelect;

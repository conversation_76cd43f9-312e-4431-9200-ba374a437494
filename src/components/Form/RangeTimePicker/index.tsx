import { Box, Grid, SvgIcon } from '@mui/material';
import dayjs from 'dayjs';
import { BackGrayIcon, ClockIcon, CloseIcon } from 'icons';
import React, { useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import Label from '../Label';
import TimeDialog from './TimeDialog';
import TimeInput from './TimeField';

interface RangeTimePickerProps<TFormValues extends FieldValues> {
  format?: string;
  minHour?: number;
  maxHour?: number;
  minutesStep?: number;
  hourStep?: number;
  label?: string;
  required?: boolean;
  labelCol?: number;
  columns?: number;
  placeholder?: string | [string, string];
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  disabled?: boolean;
}

const RangeTimePicker = <TFormValues extends FieldValues>({
  format = 'HH時mm分',
  minutesStep = 1,
  hourStep = 1,
  label,
  required,
  control,
  name,
  labelCol = 3,
  columns = 12,
  placeholder,
  disabled,
  minHour,
  maxHour,
}: RangeTimePickerProps<TFormValues>) => {
  const {
    field: { value: timeRange = null, onBlur, onChange, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const [open, setOpen] = useState(false);

  const isHorizontalLayout = labelCol < 12;

  // Placeholder logic
  const startPlaceholder = Array.isArray(placeholder)
    ? placeholder[0]
    : placeholder;
  const endPlaceholder = Array.isArray(placeholder)
    ? placeholder[1]
    : placeholder;

  // Handle Save Logic
  const handleSave = (selectedValue: string[] | null) => {
    onChange(selectedValue);
    onBlur();
    setOpen(false);
  };

  const startTime =
    timeRange && timeRange[0] ? dayjs(timeRange[0]).format(format) : '';
  const endTime =
    timeRange && timeRange[1] ? dayjs(timeRange[1]).format(format) : '';

  return (
    <div className="range-time-picker">
      <Grid
        container
        columnSpacing={isHorizontalLayout ? 4 : 0}
        columns={columns}
      >
        {label ? (
          <Label label={label} labelCol={labelCol} required={required} />
        ) : (
          <Grid item xs={labelCol} />
        )}
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
          direction={'column'}
        >
          <Box
            display="flex"
            alignItems="center"
            borderRadius="8px"
            bgcolor="#f6f8f9"
            gap={1}
            padding={{ xs: '6px 10px', tablet: '10px 10px 10px 13px' }}
            width="100%"
            border={'2px solid #f6f8f9'}
            sx={{
              cursor: disabled ? 'not-allowed' : 'pointer',
              position: 'relative',
              borderColor: error ? '#db5a42' : '#f6f8f9',
              '&:hover': !disabled
                ? {
                    borderColor: error ? '#db5a42' : '#f5af3c',
                  }
                : {},
            }}
            onClick={() => !disabled && setOpen(true)}
          >
            <Box
              display="flex"
              flexDirection="column"
              alignItems="flex-start"
              flex={1}
            >
              <TimeInput
                placeholder={startPlaceholder || 'Start time'}
                value={startTime}
                disabled={disabled}
                {...otherField}
              />
            </Box>
            <Box sx={{ width: 20, height: 20 }}>
              <BackGrayIcon size={20} />
            </Box>
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              flex={1}
            >
              <TimeInput
                placeholder={endPlaceholder || 'End time'}
                value={endTime}
                disabled={disabled}
                {...otherField}
              />
            </Box>
            {(startTime || endTime) && (
              <SvgIcon
                onClick={(e: any) => {
                  e.stopPropagation();
                  onChange(null);
                }}
                component={CloseIcon}
                className="tabletStyle"
                sx={{ color: 'primary.main' }}
              />
            )}
            {!startTime && !endTime && (
              <SvgIcon component={ClockIcon} className="tabletStyle" />
            )}
          </Box>
        </Grid>
        <Grid
          item
          xs={columns}
          tablet={labelCol}
          sx={{ display: { xs: 'none', tablet: 'block' } }}
        />
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
        >
          <HelperText error={error?.message} />
        </Grid>
        <TimeDialog
          open={open}
          onClose={() => {
            onBlur();
            setOpen(false);
          }}
          onSave={handleSave}
          minutesStep={minutesStep}
          hourStep={hourStep}
          value={timeRange}
          minHour={minHour}
          maxHour={maxHour}
        />
      </Grid>
    </div>
  );
};

export default RangeTimePicker;

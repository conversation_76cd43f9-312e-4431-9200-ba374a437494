import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Grid,
  IconButton,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

import TimePanelColumn from './TimePanelColumn';

interface TimeDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (value: string[] | null) => void;
  value?: [string, string] | null;
  minutesStep: number;
  hourStep: number;
  minHour?: number;
  maxHour?: number;
}

interface ISelectedTimeState {
  startHour: number | null;
  startMinute: number | null;
  endHour: number | null;
  endMinute: number | null;
}
const TimeDialog = ({
  open,
  onClose,
  onSave,
  minutesStep,
  hourStep,
  value,
  minHour,
  maxHour,
}: TimeDialogProps) => {
  const [selectedTime, setSelectedTime] = useState<ISelectedTimeState>({
    startHour: null,
    startMinute: null,
    endHour: null,
    endMinute: null,
  });

  useEffect(() => {
    if (value && open) {
      const startTime = value && value[0] ? dayjs(value[0]) : null;
      const startHour = startTime ? startTime.get('hour') : null;
      const startMinute = startTime ? startTime.get('minute') : null;
      const endTime = value && value[1] ? dayjs(value[1]) : null;
      const endHour = endTime ? endTime.get('hour') : null;
      const endMinute = endTime ? endTime.get('minute') : null;
      setSelectedTime({ startHour, startMinute, endHour, endMinute });
    }
  }, [value, open]);

  const handleSelectStartHour = (selectedValue: number) => {
    setSelectedTime((prevValue) => ({
      ...prevValue,
      startHour: selectedValue,
      startMinute: prevValue.startMinute === null ? 0 : prevValue.startMinute,
    }));
  };

  const handleSelectStartMinute = (selectedValue: number) => {
    setSelectedTime((prevValue) => ({
      ...prevValue,
      startMinute: selectedValue,
      startHour:
        prevValue.startHour === null ? minHour || 0 : prevValue.startHour,
    }));
  };

  const handleSelectEndHour = (selectedValue: number) => {
    setSelectedTime((prevValue) => ({
      ...prevValue,
      endHour: selectedValue,
      endMinute: prevValue.endMinute === null ? 0 : prevValue.endMinute,
    }));
  };

  const handleSelectEndMinute = (selectedValue: number) => {
    setSelectedTime((prevValue) => ({
      ...prevValue,
      endMinute: selectedValue,
      endHour:
        selectedTime.endHour === null ? minHour || 0 : selectedTime.endHour,
    }));
  };
  const handleClear = () => {
    setSelectedTime({
      startHour: null,
      startMinute: null,
      endHour: null,
      endMinute: null,
    });
  };

  const handleClose = () => {
    onClose();
    handleClear();
  };

  const handleSave = () => {
    if (
      selectedTime.startHour !== null &&
      selectedTime.startMinute !== null &&
      selectedTime.endHour !== null &&
      selectedTime.endMinute !== null
    ) {
      const startDay = dayjs(value ? value[0] : undefined)
        .set('hour', selectedTime.startHour)
        .set('minute', selectedTime.startMinute);
      const endDay = dayjs(value ? value[1] : undefined)
        .set('hour', selectedTime.endHour)
        .set('minute', selectedTime.endMinute);
      const isValidRange = startDay.isBefore(endDay);
      onSave(
        isValidRange
          ? [startDay.toISOString(), endDay.toISOString()]
          : [endDay.toISOString(), startDay.toISOString()],
      );
    } else onSave(null);
    setSelectedTime({
      startHour: null,
      startMinute: null,
      endHour: null,
      endMinute: null,
    });
  };

  return (
    <Dialog
      onClose={onClose}
      open={open}
      maxWidth="xs"
      PaperProps={{
        sx: {
          borderRadius: '16px',
          width: '100%',
          maxWidth: '480px',
          position: 'relative',
          m: 2,
        },
      }}
    >
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: 16,
          top: 16,
          zIndex: 1,
          padding: '7px',
        }}
      >
        <Box component="img" src="/icons/close.svg" alt="close" />
      </IconButton>

      <DialogContent sx={{ p: '40px 32px 0 32px' }}>
        <Typography
          textAlign="center"
          fontSize={20}
          fontWeight={700}
          color="#4A4A4A"
          mb="40px"
        >
          時間帯をご選択ください
        </Typography>

        <Grid container spacing={3} alignItems="center" sx={{ mb: '16px' }}>
          <Grid item xs={5}>
            <Box display="flex" gap={0} justifyContent="center">
              <TimePanelColumn
                label="Hour"
                min={minHour || 0}
                max={maxHour || 23}
                value={selectedTime.startHour}
                onSelect={handleSelectStartHour}
                hourStep={hourStep}
                style={{
                  borderRadius: '8px 0 0 8px',
                }}
              />
              <TimePanelColumn
                label="Minute"
                min={0}
                max={59}
                onSelect={handleSelectStartMinute}
                minuteStep={minutesStep || 1}
                value={selectedTime.startMinute}
                style={{
                  borderRadius: '0 8px 8px 0',
                }}
              />
            </Box>
          </Grid>

          <Grid item xs={2} sx={{ display: 'flex', justifyContent: 'center' }}>
            <Box
              component="img"
              src="/icons/arrow-right.svg"
              alt="arrow"
              sx={{ width: '24px', height: '24px' }}
            />
          </Grid>

          <Grid item xs={5}>
            <Box display="flex" gap={0} justifyContent="center">
              <TimePanelColumn
                value={selectedTime.endHour}
                label="Hour"
                min={minHour || 0}
                max={maxHour || 23}
                onSelect={handleSelectEndHour}
                style={{
                  borderRadius: '8px 0 0 8px',
                }}
              />
              <TimePanelColumn
                label="Minute"
                min={0}
                max={59}
                onSelect={handleSelectEndMinute}
                minuteStep={minutesStep || 1}
                value={selectedTime.endMinute}
                style={{
                  borderRadius: '0 8px 8px 0',
                }}
              />
            </Box>
          </Grid>
        </Grid>

        <Typography
          textAlign="center"
          fontSize={12}
          color="#887F70"
          sx={{ mb: '40px' }}
        >
          ※できるだけ幅広い時間帯でご登録をお願いいたします。
        </Typography>
      </DialogContent>

      <DialogActions sx={{ p: '0 32px 32px', gap: '16px' }}>
        <Button
          variant="outlined"
          onClick={handleClose}
          sx={{
            width: 200,
            height: 48,
            borderRadius: '24px',
            border: '1px solid #FFCD00',
            color: '#FFCD00',
            '&:hover': {
              border: '1px solid #FFCD00',
              bgcolor: 'transparent',
            },
          }}
        >
          キャンセル
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            width: 200,
            height: 48,
            borderRadius: '24px',
            bgcolor: '#FFCD00',
            '&:hover': {
              bgcolor: '#FFCD00',
            },
          }}
        >
          保存
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TimeDialog;

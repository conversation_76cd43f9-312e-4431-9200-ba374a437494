import type { SxProps, Theme } from '@mui/material';

const styles = {
  panelColumn: {
    border: '1px solid #ccc',
    margin: 0,
    listStyle: 'none',
    maxHeight: '264px',
    minHeight: '264px',
    overflowY: 'scroll',
    scrollbarWidth: 'thin',
    padding: '4px',
    scrollPaddingTop: '4px',
  },
  listItem: {
    cursor: 'pointer',
    padding: '5px 0',
    textAlign: 'center',
    borderRadius: '4px',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
    },
  },
  selectedItem: {
    backgroundColor: '#FAD59E',
  },
  hoverItem: {},
} as Record<string, SxProps<Theme>>;

export default styles;

// TimePanelColumn.tsx
import { Box } from '@mui/material';
import React, { useEffect, useRef, useState } from 'react';

import styles from './styles';

interface TimePanelColumnProps {
  label: string;
  min: number;
  max: number;
  onSelect: (value: number) => void;
  hourStep?: number;
  minuteStep?: number;
  value?: number | null;
  style?: React.CSSProperties;
}

// Helper function to generate options based on min, max, and step values
const generateTimeOptions = (min: number, max: number, step: number = 1) => {
  const options: { label: string; value: number }[] = [];
  for (let i = min; i <= max; i += step) {
    options.push({ label: i < 10 ? `0${i}` : `${i}`, value: i });
  }
  return options;
};

const getStep = (
  label: string,
  hourStep: number,
  minuteStep: number,
): number => {
  if (label === 'Hour') return hourStep;
  if (label === 'Minute') return minuteStep;
  return 1;
};

const TimePanelColumn: React.FC<TimePanelColumnProps> = ({
  label,
  min,
  max,
  onSelect,
  hourStep = 1,
  minuteStep = 1,
  value = null,
  style,
}) => {
  const [selectedValue, setSelectedValue] = useState<number | null>(null);
  const selectedRef = useRef<HTMLDivElement | null>(null);
  const step = getStep(label, hourStep, minuteStep);
  const options = generateTimeOptions(min, max, step);

  useEffect(() => {
    setSelectedValue(value);
  }, [value]);

  useEffect(() => {
    selectedRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [selectedValue]);

  const handleSelect = (cValue: number) => {
    setSelectedValue(cValue);
    onSelect(cValue);
  };

  const getItemStyles = (option: { label: string; value: number }) => {
    return {
      ...styles.listItem,
      ...(option.value === selectedValue ? styles.selectedItem : {}),
    };
  };

  return (
    <div style={{ width: '85px' }}>
      <Box component="ul" style={style} sx={styles.panelColumn}>
        {options.map((option) => (
          <Box
            component={'li'}
            key={option.value}
            sx={getItemStyles(option)}
            ref={selectedValue === option.value ? selectedRef : null}
            onClick={() => handleSelect(option.value)}
            mb="4px"
          >
            {option.label}
          </Box>
        ))}
      </Box>
    </div>
  );
};

export default TimePanelColumn;

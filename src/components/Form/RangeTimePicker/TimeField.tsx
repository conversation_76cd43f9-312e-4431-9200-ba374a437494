import type { StandardTextFieldProps } from '@mui/material';
import { TextField } from '@mui/material';

interface TimeInputProps extends StandardTextFieldProps {
  name: string;
  placeholder?: string;
  value?: string | null;
  onClick?: () => void;
  onBlur?: () => void;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  error?: boolean;
  helperText?: string;
}

const TimeInput = ({
  placeholder,
  value = '',
  onClick,
  onBlur,
  onChange,
  error = false,
  helperText,
  name,
  ...props
}: TimeInputProps) => (
  <TextField
    name={name}
    variant="standard"
    placeholder={placeholder}
    value={value}
    onClick={onClick}
    onBlur={onBlur}
    onChange={onChange}
    error={error}
    helperText={helperText}
    inputProps={{
      sx: {
        cursor: props.disabled ? 'not-allowed' : 'pointer',
        padding: 0,
        lineHeight: '24px',
        height: 'unset',
      },
    }}
    InputProps={{
      readOnly: true,
      disableUnderline: true,
      sx: {
        textAlign: 'center',
      },
    }}
    sx={{
      width: '100%',
      maxWidth: '203px',
    }}
    {...props}
  />
);

export default TimeInput;

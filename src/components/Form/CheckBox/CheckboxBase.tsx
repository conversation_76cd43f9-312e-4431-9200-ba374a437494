import type { CheckboxProps } from '@mui/material';
import { Checkbox, SvgIcon } from '@mui/material';
import { CheckedIcon, UncheckIcon } from 'icons';

export interface ICheckboxBase extends CheckboxProps {
  fontSize?: 'small' | 'inherit' | 'medium' | 'large';
  iconClassName?: string;
}
const CheckboxBase = ({ iconClassName, fontSize, ...props }: ICheckboxBase) => {
  return (
    <Checkbox
      icon={
        <SvgIcon
          className={iconClassName}
          component={UncheckIcon}
          fontSize={fontSize}
        />
      }
      checkedIcon={
        <SvgIcon
          className={iconClassName}
          component={CheckedIcon}
          fontSize={fontSize}
        />
      }
      sx={{
        p: 1,
      }}
      {...props}
    />
  );
};

export default CheckboxBase;

import type { GridProps } from '@mui/material';
import { FormControlLabel, FormGroup, Grid, Typography } from '@mui/material';
import type { RadioGroupProps } from '@mui/material/RadioGroup';
import type { IListItem } from 'hooks/types';
import i18n from 'i18n';
import { isEmpty } from 'lodash';
import type { ReactNode } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import Label from '../Label';
import CheckboxBase from './CheckboxBase';

interface CheckBoxFieldProps<TFormValues extends FieldValues>
  extends RadioGroupProps {
  label?: string;
  required?: boolean;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  data: IListItem[];
  labelCol?: number;
  columns?: number;
  helperText?: string;
  showSelectAll?: boolean;
  layout?: 'vertical' | 'horizontal';
  extraLabel?: string | ReactNode;
  iconClassName?: string;
  optionContainerProps?: GridProps;
  showNothingSpecial?: boolean;
}

const CheckBox = <TFormValues extends FieldValues>({
  label,
  required,
  control,
  name,
  data = [],
  columns = 12,
  labelCol = 3,
  helperText,
  showSelectAll,
  layout = 'vertical',
  extraLabel,
  iconClassName = 'tabletStyle',
  optionContainerProps,
  showNothingSpecial,
}: CheckBoxFieldProps<TFormValues>) => {
  const { t } = i18n;
  const {
    field: { value = [], onChange },
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const allIds = data.map((item) => item._id);
  return (
    <div>
      <Grid
        container
        columnSpacing={4}
        mt={{ xs: '-10px', tablet: -1 }}
        columns={columns}
      >
        {label && (
          <Label
            label={label}
            labelCol={labelCol}
            required={required}
            extraLabel={extraLabel}
            paddingTop={1}
          />
        )}
        <Grid
          item
          xs={12}
          tablet={labelCol < columns ? columns - labelCol : columns}
        >
          <FormGroup>
            <Grid container>
              {showSelectAll && !isEmpty(data) && (
                <Grid
                  item
                  xs={showNothingSpecial ? 6 : 12}
                  tablet={showNothingSpecial ? 4 : 12}
                >
                  <FormControlLabel
                    sx={{
                      whiteSpace: 'pre-line',
                      alignItems: 'flex-start',
                      '> p': {
                        marginTop: { xs: '10px', tablet: 1 },
                      },
                    }}
                    control={
                      <CheckboxBase
                        sx={{ p: { xs: '10px', tablet: 1 } }}
                        iconClassName={iconClassName}
                        checked={value.length === data.length}
                      />
                    }
                    label={
                      <Typography fontSize={{ xs: 14, tablet: 16 }}>
                        {t('global.selectAll')}
                      </Typography>
                    }
                    onChange={(_, checked) => {
                      if (checked) {
                        onChange(allIds);
                      } else {
                        onChange([]);
                      }
                    }}
                  />
                </Grid>
              )}
              {showNothingSpecial && (
                <Grid item xs={6} tablet={8}>
                  <FormControlLabel
                    sx={{
                      whiteSpace: 'pre-line',
                      alignItems: 'flex-start',
                      '> p': {
                        marginTop: { xs: '10px', tablet: 1 },
                      },
                    }}
                    control={
                      <CheckboxBase
                        sx={{ p: { xs: '10px', tablet: 1 } }}
                        iconClassName={iconClassName}
                        checked={
                          value.length > 0 && value[0] === 'NOTHING_SPECIAL'
                        }
                      />
                    }
                    label={
                      <Typography fontSize={{ xs: 14, tablet: 16 }}>
                        特になし
                      </Typography>
                    }
                    onChange={(_, checked) => {
                      if (checked) {
                        onChange(['NOTHING_SPECIAL']);
                      } else {
                        onChange([]);
                      }
                    }}
                  />
                </Grid>
              )}
              {data.map((option) => {
                return (
                  <Grid
                    item
                    xs={layout === 'vertical' ? 12 : 6}
                    tablet={layout === 'vertical' ? 12 : 4}
                    key={option._id}
                    {...optionContainerProps}
                  >
                    <FormControlLabel
                      sx={{
                        whiteSpace: 'pre-line',
                        alignItems: 'flex-start',
                        '> p': {
                          marginTop: { xs: '10px', tablet: 1 },
                        },
                      }}
                      control={
                        <CheckboxBase
                          sx={{ p: { xs: '10px', tablet: 1 } }}
                          iconClassName={iconClassName}
                          checked={value.includes(option._id as never)}
                        />
                      }
                      label={
                        <Typography fontSize={{ xs: 14, tablet: 16 }}>
                          {option.value}
                        </Typography>
                      }
                      onChange={(_, checked) => {
                        if (checked) {
                          onChange(
                            (value || [])
                              .filter((item) => item !== 'NOTHING_SPECIAL')
                              .concat(option._id as never),
                          );
                        } else {
                          onChange(value.filter((item) => item !== option._id));
                        }
                      }}
                    />
                  </Grid>
                );
              })}
            </Grid>
          </FormGroup>
          <Grid item xs={12} tablet={labelCol} />
          <Grid
            item
            xs={12}
            tablet={labelCol < columns ? columns - labelCol : columns}
          >
            <HelperText
              error={error?.message}
              value={value}
              helperText={helperText}
            />
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
};

export default CheckBox;

import type { OutlinedInputProps } from '@mui/material';
import { Box, FormControl, Grid, OutlinedInput } from '@mui/material';
import { isNumber } from 'lodash';
import type { ReactNode } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { NumericFormat } from 'react-number-format';
import Helper from 'utils/helpers';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from '../styles';

interface NumberFieldProps<TFormValues extends FieldValues>
  extends Omit<OutlinedInputProps, 'defaultValue' | 'type' | 'value'> {
  label?: string;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  maxLength?: number;
  required?: boolean;
  labelCol?: number;
  columns?: number;
  fixedHelperText?: boolean;
  extraLabel?: string | ReactNode;
  helperText?: string;
  hideError?: boolean;
  adornment?: string | ReactNode;
  leftAdorment?: string | ReactNode;
  onBlur?: () => void;
  min?: number;
  max?: number;
  decimalScale?: number;
}

const NumberField = <TFormValues extends FieldValues>({
  label,
  maxLength,
  required,
  control,
  name,
  columns = 12,
  labelCol = 3,
  extraLabel,
  helperText,
  hideError = false,
  adornment,
  max,
  min,
  decimalScale = 0,
  leftAdorment,
  ...props
}: NumberFieldProps<TFormValues>) => {
  const {
    field: { value, onBlur, onChange },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const isHorizontalLayout = labelCol < 12;

  const handleBlur = () => {
    if (isNumber(max) && value && Number(value) > max) {
      onChange(max);
    }
    if (isNumber(min) && value && Number(value) < min) {
      onChange(min);
    }
    onBlur();
    if (props.onBlur) {
      props.onBlur();
    }
  };

  return (
    <div>
      <Grid
        container
        columnSpacing={isHorizontalLayout ? 4 : 0}
        columns={columns}
      >
        {label && (
          <Label
            label={label}
            labelCol={labelCol}
            required={required}
            extraLabel={extraLabel}
          />
        )}
        <Grid
          item
          xs={columns}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
        >
          <FormControl fullWidth sx={{ justifyContent: 'center' }}>
            <Box display="flex" alignItems="center">
              {leftAdorment}
              <NumericFormat
                className="tabletStyle"
                sx={styles.input}
                customInput={OutlinedInput}
                allowNegative={false}
                onValueChange={(values) => {
                  if (
                    decimalScale === 0 &&
                    values.floatValue &&
                    values.floatValue % 1 !== 0
                  ) {
                    onChange(values.formattedValue);
                  } else onChange(values.value);
                }}
                onBlur={handleBlur}
                decimalScale={decimalScale}
                thousandSeparator=","
                value={
                  typeof value === 'string' || typeof value === 'number'
                    ? Helper.addComma(value)
                    : value
                }
                error={!!error}
                {...props}
              />
              {adornment}
            </Box>
          </FormControl>
        </Grid>
        {!hideError && (
          <>
            <Grid
              item
              xs={columns}
              tablet={labelCol}
              sx={{ display: { xs: 'none', tablet: 'block' } }}
            />
            <Grid
              item
              xs={columns}
              tablet={labelCol < columns ? columns - labelCol : columns}
            >
              <HelperText
                error={error?.message}
                value={value}
                maxLength={maxLength}
                helperText={helperText}
              />
            </Grid>
          </>
        )}
      </Grid>
    </div>
  );
};

export default NumberField;

import type { RatingProps } from '@mui/material';
import { Rating, SvgIcon } from '@mui/material';
import { StarEmptyIcon, StarFilledIcon, StarLineIcon } from 'icons';

interface RatingInputProps extends RatingProps {
  showEmptyStar?: boolean;
}

const BaseRating = ({ showEmptyStar = false, ...props }: RatingInputProps) => {
  return (
    <Rating
      {...props}
      size="large"
      icon={
        <SvgIcon
          fontSize="large"
          className="tabletStyle"
          component={StarFilledIcon}
        />
      }
      emptyIcon={
        <SvgIcon
          fontSize="large"
          className="tabletStyle"
          component={showEmptyStar ? StarEmptyIcon : StarLineIcon}
        />
      }
    />
  );
};

export default BaseRating;

import type { BoxProps, RatingProps } from '@mui/material';
import { Box, SvgIcon } from '@mui/material';
import { StarEmptyIcon, StarFilledIcon } from 'icons';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import BaseRating from './BaseRating';

interface RatingInputProps<TFormValues extends FieldValues>
  extends RatingProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  containerProps?: BoxProps;
}

const RatingField = <TFormValues extends FieldValues>({
  control,
  name,
  containerProps,
  ...props
}: RatingInputProps<TFormValues>) => {
  const {
    field: { value = null, onChange, onBlur },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <Box {...containerProps}>
      <BaseRating
        {...props}
        name={name}
        value={Number(value)}
        onChange={onChange}
        onBlur={onBlur}
        size="large"
        icon={<SvgIcon fontSize="large" component={StarFilledIcon} />}
        emptyIcon={<SvgIcon fontSize="large" component={StarEmptyIcon} />}
      />
      <HelperText error={error?.message} />
    </Box>
  );
};

export default RatingField;

import {
  Box,
  FormControlLabel,
  Grid,
  Radio as MuiRadio,
  RadioGroup,
  SvgIcon,
  Typography,
} from '@mui/material';
import type { RadioGroupProps } from '@mui/material/RadioGroup';
import { t } from 'i18n';
import { RadioActive, RadioDefault } from 'icons';
import type { ReactNode } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from './styles';

export type IDataOfRadio = {
  _id: string;
  value: string | ReactNode;
  description?: string | ReactNode;
  endAdornment?: string | ReactNode;
  meetingType?: string;
};

interface RadioFieldProps<TFormValues extends FieldValues>
  extends RadioGroupProps {
  label?: string | ReactNode;
  required?: boolean;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  data: IDataOfRadio[];
  labelCol?: number;
  columns?: number;
  helperText?: string;
  viewMode?: boolean;
  extraLabel?: string | ReactNode;
  type?: 'default' | 'outline';
  description?: string | ReactNode;
}

const MenuTextWithExpand = ({ text }: { text: string | ReactNode }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isTruncated, setIsTruncated] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  // Only apply truncation logic for strings
  const isString = typeof text === 'string';

  useEffect(() => {
    if (!isString) return undefined;

    const checkTruncation = () => {
      if (textRef.current) {
        const element = textRef.current;
        setIsTruncated(element.scrollHeight > element.clientHeight);
      }
    };

    checkTruncation();
    window.addEventListener('resize', checkTruncation);
    return () => window.removeEventListener('resize', checkTruncation);
  }, [text, isString]);

  // If text is not a string, render it normally without truncation
  if (!isString) {
    return (
      <Box
        sx={{
          color: '#625B50',
          fontSize: 14,
          lineHeight: '20px',
          fontWeight: 600,
        }}
      >
        {text}
      </Box>
    );
  }

  return (
    <Box>
      <Box
        ref={textRef}
        sx={{
          color: '#625B50',
          fontSize: 14,
          lineHeight: '20px',
          fontWeight: 600,
          display: '-webkit-box',
          WebkitLineClamp: isExpanded ? 'unset' : 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          wordBreak: 'break-word',
        }}
      >
        {text}
      </Box>
      {isTruncated && !isExpanded && (
        <Typography
          component="span"
          onClick={() => setIsExpanded(true)}
          sx={{
            fontSize: 14,
            lineHeight: '20px',
            fontWeight: 400,
            color: '#FFA700',
            cursor: 'pointer',
            '&:hover': {
              opacity: 0.8,
            },
          }}
        >
          続きを読む
        </Typography>
      )}
    </Box>
  );
};

const Radio = <TFormValues extends FieldValues>({
  label,
  required,
  control,
  name,
  data,
  columns = 12,
  labelCol = 3,
  helperText,
  viewMode,
  extraLabel,
  row = true,
  type = 'default',
  description,
  ...props
}: RadioFieldProps<TFormValues>) => {
  const {
    field: { value = null, ...otherField },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <Box sx={type === 'outline' ? styles.root : {}}>
      <Grid container columnSpacing={4} mt={{ tablet: -1 }} columns={columns}>
        {label && (
          <Label
            label={label}
            labelCol={labelCol}
            required={required}
            extraLabel={extraLabel}
            paddingTop={1}
          />
        )}
        <Grid
          item
          xs={columns}
          tablet={!row || !(labelCol < columns) ? columns : columns - labelCol}
          display="flex"
          mt={{ xs: -1, tablet: 0 }}
          className="radio-option-wrapper"
        >
          {!viewMode ? (
            <Box style={{ width: '100%' }}>
              {description && <HelperText helperText={description} />}
              <RadioGroup
                aria-labelledby="radio-buttons-group"
                row={row}
                sx={{ mt: { xs: 1, tablet: 0 }, width: '100%' }}
                value={value}
                {...props}
                {...otherField}
              >
                {data.map((item) => {
                  // Determine background color based on selection and meetingType
                  const isSelected = value === item._id;
                  const getBackgroundColor = () => {
                    if (isSelected) {
                      return '#FDF3D8'; // Current selected color
                    }
                    if (item.meetingType === 'SINGLE') {
                      return '#EDF1F3';
                    }
                    return 'white';
                  };

                  const getBorderColor = () => {
                    if (isSelected) {
                      return '#FFA700'; // Current selected border color
                    }
                    return '#CEDAE0'; // Default border color
                  };

                  return (
                    <FormControlLabel
                      key={item._id}
                      value={item._id}
                      sx={{
                        mr: !row
                          ? 0
                          : {
                              xs: type === 'outline' ? '0px' : '30px',
                              tablet: type === 'outline' ? 0 : 4,
                            },
                        '.MuiTypography-root': {
                          fontSize: { xs: 14, tablet: 16 },
                        },
                        '&:last-child': {
                          mr: 0,
                        },
                        ...(type === 'outline'
                          ? {
                              mb: '16px',
                              p: '16px 16px 16px 8px',
                              border: `1px solid ${getBorderColor()}`,
                              borderRadius: '16px',
                              background: getBackgroundColor(),
                              transition: '0.5s',
                              '&:last-child': {
                                mb: 0,
                              },
                            }
                          : {}),
                      }}
                      className={
                        type === 'outline' && value === item._id
                          ? ' active'
                          : ''
                      }
                      control={
                        <MuiRadio
                          sx={{ p: { xs: '10px', tablet: 1 } }}
                          checkedIcon={
                            <SvgIcon
                              className="tabletStyle"
                              component={RadioActive}
                            />
                          }
                          icon={
                            <SvgIcon
                              className="tabletStyle"
                              component={RadioDefault}
                            />
                          }
                        />
                      }
                      label={
                        type === 'outline' ? (
                          <Box
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              marginLeft: 11,
                            }}
                          >
                            <Box sx={{ flex: 1, minWidth: 0, mr: 1 }}>
                              <MenuTextWithExpand text={item?.value} />
                              <Box
                                sx={{
                                  color: '#887F70',
                                  fontSize: 12,
                                  lineHeight: '16px',
                                }}
                                mt="8px"
                              >
                                {item?.description}
                              </Box>
                            </Box>
                            {item?.endAdornment && (
                              <Box
                                sx={{
                                  display: 'flex',
                                  textAlign: 'right',
                                  color: '#FFA700',
                                  fontSize: 14,
                                  lineHeight: '20px',
                                  fontWeight: 600,
                                  alignItems: { md: 'center', sx: 'start' },
                                }}
                              >
                                <Box>{item?.endAdornment}</Box>
                              </Box>
                            )}
                          </Box>
                        ) : (
                          item.value
                        )
                      }
                    />
                  );
                })}
              </RadioGroup>
            </Box>
          ) : (
            <Typography color={value ? 'text.primary' : 'hint'} mt={1}>
              {data.find((item) => item._id === value)?.value ||
                t('global.noInfo')}
            </Typography>
          )}
        </Grid>
        {!viewMode && (
          <>
            <Grid item xs={columns} tablet={labelCol} />
            <Grid
              item
              xs={columns}
              tablet={labelCol < columns ? columns - labelCol : columns}
            >
              <HelperText error={error?.message} helperText={helperText} />
            </Grid>
          </>
        )}
      </Grid>
    </Box>
  );
};

export default Radio;

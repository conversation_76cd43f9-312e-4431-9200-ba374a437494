import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  root: {
    'div[aria-labelledby="radio-buttons-group"]': {
      width: '100%',
      '.MuiFormControlLabel-root': {
        width: '100%',
        marginLeft: 0,
      },
      '.MuiFormControlLabel-label': {
        width: '100%',
      },
    },
  },
  outlineLabel: {
    mb: '16px',
    p: '16px 16px 16px 8px',
    border: '1px solid #CEDAE0',
    borderRadius: '16px',
    '&:last-child': {
      mb: 0,
    },

    '&.active': {
      borderColor: '#FFA700',
      background: '#FDF3D8',
      transition: '0.5s',
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

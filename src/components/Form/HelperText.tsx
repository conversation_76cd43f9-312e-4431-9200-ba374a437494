import { FormHelperText, Stack, Typography } from '@mui/material';
import { motion } from 'framer-motion';
import { ErrorIcon } from 'icons';
import type { ReactNode } from 'react';

import styles from './styles';

const HelperText = ({
  error,
  helperText,
  maxLength,
  value = '',
  onClick,
}: {
  error?: string;
  value?: string | unknown[];
  maxLength?: number;
  helperText?: ReactNode;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
}) => {
  return (
    <FormHelperText
      onClick={onClick || undefined}
      component="div"
      error={!!error}
      sx={{
        mt: error || helperText || maxLength ? { xs: '4px', tablet: 1 } : 0,
        cursor: onClick ? 'pointer' : 'default',
      }}
    >
      <Stack
        className="helper-text-container"
        direction="row"
        justifyContent="space-between"
      >
        {error && (
          <Stack
            component={motion.div}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="form-error"
            direction="row"
            spacing={{ tablet: 1, xs: '4px' }}
            sx={styles.errorContainer}
          >
            <ErrorIcon />
            <Typography fontSize={{ xs: 12, tablet: 14 }}>{error}</Typography>
          </Stack>
        )}
        {!error && helperText && (
          <Typography
            fontSize={{ xs: 12, tablet: 14 }}
            color="hint"
            className="form-hint"
            textAlign="start"
          >
            {helperText}
          </Typography>
        )}
        {maxLength && (
          <>
            {!error && !helperText && <div />}
            <Typography
              className="form-max-length"
              fontSize={{ tablet: 14, xs: 12 }}
              color="hint"
            >
              {`${value.length}/${maxLength}`}
            </Typography>
          </>
        )}
      </Stack>
    </FormHelperText>
  );
};

export default HelperText;

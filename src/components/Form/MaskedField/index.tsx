import type { OutlinedInputProps } from '@mui/material';
import { FormControl, Grid, OutlinedInput } from '@mui/material';
import type { ReactNode } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { PatternFormat } from 'react-number-format';

import HelperText from '../HelperText';
import Label from '../Label';
import styles from '../styles';

interface TextInputProps<TFormValues extends FieldValues>
  extends Omit<OutlinedInputProps, 'defaultValue' | 'type'> {
  label?: string;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  maxLength?: number;
  required?: boolean;
  labelCol?: number;
  columns?: number;
  fixedHelperText?: boolean;
  extraLabel?: string | ReactNode;
  helperText?: string;
  format: string;
  hideError?: boolean;
}

const MaskedField = <TFormValues extends FieldValues>({
  label,
  maxLength,
  required,
  control,
  name,
  columns = 12,
  labelCol = 3,
  extraLabel,
  helperText,
  format,
  hideError,
  ...props
}: TextInputProps<TFormValues>) => {
  const {
    field: { value = '', onChange, onBlur },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const isHorizontalLayout = labelCol < 12;

  return (
    <div>
      <Grid
        container
        columnSpacing={isHorizontalLayout ? 4 : 0}
        columns={columns}
      >
        {label && (
          <Label
            label={label}
            labelCol={labelCol}
            required={required}
            extraLabel={extraLabel}
          />
        )}
        <Grid
          item
          xs={12}
          tablet={labelCol < columns ? columns - labelCol : columns}
          display="flex"
        >
          <FormControl fullWidth sx={{ justifyContent: 'center' }}>
            <PatternFormat
              value={value as any}
              className="tabletStyle"
              customInput={OutlinedInput}
              error={!!error}
              placeholder={props.placeholder}
              format={format}
              sx={styles.input}
              {...props}
              onChange={onChange}
              onBlur={onBlur}
            />
          </FormControl>
        </Grid>
        {!hideError && (
          <>
            <Grid
              item
              xs={12}
              tablet={labelCol}
              sx={{ display: { xs: 'none', tablet: 'block' } }}
            />
            <Grid
              item
              xs={12}
              tablet={labelCol < columns ? columns - labelCol : columns}
            >
              <HelperText
                error={error?.message}
                value={value}
                maxLength={maxLength}
                helperText={helperText}
              />
            </Grid>
          </>
        )}
      </Grid>
    </div>
  );
};

export default MaskedField;

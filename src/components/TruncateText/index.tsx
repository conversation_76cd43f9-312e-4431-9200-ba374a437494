import type { ButtonProps, TypographyProps } from '@mui/material';
import { Box, Button, Typography } from '@mui/material';
import i18n from 'i18n';
import { ArrowDownIcon, ArrowUpIcon } from 'icons';
import { useState } from 'react';
import TruncateMarkup from 'react-truncate-markup';

const TruncateText = ({
  text,
  textProps,
  lines = 3,
  eliipsisTextProps,
}: {
  text: string;
  textProps?: TypographyProps;
  eliipsisTextProps?: ButtonProps;
  lines?: number;
}) => {
  const { t } = i18n;
  const [shouldTruncate, toggleTruncate] = useState(false);
  const readMoreEllipsis = (
    <span>
      ...
      <Box component="div">
        <Button
          sx={{ ml: -2, fontSize: { xs: 12, tablet: 14 } }}
          className="truncate-text__ellipsis"
          endIcon={<ArrowDownIcon />}
          {...eliipsisTextProps}
          onClick={() => toggleTruncate(true)}
        >
          {t('global.seeMore')}
        </Button>
      </Box>
    </span>
  );

  if (!shouldTruncate) {
    return (
      <TruncateMarkup lines={lines + 2} ellipsis={readMoreEllipsis}>
        <Typography component="span" {...textProps}>
          {text}
        </Typography>
      </TruncateMarkup>
    );
  }
  return (
    <>
      <Typography {...textProps}>{text}</Typography>
      <Button
        sx={{ ml: -2, fontSize: { xs: 12, tablet: 14 } }}
        className="truncate-text__ellipsis"
        endIcon={<ArrowUpIcon />}
        {...eliipsisTextProps}
        onClick={() => toggleTruncate(false)}
      >
        {t('global.seeLess')}
      </Button>
    </>
  );
};

export default TruncateText;

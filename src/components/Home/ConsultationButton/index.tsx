import type { IconTextButtonProps } from 'components/UI/IconTextButton';
import IconTextButton from 'components/UI/IconTextButton';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import useGlobalState from 'hooks/useGlobalState';
import i18n from 'i18n';
import { RegisterIcon } from 'icons';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import { ROLES } from 'utils/constants';
import Helper from 'utils/helpers';

const ConsultationButton = ({
  onHeader,
  ...props
}: Omit<IconTextButtonProps, 'text' | 'icon'> & { onHeader?: boolean }) => {
  const { t } = i18n;
  const { push } = useRouter();
  const { setLoginModal } = useGlobalState();
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetail,
    enabled: false,
  });
  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;
  const { data: currentUser } = useFetchUser<CustomerData>({ enabled: false });
  const isCompletedFields =
    currentUser?.phone &&
    currentUser?.isCompletedProfile &&
    consultationDetail.backgroundOfDivorce &&
    consultationDetail.marriageInformation &&
    (!Helper.checkIsEmptyObject(consultationDetail.expectLawyer) ||
      !Helper.checkIsEmptyObject(consultationDetail.expectCounselor));

  const isHeaderDisplay =
    Helper.getUserRole() === ROLES.CUSTOMER &&
    onHeader &&
    !isEmpty(currentUser) &&
    !isEmpty(consultationDetail) &&
    !isCompletedFields;

  if (isHeaderDisplay || !onHeader || !Helper.getWebCookie()) {
    return (
      <IconTextButton
        containerStyle={{ maxWidth: 240 }}
        fullWidth
        onClick={() => {
          if (!Helper.getWebCookie()) {
            setLoginModal(true);
          } else if (!isCompletedFields) {
            push('/customer/consultation-form');
          } else {
            push('/customer/my-page');
          }
        }}
        {...props}
        subText={t('home.clarify')}
        text={t('home.divorce')}
        middleText="を"
        endText="入力"
        icon={<RegisterIcon />}
      />
    );
  }
  return null;
};

export default ConsultationButton;

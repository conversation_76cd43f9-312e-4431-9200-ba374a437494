import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  contaier: {
    padding: {
      xs: '32px 16px 40px',
      tablet: '80px 16px 88px',
      ac: '80px 0px 88px',
    },
  },
  slider: {
    mt: { xs: 2, tablet: 6 },
    display: 'flex',
  },
  divider: {
    marginTop: '14px',
    marginLeft: 'auto',
    marginRight: 'auto',
    background: 'linear-gradient(to right, #fdc84b 50%, #cedae0 50%)',
    width: '144px',
    height: '8px',
  },
  gridContainer: {
    justifyContent: 'center',
    '.MuiGrid-item:last-of-type': {
      display: 'none',
      '@media (max-width: 767px)': {
        display: 'block',
      },
    },
    '@media (max-width: 767px)': {
      '.MuiGrid-item:not(:first-of-type)': {
        display: 'none',
      },
    },
  },
  mobileList: {
    display: 'none',
    '@media (max-width: 767px)': {
      display: 'block',
      marginTop: 3,
    },
  },
  articleItem: {
    borderRadius: 2,
    border: (theme) => ({
      xs: `1.5px solid ${theme.palette.neutral4}`,
      tablet: `2px solid ${theme.palette.neutral4}`,
    }),
    p: { xs: '14.5px', tablet: '22px' },
    height: '100%',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import type { SxP<PERSON>, Theme } from '@mui/material';
import { <PERSON>, Button, Container, Grid, Stack, Typography } from '@mui/material';
import FadeMotion, { fadeInUp } from 'components/Animation/FadeMotion';
import { motion } from 'framer-motion';
import type { IArticleListItem } from 'hooks/types';
import { t } from 'i18n';
import { get } from 'lodash';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { ProviderType } from 'utils/constants';

import ArticleMobileList from '../ArticleMobileList';
import styles from './styles';

export interface IArticleSection {
  containerStyle?: SxProps<Theme>;
  data: IArticleListItem[];
  displayNumber?: number;
  displayRows?: number;
}
const ArticleSection = ({
  containerStyle = [],
  data = [],
}: IArticleSection) => {
  const { push } = useRouter();

  return (
    <Container
      maxWidth="ac"
      disableGutters
      className="article-list-section"
      sx={[
        styles.contaier,
        ...(Array.isArray(containerStyle) ? containerStyle : [containerStyle]),
      ]}
    >
      <FadeMotion>
        <Typography
          component={motion.p}
          variants={fadeInUp}
          fontSize={{ xs: '24px', tablet: '32px' }}
          fontWeight="bold"
          color="#FCAB28"
          textAlign="center"
        >
          {t('home.article')}
        </Typography>
        <Box
          component={motion.div}
          variants={fadeInUp}
          className="logo-divider"
          mt={{ xs: 2, tablet: 2 }}
        />
        <Box sx={styles.slider} component={motion.div} variants={fadeInUp}>
          <Grid
            container
            columnSpacing={3}
            rowSpacing={2}
            sx={styles.gridContainer}
          >
            {data.slice(0, 4).map((article) => (
              <Grid
                item
                xs={12}
                tablet={4}
                onClick={() => push(`/articles/${article._id}`)}
                key={article._id}
              >
                <Box
                  sx={styles.articleItem}
                  display="flex"
                  flexDirection="column"
                  className="pointer article-wrapper"
                >
                  <Typography
                    fontSize={18}
                    fontWeight="bold"
                    className="line-clamp two-line break-word article-title"
                    flex={1}
                    color="#464646"
                  >
                    {article.title}
                  </Typography>
                  <Box
                    mt="18px"
                    borderRadius={1}
                    overflow="hidden"
                    className="article-image-wrapper"
                    sx={{
                      aspectRatio: '272 / 153',
                      transform: 'translateZ(0)',
                    }}
                  >
                    <Image
                      alt="article-image"
                      className="scale"
                      priority
                      sizes="(min-width: 768px) 27vw, 100vw"
                      width={1}
                      height={153 / 272}
                      src={
                        get(article, 'image.originUrl') ||
                        '/images/default-image.png'
                      }
                      style={{
                        width: '100%',
                        height: '100%',
                        borderRadius: '16px',
                        objectFit: 'cover',
                      }}
                    />
                  </Box>
                  <Stack direction="row" mt={2} className="author">
                    <Box
                      mr={2}
                      minWidth={{ xs: 48, tablet: 64 }}
                      sx={{ aspectRatio: '1' }}
                      position="relative"
                    >
                      <Image
                        alt="lawyer-image"
                        priority
                        sizes="13vw"
                        fill
                        src={get(
                          article,
                          'provider.images[0].originUrl',
                          '/images/default-avatar.png',
                        )}
                        style={{
                          borderRadius: '50%',
                        }}
                      />
                    </Box>
                    <Box mt={{ xs: '3px', tablet: '11px' }}>
                      <Typography
                        fontSize="12px"
                        fontWeight="bold"
                        color="#464646"
                      >
                        {article.provider.type === ProviderType.LAWYER
                          ? t('home.lawyer')
                          : 'カウンセラー '}
                      </Typography>
                      <Typography
                        fontSize="16px"
                        fontWeight="bold"
                        color="#464646"
                        className="line-clamp"
                      >
                        {article.provider.fullName}
                      </Typography>
                    </Box>
                  </Stack>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
        <Box sx={styles.mobileList}>
          <ArticleMobileList
            data={data.slice(1, 4)}
            onItemClick={({ _id }) => push(`/articles/${_id}`)}
          />
        </Box>
        <Box
          component={motion.div}
          variants={fadeInUp}
          display="flex"
          justifyContent="center"
          mt={{ xs: 5, tablet: '48px' }}
        >
          <Button
            variant="contained"
            fullWidth
            color="secondary"
            className="shadow tabletStyle"
            size="large"
            href="/articles"
            sx={{
              '&.shadow.tabletStyle': {
                fontSize: { xs: 18, tablet: 20 },
                height: { xs: 48, tablet: 59 },
              },

              maxWidth: { xs: 325, tablet: 358 },
            }}
            LinkComponent={Link}
          >
            インタビューの一覧を見る
          </Button>
        </Box>
      </FadeMotion>
    </Container>
  );
};

export default ArticleSection;

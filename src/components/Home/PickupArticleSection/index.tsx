import type { SxProps, Theme } from '@mui/material';
import { Box, Button, Container, Grid, Typography } from '@mui/material';
import FadeMotion, { fadeInUp } from 'components/Animation/FadeMotion';
import EmblaCarousel from 'components/EmblaCarousel';
import dayjs from 'dayjs';
import { motion } from 'framer-motion';
import type { IPickupArticleListItem } from 'hooks/types';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { Element } from 'react-scroll';
import { MomentFormat } from 'utils/constants';

import ArticleMobileList from '../ArticleMobileList';
import styles from './styles';

export interface IPickupArticleSection {
  data: IPickupArticleListItem[];
  containerStyle?: SxProps<Theme>;
}
function PickupArticleCard({ article }: { article: IPickupArticleListItem }) {
  const { push } = useRouter();

  return (
    <Grid
      sx={{
        height: {
          xs: 200,
          tablet: 313,
        },
      }}
      item
      xs={12}
      tablet={4}
      key={article._id}
      onClick={() => push(`/pickup-articles/${article._id}`)}
    >
      <Box
        sx={styles.itemContainer}
        display="flex"
        flexDirection="column"
        className="pointer"
      >
        <Box sx={styles.imageContainer}>
          <Image
            alt=""
            src={article.image?.originUrl || '/images/default-image.png'}
            fill
            className="scale"
            sizes="(min-width: 768px) 30vw, 100vw"
            style={{
              borderTopRightRadius: '16px',
              borderTopLeftRadius: '16px',
              objectFit: 'cover',
            }}
          />
        </Box>
        <Box
          p={{ xs: 2, tablet: 3 }}
          bgcolor="white"
          flex={1}
          sx={{
            borderBottomLeftRadius: '16px',
            borderBottomRightRadius: '16px',
          }}
        >
          <Typography
            fontSize={18}
            color="#464646"
            fontWeight="bold"
            className="line-clamp two-line"
          >
            {article.title}
          </Typography>
          <Typography sx={styles.date}>
            {dayjs(article.updatedAt).format(MomentFormat.DOT_YEAR_MONTH_DATE)}
          </Typography>
        </Box>
      </Box>
    </Grid>
  );
}

const PickupArticleSection = ({
  data = [],
  containerStyle = [],
}: IPickupArticleSection) => {
  const { push } = useRouter();

  return (
    <Box
      className="pickup-article-list-section"
      sx={[
        styles.container,
        ...(Array.isArray(containerStyle) ? containerStyle : [containerStyle]),
      ]}
    >
      <FadeMotion>
        <motion.span variants={fadeInUp}>
          <Element name="pickup-articles">
            <Typography
              fontSize={{ xs: 24, tablet: 32 }}
              fontWeight="bold"
              color="primary"
              textAlign="center"
            >
              リコ活特集記事
            </Typography>
          </Element>

          <Box className="logo-divider" mt={{ xs: 2, tablet: 2 }} />
        </motion.span>
        <motion.span variants={fadeInUp}>
          <Box my={{ xs: '24px', tablet: '48px' }}>
            <Container maxWidth="ac" disableGutters sx={styles.slider}>
              <Box sx={styles.hiddenMB}>
                <EmblaCarousel
                  options={{
                    loop: true,
                    startIndex: 0,
                    draggable: false,
                    align: 'start',
                    slidesToScroll: 3,
                  }}
                  itemsToShow={3}
                  slides={data.slice(0, 6).map((article) => (
                    <PickupArticleCard article={article} key={article._id} />
                  ))}
                />
              </Box>

              <Box sx={styles.hiddenTablet}>
                {data.slice(0, 1).map((article) => (
                  <PickupArticleCard article={article} key={article._id} />
                ))}
              </Box>
            </Container>
          </Box>
        </motion.span>
        <Box sx={styles.mobileList}>
          <ArticleMobileList
            data={data.slice(1, 4)}
            onItemClick={({ _id }) => push(`/pickup-articles/${_id}`)}
          />
        </Box>
        <motion.div variants={fadeInUp}>
          <Box display="flex" justifyContent="center" mt={{ xs: 3, tablet: 6 }}>
            <Button
              variant="contained"
              color="secondary"
              className="shadow tabletStyle"
              size="large"
              fullWidth
              LinkComponent={Link}
              href="/pickup-articles"
              sx={{
                maxWidth: { xs: 325, tablet: 358 },
                '&.shadow.tabletStyle': {
                  fontSize: { xs: 18, tablet: 20 },
                  height: { xs: 48, tablet: 59 },
                },
              }}
            >
              特集記事の一覧を見る
            </Button>
          </Box>
        </motion.div>
      </FadeMotion>
    </Box>
  );
};

export default PickupArticleSection;

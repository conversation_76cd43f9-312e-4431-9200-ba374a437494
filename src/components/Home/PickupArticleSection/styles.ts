import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    padding: {
      xs: '32px 16px 40px',
      tablet: '80px 16px 88px',
      ac: '80px 0px 88px',
    },
  },
  gridContainer: {
    justifyContent: 'center',
    '@media (max-width: 767px)': {
      '.MuiGrid-item:not(:first-of-type)': {
        display: 'none',
      },
    },
  },
  mobileList: {
    display: 'none',
    '@media (max-width: 767px)': {
      display: 'block',
    },
  },
  itemContainer: {
    borderRadius: 2,
    height: '100%',
    border: '2px solid #d2dce1',
  },
  imageContainer: {
    position: 'relative',
    borderTopRightRadius: '16px',
    borderTopLeftRadius: '16px',
    overflow: 'hidden',
    transform: 'translateZ(0)',
    aspectRatio: '320 / 180',
  },
  date: {
    color: 'orange',
    fontSize: 14,
    fontWeight: 'bold',
    letterSpacing: '1.17px',
    mt: '10px',
  },

  hiddenMB: {
    display: {
      xs: 'none',
      tablet: 'block',
    },
  },
  hiddenTablet: {
    display: {
      xs: 'block',
      tablet: 'none',
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

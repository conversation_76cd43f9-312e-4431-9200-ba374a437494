import { Box, Container, Grid, Typography } from '@mui/material';
import { StoryIcon1, StoryIcon2, StoryIcon3, StoryIcon4 } from 'icons';

import styles from './styles';

const experienceContents = [
  {
    icon: <StoryIcon1 />,
    content: (
      <Typography sx={styles.contentText} color="#464646" fontWeight={500}>
        <Typography
          sx={styles.contentText}
          component="span"
          color="#FCAB28"
          mr={{ xs: '2px', tablet: '4px' }}
          fontWeight={700}
        >
          カルテの入力
        </Typography>
        で{'\n'}
        自分が何で悩んでいるかを{'\n'}考える機会になりました
      </Typography>
    ),
  },
  {
    icon: <StoryIcon3 />,
    content: (
      <Typography sx={styles.contentText} color="#464646" fontWeight={500}>
        <Typography
          sx={styles.contentText}
          component="span"
          color="#FCAB28"
          mr={{ xs: '2px', tablet: '4px' }}
          fontWeight={700}
        >
          専門家のプロフに動画
        </Typography>
        もあり{'\n'}人柄までイメージしやすくて{'\n'}安心できました
      </Typography>
    ),
  },
  {
    icon: <StoryIcon2 />,
    content: (
      <Typography sx={styles.contentText} color="#464646" fontWeight={500}>
        <Typography
          sx={styles.contentText}
          component="span"
          color="#FCAB28"
          mr={{ xs: '2px', tablet: '4px' }}
          fontWeight={700}
        >
          相談したら離婚が加速する？
        </Typography>
        {'\n'}
        と思っていたが相談を通して{'\n'}冷静な整理ができました
      </Typography>
    ),
  },

  {
    icon: <StoryIcon4 />,
    content: (
      <Typography sx={styles.contentText} color="#464646" fontWeight={500}>
        <Typography
          sx={styles.contentText}
          component="span"
          color="#FCAB28"
          mr={{ xs: '2px', tablet: '4px' }}
          fontWeight={700}
        >
          弁護士とカウンセラー
        </Typography>
        {'\n'}
        両方に相談できて手続きも{'\n'}気持ちも助けられました
      </Typography>
    ),
  },
];
const ExperienceSection = () => {
  return (
    <Box sx={styles.container}>
      <Typography sx={styles.title}>リコ活の体験談</Typography>
      <Box className="logo-divider" mt={{ xs: '10px', tablet: 2 }} />
      <Container maxWidth="hc" disableGutters sx={{ mt: 6 }}>
        <Grid container spacing={2}>
          {experienceContents.map((content, index) => (
            <Grid item xs={12} tablet={6} key={index}>
              <Box sx={styles.itemWrapper}>
                {content.icon}
                <Box flex={1}>{content.content}</Box>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default ExperienceSection;

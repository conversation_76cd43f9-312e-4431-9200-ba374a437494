import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    p: { xs: '32px 16px 40px', ac: '88px 16px 80px' },
  },
  title: {
    fontSize: { xs: '24px', ac: '32px' },
    fontWeight: 'bold',
    color: '#FCAB28',
    textAlign: 'center',
  },
  itemWrapper: {
    borderRadius: '12px',
    bgcolor: '#fff5cc',
    padding: { xs: '16px 20px', ac: '24px 36px' },
    display: 'flex',
    gap: { xs: 3, tablet: 4 },
    svg: {
      width: { xs: 70, ac: 101 },
      height: { xs: 70, ac: 101 },
    },
  },
  contentText: {
    fontSize: { xs: 14, ac: 20 },
    lineHeight: { xs: '21px', ac: '35px' },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

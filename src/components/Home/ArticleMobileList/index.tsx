import { Box, Typography } from '@mui/material';
import dayjs from 'dayjs';
import Image from 'next/image';
import { MomentFormat } from 'utils/constants';

const ArticleMobileList = ({
  data,
  onItemClick,
  showDate = false,
}: {
  onItemClick: ({ _id, link }: { _id: string; link?: string }) => void;
  showDate?: boolean;
  data: {
    image: { originUrl?: string };
    title: string;
    _id: string;
    link?: string;
    updatedAt: string;
  }[];
}) => {
  return (
    <Box
      display="flex"
      flexDirection="column"
      gap={2}
      className="article-mobile-list"
    >
      {data.map((item) => (
        <Box
          display="flex"
          key={item._id}
          gap="12px"
          onClick={() => onItemClick(item)}
        >
          <Image
            src={item.image.originUrl || '/images/default-image.png'}
            alt=""
            width={72}
            height={72}
            quality={100}
            style={{ borderRadius: '12px', objectFit: 'cover' }}
          />
          <Box>
            <Typography
              fontSize={16}
              color="#464646"
              fontWeight={500}
              className="line-clamp three-line "
            >
              {item.title}
            </Typography>
            {showDate && (
              <Typography
                fontSize={14}
                color="#FF8C00"
                fontWeight={500}
                mt="4px"
              >
                {dayjs(item.updatedAt).format(MomentFormat.DOT_YEAR_MONTH_DATE)}
              </Typography>
            )}
          </Box>
        </Box>
      ))}
    </Box>
  );
};

export default ArticleMobileList;

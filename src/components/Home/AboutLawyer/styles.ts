import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    '@media (max-width: 1080px)': {
      paddingLeft: '16px',
      paddingRight: '16px',
    },
  },
  contactBtn: {
    maxWidth: 358,
    backgroundColor: '#fff',
    '&:hover': { backgroundColor: '#fff' },
    '&.shadow': {
      '@media (max-width: 768px)': {
        maxWidth: 327,
        borderRadius: 24,
        height: 48,
        fontSize: 14,
        fontWeight: 700,
        svg: {
          width: 24,
          height: 24,
        },
      },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

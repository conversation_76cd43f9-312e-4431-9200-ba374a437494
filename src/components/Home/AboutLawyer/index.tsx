import { <PERSON>, Button, Stack, Typography } from '@mui/material';
import FadeMotion, {
  fadeInLeft,
  fadeInUp,
} from 'components/Animation/FadeMotion';
import { motion } from 'framer-motion';
import { useFetchUser } from 'hooks';
import i18n from 'i18n';
import { ArrowRightIcon, WhiteLogoIcon } from 'icons';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';
import { Element } from 'react-scroll';

import styles from './styles';

const NewConsultationButton = dynamic(
  () => import('components/Home/HomeConsultationButton'),
  { ssr: false },
);

const AboutLawyer = () => {
  const { t } = i18n;
  const { isSuccess } = useFetchUser({ enabled: false });
  return (
    <Box>
      <Element name="aboutLawyer">
        <Box bgcolor="orange">
          <FadeMotion>
            <Box
              maxWidth={1080}
              m="0 auto"
              width="100%"
              pt="36px"
              sx={styles.container}
            >
              <Stack
                component={motion.span}
                variants={fadeInUp}
                direction="row"
                alignItems="flex-end"
                mb="36px"
                justifyContent="center"
                sx={{
                  gap: '5px',
                  svg: {
                    width: '156px',
                    height: '68px',
                  },
                }}
              >
                <WhiteLogoIcon />

                <Typography
                  fontSize={{ xs: 24, tablet: 32 }}
                  fontWeight="bold"
                  color="white"
                >
                  {t('home.whatIs')}
                </Typography>
              </Stack>
              <Box display={{ ncol: 'flex' }}>
                <Box
                  pb={{ xs: 4, tablet: 8 }}
                  flex={1}
                  pl={{ xs: 3, tablet: '22px' }}
                >
                  <FadeMotion>
                    <Typography
                      component={motion.span}
                      variants={fadeInUp}
                      fontSize={{ xs: 20, tablet: 28 }}
                      fontWeight="bold"
                      color="white"
                    >
                      離婚、夫婦問題・修復の{'\n'}課題解決型マッチングメディア
                    </Typography>
                    <motion.span variants={fadeInUp}>
                      <Typography
                        fontSize={{ xs: 14, tablet: 20 }}
                        fontWeight={500}
                        color="#464646"
                        mt={2}
                        lineHeight={1.6}
                      >
                        家族のカタチが多様化する時代。{'\n'}
                        離婚、夫婦問題・修復で悩む人に、{'\n'}
                        最新の実績を持つ専門家とのマッチング、{'\n'}
                        さらには新しい時代の情報や
                        <Box
                          component="br"
                          display={{ xs: 'block', tablet: 'none' }}
                        />
                        価値観のシェアまで提供します。
                        {'\n'}
                        すべては、幸せに向けた再出発のために。
                      </Typography>
                    </motion.span>
                  </FadeMotion>
                </Box>
                <Box
                  alignSelf="flex-end"
                  position="relative"
                  maxWidth={445}
                  overflow="hidden"
                  flex={1}
                >
                  <motion.div variants={fadeInLeft}>
                    <Image
                      src="/images/footer-image.webp"
                      alt=""
                      width={1}
                      quality={100}
                      height={331 / 445}
                      sizes="41vw"
                      style={{
                        width: '100%',
                        height: 'auto',
                        display: 'block',
                      }}
                    />
                  </motion.div>
                </Box>
              </Box>
            </Box>
          </FadeMotion>
        </Box>
        <Box
          p={{ xs: '40px 24px 44px', tablet: '64px 16px' }}
          justifyContent="center"
          bgcolor="#fff5cc"
        >
          <Typography
            fontSize={{ xs: 24, md: 36 }}
            color="#FCAB28"
            fontWeight={500}
            textAlign="center"
            mb={{ xs: '36px', tablet: 5 }}
            whiteSpace={{ xs: 'pre-line', tablet: 'normal' }}
          >
            離婚、夫婦問題・修復も{'\n'}オンラインで無料相談
          </Typography>
          <Box display="flex" alignItems="center" flexDirection="column">
            <NewConsultationButton />
            <Button
              href="/contact-us"
              variant="outlined"
              color="primary"
              type="submit"
              fullWidth
              size="large"
              sx={{
                ...styles.contactBtn,
                marginTop: isSuccess ? { xs: '20px', tablet: '16px' } : '40px',
              }}
              className="shadow tabletStyle"
              LinkComponent={Link}
              endIcon={<ArrowRightIcon width={32} height={32} color="gold" />}
            >
              専門家の登録はこちら
            </Button>
          </Box>
        </Box>
      </Element>
    </Box>
  );
};

export default AboutLawyer;

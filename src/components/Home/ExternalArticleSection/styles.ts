import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    padding: {
      xs: '32px 16px 40px',
      tablet: '80px 16px 88px',
      ac: '80px 0px 88px',
    },
  },
  gridContainer: {
    justifyContent: 'center',
    '@media (max-width: 767px)': {
      display: 'none',
    },
  },
  mobileList: {
    display: 'none',
    '@media (max-width: 767px)': {
      display: 'block',
    },
  },
  itemContainer: {
    borderRadius: 2,
    height: '100%',
    border: '2px solid #d2dce1',
  },
  imageContainer: {
    position: 'relative',
    borderTopRightRadius: '16px',
    borderTopLeftRadius: '16px',
    overflow: 'hidden',
    transform: 'translateZ(0)',
    aspectRatio: '320 / 180',
  },
  date: {
    color: 'orange',
    fontSize: { xs: '12px', tablet: '14px' },
    fontWeight: 'bold',
    letterSpacing: '1.17px',
    mt: '10px',
  },
  dotContainer: {
    '&.slick-dots': {
      position: 'unset',
      display: 'flex',
      justifyContent: 'center',
      mt: 2,
      li: {
        height: '8px',
        width: '8px',
        margin: 0,
        '&:not(.slick-active) >div': {
          bgcolor: 'neutral8',
          opacity: 0.5,
        },
        '&.slick-active': {
          '> div': {
            bgcolor: 'white',
          },
        },
      },
    },
  },
  dot: {
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    bgcolor: 'neutral2',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import type { SxProps, Theme } from '@mui/material';
import { Box, Button, Container, Grid, Typography } from '@mui/material';
import FadeMotion, { fadeInUp } from 'components/Animation/FadeMotion';
import MuiLink from 'components/Link';
import dayjs from 'dayjs';
import { motion } from 'framer-motion';
import type { IExternalArticleListItem } from 'models/article/interface';
import Image from 'next/image';
import Link from 'next/link';
import { Element } from 'react-scroll';
import { MomentFormat } from 'utils/constants';
import Helper from 'utils/helpers';

import ArticleMobileList from '../ArticleMobileList';
import styles from './styles';

export interface IExternalSection {
  data: IExternalArticleListItem[];
  containerStyle?: SxProps<Theme>;
}
const ExternalArticleSection = ({
  data = [],
  containerStyle = [],
}: IExternalSection) => {
  return (
    <Box
      className="external-article-list-section"
      sx={[
        styles.container,
        ...(Array.isArray(containerStyle) ? containerStyle : [containerStyle]),
      ]}
    >
      <FadeMotion>
        <motion.span variants={fadeInUp}>
          <Element name="external-articles">
            <Typography
              fontSize={{ xs: 24, tablet: 32 }}
              fontWeight="bold"
              color="orange"
              textAlign="center"
            >
              リコ活 MEDIA
            </Typography>
          </Element>

          <Box className="logo-divider" mt={{ xs: 2, tablet: 2 }} />
        </motion.span>
        <motion.span variants={fadeInUp}>
          <Box my={{ xs: '24px', tablet: '48px' }}>
            <Container maxWidth="ac" disableGutters sx={styles.slider}>
              <Grid container spacing={3} sx={styles.gridContainer}>
                {data.slice(0, 3).map((article) => (
                  <Grid item xs={12} tablet={4} key={article._id}>
                    <MuiLink
                      underline="none"
                      key={article._id}
                      rel="noreferrer"
                      target="_blank"
                      sx={{ height: '100%' }}
                      href={Helper.formatUrl(article.link) as never}
                    >
                      <Box
                        sx={styles.itemContainer}
                        display="flex"
                        flexDirection="column"
                        className="pointer"
                      >
                        <Box sx={styles.imageContainer}>
                          <Image
                            alt=""
                            src={
                              article.image?.originUrl ||
                              '/images/default-image.png'
                            }
                            fill
                            className="scale"
                            sizes="(min-width: 768px) 30vw, 100vw"
                            style={{
                              borderTopRightRadius: '16px',
                              borderTopLeftRadius: '16px',
                              objectFit: 'cover',
                            }}
                          />
                        </Box>
                        <Box
                          p={{ xs: 2, tablet: 3 }}
                          bgcolor="white"
                          flex={1}
                          sx={{
                            borderBottomLeftRadius: '16px',
                            borderBottomRightRadius: '16px',
                          }}
                        >
                          <Typography
                            fontSize={{ tablet: 18 }}
                            color="#464646"
                            fontWeight="bold"
                            className="line-clamp two-line"
                          >
                            {article.title}
                          </Typography>
                          <Typography sx={styles.date}>
                            {dayjs(article.updatedAt).format(
                              MomentFormat.DOT_YEAR_MONTH_DATE,
                            )}
                          </Typography>
                        </Box>
                      </Box>
                    </MuiLink>
                  </Grid>
                ))}
              </Grid>
            </Container>
          </Box>
        </motion.span>
        <Box sx={styles.mobileList}>
          <ArticleMobileList
            onItemClick={({ link }) => link && window.open(link)}
            data={data.slice(0, 3)}
          />
        </Box>
        <motion.div variants={fadeInUp}>
          <Box display="flex" justifyContent="center" mt={{ xs: 3, tablet: 6 }}>
            <Button
              variant="contained"
              color="secondary"
              className="shadow tabletStyle"
              size="large"
              fullWidth
              LinkComponent={Link}
              href="/media/"
              target="_blank"
              sx={{
                '&.shadow.tabletStyle': {
                  fontSize: { xs: 18, tablet: 20 },
                  height: { xs: 48, tablet: 59 },
                },
                maxWidth: { xs: 325, tablet: 358 },
              }}
            >
              リコ活 MEDIA を見る
            </Button>
          </Box>
        </motion.div>
      </FadeMotion>
    </Box>
  );
};

export default ExternalArticleSection;

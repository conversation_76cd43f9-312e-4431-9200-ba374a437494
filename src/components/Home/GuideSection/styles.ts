import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    bgcolor: 'white',
    pt: { xs: 7, tablet: 8 },
    pb: { xs: 8, tablet: 11 },
  },
  title: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-end',
    svg: {
      width: { xs: 111, tablet: 148 },
      height: { xs: 48, tablet: 64 },
    },
  },
  guideText: {
    mt: { xs: 3, tablet: 5 },
    fontWeight: 'bold',
    fontSize: { xs: 18, tablet: 24 },
    textAlign: 'center',
    color: 'heading',
    whiteSpace: { xs: 'pre-line', tablet: 'normal' },
    px: { tablet: 3, sl: 0 },
  },
  highlightText: {
    fontWeight: 'bold',
    fontSize: { xs: 18, tablet: 24 },
    pl: '6px',
    mr: '-3px',
    whiteSpace: { xs: 'pre-line', tablet: 'normal' },
    backgroundImage: (theme) =>
      `linear-gradient(0, ${theme.palette.tertiary} 38%,  transparent 38%)`,
  },
  imageContainer: {
    display: 'flex',
    flexDirection: { xs: 'column', tablet: 'row' },
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  image: {
    width: 1,
    position: 'relative',
    maxWidth: { xs: '269px', tablet: 'unset' },
  },
  middleImage: {
    width: 1,
    position: 'relative',
    maxWidth: { xs: '311px', tablet: 'unset' },
  },
  arrowImage: {
    width: { xs: 48, tablet: 55 },
    height: { xs: 48, tablet: 55 },
    position: 'relative',
    flexShrink: 0,
    transform: { xs: 'rotate(90deg)', tablet: 'unset' },
  },
  nextImage: { width: '100%', height: 'auto' },
  actionButton: {
    width: { xs: 311, tablet: 240 },
    '.MuiButton-startIcon': {
      mr: { xs: 9, tablet: 2 },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import { Box, Container, Typography } from '@mui/material';
import FadeMotion, { fadeInUp } from 'components/Animation/FadeMotion';
import { motion } from 'framer-motion';
import { t } from 'i18n';
import { LogoIcon } from 'icons';
import dynamic from 'next/dynamic';
import Image from 'next/image';

import styles from './styles';

const ConsultationButton = dynamic(
  () => import('components/Home/ConsultationButton'),
  { ssr: false },
);

const GuideSection = () => {
  return (
    <Box sx={styles.container}>
      <FadeMotion>
        <Box sx={styles.title} component={motion.div} variants={fadeInUp}>
          <LogoIcon />
          <Typography
            fontSize={{ xs: 24, tablet: 32 }}
            lineHeight="normal"
            variant="sectionLabel"
            ml="4px"
          >
            {t('home.canDo')}
          </Typography>
        </Box>
        <Box className="divider" mt={{ xs: 2, tablet: 3 }} />
        <Typography
          sx={styles.guideText}
          component={motion.div}
          variants={fadeInUp}
        >
          <Typography sx={styles.highlightText} component="span">
            {t('home.highlightGuideText')}
          </Typography>
          {t('home.guideText')}
        </Typography>

        <Container
          disableGutters
          maxWidth="lg"
          sx={{ mt: { xs: 5, tablet: '65px' }, px: { tablet: 3, sl: 0 } }}
        >
          <Box
            sx={styles.imageContainer}
            component={motion.div}
            variants={fadeInUp}
          >
            <Box sx={styles.image}>
              <Image
                alt=""
                priority
                src="/images/home-1.webp"
                sizes="100vw"
                width={1}
                height={284 / 303}
                style={{ width: '100%', height: 'auto' }}
              />
            </Box>
            <Box
              sx={styles.arrowImage}
              ml={{ tablet: '-10px' }}
              mr={{ tablet: '46px' }}
              mb={{ xs: 1, tablet: 0 }}
            >
              <Image
                alt=""
                src="/images/home-arrow.webp"
                priority
                fill
                sizes="100vw"
              />
            </Box>
            <Box sx={styles.middleImage}>
              <Image
                alt=""
                priority
                src="/images/home-2.webp"
                sizes="100vw"
                style={{ width: '100%', height: 'auto' }}
                width={1}
                height={284 / 303}
              />
            </Box>
            <Box
              sx={styles.arrowImage}
              mr={{ tablet: '-10px' }}
              ml={{ tablet: '46px' }}
              mt={{ xs: 1, tablet: 0 }}
              zIndex={1}
            >
              <Image
                alt=""
                src="/images/home-arrow.webp"
                priority
                fill
                sizes="100vw"
              />
            </Box>
            <Box sx={styles.image}>
              <Image
                alt=""
                src="/images/home-3.webp"
                priority
                sizes="100vw"
                style={{ width: '100%', height: 'auto' }}
                width={1}
                height={284 / 303}
              />
            </Box>
          </Box>
          <Box display="flex" justifyContent="center" mt="40px">
            <ConsultationButton containerStyle={styles.actionButton} />
          </Box>
        </Container>
      </FadeMotion>
    </Box>
  );
};

export default GuideSection;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  usageArrow: {
    ':after': {
      content: '""',
      position: 'absolute',
      width: 0,
      height: 0,
      borderStyle: 'solid',
      borderWidth: {
        xs: '27px calc(50vw) 0 calc(50vw)',
        tablet: '40px calc(50vw - 10px) 0 calc(50vw - 10px)',
      },
      borderColor: '#FCAB28 transparent transparent transparent',
    },
  },
  background: {
    bgcolor: 'orange',
    padding: { xs: '32px 16px 17px', tablet: '48px 16px 36px' },
  },
  contentBox: {
    padding: { xs: '24px 20px 20px', tablet: '32px 24px 28px' },
    borderRadius: '12px',
    bgcolor: 'white',
  },
  blockTitle: {
    color: '#464646',
    fontSize: { xs: 16, tablet: 20 },
    fontWeight: 500,
    maxWidth: 374,
    margin: { xs: '15px auto 17px', tablet: '32px auto' },
    textAlign: 'center',
  },
  logoText: {
    fontSize: { xs: 24, tablet: 32 },
    lineHeight: 'normal',
    fontWeight: 700,
    color: '#464646',
  },
  contentWrapper: {
    border: '3px solid #fbcd00',
    display: 'flex',
    borderRadius: '12px',
    height: '100%',
    width: '100%',
  },
  contentLabel: {
    width: { xs: '38%', tablet: '36%' },
    bgcolor: '#fbcd00',
    color: '#464646',
    fontSize: { xs: 18, tablet: 24 },
    fontWeight: 500,
    display: 'flex',
    whiteSpace: 'pre-line',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    alignSelf: 'stretch',
    ':after': {
      content: "''",
      width: 0,
      height: 0,
      borderStyle: 'solid',
      borderWidth: { xs: '49.5px 0 49.5px 26px', tablet: '58px 0 58px 36px' },
      borderColor: 'transparent transparent transparent #fbcd00',
      position: 'absolute',
      right: 0.5,
      transform: 'translateX(100%)',
    },
  },
  rightContent: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    paddingBottom: '15px',
    paddingTop: { xs: '12px', tablet: 0 },
    svg: {
      width: { xs: 47, tablet: 72 },
      height: { xs: 47, tablet: 72 },
    },
  },
  rightText: {
    fontSize: { xs: 20, tablet: 24 },
    fontWeight: 500,
    mt: { xs: '-3px', tablet: '-6px' },
    color: '#464646',
    textAlign: 'center',
  },
  title: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-end',
    gap: '5px',
    svg: {
      width: { xs: 111, tablet: 148 },
      height: { xs: 48, tablet: 64 },
    },
  },
  scheduleBlock: {
    padding: { xs: '24px 24px 24px 16px', tablet: '32px 29px 32px' },
    display: 'flex',
    alignItems: 'center',
    borderRadius: '12px',
    justifyContent: 'center',
    svg: {
      width: { xs: 63, tablet: 100 },
      height: { xs: 63, tablet: 100 },
    },
  },
  dotText: {
    fontSize: { xs: 13, tablet: 18 },
    fontWeight: 500,
    color: '#464646',
    ':before': {
      content: '"・"',
      marginRight: '4px',
    },
  },
  divider: {
    marginTop: '14px',
    marginLeft: 'auto',
    marginRight: 'auto',
    background: 'linear-gradient(to right, #fdc84b 50%, #cedae0 50%)',
    width: '144px',
    height: '8px',
  },
  seeDetailBtn: {
    maxWidth: 298,
    '&.shadow': {
      '@media (max-width: 768px)': {
        maxWidth: 214,
        height: 48,
        fontSize: 14,
        fontWeight: 700,
        borderRadius: 24,
        svg: {
          width: 24,
          height: 24,
        },
      },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

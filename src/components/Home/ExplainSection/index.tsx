import {
  <PERSON>,
  <PERSON><PERSON>,
  ButtonB<PERSON>,
  Container,
  Grid,
  Typography,
} from '@mui/material';
import { useFetchUser } from 'hooks';
import { ArrowRightIcon, HomeScheduleIcon, LogoIcon, UsageIcon3 } from 'icons';
import dynamic from 'next/dynamic';
import { Link as ScrollLink } from 'react-scroll';

import LogoTitle from '../LogoTitle';
import styles from './styles';

const NewConsultationButton = dynamic(
  () => import('components/Home/HomeConsultationButton'),
  { ssr: false },
);

const ExplainSection = () => {
  const { isSuccess } = useFetchUser({ enabled: false });
  return (
    <Box>
      <Box sx={styles.usageArrow}>
        <Box sx={styles.background}>
          <Typography
            fontWeight={700}
            fontSize={{ xs: 20, tablet: 32 }}
            color="white"
            textAlign="center"
          >
            {`リコ活独自の「専門家」「カルテ」で\n離婚、夫婦問題・修復も解決`}
          </Typography>

          <Container
            maxWidth="hc"
            disableGutters
            sx={{ mt: { xs: '28px', tablet: '44px' } }}
          >
            <Grid container columnSpacing={4}>
              <Grid item xs={12} md={6}>
                <Box sx={styles.contentBox}>
                  <LogoTitle title="専門家" />
                  <Typography sx={styles.blockTitle}>
                    弁護士とカウンセラー、両方いるから「離婚」+「夫婦問題・修復」の相談も
                  </Typography>
                  <Box display="flex" flexDirection="column" gap="16px">
                    <ScrollLink to="lawyer-list" smooth offset={-96}>
                      <ButtonBase sx={styles.contentWrapper}>
                        <Box sx={styles.contentLabel}>
                          <Typography fontWeight="inherit" fontSize="inherit">
                            離婚 なら
                          </Typography>
                        </Box>
                        <Box sx={styles.rightContent}>
                          <UsageIcon3 />
                          <Typography sx={styles.rightText}>弁護士</Typography>
                        </Box>
                      </ButtonBase>
                    </ScrollLink>
                    <ScrollLink to="counselor-list" smooth offset={-96}>
                      <ButtonBase sx={styles.contentWrapper}>
                        <Box sx={styles.contentLabel}>
                          <Typography fontWeight="inherit" fontSize="inherit">
                            夫婦問題{'\n'}修復 なら
                          </Typography>
                        </Box>
                        <Box sx={styles.rightContent}>
                          <UsageIcon3 />
                          <Typography sx={styles.rightText}>
                            カウンセラー
                          </Typography>
                        </Box>
                      </ButtonBase>
                    </ScrollLink>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box
                  sx={styles.contentBox}
                  mt={{ xs: 2, md: 0, height: '100%' }}
                >
                  <Box sx={styles.title}>
                    <LogoIcon />
                    <Typography sx={styles.logoText}>カルテ</Typography>
                  </Box>
                  <Box mt="14px" className="logo-divider" />
                  <Typography sx={styles.blockTitle}>
                    専門家への相談時間をフル活用できる{'\n'}
                    <Typography
                      component="span"
                      sx={[
                        styles.blockTitle as never,
                        { color: 'primary.main' },
                      ]}
                    >
                      「事前課題チェックリスト」
                    </Typography>
                  </Typography>
                  <Box
                    height={4}
                    bgcolor="secondary.main"
                    sx={{ borderRadius: '3px' }}
                  />
                  <Box sx={styles.scheduleBlock}>
                    <HomeScheduleIcon />
                    <Box display="flex" flexDirection="column" gap="2px">
                      <Typography sx={styles.dotText}>
                        相談ポイントを事前に整理
                      </Typography>
                      <Typography sx={styles.dotText}>
                        ピッタリの専門家をご提案
                      </Typography>
                      <Typography sx={styles.dotText}>
                        専門家との相談がスムーズ
                      </Typography>
                    </Box>
                  </Box>
                  <Box display="flex" justifyContent="center">
                    <Button
                      href={process.env.HOME_EXTERNAL_LINK || '/'}
                      variant="outlined"
                      color="primary"
                      type="submit"
                      fullWidth
                      size="large"
                      sx={styles.seeDetailBtn}
                      className="shadow"
                      LinkComponent="a"
                      target="_blank"
                      rel="noreferrer"
                      endIcon={
                        <ArrowRightIcon width={32} height={32} color="gold" />
                      }
                    >
                      詳細はこちら
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Container>
        </Box>
      </Box>
      <Box
        minHeight={{ xs: '217px', tablet: '295px' }}
        bgcolor="#fff5cc"
        display="flex"
        justifyContent="center"
        alignItems="center"
        p={
          isSuccess
            ? { xs: '67px 16px 40px', tablet: '108px 24px 68px' }
            : { xs: '75px 16px 48px', tablet: '80px 24px 48px' }
        }
      >
        <Box>
          <NewConsultationButton />
        </Box>
      </Box>
    </Box>
  );
};

export default ExplainSection;

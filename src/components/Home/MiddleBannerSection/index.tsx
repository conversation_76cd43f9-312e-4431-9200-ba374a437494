import { Box, Container } from '@mui/material';
import useBreakpoint from 'hooks/useBreakpoint';
import Image from 'next/image';

import styles from './styles';

const MiddleBanner = () => {
  const isBreakpoint = useBreakpoint({});
  // TODO: Separate this to 2 Image and using sizes of each to reduce image size load
  return (
    <Box bgcolor="primary.main">
      <Container maxWidth="lg" disableGutters>
        <Image
          src={
            isBreakpoint
              ? '/images/mobile-home-middle-banner.png'
              : '/images/home-middle-banner.webp'
          }
          alt="home-banner"
          sizes="100vw"
          style={styles.middleBanner}
          width={1}
          quality={100}
          height={isBreakpoint ? 229 / 375 : 400 / 1136}
        />
      </Container>
    </Box>
  );
};

export default MiddleBanner;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    padding: { xs: '32px 24px 40px', tablet: '88px 24px' },
  },
  title: {
    fontSize: { xs: '24px', tablet: '32px' },
    fontWeight: 'bold',
    color: '#FCAB28',
    textAlign: 'center',
  },
  contentWrapper: {
    maxWidth: 916,
    margin: { xs: '24px auto 0px', tablet: '48px auto 0px' },
    display: 'flex',
    flexDirection: 'column',
    gap: '30px',
  },
  usageWrapper: {
    display: 'flex',
    pl: { xs: '6px', md: '99px' },
    gap: { xs: 3, tablet: 7 },
    mb: { xs: 1, tablet: 2 },
    '&:last-child': {
      mb: 0,
    },
    svg: {
      width: { xs: 56, tablet: 64 },
      height: { xs: 56, tablet: 64 },
    },
  },
  stepText: {
    textAlign: 'center',
    color: '#fbcd00',
    fontSize: { xs: 16, tablet: 20 },
    fontWeight: 700,
  },
  titleText: {
    color: '#464646',
    fontSize: { xs: 18, tablet: 24 },
    fontWeight: 700,
    mt: { xs: '4px', tablet: '9px' },
  },
  contentText: {
    fontSize: { xs: 14, tablet: 20 },
    color: '#464646',
    mt: { xs: '2px', tablet: '4px' },
    whiteSpace: { xs: 'pre-line', tablet: 'normal' },
    maxWidth: '626px',
  },
  line: {
    borderBottom: 'solid 2.8px #fbcd00',
    position: 'relative',
    '::after': {
      content: "''",
      position: 'absolute',
      width: 0,
      height: 0,
      borderStyle: 'solid',
      borderWidth: '14px 16px 0 16px',
      borderColor: '#fbcd00 transparent transparent transparent',
      left: { xs: 19, tablet: 115 },
    },
    '::before': {
      content: "''",
      position: 'absolute',
      width: 0,
      height: 0,
      borderStyle: 'solid',
      borderWidth: '12.8px 13px 0 13px',
      borderColor: '#fff transparent transparent transparent',
      zIndex: 1,
      left: { xs: 22, tablet: 118 },
      top: -0.8,
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import { Box, Container, Typography } from '@mui/material';
import { fadeInUp } from 'components/Animation/FadeMotion';
import { motion } from 'framer-motion';
import { UsageIcon1, UsageIcon2, UsageIcon3, UsageIcon4 } from 'icons';

import styles from './styles';

const usageContents = [
  {
    icon: <UsageIcon1 />,
    title: 'メールで無料登録',
    content: 'メールだけで簡単30秒。',
  },
  {
    icon: <UsageIcon2 />,
    title: 'カルテ入力',
    content: 'ログイン後各種情報を入力。\n相談で役立つ事前課題チェック',
  },
  {
    icon: <UsageIcon3 />,
    title: '専門家を検索・相談予約',
    content: '弁護士、カウンセラーから選択。\nお支払い金額は事前確定で安心',
  },
  {
    icon: <UsageIcon4 />,
    title: '相談（初回無料※）',
    content:
      '日本全国オンラインで実施できます。\n対面での実施もご相談ください\n※初回無料相談の有無は専門家ごとに異なります。各専門家ページをご確認ください。',
  },
];

const HowToUseSection = () => {
  return (
    <Box sx={styles.container}>
      <Container sx={{ maxWidth: '916px' }} disableGutters>
        <Typography component={motion.p} variants={fadeInUp} sx={styles.title}>
          リコ活の使い方
        </Typography>
        <Box
          component={motion.div}
          variants={fadeInUp}
          className="logo-divider"
          mt={{ xs: '10px', tablet: 2 }}
        />
        <Box sx={styles.contentWrapper}>
          {usageContents.map((item, index) => (
            <Box key={index}>
              <Box sx={styles.usageWrapper}>
                <Box>
                  <Typography sx={styles.stepText}>Step {index + 1}</Typography>
                  {item.icon}
                </Box>
                <Box flex={1}>
                  <Typography sx={styles.titleText}>{item.title}</Typography>
                  <Typography sx={styles.contentText}>
                    {item.content}
                  </Typography>
                </Box>
              </Box>
              {index < usageContents.length - 1 && <Box sx={styles.line} />}
            </Box>
          ))}
        </Box>
      </Container>
    </Box>
  );
};

export default HowToUseSection;

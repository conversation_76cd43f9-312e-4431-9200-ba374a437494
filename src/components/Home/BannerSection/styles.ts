import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    bgcolor: 'orange',
    minHeight: { xs: '322px', tablet: '313px' },
    pt: { xs: 5, tablet: '114px' },
    px: { xs: 1, tablet: 0 },
  },
  text: {
    textAlign: 'center',
    fontWeight: 'bold',
    color: 'white',
    position: 'relative',
    zIndex: 1,
    fontSize: { xs: 24, tablet: 32 },
    textShadow: '0 2px 4px #ef7453',
    lineHeight: 1.5,
  },
  childImage: {
    display: { xs: 'none', sl: 'block' },
    right: { sl: 213 },
    position: { sl: 'absolute' },
    top: { sl: -90 },
    width: { sl: 102 },
    height: { sl: 102 },
  },
  womanImage: {
    display: { xs: 'none', sl: 'block' },
    position: { sl: 'absolute' },
    top: { sl: -117 },
    left: { sl: 37 },
    aspectRatio: '302 / 316',
    width: 302,
  },
  manImage: {
    display: { xs: 'none', sl: 'block' },
    right: { sl: 72 },
    position: { sl: 'absolute' },
    top: { sl: -1 },
    width: { sl: 219 },
    height: { sl: 202 },
  },
  mobileImage: {
    maxWidth: 321,
    aspectRatio: '321 / 171',
    position: 'relative',
    margin: '0 auto',
    display: { sl: 'none' },
    mt: '38px',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

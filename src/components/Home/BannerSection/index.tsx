import { Box, Container, Typography } from '@mui/material';
import FadeMotion from 'components/Animation/FadeMotion';
import { motion } from 'framer-motion';
import Image from 'next/image';

import styles from './styles';

const pullupVariant = {
  initial: { y: 100, opacity: 0 },
  animate: (i: any) => ({
    y: 0,
    opacity: 1,
    transition: {
      delay: i * 0.05, // Delay each letter's animation by 0.05 seconds
    },
  }),
};

const BannerSection = () => {
  return (
    <Box sx={styles.container}>
      <Container maxWidth="lg" disableGutters sx={{ position: 'relative' }}>
        <FadeMotion>
          <Typography sx={styles.text}>
            {`離婚、夫婦問題・修復も\nオンラインで無料相談`
              .split('')
              .map((word, i) => (
                <motion.span
                  key={i}
                  variants={pullupVariant}
                  initial="initial"
                  animate="animate"
                  custom={i}
                >
                  {word}
                </motion.span>
              ))}
          </Typography>
          <Box sx={styles.mobileImage} component={motion.div}>
            <Image
              width={1}
              quality={100}
              height={331 / 445}
              style={{
                width: '100%',
                height: 'auto',
                display: 'block',
              }}
              src="/images/footer-image.webp"
              alt=""
              priority
              sizes="(max-width: 1032px) 100vw, 0vw"
            />
          </Box>
          <Box sx={styles.childImage}>
            <Image
              fill
              src="/images/child-image.webp"
              alt=""
              priority
              quality={100}
              sizes="(min-width: 1032px) 15vw, 0vw"
            />
          </Box>
          <Box sx={styles.womanImage}>
            <Image
              priority
              fill
              src="/images/female-image.webp"
              alt=""
              quality={100}
              sizes="(min-width: 1032px) 40vw, 0vw"
            />
          </Box>
          <Box sx={styles.manImage}>
            <Image
              fill
              src="/images/male-image.webp"
              alt=""
              priority
              quality={100}
              sizes="(min-width: 1032px) 30vw, 0vw"
            />
          </Box>
        </FadeMotion>
      </Container>
    </Box>
  );
};

export default BannerSection;

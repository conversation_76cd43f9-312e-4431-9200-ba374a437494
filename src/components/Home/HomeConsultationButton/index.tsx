import type { LoadingButtonProps } from '@mui/lab';
import { Box, Typography } from '@mui/material';
import TitleButton from 'components/UI/TitleButton';
import { useFetchDetail, useFetchUser } from 'hooks';
import apiQuery from 'hooks/apiQuery';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import { useRouter } from 'next/router';
import Helper from 'utils/helpers';

const HomeConsultationButton = ({
  afterLoginText = '相談時間をフル活用',
  ...props
}: Omit<LoadingButtonProps, 'text' | 'icon'> & { afterLoginText?: string }) => {
  const { push } = useRouter();
  const fetchConsultationDetail = useFetchDetail({
    ...apiQuery.consultationDetail,
    enabled: false,
  });
  const consultationDetail =
    fetchConsultationDetail.detail as ConsultationRecord;
  const { data: currentUser } = useFetchUser<CustomerData>({ enabled: true });
  const isCompletedFields =
    currentUser?.phone &&
    currentUser?.isCompletedProfile &&
    consultationDetail.backgroundOfDivorce &&
    consultationDetail.marriageInformation &&
    (!Helper.checkIsEmptyObject(consultationDetail.expectLawyer) ||
      !Helper.checkIsEmptyObject(consultationDetail.expectCounselor));

  return (
    <Box width="fit-content">
      <TitleButton
        title={!Helper.getWebCookie() ? 'メールで簡単30秒' : afterLoginText}
        text={!Helper.getWebCookie() ? 'お悩みを無料相談' : 'カルテを入力'}
        subText="［ 秘密厳守 ］"
        onClick={() => {
          if (!Helper.getWebCookie()) {
            push('/register');
          } else if (!isCompletedFields) {
            push('/customer/consultation-form');
          } else {
            push('/customer/my-page');
          }
        }}
        {...props}
      />
      {!Helper.getWebCookie() && (
        <Typography
          color="heading"
          fontSize={{ xs: 12, tablet: 14 }}
          marginTop="13px"
          whiteSpace="pre-line"
          textAlign="center"
        >
          {`※初回無料相談の有無は専門家ごとに異なります。\n各専門家ページをご確認ください。`}
        </Typography>
      )}
    </Box>
  );
};

export default HomeConsultationButton;

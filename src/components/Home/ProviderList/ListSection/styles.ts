import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    border: { xs: '2px solid #FCAB28', tablet: '2.5px solid #FCAB28' },
    borderRadius: '12px',
  },
  titleWrapper: {
    bgcolor: '#FCAB28',
    paddingTop: { xs: '12px', tablet: '13.5px' },
    paddingBottom: { xs: '14px', tablet: 2 },
    borderTopLeftRadius: '9px',
    borderTopRightRadius: '9px',
  },
  listContainer: {
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: '24px',
    '& :nth-of-type(n+9)': {
      display: 'none',
    },
    '@media (max-width: 768px)': {
      '& :nth-of-type(n+7)': {
        display: 'none',
      },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

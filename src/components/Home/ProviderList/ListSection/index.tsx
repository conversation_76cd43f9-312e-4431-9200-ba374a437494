import {
  <PERSON>,
  Button,
  Container,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material';
import Rating from 'components/UI/Rating';
import { t } from 'i18next';
import { ArrowRightIcon } from 'icons';
import { times } from 'lodash';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { PROVIDER_LIST_PATH, ProviderType } from 'utils/constants';

import styles from './styles';

const ListSection = ({
  data,
  title,
  role = ProviderType.LAWYER,
  loading,
}: {
  data: {
    _id: string;
    fullName: string;
    nickname?: string;
    images: { originUrl: string }[];
    avgRating: number;
    totalReview: number;
  }[];
  title: string;
  role?: ProviderType;
  loading?: boolean;
}) => {
  const { push } = useRouter();
  return (
    <Container maxWidth="hc" disableGutters>
      <Box sx={styles.container}>
        <Box sx={styles.titleWrapper}>
          <Typography
            fontSize={{ xs: 20, tablet: 26 }}
            fontWeight={700}
            color="white"
            textAlign="center"
          >
            {title}
            <Typography
              component="span"
              color="#464646"
              ml={1}
              fontSize={{ xs: 20, tablet: 26 }}
              fontWeight={700}
            >
              {role === ProviderType.LAWYER ? '弁護士' : 'カウンセラー'}
            </Typography>
          </Typography>
        </Box>
        <Box padding={{ xs: '28px 23px', tablet: '32px 16px' }}>
          <Stack direction="row" sx={styles.listContainer}>
            {loading &&
              times(8, (index) => (
                <Box key={index}>
                  <Box
                    sx={{ width: 1, maxWidth: { xs: '80px', tablet: '96px' } }}
                  >
                    <Box
                      margin="0 auto"
                      justifyContent="center"
                      height={{ xs: '80px', tablet: '96px' }}
                      width={{ xs: '80px', tablet: '96px' }}
                    >
                      <Skeleton variant="circular" width="100%" height="100%" />
                    </Box>
                    <Box
                      height={{ xs: '16px', tablet: '17.5px' }}
                      mt={{ xs: '6px', tablet: '10px' }}
                    >
                      <Skeleton />
                    </Box>
                    <Box height={{ xs: '17.5px', tablet: '23px' }}>
                      <Skeleton />
                    </Box>
                  </Box>
                </Box>
              ))}
            {!loading &&
              data.map((provider) => (
                <Box
                  key={provider._id}
                  onClick={() =>
                    push(`${PROVIDER_LIST_PATH[role]}/${provider._id}`)
                  }
                >
                  <Box
                    className="scale pointer"
                    sx={{
                      width: 1,
                      maxWidth: { xs: '80px', tablet: '96px' },
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <Box
                      margin="0 auto"
                      justifyContent="center"
                      height={{ xs: '80px', tablet: '96px' }}
                      width={{ xs: '80px', tablet: '96px' }}
                    >
                      <Image
                        alt=""
                        src={
                          provider.images[0]?.originUrl ||
                          '/images/default-avatar.png'
                        }
                        width={1}
                        height={1}
                        sizes="(min-width: 1135px) 10vw, 25vw"
                        style={{
                          borderRadius: '50%',
                          width: '100%',
                          height: 'auto',
                        }}
                      />
                    </Box>
                    <Typography
                      mt={{ xs: '6px', tablet: '10px' }}
                      fontSize={{ xs: '11px', tablet: '12px' }}
                      fontWeight="bold"
                      textAlign="center"
                      color="#464646"
                    >
                      {role === ProviderType.COUNSELOR
                        ? 'カウンセラー'
                        : t('home.lawyer')}
                    </Typography>
                    <Typography
                      fontSize={{ xs: '12px', tablet: '16px' }}
                      fontWeight="bold"
                      textAlign="center"
                      color="#464646"
                      style={{
                        display: '-webkit-box',
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        WebkitLineClamp: 2,
                        marginBottom: '10px',
                      }}
                    >
                      {(role === ProviderType.COUNSELOR && provider.nickname) ||
                        provider.fullName}
                    </Typography>
                    {role === ProviderType.COUNSELOR && (
                      <Box sx={{ marginTop: 'auto' }}>
                        <Rating
                          sx={{ justifyContent: 'center' }}
                          rate={{
                            // avgRating: Helper.calculateRoundedRating(
                            //   provider.avgRating,
                            // ),
                            avgRating: provider.avgRating,
                            totalReview: provider.totalReview,
                          }}
                        />
                      </Box>
                    )}
                  </Box>
                </Box>
              ))}
          </Stack>
          <Box display="flex" justifyContent="center">
            <Button
              variant="contained"
              className="shadow tabletStyle"
              size="large"
              color="yellow"
              fullWidth
              sx={{
                maxWidth: 358,
                mt: { xs: 4, tablet: 5 },
                '&.shadow.tabletStyle': {
                  fontSize: { xs: 18, tablet: 20 },
                  height: { xs: 48, tablet: 59 },
                },
              }}
              LinkComponent={Link}
              href={`${PROVIDER_LIST_PATH[role]}`}
              endIcon={<ArrowRightIcon width={32} height={32} color="white" />}
            >
              {role === ProviderType.LAWYER ? '弁護士' : 'カウンセラー'} を探す
            </Button>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default ListSection;

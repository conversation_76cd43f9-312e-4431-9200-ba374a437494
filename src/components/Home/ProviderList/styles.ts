import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    padding: { xs: '32px 16px 40px', tablet: '80px 16px 88px' },
  },
  mainText: {
    color: '#464646',
    fontSize: { xs: 16, tablet: 24 },
    fontWeight: { xs: 500, tablet: 700 },
    textAlign: 'center',
    mt: { xs: 2, tablet: 3 },
    mb: { xs: 2, tablet: 5 },
    whiteSpace: { xs: 'pre-line', tablet: 'normal' },
  },
  divider: {
    marginTop: '14px',
    marginLeft: 'auto',
    marginRight: 'auto',
    background: 'linear-gradient(to right, #fdc84b 50%, #cedae0 50%)',
    width: '144px',
    height: '8px',
  },
  listContainer: {
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: '24px',
    '& :nth-of-type(n+9)': {
      display: 'none',
    },
    '@media (max-width: 768px)': {
      '& :nth-of-type(n+7)': {
        display: 'none',
      },
    },
  },
  contactBtn: {
    maxWidth: 358,
    marginTop: 5,
    '&.shadow': {
      '@media (max-width: 768px)': {
        marginTop: 3,
        marginLeft: '25px',
        marginRight: '25px',
        borderRadius: 24,
        height: 48,
        fontSize: 14,
        fontWeight: 700,
        svg: {
          width: 24,
          height: 24,
        },
      },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

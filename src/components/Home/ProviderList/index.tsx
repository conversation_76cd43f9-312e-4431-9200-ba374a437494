import { Box, Button, Typography } from '@mui/material';
import FadeMotion, { fadeInUp } from 'components/Animation/FadeMotion';
import { motion } from 'framer-motion';
import { ArrowRightIcon } from 'icons';
import Link from 'next/link';
import { Element } from 'react-scroll';
import { ProviderType } from 'utils/constants';

import LogoTitle from '../LogoTitle';
import ListSection from './ListSection';
import styles from './styles';

const NewProviderList = ({
  lawyers,
  counselors,
  isLawyerLoading,
  isCounselorLoading,
}: {
  lawyers: any;
  counselors: any;
  isLawyerLoading: boolean;
  isCounselorLoading: boolean;
}) => {
  return (
    <Box sx={styles.container}>
      <FadeMotion>
        <Element name="lawyer-list">
          <LogoTitle title="専門家" />
        </Element>

        <Typography sx={styles.mainText}>
          弁護士とカウンセラー両方いるから、{'\n'}
          離婚だけではなく、夫婦問題・修復の相談も
        </Typography>
        <Box component={motion.div} variants={fadeInUp}>
          <ListSection
            data={lawyers}
            title="離婚なら"
            role={ProviderType.LAWYER}
            loading={isLawyerLoading}
          />
        </Box>
        <Element name="counselor-list"></Element>
        <Box mt={3} component={motion.div} variants={fadeInUp}>
          <ListSection
            data={counselors}
            title="夫婦問題・修復なら"
            role={ProviderType.COUNSELOR}
            loading={isCounselorLoading}
          />
        </Box>
        <Box display="flex" justifyContent="center">
          <Button
            href="/contact-us"
            variant="outlined"
            color="primary"
            type="submit"
            fullWidth
            size="large"
            sx={styles.contactBtn}
            className="shadow tabletStyle"
            LinkComponent={Link}
            endIcon={<ArrowRightIcon width={32} height={32} color="gold" />}
          >
            専門家の登録はこちら
          </Button>
        </Box>
      </FadeMotion>
    </Box>
  );
};

export default NewProviderList;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  logoText: {
    fontSize: { xs: 24, tablet: 32 },
    lineHeight: 'normal',
    fontWeight: 700,
    color: '#464646',
  },
  title: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-end',
    gap: { xs: '3px', tablet: '5px' },
    svg: {
      width: { xs: 109, tablet: 148 },
      height: { xs: 47, tablet: 64 },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

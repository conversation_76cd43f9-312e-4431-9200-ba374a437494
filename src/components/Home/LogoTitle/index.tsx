import { Box, Typography } from '@mui/material';
import { LogoIcon } from 'icons';

import styles from './styles';

const LogoTitle = ({ title }: { title: string }) => {
  return (
    <div>
      <Box sx={styles.title}>
        <LogoIcon />
        <Typography sx={styles.logoText}>{title}</Typography>
      </Box>
      <Box mt="14px" className="logo-divider" />
    </div>
  );
};

export default LogoTitle;

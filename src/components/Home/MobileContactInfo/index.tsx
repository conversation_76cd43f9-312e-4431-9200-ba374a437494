import { Box, Divider, Typography } from '@mui/material';
import { ContactIcon, LineIcon } from 'icons';
import Link from 'next/link';
import { getLineUrl } from 'utils/getLineUrl';

const MobileContactInfo = () => {
  return (
    <Box
      position="sticky"
      alignItems="center"
      bottom={0}
      p="8px 28px 16px"
      justifyContent="center"
      bgcolor="white"
      boxShadow="0px 0px 4px 0px rgba(91, 107, 128, 0.2)"
      display={{ xs: 'flex', md: 'none' }}
      zIndex={2}
    >
      <Box href={getLineUrl()} component={Link} target="_blank">
        <Box display="flex" justifyContent="center" gap="4px">
          <LineIcon width={20} height={20} />
          <Typography fontWeight={700} color="#06C755" fontSize={14}>
            LINEで無料相談
          </Typography>
        </Box>
        <Typography marginTop="4px" fontSize={12} color="text.primary">
          リコ活スタッフへ相談 ＞
        </Typography>
      </Box>

      {process.env.SHOW_OPERATOR_CONTACT === 'true' && (
        <>
          <Divider
            orientation="vertical"
            sx={{ height: '24px', margin: '0px 16px' }}
          />
          <Box
            href={`tel:${process.env.OPERATOR_PHONE}`}
            component={Link}
            target="_blank"
          >
            <Box display="flex" justifyContent="center" gap="4px">
              <ContactIcon width={20} height={20} />
              <Typography fontWeight={700} color="primary" fontSize={14}>
                {process.env.OPERATOR_PHONE}
              </Typography>
            </Box>
            <Typography marginTop="4px" fontSize={12} color="text.primary">
              受付時間 9:00〜18:00 (平日)
            </Typography>
          </Box>
        </>
      )}
    </Box>
  );
};

export default MobileContactInfo;

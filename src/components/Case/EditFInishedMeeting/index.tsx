import { Box, Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { t } from 'i18n';
import { CopyIcon, InfoIcon } from 'icons';
import type { IMeetingsItem } from 'models/case/interface';
import { MeetingType, MomentFormat } from 'utils/constants';

const EditFinishedMeeting = ({ data }: { data: IMeetingsItem }) => {
  return (
    <>
      <Box p="9px 17px 7px" borderRadius="6px" bgcolor="divine">
        <Stack
          spacing={1}
          direction="row"
          alignItems="center"
          sx={{ svg: { color: 'icon' } }}
          justifyContent="center"
        >
          <InfoIcon />
          <Typography fontSize={14} color="neutral6" fontWeight={500}>
            {t('rechargePayment.alert')}
          </Typography>
        </Stack>
      </Box>
      <Box
        mt={4}
        display="grid"
        sx={{
          gridTemplateColumns: 'max-content auto',
          gridGap: '12px 88px',
        }}
      >
        <Typography fontWeight="bold">
          {t('confirmMeeting.meetingDate')}
        </Typography>
        <Typography>
          {dayjs(data.finalizedDate).format(
            MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
          )}
        </Typography>
        <Typography fontWeight="bold">
          {t('confirmMeeting.meetingType')}
        </Typography>
        <Stack spacing="4px">
          <Typography>
            {data.type === MeetingType.ONLINE
              ? t('caseDetail.methodOnline')
              : t('caseDetail.methodInPerson')}
          </Typography>
          {data.meetingUrl && data.type === MeetingType.ONLINE && (
            <Stack direction="row">
              <Typography color="icon" className="line-clamp">
                {data.meetingUrl}
              </Typography>
              <Box
                sx={{
                  display: 'block',
                  ml: 1,
                  p: 0,
                  svg: {
                    color: 'icon',
                  },
                }}
              >
                <CopyIcon />
              </Box>
            </Stack>
          )}
        </Stack>
        <Typography fontWeight="bold">
          {t('confirmMeeting.duration')}
        </Typography>
        <Typography>{`${data.paymentInfo?.duration}分`}</Typography>
      </Box>
    </>
  );
};

export default EditFinishedMeeting;

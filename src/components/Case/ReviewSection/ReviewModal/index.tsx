import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Stack } from '@mui/material';
import type { ConfirmModalProps } from 'components/ConfirmModal/modal';
import ConfirmModal from 'components/ConfirmModal/modal';
import { RatingField, TextField } from 'components/Form';
import Image from 'next/image';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import type { ReviewFormValues } from './schema';
import schema from './schema';

export interface ReviewModalProps extends ConfirmModalProps {
  onSubmit: (values: ReviewFormValues, isDirty: boolean) => void;
  placeholder?: string;
  defaultValues: Partial<ReviewFormValues>;
  avatar?: string;
}
const ReviewModal = ({
  onSubmit,
  placeholder,
  defaultValues,
  avatar,
  ...props
}: ReviewModalProps) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useForm<ReviewFormValues>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues,
  });

  useEffect(() => {
    reset({ comments: defaultValues.comments, point: defaultValues.point });
  }, [defaultValues.comments, defaultValues.point, reset]);

  useEffect(() => {
    if (!props.open) {
      reset();
    }
  }, [props.open, reset]);

  return (
    <ConfirmModal {...props}>
      <span>
        <Box
          mt={{ xs: 2, tablet: 3 }}
          component="form"
          id="review-form"
          onSubmit={handleSubmit((values) => onSubmit(values, isDirty))}
        >
          <Stack gap={{ xs: '12px', tablet: 2 }}>
            {avatar && (
              <Box
                width={{ xs: 64, tablet: 84 }}
                height={{ xs: 64, tablet: 84 }}
                position="relative"
                margin="0 auto"
              >
                <Image
                  style={{ borderRadius: '50%' }}
                  alt="avatar"
                  src={avatar}
                  fill
                />
              </Box>
            )}
            <RatingField
              control={control}
              name="point"
              containerProps={{
                sx: {
                  textAlign: 'center',
                  '& .form-error': {
                    justifyContent: 'center',
                  },
                },
              }}
            />
            <TextField
              multiline
              rows={7}
              maxLength={500}
              control={control}
              name="comments"
              labelCol={12}
              placeholder={placeholder}
            />
          </Stack>
        </Box>
      </span>
    </ConfirmModal>
  );
};

export default ReviewModal;

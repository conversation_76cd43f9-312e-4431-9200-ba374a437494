import { Box, Button, Stack, Typography } from '@mui/material';
import type { ReviewFormValues } from 'components/Case/ReviewSection/ReviewModal/schema';
import BaseRating from 'components/Form/RatingField/BaseRating';
import TruncateText from 'components/TruncateText';
import { t } from 'i18n';
import { EditIcon, RecommendIcon } from 'icons';
import type { IReviewObject } from 'models/case/interface';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
import { CaseStatusType } from 'utils/constants';

import styles from './styles';

const ReviewModal = dynamic(
  () => import('components/Case/ReviewSection/ReviewModal'),
  { ssr: false },
);

const closedStatus = [
  CaseStatusType.ASSIGNED,
  CaseStatusType.CANCELED_BY_CONSUMER,
  CaseStatusType.CANCELED_BY_PROVIDER,
  CaseStatusType.CLOSED,
];

const ReviewSection = ({
  onSubmit,
  onCancel,
  reviewData,
  isLoading,
  caseStatus,
  title,
  content,
  avatar,
  placeholder,
}: {
  onSubmit: (values: ReviewFormValues) => void;
  onCancel: () => void;
  reviewData?: IReviewObject;
  isLoading: boolean;
  caseStatus: CaseStatusType;
  title?: string;
  content?: string;
  avatar?: string;
  placeholder?: string;
}) => {
  const [openReview, setOpenReview] = useState(false);
  const [updateReview, setUpdateReview] = useState(false);

  const isShowReviewPopup = reviewData?.isShowAgain;

  useEffect(() => {
    if (isShowReviewPopup) {
      setOpenReview(true);
    }
  }, [isShowReviewPopup]);

  const handleCancelReview = () => {
    if (isShowReviewPopup !== false) {
      onCancel();
    }
    setOpenReview(false);
    setUpdateReview(false);
  };

  const handleReviewCustomer = async (
    values: ReviewFormValues,
    isDirty: boolean,
  ) => {
    if (isDirty) {
      await onSubmit(values);
    }
    setUpdateReview(false);
    setOpenReview(false);
  };

  if (isShowReviewPopup === undefined) {
    return (
      <ReviewModal
        content={content}
        title={title}
        avatar={avatar}
        open={openReview || updateReview}
        onCancel={handleCancelReview}
        confirmText={t('reviewMeeting.confirmText')}
        placeholder={placeholder}
        confirmButtonProps={{
          form: 'review-form',
          type: 'submit',
          loading: isLoading,
        }}
        defaultValues={{
          point: reviewData?.point || 0,
          comments: reviewData?.comments || '',
        }}
        onSubmit={handleReviewCustomer}
      />
    );
  }

  return (
    <Box sx={styles.container} className="review-section">
      <Box display="flex" justifyContent="space-between">
        <Stack direction="row" spacing={1} alignItems="center">
          <RecommendIcon />
          <Typography
            fontSize={{ xs: 20, tablet: 24 }}
            fontWeight="bold"
            color="heading"
          >
            評価
          </Typography>
        </Stack>
        {!closedStatus.includes(caseStatus) && reviewData && (
          <Button
            variant="contained"
            color="secondary"
            onClick={() => setUpdateReview(true)}
            startIcon={<EditIcon />}
            sx={styles.editIcon}
            className="tabletStyle"
          >
            {t('global.edit')}
          </Button>
        )}
      </Box>
      {!reviewData?.point && !reviewData?.comments ? (
        <Typography
          className="16-to-14"
          component="div"
          mt={{ xs: 2, tablet: '28px' }}
          color="hint"
        >
          {t('reviewMeeting.noReview')}
        </Typography>
      ) : (
        <>
          <Box mt="28px" textAlign="center">
            <BaseRating
              showEmptyStar
              value={reviewData?.point}
              sx={{ pointerEvents: 'none' }}
            />
          </Box>
          {reviewData?.comments?.trim() ? (
            <Box p={3} borderRadius="12px" mt={2} bgcolor="backgroundColor">
              <TruncateText lines={5} text={reviewData?.comments || ''} />
            </Box>
          ) : (
            <Typography mt={1} color="hint">
              備考がありません
            </Typography>
          )}
        </>
      )}
      <ReviewModal
        content={content}
        title={title}
        avatar={avatar}
        open={openReview || updateReview}
        onCancel={handleCancelReview}
        confirmText={t('reviewMeeting.confirmText')}
        placeholder={placeholder}
        confirmButtonProps={{
          form: 'review-form',
          type: 'submit',
          loading: isLoading,
        }}
        defaultValues={{
          point: reviewData?.point || 0,
          comments: reviewData?.comments || '',
        }}
        onSubmit={handleReviewCustomer}
      />
    </Box>
  );
};

export default ReviewSection;

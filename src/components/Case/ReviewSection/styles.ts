import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    p: { xs: 2, tablet: 4 },
    bgcolor: 'white',
    flex: 1,
    borderRadius: { xs: 0, tablet: 2 },
    mb: { xs: 1, md: 2 },
  },
  editIcon: {
    color: 'white',
    fontSize: '16px',
    ':not(.Mui-disabled)': { svg: { color: 'white' } },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

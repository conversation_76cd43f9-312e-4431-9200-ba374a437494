import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  caseInfo: {
    display: 'flex',
    justifyContent: 'space-between',
    mb: { xs: '12px', tablet: 1 },
  },
  tooltipPopper: {
    '& .MuiTooltip-tooltip': {
      bgcolor: '#fdf6e2',
      boxShadow: '0 8px 16px -8px #d2dce1',
      color: 'heading',
      fontSize: { xs: 12, tablet: 14 },
      fontWeight: 'normal',
    },
  },
  meetingItem: {
    borderRadius: '12px',
    border: (theme) => `solid 1px ${theme.palette.neutral4}`,
    p: { xs: 2, tablet: 3 },
    mt: 2,
  },
  iconPayment: {
    svg: {
      display: 'block',
      width: { xs: 24, tablet: 30 },
      height: { xs: 16, tablet: 20 },
    },
  },
  iconMeetingNote: {
    svg: {
      display: 'block',
      width: { xs: 20, tablet: 24 },
      height: { xs: 20, tablet: 24 },
    },
  },
  infoButton: {
    p: 0,
    ml: '4px',
    svg: {
      color: 'icon',
      display: 'block',
      width: {
        xs: 16,
        tablet: 20,
      },
      height: {
        xs: 16,
        tablet: 20,
      },
    },
  },
  consumerNoteContainer: {
    borderRadius: 1,
    // bgcolor: 'backgroundColor',
    p: 2,
    mt: { xs: '12px', tablet: 2 },
    '.truncate-text__ellipsis': {
      pb: 0,
    },
  },
  cardInfo: {
    fontSize: { xs: 10, tablet: 12 },
    color: 'text.primary',
  },
  textPaymentInfo: {
    fontSize: { xs: 12, tablet: 14 },
    color: 'text.primary',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  tooltip: {
    bgcolor: '#fdf6e2',
  },
  titleText: {
    fontSize: { xs: 12, tablet: 14 },
    color: 'text.primary',
    maxWidth: { xs: 80, tablet: 120, xl: 293 },
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    wordBreak: 'break-word',
    verticalAlign: 'bottom',
  },
  moreIcon: {
    p: 0,
  },
} as Record<string, SxProps<Theme>>;

export default styles;

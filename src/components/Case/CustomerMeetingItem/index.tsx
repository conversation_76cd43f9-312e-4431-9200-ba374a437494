import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box,
  Button,
  ClickAwayListener,
  Divider,
  IconButton,
  Stack,
  SvgIcon,
  Tooltip,
  Typography,
} from '@mui/material';
import ConfirmModal from 'components/ConfirmModal/modal';
import { TextField } from 'components/Form';
import LoadingOverlay from 'components/LoadingOverlay';
import TruncateText from 'components/TruncateText';
import dayjs from 'dayjs';
import useBreakpoint from 'hooks/useBreakpoint';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import i18n, { t } from 'i18n';
import {
  CashIcon,
  InfoIcon,
  MeetingNoteIcon,
  MenuExtend,
  MoreIcon,
  MyPagePaymentIcon,
  TransferIcon,
} from 'icons';
import type { IMeetingsItem } from 'models/case/interface';
import caseQuery from 'models/case/query';
import type { IMeetingType } from 'models/consultation/interface';
import { IMeetingTypeLabel } from 'models/consultation/interface';
import type { ICoupon } from 'models/coupon/interface';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import CaseUtil from 'utils/caseUtil';
import {
  CardIcon,
  CaseStatusType,
  dropdownMenus,
  MeetingStatusType,
  MeetingType,
  MomentFormat,
  PAYMENT_TEXT,
  PaymentMethod,
  PaymentStatusType,
} from 'utils/constants';
import Helper from 'utils/helpers';
import type { InferType } from 'yup';
import { object, string } from 'yup';

import CouponDetailModal from '../CouponDetailModal';
import CouponTag from '../CouponTag';
import ExpectDate from '../ExpectDate';
import MeetingTag from '../MeetingTag';
import MeetingUrl from '../MeetingUrl';
import PaymentTooltipButton from '../PaymentTooltipButton';
import styles from './styles';

const ChangeCreditCardModal = dynamic(
  () => import('components/Customer/ChangeCardModal'),
);
const DropdownMenu = dynamic(() => import('components/UI/DropdownMenu'));

const cancelSchema = object({
  cancelReason: string()
    .max(200, i18n.t('validation.maxLength', { number: 200 }))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
});

const CustomerMeetingItem = ({
  meeting,
  refetchMeetingList,
  isLawyer,
  caseStatus,
  refetchCase,
  caseId,
}: {
  meeting: IMeetingsItem;
  refetchMeetingList: () => void;
  isLawyer: boolean;
  caseStatus: CaseStatusType;
  refetchCase: () => void;
  caseId: string;
}) => {
  const { setConfirmModal } = useGlobalState();
  const isBreakpoint = useBreakpoint({});
  const [openCancelModal, setOpenCancelModal] = useState(false);
  const [openTooltip, setOpenTooltip] = useState(false);
  const [openMenuTooltip, setOpenMenuTooltip] = useState(false);
  const [openChangeCardModal, setOpenChangeCardModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openCouponDetailModal, setOpenCouponDetailModal] = useState(false);
  const handleClickMoreButton = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    setAnchorEl(event.currentTarget);
  };

  const { control, handleSubmit, resetField } = useForm<
    InferType<typeof cancelSchema>
  >({
    resolver: yupResolver(cancelSchema),
  });

  useEffect(() => {
    if (openCancelModal) {
      resetField('cancelReason', { keepError: false });
    }
  }, [openCancelModal, resetField]);

  const { mutateAsync: cancelMeeting, isLoading: isCanceling } = useMutate<{
    status: MeetingStatusType;
    meetingId: string;
    cancelReason?: string;
  }>(caseQuery.customerCancelMeetingOfCounselor);
  const { duration, discount } = meeting.paymentInfo;
  const menuTittle = meeting.consultationMenu?.title;
  const cardInfo = meeting.paymentInfo.card;
  const cardNumber = cardInfo?.details.lastNumber;
  const cardType = cardInfo?.details.brand;
  const meetingType = meeting.consultationMenu?.meetingType as IMeetingType;
  const extendMenuDuration =
    meeting.extendMenus && meeting.extendMenus[0]?.duration;
  const extendMenuPrice = meeting.extendMenus && meeting.extendMenus[0]?.price;
  const {
    status: meetingStatus,
    meetingUrl,
    type,
    expectDate,
    finalizedDate,
    _id: meetingId,
  } = meeting;
  const {
    finalPrice = 0,
    unitPrice,
    status: paymentStatus = PaymentStatusType.PENDING,
    method: paymentMethod,
  } = meeting.paymentInfo;
  const displayStatus = CaseUtil.getDisplayPaymentStatus({
    paymentStatus,
    meetingStatus,
  });
  const basicPrice = Helper.addComma(
    Helper.getBasicPrice(unitPrice, duration) + (extendMenuPrice || 0),
  );
  const isFinishedMeeting = meetingStatus === MeetingStatusType.FINISHED;

  const isMeetingClosed =
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
      MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
    ].includes(meetingStatus) ||
    (meetingStatus === MeetingStatusType.FINISHED &&
      paymentStatus === PaymentStatusType.COMPLETED);

  const canChangeCreditCard =
    paymentMethod === PaymentMethod.CREDIT_CARD &&
    (([
      MeetingStatusType.WAITING_ANWSER_FROM_PROVIDER,
      MeetingStatusType.SCHEDULE_IS_DECIDED,
    ].includes(meetingStatus) &&
      paymentStatus === PaymentStatusType.PENDING) ||
      (isFinishedMeeting && paymentStatus === PaymentStatusType.ERROR));

  const isShowMeetingUrl = meetingUrl && type === MeetingType.ONLINE;
  const isShowExtendMenu =
    meeting.extendMenus && meeting.extendMenus.length > 0;
  const isDisableMeetingUrl =
    isFinishedMeeting ||
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
      MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
    ].includes(meetingStatus);
  const isCheckMoreButton = !isMeetingClosed && !isLawyer;

  const isCheckAfter18h =
    (finalizedDate &&
      dayjs(finalizedDate).subtract(1, 'd').set('hour', 18).set('minute', 0) <=
        dayjs()) ||
    (expectDate.length > 0 &&
      dayjs(expectDate[0]?.start)
        .subtract(1, 'd')
        .set('hour', 18)
        .set('minute', 0) <= dayjs());

  const { couponInfo } = meeting.paymentInfo;
  const handleCancelMeeting: SubmitHandler<
    InferType<typeof cancelSchema>
  > = async (values) => {
    const status = MeetingStatusType.CANCELED_BY_CONSUMER;
    setOpenCancelModal(false);
    cancelMeeting(
      { status, meetingId, ...values },
      {
        onSuccess: () => {
          refetchMeetingList();
          refetchCase();
        },
      },
    );
  };

  const handleMeetingAction = (key: string) => {
    setAnchorEl(null);
    if (key === 'cancel') {
      if (
        isCheckAfter18h &&
        caseStatus !== CaseStatusType.WAITING_ANWSER_FROM_PROVIDER
      ) {
        setConfirmModal({
          icon: 'error',
          hideCancelButton: true,
          content: (
            <Typography fontSize={14}>
              面談前日の18:00以降にキャンセルしたい場合は、リコ活事業部
              カスタマーサポートへ直接ご連絡ください。
            </Typography>
          ),
        });
      } else {
        setOpenCancelModal(true);
      }
    }
  };

  return (
    <Box>
      <Box mt={2} position="relative">
        <Box
          sx={styles.meetingItem}
          bgcolor={isMeetingClosed ? 'backgroundColor' : 'white'}
        >
          <Box sx={styles.caseInfo}>
            {finalizedDate ? (
              <Typography
                fontWeight={700}
                fontSize={{ tablet: 18 }}
                color="heading"
                whiteSpace={{ xs: 'pre-line', tablet: 'normal' }}
              >
                {`${dayjs(finalizedDate).format(
                  MomentFormat.JP_YEAR_MONTH_DATE_DAY,
                )}\n${dayjs(finalizedDate).format(
                  MomentFormat.JP_HOUR_MINUTE,
                )}`}
              </Typography>
            ) : (
              <Stack direction="row">
                <Typography
                  fontWeight={500}
                  fontSize={{ tablet: 18 }}
                  color="hint"
                  whiteSpace={{ xs: 'pre-line', tablet: 'normal' }}
                >
                  {meeting.expectDate.length === 1
                    ? `${dayjs(meeting.expectDate[0]?.start).format(
                        MomentFormat.JP_YEAR_MONTH_DATE_DAY,
                      )}\n${dayjs(meeting.expectDate[0]?.start).format(
                        MomentFormat.JP_HOUR_MINUTE,
                      )}`
                    : t('caseDetail.undecidedDate')}
                </Typography>
                {meeting.expectDate.length !== 1 && (
                  <ClickAwayListener onClickAway={() => setOpenTooltip(false)}>
                    <Tooltip
                      title={<ExpectDate expectDate={meeting.expectDate} />}
                      arrow
                      open={openTooltip}
                      placement="right"
                      slotProps={{
                        arrow: {
                          sx: { color: '#fdf6e2' },
                        },
                        tooltip: {
                          sx: {
                            p: 0,
                            '&.MuiTooltip-tooltip.MuiTooltip-tooltipPlacementRight':
                              {
                                marginLeft: 1,
                              },
                          },
                        },
                      }}
                      PopperProps={{
                        sx: styles.tooltipPopper,
                      }}
                    >
                      <IconButton
                        onClick={() => setOpenTooltip(true)}
                        size="small"
                        sx={styles.infoButton}
                      >
                        <InfoIcon />
                      </IconButton>
                    </Tooltip>
                  </ClickAwayListener>
                )}
              </Stack>
            )}
            <Box>
              <MeetingTag status={meetingStatus} />
            </Box>
          </Box>
          <Box display="flex" alignItems="center">
            <Box
              sx={styles.iconMeetingNote}
              marginRight={{ xs: '4px', tablet: '8px' }}
            >
              <MeetingNoteIcon />
            </Box>
            <Typography sx={styles.textPaymentInfo}>
              {menuTittle && (
                <Tooltip
                  title={`${t('caseDetail.duration', {
                    number: duration,
                  })} - ${menuTittle}`}
                  open={openMenuTooltip}
                  onClick={() => setOpenMenuTooltip(true)}
                  onMouseOver={() => setOpenMenuTooltip(true)}
                  onMouseLeave={() => setOpenMenuTooltip(false)}
                  PopperProps={{
                    sx: styles.tooltipPopper,
                  }}
                >
                  <Typography sx={styles.titleText}>{menuTittle}</Typography>
                </Tooltip>
              )}
              {menuTittle && ' - '}
              {t('caseDetail.duration', {
                number: duration,
              })}
              {meetingType ? (
                <>
                  &nbsp;&nbsp;
                  {'•'}
                  &nbsp;&nbsp;
                  {IMeetingTypeLabel[meetingType]}
                </>
              ) : null}
              {isShowMeetingUrl && (
                <>
                  {' '}
                  &nbsp;&nbsp;
                  {'•'}
                  &nbsp;&nbsp;オンライン面談:&nbsp;&nbsp;
                </>
              )}
            </Typography>
            {isShowMeetingUrl && (
              <MeetingUrl
                meetingUrl={meetingUrl}
                isDisableMeetingUrl={isDisableMeetingUrl}
              />
            )}
          </Box>
          {isShowExtendMenu && (
            <Box
              display="flex"
              alignItems="center"
              sx={{ marginTop: { xs: '8px', tablet: '12px' } }}
            >
              <Box
                sx={styles.iconMeetingNote}
                marginRight={{ xs: '4px', tablet: '8px' }}
              >
                <MenuExtend />
              </Box>
              <Typography sx={styles.textPaymentInfo}>
                延長メニュー &nbsp;&nbsp;
                {'•'}
                &nbsp;&nbsp;
                {t('caseDetail.duration', {
                  number: extendMenuDuration,
                })}
                &nbsp;
                {'-'}
                &nbsp;
                {Helper.addComma(extendMenuPrice || 0)}円
              </Typography>
            </Box>
          )}
          {meeting.cancelReason && (
            <Box
              sx={styles.consumerNoteContainer}
              bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
              border="1px solid #DBE3E7"
            >
              <Typography
                fontSize={14}
                fontWeight="bold"
                mb={1}
                color="#db5a42"
              >
                キャンセル理由
              </Typography>
              <TruncateText
                textProps={{
                  sx: {
                    whiteSpace: 'pre-line',
                    color: 'heading',
                    fontSize: 14,
                  },
                }}
                lines={2}
                text={meeting.cancelReason}
              />
            </Box>
          )}
          {meeting.consumerNote && (
            <Box
              sx={styles.consumerNoteContainer}
              bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
              border="1px solid #DBE3E7"
            >
              <Typography
                color="heading"
                fontSize={14}
                fontWeight="bold"
                mb={1}
              >
                {t('caseDetail.customerNote')}
              </Typography>
              <TruncateText
                textProps={{
                  sx: {
                    whiteSpace: 'pre-line',
                    fontSize: 14,
                    color: 'heading',
                  },
                }}
                lines={2}
                text={meeting.consumerNote}
              />
            </Box>
          )}
          <Divider sx={{ my: { xs: 2, tablet: 3 } }} />
          <Box
            sx={styles.consumerNoteContainer}
            bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
            border="1px solid #DBE3E7"
            mb={2}
          >
            <Stack flexDirection="row" gap={1} justifyContent="space-between">
              <Typography fontSize={14}>合計金額</Typography>
              <Typography fontSize={14} fontWeight={700}>
                {Helper.addComma(basicPrice)}円
              </Typography>
            </Stack>
            {couponInfo && (
              <Stack
                flexDirection="row"
                gap={1}
                justifyContent="space-between"
                mt={1}
                flexWrap="wrap"
              >
                <Stack flexDirection="row">
                  <Typography fontSize={14} mr={1}>
                    クーポン値引
                  </Typography>
                  <div
                    style={{ cursor: 'pointer' }}
                    onClick={() => setOpenCouponDetailModal(true)}
                  >
                    <CouponTag
                      code={couponInfo.coupon.code}
                      wrapperProps={{ mr: '6px', height: 'fit-content' }}
                      circleColor="#F6F8F9"
                    />
                  </div>
                </Stack>
                <Typography fontSize={14}>
                  - {Helper.addComma(couponInfo.couponAmount || 0)}円
                </Typography>
              </Stack>
            )}
            {!!discount && discount > 0 && (
              <Stack
                flexDirection="row"
                gap={1}
                justifyContent="space-between"
                mt={1}
              >
                <Typography fontSize={14}>割引金額</Typography>
                <Typography fontSize={14}>
                  - {Helper.addComma(discount)}円
                </Typography>
              </Stack>
            )}
          </Box>
          <Box>
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Stack direction="row" spacing={1} mb={1} alignItems="center">
                <Stack direction="row" alignItems="center">
                  <Typography
                    fontSize={{ xs: 14, tablet: 16 }}
                    fontWeight={500}
                    color="text.primary"
                    mr="2px"
                  >
                    {t('caseDetail.finalPrice')}
                    <Typography
                      fontSize={{ xs: 14, tablet: 16 }}
                      fontWeight={700}
                      color="heading"
                      component="span"
                    >
                      {Helper.addComma(finalPrice)}円
                    </Typography>
                  </Typography>
                  <PaymentTooltipButton
                    title="お支払い予定金額に関して"
                    content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
                  />
                </Stack>
              </Stack>
              <Box display={{ xs: 'flex', tablet: 'none' }}>
                {isCheckMoreButton && (
                  <Box>
                    <IconButton
                      size="medium"
                      sx={styles.moreIcon}
                      onClick={handleClickMoreButton}
                      id="more-button"
                    >
                      <MoreIcon />
                    </IconButton>
                  </Box>
                )}
              </Box>
            </Box>
            <Stack direction="row" justifyContent="space-between">
              <Stack direction="row" spacing={{ xs: '4px', tablet: 1 }}>
                <Box sx={styles.iconPayment}>
                  {paymentMethod === PaymentMethod.CREDIT_CARD &&
                    cardType &&
                    CardIcon[cardType]}
                  {paymentMethod === PaymentMethod.CASH && <CashIcon />}
                  {paymentMethod === PaymentMethod.BANK_TRANSFER && (
                    <TransferIcon />
                  )}
                </Box>
                <Stack direction={{ tablet: 'row' }} spacing={1}>
                  <Typography sx={styles.cardInfo}>
                    {paymentMethod === PaymentMethod.CREDIT_CARD &&
                      !isBreakpoint &&
                      cardNumber &&
                      Helper.formatCardNumberText(cardNumber)}
                    {paymentMethod === PaymentMethod.CREDIT_CARD &&
                      isBreakpoint &&
                      t('caseDetail.creditCard')}
                    {paymentMethod === PaymentMethod.CASH &&
                      t('caseDetail.cashPayment')}
                    {paymentMethod === PaymentMethod.BANK_TRANSFER &&
                      t('caseDetail.bankTransfer')}
                  </Typography>
                  <Typography
                    sx={styles.cardInfo}
                    display={{ xs: 'none', tablet: 'block' }}
                  >
                    {'•'}
                  </Typography>
                  <Typography
                    className="12-to-10"
                    fontWeight={500}
                    color={PAYMENT_TEXT[displayStatus].color}
                  >
                    {PAYMENT_TEXT[displayStatus].text}
                  </Typography>
                </Stack>
              </Stack>
              <Stack
                direction="row"
                spacing={1}
                alignItems="center"
                mt={{ tablet: '-26px' }}
              >
                {canChangeCreditCard && (
                  <Box>
                    <Button
                      variant="whiteOutlined"
                      className="tabletStyle"
                      startIcon={
                        <SvgIcon
                          component={MyPagePaymentIcon}
                          className="tabletStyle"
                        />
                      }
                      onClick={() => setOpenChangeCardModal(true)}
                    >
                      <Typography fontSize={14} fontWeight="bold">
                        {t('caseDetail.changeCreditCard')}
                      </Typography>
                    </Button>
                    <ChangeCreditCardModal
                      usingCard={meeting.paymentInfo.card?.id}
                      meetingId={meeting._id}
                      caseId={caseId}
                      open={openChangeCardModal}
                      use3Ds={finalPrice > 0}
                      onClose={() => setOpenChangeCardModal(false)}
                      onSuccess={({ isDirty, changeCardResponse }) => {
                        if (isDirty && changeCardResponse) {
                          const { redirectUrl } = changeCardResponse;

                          if (redirectUrl) {
                            window.location.href = redirectUrl;
                          }
                        }
                        refetchMeetingList();
                        setOpenChangeCardModal(false);
                      }}
                    />
                  </Box>
                )}
                <Box display={{ xs: 'none', tablet: 'flex' }}>
                  {isCheckMoreButton && (
                    <Box>
                      <IconButton
                        size="medium"
                        sx={styles.moreIcon}
                        onClick={handleClickMoreButton}
                        id="more-button"
                      >
                        <MoreIcon />
                      </IconButton>
                    </Box>
                  )}
                </Box>
              </Stack>
            </Stack>
          </Box>
        </Box>
        <DropdownMenu
          menus={dropdownMenus.filter((item) => item.key !== 'edit')}
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
          onMenuItemClick={handleMeetingAction}
        />
        <LoadingOverlay visible={isCanceling} />
      </Box>
      <ConfirmModal
        open={openCancelModal}
        title="面談キャンセル"
        confirmText="確定"
        cancelText="キャンセル"
        onCancel={() => setOpenCancelModal(false)}
        confirmButtonProps={{ form: 'cancel-meeting', type: 'submit' }}
        dialogProps={{ sx: { zIndex: 150 } }}
        content={
          <Box>
            <Typography
              fontSize={14}
              textAlign="center"
              display={{ xs: 'block', tablet: 'flex' }}
              justifyContent="center"
              sx={{ whiteSpace: { xs: 'nowrap', tablet: 'normal' } }}
            >
              <Typography
                fontSize={14}
                fontWeight="bold"
                color="text.primary"
                textAlign="center"
              >
                {finalizedDate
                  ? `${dayjs(finalizedDate).format(
                      MomentFormat.JP_YEAR_MONTH_DATE_TIME,
                    )}`
                  : `${dayjs(expectDate[0]?.start).format(
                      MomentFormat.JP_YEAR_MONTH_DATE_TIME,
                    )}`}
              </Typography>
              &nbsp;&nbsp;{'\nの面談をキャンセルします'}
            </Typography>
            <Typography
              fontSize={{ xs: 12, tablet: 14 }}
              mt={{ xs: '12px', tablet: 3 }}
              textAlign="center"
            >
              面談前日の18時以降のキャンセルは、{'\n'}
              キャンセル料が発生する場合があります
            </Typography>
            <Box mt={{ xs: 2, tablet: '4px' }}>
              <form
                id="cancel-meeting"
                onSubmit={handleSubmit(handleCancelMeeting)}
              >
                <TextField
                  labelCol={0}
                  label="キャンセル理由"
                  name="cancelReason"
                  control={control}
                  maxLength={200}
                  multiline
                  minRows={7}
                  placeholder="キャンセル理由"
                />
              </form>
            </Box>
          </Box>
        }
      />
      <CouponDetailModal
        couponData={couponInfo?.coupon as ICoupon}
        open={openCouponDetailModal}
        onClose={() => setOpenCouponDetailModal(false)}
      />
    </Box>
  );
};

export default CustomerMeetingItem;

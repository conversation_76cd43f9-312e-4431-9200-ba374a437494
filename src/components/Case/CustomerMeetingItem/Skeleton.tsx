import { Box, Divider, Skeleton } from '@mui/material';
import { times } from 'lodash';

import styles from './styles';

const SkeletonList = () => {
  return (
    <Box mt={{ xs: 2, tablet: 3 }}>
      {times(2).map((number) => {
        return (
          <Box mt={2} key={number}>
            <Box sx={styles.meetingItem}>
              <Box sx={styles.caseInfo}>
                <Skeleton
                  variant="text"
                  sx={{ fontSize: { tablet: 18 } }}
                  width="200px"
                />
                <Skeleton
                  variant="rounded"
                  width="88px"
                  sx={{ height: { xs: 20, tablet: 24 } }}
                />
              </Box>
              <Skeleton
                variant="text"
                sx={{ fontSize: { xs: 12, tablet: 14 } }}
                width="300px"
              />
              <Box mt={2}>
                <Skeleton variant="rounded" height="128px" />
              </Box>
              <Divider sx={{ my: { xs: 2, tablet: 3 } }} />
              <Box sx={styles.caseInfo}>
                <Skeleton
                  variant="text"
                  sx={{ fontSize: { xs: 14, tablet: 16 } }}
                  width="200px"
                />
                <Skeleton
                  variant="rounded"
                  width="100px"
                  sx={{ height: { xs: '32px', tablet: '40px' } }}
                />
              </Box>
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};

export default SkeletonList;

import { LoadingButton } from '@mui/lab';
import { Box, Grid, Stack, Typography } from '@mui/material';
import BookingConsultationMenuList from 'components/ConsultationMenu/BookingConsultationMenu';
import { DatePicker, Radio, Select, TextField } from 'components/Form';
import RangeTimePicker from 'components/Form/RangeTimePicker';
import TermPolicy from 'components/UI/TermPolicy';
import dayjs from 'dayjs';
import { t } from 'i18n';
import type { IConsultationItem } from 'models/consultation/interface';
import type { CreateMeetingValues } from 'pages/customer/booking/[lawyerId]/schema';
import { useState } from 'react';
import type { Control, UseFormTrigger } from 'react-hook-form';
import { useWatch } from 'react-hook-form';
import { MEETING_DURATION, MEETING_TYPE_TEXT } from 'utils/constants';
import Helper from 'utils/helpers';

export interface BookingFormProps {
  loading?: boolean;
  control: Control<CreateMeetingValues>;
  menuList: IConsultationItem[];
  hasOnlineSupport: boolean | undefined;
  trigger?: UseFormTrigger<CreateMeetingValues>;
  getDisableSubmitBtnState?: ({
    agree,
  }: {
    agree: boolean;
  }) => boolean | undefined;
}
const BookingForm = ({
  loading,
  control,
  menuList,
  hasOnlineSupport,
  trigger,
  getDisableSubmitBtnState,
}: BookingFormProps) => {
  const [agree, setAgree] = useState(false);
  const watchFirstChoice = useWatch({ control, name: 'firstChoice' });
  const watchSecondChoice = useWatch({ control, name: 'secondChoice' });
  const watchThirdChoice = useWatch({ control, name: 'thirdChoice' });
  const watchFirstChoiceTime = useWatch({ control, name: 'firstChoiceTime' });
  const watchTimeSecondChoice = useWatch({ control, name: 'secondChoiceTime' });
  const watchTimeThirdChoice = useWatch({ control, name: 'thirdChoiceTime' });

  return (
    <Box
      bgcolor="white"
      p={{ xs: 2, tablet: 4 }}
      borderRadius={{ xs: 0, tablet: 2 }}
      mt={{ xs: 1, tablet: 2 }}
    >
      <Stack spacing={{ xs: '20px', tablet: 2 }}>
        <Radio
          name="type"
          data={
            hasOnlineSupport
              ? Helper.convertObjectToOptions(MEETING_TYPE_TEXT)
              : [{ _id: 'IN_PERSON', value: MEETING_TYPE_TEXT.IN_PERSON }]
          }
          labelCol={3}
          columns={13}
          label="面談方法"
          control={control}
          required
        />
        <Select
          labelCol={3}
          columns={13}
          name="duration"
          label="面談時間"
          placeholder="面談時間"
          data={Helper.convertObjectToOptions(MEETING_DURATION)}
          control={control}
          required
        />
        {menuList.length > 0 && (
          <Box>
            <Grid container columns={13} columnSpacing={{ tablet: 4 }}>
              <Grid item xs={0} tablet={3}></Grid>
              <Grid item xs tablet={10}>
                <BookingConsultationMenuList data={menuList} />
              </Grid>
            </Grid>
          </Box>
        )}

        <Box>
          <Stack spacing={2}>
            <Box display="flex" alignItems="center" gap={1}>
              <Box
                component="img"
                src="/icons/capa-1.svg"
                alt="calendar"
                sx={{
                  width: { xs: 20, tablet: 24 },
                  height: { xs: 20, tablet: 24 },
                }}
              />
              <Typography
                fontWeight={700}
                color="heading"
                fontSize={{ xs: 16, tablet: 20 }}
              >
                面談希望日時
              </Typography>
            </Box>
            <Typography
              fontSize={12}
              fontWeight={400}
              sx={{
                whiteSpace: 'pre-line',
                mb: 2,
                letterSpacing: '-0.6px',
                lineHeight: '20px',
              }}
            >
              ご登録いただいた日時の中から面談を設定させていただくため、できるだけ複数の日と幅広い時間帯でご登録をお願いいたします。
              {'\n'}
              ご登録いただいた日時での面談の設定が難しい場合は、別途調整させていただくことがございます。
              {'\n'}
              予めご了承ください。
            </Typography>
          </Stack>
        </Box>

        <Stack spacing={1}>
          <DatePicker
            label={t('booking.firstChoice')}
            placeholder="yyyy年mm月dd日 (w)"
            name="firstChoice"
            control={control}
            minDate={dayjs()}
            required
            labelCol={3}
            columns={13}
            onValueChange={() => {
              if (watchFirstChoiceTime && trigger) {
                trigger('firstChoiceTime');
              }
            }}
          />
          <RangeTimePicker
            control={control}
            required
            labelCol={3}
            columns={13}
            format="HH時mm分"
            name="firstChoiceTime"
            minutesStep={15}
            minHour={9}
            maxHour={21}
            disabled={!watchFirstChoice}
            placeholder={'hh時mm分'}
          />
        </Stack>
        <Stack spacing={1}>
          <DatePicker
            label={t('booking.secondChoice')}
            placeholder="yyyy年mm月dd日 (w)"
            name="secondChoice"
            control={control}
            minDate={dayjs()}
            required
            labelCol={3}
            columns={13}
            onValueChange={() => {
              if (watchTimeSecondChoice && trigger) {
                trigger('secondChoiceTime');
              }
            }}
          />
          <RangeTimePicker
            control={control}
            required
            labelCol={3}
            columns={13}
            format="HH時mm分"
            name="secondChoiceTime"
            minutesStep={15}
            minHour={9}
            maxHour={21}
            placeholder={'hh時mm分'}
            disabled={!watchSecondChoice}
          />
        </Stack>
        <Stack spacing={1}>
          <DatePicker
            label={t('booking.thirdChoice')}
            placeholder="yyyy年mm月dd日 (w)"
            name="thirdChoice"
            control={control}
            minDate={dayjs()}
            required
            labelCol={3}
            columns={13}
            onValueChange={() => {
              if (watchTimeThirdChoice && trigger) {
                trigger('thirdChoiceTime');
              }
            }}
          />
          <RangeTimePicker
            control={control}
            required
            labelCol={3}
            columns={13}
            format="HH時mm分"
            name="thirdChoiceTime"
            minutesStep={15}
            minHour={9}
            maxHour={21}
            placeholder={'hh時mm分'}
            disabled={!watchThirdChoice}
          />
        </Stack>
        <TextField
          label={t('booking.consumerNote')}
          placeholder="お気軽にご記入ください"
          name="consumerNote"
          control={control}
          maxLength={500}
          multiline
          minRows={5}
          labelCol={3}
          columns={13}
        />
      </Stack>
      <Box display={{ xs: 'none', sl: 'block' }}>
        <Box
          mt="20px"
          display="flex"
          justifyContent={{ xs: ' flex-start', tablet: 'center' }}
        >
          <TermPolicy checked={agree} onChange={(data) => setAgree(data)} />
        </Box>
        <Box
          display="flex"
          justifyContent="center"
          mt={{ xs: '18px', tablet: '20px' }}
        >
          <LoadingButton
            color="secondary"
            type="submit"
            variant="contained"
            size="large"
            className="tabletStyle shadow"
            fullWidth
            loading={loading}
            disabled={
              getDisableSubmitBtnState
                ? getDisableSubmitBtnState({ agree })
                : !agree
            }
            sx={{
              maxWidth: 368,
            }}
          >
            内容を確認する
          </LoadingButton>
        </Box>
      </Box>
    </Box>
  );
};

export default BookingForm;

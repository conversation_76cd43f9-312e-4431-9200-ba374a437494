import { Typography } from '@mui/material';
import dayjs from 'dayjs';
import { t } from 'i18n';
import { MomentFormat } from 'utils/constants';

const PaymentErrorContent = ({
  finalizedDate,
  cardError,
}: {
  finalizedDate?: string;
  cardError?: boolean;
}) => {
  return (
    <Typography component="span" fontSize={14} color="text.primary">
      <Typography
        fontSize={14}
        color="error.main"
        fontWeight="bold"
        component="span"
      >
        {t('caseDetail.paymentError')}
      </Typography>
      <Typography
        fontSize={14}
        fontWeight="bold"
        color="text.primary"
        component="span"
      >
        {` : ${dayjs(finalizedDate).format(MomentFormat.JP_YEAR_MONTH_DATE)}`}
      </Typography>
      {t('rechargePayment.titlePaymentError1')}
      {cardError
        ? `\n${t('rechargePayment.titlePaymentError2')}`
        : `\n${t('rechargePayment.titlePaymentError3')}`}
    </Typography>
  );
};

export default PaymentErrorContent;

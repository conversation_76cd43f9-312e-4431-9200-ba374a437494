import {
  Box,
  ClickAwayListener,
  IconButton,
  SvgIcon,
  Tooltip,
  Typography,
} from '@mui/material';
import { InfoIcon } from 'icons';
import { useState } from 'react';

const PaymentTooltipButton = ({
  title = 'クーポンに関して',
  content = 'クーポンの割引金額は、リコ活が全額負担しています。これによりあなたの売り上げが減ることはありません。',
  zIndex = 1503,
}: {
  title?: string;
  content?: string;
  zIndex?: number;
}) => {
  const [openTooltip, setOpenTooltip] = useState(false);
  return (
    <ClickAwayListener onClickAway={() => setOpenTooltip(false)}>
      <Tooltip
        title={
          <Box p="6px 8px" borderRadius="2px">
            <Typography fontWeight={700} fontSize={14} color="heading">
              {title}
            </Typography>
            <Typography
              fontSize={14}
              color="text.primary"
              whiteSpace="pre-line"
            >
              {content}
            </Typography>
          </Box>
        }
        arrow
        open={openTooltip}
        PopperProps={{ sx: { zIndex } }}
        slotProps={{
          arrow: {
            sx: { color: '#fdf6e2' },
          },
          tooltip: {
            sx: {
              bgcolor: '#FFF5CC',
              maxWidth: { tablet: 374 },
              p: 0,
              boxShadow: '0px 2px 8px 0px rgba(0, 0, 0, 0.15)',
              borderRadius: '3px',
              '&.MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom': {
                marginTop: 1,
              },
            },
          },
        }}
      >
        <IconButton
          onMouseOver={() => setOpenTooltip(true)}
          onMouseLeave={() => setOpenTooltip(false)}
          onClick={() => setOpenTooltip(true)}
          sx={{
            zIndex: 1,
            p: '2px',
            svg: { color: 'icon' },
          }}
        >
          <SvgIcon
            sx={{
              width: 16,
              height: 16,
            }}
            viewBox="0 0 16 16"
            inheritViewBox
            component={InfoIcon}
          />
        </IconButton>
      </Tooltip>
    </ClickAwayListener>
  );
};

export default PaymentTooltipButton;

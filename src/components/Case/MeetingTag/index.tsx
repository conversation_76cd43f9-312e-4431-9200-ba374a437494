import type { ChipProps } from '@mui/material';
import {
  Button,
  ButtonBase,
  Chip,
  Popover,
  SvgIcon,
  Typography,
} from '@mui/material';
import useGlobalState from 'hooks/useGlobalState';
import { ArrowDownIcon } from 'icons';
import React from 'react';
import { CASE_MEETING_TAG, MeetingStatusType } from 'utils/constants';

const dropDownIcon = (
  <SvgIcon fontSize="medium">
    <ArrowDownIcon />
  </SvgIcon>
);
export interface IMeetingTag extends ChipProps {
  status: MeetingStatusType;
  handleUpdateMeeting?: () => void;
  isLawyer?: boolean;
  isLoadingUpdate?: boolean;
}

const MeetingTag = ({
  status,
  isLawyer,
  isLoadingUpdate,
  handleUpdateMeeting,
}: IMeetingTag) => {
  const { setConfirmModal } = useGlobalState();
  const isWaitingAnswerFromProvider =
    status === MeetingStatusType.WAITING_ANWSER_FROM_PROVIDER;
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null,
  );
  const handleUpdateMeetingStatus = () => {
    setConfirmModal({
      title: 'ステータスを変更',
      confirmText: 'はい',
      cancelText: 'いいえ',
      confirmLoading: isLoadingUpdate,
      onConfirm: () => handleUpdateMeeting?.(),
      content: (
        <Typography fontSize={14}>
          ステータスを「折り返し待ち」に変更しますか？
        </Typography>
      ),
    });
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (isWaitingAnswerFromProvider) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClosePopover = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'lawyer-popover' : undefined;
  const chipSx = {
    bgcolor: CASE_MEETING_TAG[status].color,
    color: CASE_MEETING_TAG[status].textColor,
    fontSize: { xs: '10px', tablet: '12px' },
    cursor: isWaitingAnswerFromProvider && isLawyer ? 'pointer' : 'default',
  };

  if (isLawyer) {
    return (
      <>
        <ButtonBase onClick={handleClick}>
          <Chip
            label={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {CASE_MEETING_TAG[status].label}
                {'  '}
                {isWaitingAnswerFromProvider && dropDownIcon}
              </div>
            }
            className="tabletStyle"
            sx={chipSx}
          />
        </ButtonBase>
        <Popover
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={handleClosePopover}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <Button
            onClick={() => {
              handleUpdateMeetingStatus();
              setAnchorEl(null);
            }}
            style={{ padding: '16px' }}
          >
            <Chip
              label={CASE_MEETING_TAG.WAITING_FOR_REPLY_FROM_CUSTOMER.label}
              className="tabletStyle"
              sx={{
                bgcolor: CASE_MEETING_TAG.WAITING_FOR_REPLY_FROM_CUSTOMER.color,
                color:
                  CASE_MEETING_TAG.WAITING_FOR_REPLY_FROM_CUSTOMER.textColor,
                fontSize: { xs: '10px', tablet: '12px' },
                cursor: 'pointer',
              }}
            />
          </Button>
        </Popover>
      </>
    );
  }

  return (
    <Chip
      label={CASE_MEETING_TAG[status].label}
      className="tabletStyle"
      sx={chipSx}
    />
  );
};

export default MeetingTag;

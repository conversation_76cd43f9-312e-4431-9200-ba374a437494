import { Box, Stack, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { t } from 'i18n';
import { Time1Icon, Time2Icon, Time3Icon } from 'icons';
import type { IMeetingsItem } from 'models/case/interface';
import { MomentFormat } from 'utils/constants';

import styles from './styles';

export interface IExpectDate {
  expectDate: IMeetingsItem['expectDate'];
}

const ExpectDate = ({ expectDate }: IExpectDate) => {
  return (
    <Box p={{ xs: '12px', tablet: 2 }}>
      <Typography fontSize={{ xs: 12, tablet: 14 }} color="heading">
        {t('caseDetail.titleExpectDate')}
      </Typography>
      <Box mt={{ xs: 1, tablet: '14px' }}>
        <Stack direction="row" spacing={{ xs: '4px', tablet: 1 }}>
          <Box sx={styles.icon}>
            <Time1Icon />
          </Box>
          <Box>
            <Typography sx={styles.textExpectDate}>
              {dayjs(expectDate[0]?.start).format('YYYY年MM月DD日 (ddd)')}
            </Typography>
            <Typography sx={styles.textExpectDate} mt={1}>
              {dayjs(expectDate[0]?.start).format(MomentFormat.JP_HOUR_MINUTE)}{' '}
              ~ {dayjs(expectDate[0]?.end).format(MomentFormat.JP_HOUR_MINUTE)}
            </Typography>
          </Box>
        </Stack>
        <Stack mt={1} direction="row" spacing={{ xs: '4px', tablet: 1 }}>
          <Box sx={styles.icon}>
            <Time2Icon />
          </Box>
          <Box>
            <Typography sx={styles.textExpectDate}>
              {dayjs(expectDate[1]?.start).format('YYYY年MM月DD日 (ddd)')}
            </Typography>
            <Typography sx={styles.textExpectDate} mt={1}>
              {dayjs(expectDate[1]?.start).format(MomentFormat.JP_HOUR_MINUTE)}{' '}
              ~ {dayjs(expectDate[1]?.end).format(MomentFormat.JP_HOUR_MINUTE)}
            </Typography>
          </Box>
        </Stack>
        <Stack mt={1} direction="row" spacing={{ xs: '4px', tablet: 1 }}>
          <Box sx={styles.icon}>
            <Time3Icon />
          </Box>
          <Box>
            <Typography sx={styles.textExpectDate}>
              {dayjs(expectDate[2]?.start).format('YYYY年MM月DD日 (ddd)')}
            </Typography>
            <Typography sx={styles.textExpectDate} mt={1}>
              {dayjs(expectDate[2]?.start).format(MomentFormat.JP_HOUR_MINUTE)}{' '}
              ~ {dayjs(expectDate[2]?.end).format(MomentFormat.JP_HOUR_MINUTE)}
            </Typography>
          </Box>
        </Stack>
      </Box>
    </Box>
  );
};

export default ExpectDate;

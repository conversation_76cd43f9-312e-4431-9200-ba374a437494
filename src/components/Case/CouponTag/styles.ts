import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  couponWrapper: {
    position: 'relative',
    '&:before': {
      position: 'absolute',
      content: "''",
      width: '12px',
      height: '12px',
      top: 'calc(50% - 6px)',
      left: '-8px',
      background: 'linear-gradient(to right, transparent 8px, #FFCD00 4px)',
      borderRadius: '50%',
      '@media (max-width: 768px)': {
        width: '10px',
        height: '10px',
        top: 'calc(50% - 5px)',
        left: '-6px',
        background: 'linear-gradient(to right, transparent 6px, #FFCD00 4px)',
      },
    },
    '&:after': {
      position: 'absolute',
      content: "''",
      width: '12px',
      height: '12px',
      top: 'calc(50% - 6px)',
      right: '-8px',
      background: 'linear-gradient(to right, #FFCD00 4px, transparent 0px)',
      borderRadius: '50%',
      '@media (max-width: 768px)': {
        width: '10px',
        height: '10px',
        top: 'calc(50% - 5px)',
        right: '-6px',
        background: 'linear-gradient(to right, #FFCD00 4px, transparent 0px)',
      },
    },
  },
  couponCircle: {
    '&:before': {
      position: 'absolute',
      content: "''",
      width: '10px',
      height: '10px',
      top: 'calc(50% - 5px)',
      left: '-7px',
      background:
        'linear-gradient(to right, transparent 6px, currentColor 4px)',
      borderRadius: '50%',
      zIndex: 1,
      '@media (max-width: 768px)': {
        width: '8px',
        height: '8px',
        top: 'calc(50% - 4px)',
        left: '-5px',
        background:
          'linear-gradient(to right, transparent 4px, currentColor 4px)',
      },
    },
    '&:after': {
      position: 'absolute',
      content: "''",
      width: '10px',
      height: '10px',
      top: 'calc(50% - 5px)',
      right: '-7px',
      backgroundColor: 'currentColor',
      borderRadius: '50%',
      zIndex: 1,
      '@media (max-width: 768px)': {
        width: '8px',
        height: '8px',
        right: '-5px',
        top: 'calc(50% - 4px)',
      },
    },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

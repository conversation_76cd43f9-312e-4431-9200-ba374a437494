import type { BoxProps } from '@mui/material';
import { Box, IconButton, Typography } from '@mui/material';
import { CouponRemoveIcon } from 'icons';

import styles from './styles';

const CouponTag = ({
  code,
  onDelete,
  circleColor = '#fff',
  wrapperProps,
}: {
  code: string;
  onDelete?: () => void;
  circleColor?: string;
  wrapperProps?: BoxProps;
}) => {
  return (
    <Box
      p={{
        xs: !onDelete ? '0px 7px' : '0px 5px 0px 7px',
        tablet: !onDelete ? '1px 7px' : '0px 4px 0px 7px',
      }}
      border="1px solid #FFCD00"
      borderRadius="4px"
      bgcolor="#FFF5CC"
      sx={styles.couponWrapper}
      color={circleColor}
      {...wrapperProps}
    >
      <Box sx={styles.couponCircle} />
      <Box display="flex" alignItems="center">
        <Typography
          fontSize={{ xs: 12, tablet: 14 }}
          fontWeight={700}
          color="heading"
          lineHeight={{ xs: '16px', tablet: '20px' }}
        >
          {code}
        </Typography>
        {onDelete && (
          <IconButton
            onClick={onDelete}
            sx={{
              p: { xs: '2px', tablet: '3px' },
              svg: {
                width: { xs: 14, tablet: 16 },
                height: { xs: 14, tablet: 16 },
              },
            }}
          >
            <CouponRemoveIcon />
          </IconButton>
        )}
      </Box>
    </Box>
  );
};

export default CouponTag;

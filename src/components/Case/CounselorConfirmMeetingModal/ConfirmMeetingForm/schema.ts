import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { t } from 'i18n';
import { isNaN } from 'lodash';
import { Regex } from 'utils/constants';
import type { InferType } from 'yup';
import { number, object, string } from 'yup';

dayjs.extend(isSameOrAfter);

const schema = object({
  discount: number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .min(0, t('validation.invalidField')),
  meetingUrl: string()
    .required()
    .test('validUrl', t('validation.invalidUrl'), (value) => {
      if (value) {
        return Regex.URL.test(value);
      }
      return true;
    }),
});

export type CounselorConfirmMeetingValues = InferType<typeof schema>;

export default schema;

import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  typeMeeting: {
    ml: 4,
    p: 3,
    border: (theme) => `solid 1px ${theme.palette.neutral4}`,
    borderRadius: '12px',
  },
  textPaymentInfo: {
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
  },
  titleText: {
    fontSize: 14,
    color: 'text.primary',
    maxWidth: 250,
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    wordBreak: 'break-word',
    verticalAlign: 'bottom',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

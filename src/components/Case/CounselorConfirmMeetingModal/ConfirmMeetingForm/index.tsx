import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Divider, Grid, Stack, Typography } from '@mui/material';
import CouponTag from 'components/Case/CouponTag';
import PaymentTooltipButton from 'components/Case/PaymentTooltipButton';
import { TextField } from 'components/Form';
import Label from 'components/Form/Label';
import NumberField from 'components/Form/NumberField';
import dayjs from 'dayjs';
import { useDeepCompareEffect } from 'hooks';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import { PaymentCardIcon } from 'icons';
import type { IMeetingsItem } from 'models/case/interface';
import type { IMeetingType } from 'models/consultation/interface';
import { IMeetingTypeLabel } from 'models/consultation/interface';
import { useMemo } from 'react';
import { useWatch } from 'react-hook-form';
import { MomentFormat, PAYMENT_METHOD } from 'utils/constants';
import Helper from 'utils/helpers';

import type { CounselorConfirmMeetingValues } from './schema';
import schema from './schema';
import styles from './styles';

export interface ConfirmMeetingFormProps {
  data: IMeetingsItem;
  onSubmit: (values: CounselorConfirmMeetingValues) => void;
}
const ConfirmMeetingForm = ({ data, onSubmit }: ConfirmMeetingFormProps) => {
  const defaultValues = useMemo(
    () => ({
      meetingUrl: data.meetingUrl,
      discount: data.paymentInfo?.discount,
    }),
    [data],
  );

  const duration = data.consultationMenu?.unitPrices[0]?.duration;
  const menuTitle = data.consultationMenu?.title;
  const meetingType = data.consultationMenu?.meetingType;
  const { couponInfo } = data.paymentInfo;
  const couponAmount = couponInfo?.couponAmount || 0;

  const { control, handleSubmit, reset } =
    useHookForm<CounselorConfirmMeetingValues>({
      mode: 'all',
      resolver: yupResolver(schema),
      defaultValues,
    });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const watchDiscount = useWatch({ name: 'discount', control }) || 0;
  const basicPrice = data.consultationMenu?.unitPrices[0]?.price || 0;

  return (
    <form onSubmit={handleSubmit(onSubmit)} id="counselor-confirm-meeting-form">
      <Stack spacing={2}>
        <Grid container columns={17}>
          <Label
            labelCol={5}
            label={t('confirmMeeting.meetingDate')}
            required
          />
          <Grid item xs={12} columnSpacing={4}>
            <Box ml={4} mt={1}>
              <Typography>
                {dayjs(data.expectDate[0]?.start).format(
                  MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                )}
              </Typography>
            </Box>
          </Grid>
        </Grid>
        <Grid container columns={17}>
          <Label labelCol={5} label="メニュー" required />
          <Grid item xs={12} columnSpacing={4}>
            <Box ml={4} mt={1}>
              <Typography>
                {t('caseDetail.duration', {
                  number: duration,
                })}
                &nbsp;
                {'-'}
                &nbsp;
                {menuTitle && (
                  <Typography
                    component="span"
                    sx={{
                      fontSize: 14,
                      color: 'text.primary',
                      wordBreak: 'break-word',
                    }}
                  >
                    {menuTitle}
                  </Typography>
                )}
                &nbsp;
                {'-'}
                &nbsp;
                {meetingType
                  ? IMeetingTypeLabel[meetingType as IMeetingType]
                  : '_'}
              </Typography>
            </Box>
          </Grid>
        </Grid>
        <Grid container columns={17}>
          <Label
            labelCol={5}
            label={t('confirmMeeting.meetingType')}
            paddingTop={3}
            required
          />
          <Grid item xs={12} columnSpacing={4}>
            <Box sx={styles.typeMeeting}>
              <Typography mb={1}>オンライン面談 </Typography>
              <TextField
                labelCol={12}
                name="meetingUrl"
                control={control}
                placeholder={t('confirmMeeting.placeholderMeetingUrl')}
              />
            </Box>
          </Grid>
        </Grid>
      </Stack>
      <Divider sx={{ mt: 3, mb: 3 }} />
      <Box display="grid">
        <Grid container columns={17}>
          <Label labelCol={5} label={t('confirmMeeting.paymentMethod')} />
          <Grid item xs={12} columnSpacing={4}>
            <Box ml={4} mt="12px">
              <Box display="flex">
                <Box
                  mr={1}
                  alignSelf="center"
                  sx={{
                    width: 'fit-content',
                    svg: {
                      display: 'block',
                      maxHeight: { xs: '20px', tablet: '24px' },
                      maxWidth: { xs: '30px', tablet: '36px' },
                    },
                  }}
                >
                  <PaymentCardIcon />
                </Box>
                <Typography>{PAYMENT_METHOD.CREDIT_CARD}</Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-end"
          mt={2}
        >
          <Typography fontWeight="bold" color="text.primary">
            {t('confirmMeeting.basicPrice')}
          </Typography>
          <Stack direction="row" spacing="2px" alignItems="flex-end">
            <Typography fontWeight="bold" color="text.primary">
              {Helper.addComma(basicPrice)}
            </Typography>
            <Typography fontWeight="bold" color="text.primary">
              円
            </Typography>
          </Stack>
        </Stack>
        <Typography fontSize={14} color="hint" mt={1} mb={2}>
          {t('confirmMeeting.description')}
        </Typography>
        <Stack
          direction="row"
          justifyContent="space-between"
          mb={2}
          flexWrap="wrap"
        >
          <Stack direction="row" alignItems="center">
            <Typography fontWeight={700}>クーポン値引</Typography>
            {couponInfo && (
              <CouponTag
                wrapperProps={{ ml: 1, mr: '6px' }}
                code={couponInfo?.coupon.code}
              />
            )}
            {couponInfo && <PaymentTooltipButton />}
          </Stack>
          {couponInfo ? (
            <Typography fontWeight={700}>
              - {Helper.addComma(couponInfo.couponAmount || 0)}円
            </Typography>
          ) : (
            <Typography color="#C1BCB3">なし</Typography>
          )}
        </Stack>
        <NumberField
          label={t('confirmMeeting.discount')}
          placeholder="割引金額を入力"
          name="discount"
          control={control}
          labelCol={4}
          columns={12}
          adornment={
            <Typography ml={1} color="text.primary">
              円
            </Typography>
          }
          min={0}
          max={Math.max(basicPrice - couponAmount, 0)}
        />
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          mt={2}
        >
          <Stack direction="row" gap="6px" alignItems="center">
            <Typography fontWeight="bold" color="heading">
              {t('confirmMeeting.finalPrice')}
            </Typography>
            <PaymentTooltipButton
              title="お支払い予定金額に関して"
              content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
            />
          </Stack>
          <Stack direction="row" spacing="2px" alignItems="flex-end">
            <Typography fontWeight="bold" color="neutral7" fontSize={24}>
              {watchDiscount <= basicPrice - couponAmount
                ? Helper.addComma(basicPrice - watchDiscount - couponAmount)
                : 0}
            </Typography>
            <Typography fontWeight="bold" color="neutral7" lineHeight="31px">
              円
            </Typography>
          </Stack>
        </Stack>
      </Box>
    </form>
  );
};

export default ConfirmMeetingForm;

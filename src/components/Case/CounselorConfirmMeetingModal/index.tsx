import { LoadingButton } from '@mui/lab';
import { Box, Button, DialogActions, Stack, Typography } from '@mui/material';
import type { DialogProps } from '@mui/material/Dialog';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useQueryClient } from '@tanstack/react-query';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import get from 'lodash/get';
import type {
  ICounselorConfirmMeeting,
  IMeetingsItem,
} from 'models/case/interface';
import caseQuery from 'models/case/query';
import type { Dispatch, SetStateAction } from 'react';

import styles from '../CounselorMeetingItem/styles';
import ConfirmMeetingForm from './ConfirmMeetingForm';
import type { CounselorConfirmMeetingValues } from './ConfirmMeetingForm/schema';

export interface IConfirmMeetingModal {
  open: boolean;
  setOpenModalConfirmMeeting: Dispatch<SetStateAction<boolean>>;
  onClose: () => void;
  data: IMeetingsItem;
  onSuccess: () => void;
  dialogProps?: DialogProps;
}

const ConfirmMeetingModal = ({
  open,
  setOpenModalConfirmMeeting,
  onClose,
  data,
  onSuccess,
  dialogProps,
}: IConfirmMeetingModal) => {
  const queryClient = useQueryClient();
  const meetingId = data._id;
  const { mutateAsync: confirmMeeting, isLoading } =
    useMutate<ICounselorConfirmMeeting>({
      ...caseQuery.counselorConfirmMeeting(meetingId),
      meta: {
        NOT_TOAST_CODES: ['MEETNG_HAS_SLOT_IS_CLOSED'],
      },
    });

  const { duration } = data.paymentInfo;
  const { setConfirmModal } = useGlobalState();
  const handleSubmitForm = (values: CounselorConfirmMeetingValues) => {
    const { meetingUrl, discount } = values;
    const payload = {
      finalizedDate: data.expectDate[0].start,
      meetingUrl,
      startSlot: data.expectDate[0].start,
      paymentInfo: {
        discount: discount || 0,
        duration,
        basicPrice: data.consultationMenu?.unitPrices[0]?.price as number,
      },
      consultationMenu: {
        ...data?.consultationMenu,
        title: data.consultationMenu?.title,
        unitPrices: [
          {
            price: data.consultationMenu?.unitPrices[0]?.price as number,
            duration,
          },
        ],
      },
    };

    confirmMeeting(payload, {
      onSuccess: () => {
        setOpenModalConfirmMeeting(false);
        queryClient.refetchQueries({
          queryKey: ['currentUser', 'calendars'],
          type: 'active',
        });
        onSuccess();
      },
      onError: (e) => {
        if (get(e, 'code') === 'MEETNG_HAS_SLOT_IS_CLOSED') {
          setConfirmModal({
            icon: 'warning',
            content:
              'すでに実施予定の面談があるため、この面談は確定できません。\nこの面談を確定する場合は、他の面談をキャンセルし再度お試しください。',
            confirmText: 'OK',
            hideCancelButton: true,
            dialogProps: { sx: { zIndex: 1502 } },
          });
        }
      },
    });
  };

  return (
    <Dialog
      open={open}
      aria-labelledby="counselor-confirm-meeting-modal-title"
      aria-describedby="counselor-confirm-meeting-modal-description"
      maxWidth="mw"
      fullWidth
      sx={{ zIndex: 1501 }}
      {...dialogProps}
    >
      <DialogTitle
        id="counselor-confirm-meeting-modal-title"
        sx={styles.dialogTitle}
      >
        <Typography
          fontSize={24}
          fontWeight="bold"
          color="heading"
          textAlign="center"
        >
          {t('confirmMeeting.titleModal')}
        </Typography>
        <Typography
          color="text.primary"
          fontSize={14}
          textAlign="center"
          mt={3}
        >
          {t('confirmMeeting.descriptionOfModal1')}
        </Typography>
        <Stack direction="row" justifyContent="center">
          <Typography fontSize={14} color="primary.main">
            「確定」
          </Typography>
          <Typography color="text.primary" fontSize={14}>
            {t('confirmMeeting.descriptionOfModal2')}
          </Typography>
        </Stack>
      </DialogTitle>
      <DialogContent sx={styles.dialogContent}>
        <Box display="grid">
          <ConfirmMeetingForm data={data} onSubmit={handleSubmitForm} />
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          p: '16px 24px 24px',
        }}
      >
        <Stack
          direction="row"
          spacing={2}
          maxWidth={416}
          width="100%"
          margin="0px auto"
        >
          <Button variant="outlined" size="large" fullWidth onClick={onClose}>
            {t('global.cancel')}
          </Button>
          <LoadingButton
            variant="contained"
            color="secondary"
            size="large"
            fullWidth
            loading={isLoading}
            type="submit"
            form="counselor-confirm-meeting-form"
          >
            {t('global.settle')}
          </LoadingButton>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmMeetingModal;

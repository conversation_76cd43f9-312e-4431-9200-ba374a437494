import { yupResolver } from '@hookform/resolvers/yup';
import { Box, IconButton, Typography } from '@mui/material';
import NumberField from 'components/Form/NumberField';
import { t } from 'i18n';
import { EditIcon, SaveIcon } from 'icons';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import Helper from 'utils/helpers';
import type { InferType } from 'yup';
import { number, object } from 'yup';

import styles from './styles';

const schema = object({
  price: number().typeError(t('validation.invalidField')).required(),
});

export type IUnitPriceForm = InferType<typeof schema>;

export interface IUnitPriceFormProps {
  onSubmit: (values: IUnitPriceForm) => void;
  defaultValues?: { price: number; duration: number };
  editPriceMode?: boolean;
  setEditPriceMode: (mode: boolean) => void;
  total?: number;
}
const UnitPriceForm = ({
  onSubmit,
  defaultValues,
  editPriceMode,
  setEditPriceMode,
  total,
}: IUnitPriceFormProps) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty },
  } = useForm<InferType<typeof schema>>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues,
  });

  useEffect(
    () =>
      reset({
        price: defaultValues?.price || 0,
      }),
    [defaultValues, reset, editPriceMode],
  );

  const handleAddUnitPrice = async (values: InferType<typeof schema>) => {
    if (isDirty) {
      onSubmit(values);
    } else setEditPriceMode(false);
  };

  return (
    <div>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={!editPriceMode ? '21px 32px' : '16px 32px'}
        bgcolor="white"
        borderRadius={2}
        mt={2}
      >
        <Typography fontWeight="bold" color="heading">
          {t('consultationMenu.unitPrice')}
        </Typography>
        {!editPriceMode ? (
          <>
            <Typography fontWeight="bold" maxWidth={236} width={1}>
              {t('consultationMenu.price')}
              <Typography component="span" ml={2}>
                {Helper.addComma(defaultValues?.price || '0')}円
              </Typography>
            </Typography>
          </>
        ) : (
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
          >
            <Box display="flex" alignItems="center" maxWidth={236}>
              <Typography mr={1} fontWeight="bold">
                {t('consultationMenu.price')}
              </Typography>
              <Box flex={1}>
                <NumberField
                  hideError
                  control={control}
                  labelCol={12}
                  name="price"
                  min={0}
                  max={999999999}
                />
              </Box>
              <Typography ml={1}>円</Typography>
            </Box>
          </Box>
        )}
        <Typography fontWeight="bold">
          {t('consultationMenu.time')}
          <Typography component="span" ml={2}>
            {defaultValues?.duration || 30}分
          </Typography>
        </Typography>
        {!editPriceMode ? (
          <IconButton
            disabled={total === 0}
            onClick={() => setEditPriceMode(true)}
            sx={styles.iconButton}
          >
            <EditIcon />
          </IconButton>
        ) : (
          <IconButton
            onClick={handleSubmit(handleAddUnitPrice)}
            disabled={total === 0}
            sx={styles.iconButton}
          >
            <SaveIcon />
          </IconButton>
        )}
      </Box>
    </div>
  );
};

export default UnitPriceForm;

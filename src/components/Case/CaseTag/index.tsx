import type { ChipProps } from '@mui/material';
import { Chip } from '@mui/material';
import type { CaseStatusType } from 'utils/constants';
import { CASE_STATUS_TAG } from 'utils/constants';

export interface ICaseTag extends ChipProps {
  status: CaseStatusType;
}

const CaseTag = ({ status }: ICaseTag) => {
  return (
    <Chip
      label={CASE_STATUS_TAG[status]?.label}
      className="tabletStyle"
      sx={{
        bgcolor: CASE_STATUS_TAG[status]?.color,
        color: CASE_STATUS_TAG[status]?.textColor,
        fontSize: { xs: '10px', tablet: '12px' },
      }}
    />
  );
};

export default CaseTag;

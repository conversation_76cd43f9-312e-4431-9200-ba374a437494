import { Box, But<PERSON>, Divider, Stack, Typography } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { Select } from 'components/Form';
import type { IOption } from 'components/Form/Select/index';
import ChipList from 'components/UI/ChipList';
import dayjs from 'dayjs';
import type { IListItem } from 'hooks/types';
import { t } from 'i18n';
import { isEmpty } from 'lodash';
import type { IMeetingType } from 'models/consultation/interface';
import { IMeetingTypeLabel } from 'models/consultation/interface';
import type { ICoupon } from 'models/coupon/interface';
import Image from 'next/image';
import Script from 'next/script';
import type { CreateMeetingValues } from 'pages/customer/booking/[lawyerId]/schema';
import type { CreateCounselorMeetingValues } from 'pages/customer/counselor-booking/[counselorId]/schema';
import { useState } from 'react';
import type { Control } from 'react-hook-form';
import CaseUtil from 'utils/caseUtil';
import {
  MEETING_TYPE_TEXT,
  MeetingType,
  MomentFormat,
  ProviderType,
} from 'utils/constants';
import Helper from 'utils/helpers';

import CouponDetailModal from '../CouponDetailModal';
import CouponTag from '../CouponTag';
import styles from './styles';

export interface IConfirmMeetingModal<T extends ProviderType> {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  providerType: T;
  control: Control<
    T extends ProviderType.LAWYER
      ? CreateMeetingValues
      : CreateCounselorMeetingValues
  >;
  cardList: IOption[];
  data: {
    avatar?: string;
    fullName?: string;
    katakanaName?: string;
    consultationField?: IListItem[];
    firstChoice?: string;
    secondChoice?: string;
    thirdChoice?: string;
    consumerNote?: string;
    duration?: number;
    unitPrice?: number;
    type?: MeetingType;
    finalizedDate?: string;
    nickName?: string;
    menuTitle?: string;
    expectDate?: string;
    meetingType?: IMeetingType;
    coupon?: ICoupon | null;
    firstChoiceTime?: string[];
    secondChoiceTime?: string[];
    thirdChoiceTime?: string[];
  };
}

const ConfirmMeetingModal = <T extends ProviderType>({
  open,
  onClose,
  control,
  cardList,
  data,
  onConfirm,
  providerType,
}: IConfirmMeetingModal<T>) => {
  const isLawyer = providerType === ProviderType.LAWYER;
  const basicPrice = !isLawyer
    ? data.unitPrice || 0
    : Helper.getBasicPrice(data.unitPrice, data.duration);
  const { usedCouponPrice } = CaseUtil.getCouponPrice({
    basicPrice,
    couponAmount: data.coupon?.couponAmount || 0,
  });
  const [openCouponDetailModal, setOpenCouponDetailModal] = useState(false);
  return (
    <Dialog
      onClose={(_, reason) => {
        if (reason !== 'backdropClick') {
          onClose();
        }
      }}
      open={open}
      aria-labelledby="confirm-meeting-modal-title"
      aria-describedby="confirm-meeting-modal-description"
      maxWidth="mw"
      fullWidth
    >
      <DialogTitle id="confirm-meeting-modal-title" sx={styles.dialogTitle}>
        <Typography sx={styles.title}>リクエスト内容確認</Typography>
      </DialogTitle>
      <DialogContent sx={styles.dialogContent}>
        <DialogContentText
          id="confirm-meeting-modal-description"
          component="div"
        >
          <Divider />
          <Stack
            spacing={{ xs: '12px', tablet: 2 }}
            mt={{ xs: '12px', tablet: 3 }}
            direction="row"
            alignItems="center"
          >
            <Box sx={styles.avatarContainer}>
              <Image
                alt="avatar"
                fill
                style={{ borderRadius: '50%' }}
                src={data.avatar || '/images/default-avatar.png'}
              />
            </Box>
            <Box flex={1}>
              <Typography fontWeight={500} color="heading" fontSize={12}>
                {isLawyer ? '担当弁護士' : '担当カウンセラー'}
              </Typography>
              <Typography sx={styles.fullName}>
                {data.nickName || data.fullName}
              </Typography>
              {!data.nickName && (
                <Typography
                  fontWeight={500}
                  fontSize={{ xs: 12, tablet: 14 }}
                  mt="1px"
                >
                  {data.katakanaName}
                </Typography>
              )}
              <ChipList
                stackProps={{ mt: 1 }}
                data={data.consultationField || []}
                labelPath="value"
              />
            </Box>
          </Stack>

          <Divider
            sx={{
              mt: { xs: '12px', tablet: 3 },
              mb: { xs: 2, tablet: 3 },
            }}
          />
          <Box display="grid" sx={styles.gridInfo}>
            <Typography sx={styles.labelText}>面談方法</Typography>
            <Typography className="16-to-14">
              {data?.type && MEETING_TYPE_TEXT[data.type]}
            </Typography>
            {data.menuTitle && (
              <>
                <Typography sx={styles.labelText}>メニュー</Typography>
                <Typography className="16-to-14">
                  {data.menuTitle} -{' '}
                  {data.meetingType ? IMeetingTypeLabel[data.meetingType] : ''}
                </Typography>
              </>
            )}
            {data.expectDate && (
              <>
                <Typography sx={styles.labelText}>面談日時</Typography>
                <Typography className="16-to-14">
                  {dayjs(data.expectDate).format(
                    MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                  )}
                </Typography>
              </>
            )}
            {data.finalizedDate && (
              <>
                <Typography sx={styles.labelText}>第1希望</Typography>
                <Typography className="16-to-14">
                  {dayjs(data.finalizedDate).format(
                    MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                  )}
                </Typography>
              </>
            )}
            {data.firstChoice && (
              <>
                <Typography sx={styles.labelText}>第1希望</Typography>
                <Typography className="16-to-14">
                  {dayjs(data.firstChoice).format(
                    MomentFormat.JP_YEAR_MONTH_DATE,
                  )}
                  <br />
                  {data.firstChoiceTime &&
                    data.firstChoiceTime[0] &&
                    data.firstChoiceTime[1] &&
                    `${dayjs(data.firstChoiceTime[0]).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )} ~ ${dayjs(data.firstChoiceTime[1]).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}`}
                </Typography>
              </>
            )}
            {data.secondChoice && (
              <>
                <Typography sx={styles.labelText}>第2希望</Typography>
                <Typography className="16-to-14">
                  {dayjs(data.secondChoice).format(
                    MomentFormat.JP_YEAR_MONTH_DATE,
                  )}
                  <br />
                  {data.secondChoiceTime &&
                    data.secondChoiceTime[0] &&
                    data.secondChoiceTime[1] &&
                    `${dayjs(data.secondChoiceTime[0]).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )} ~ ${dayjs(data.secondChoiceTime[1]).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}`}
                </Typography>
              </>
            )}
            {data.thirdChoice && (
              <>
                <Typography sx={styles.labelText}>第3希望</Typography>
                <Typography className="16-to-14">
                  {dayjs(data.thirdChoice).format(
                    MomentFormat.JP_YEAR_MONTH_DATE,
                  )}
                  <br />
                  {data.thirdChoiceTime &&
                    data.thirdChoiceTime[0] &&
                    data.thirdChoiceTime[1] &&
                    `${dayjs(data.thirdChoiceTime[0]).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )} ~ ${dayjs(data.thirdChoiceTime[1]).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}`}
                </Typography>
              </>
            )}
            <Typography sx={styles.labelText}>要望</Typography>
            {!isEmpty(data.consumerNote) ? (
              <Typography className="16-to-14">{data.consumerNote}</Typography>
            ) : (
              <Typography className="16-to-14" color="placeholder">
                {'情報がありません'}
              </Typography>
            )}
          </Box>
          <Divider sx={{ my: { xs: 2, tablet: 3 } }} />
          {/* Only show card info when choosing online meeting */}
          {data.type === MeetingType.ONLINE && (
            <Box display="grid" sx={styles.gridInfo}>
              <Typography className="16-to-14" fontWeight="bold" mt="12px">
                お支払い方法
              </Typography>
              <Box flex={1}>
                <Select
                  name={'cardNumber' as never}
                  control={control}
                  labelCol={12}
                  data={cardList}
                  placeholder={t('booking.placeholderSelectCard')}
                />
              </Box>
            </Box>
          )}
          {data.coupon && (
            <Box
              display="flex"
              mt="24px"
              justifyContent="space-between"
              alignItems="center"
              flexWrap="wrap"
            >
              <Box display="flex" alignItems="center">
                <Typography className="16-to-14" fontWeight="bold" mr={1}>
                  クーポン値引
                </Typography>
                <div
                  style={{ cursor: 'pointer' }}
                  onClick={() => setOpenCouponDetailModal(true)}
                >
                  <CouponTag code={data.coupon.code} />
                </div>
              </Box>
              <Typography className="16-to-14">
                {data.coupon.couponAmount > 0 && '-'}
                {Helper.addComma(data.coupon.couponAmount || 0)}円
              </Typography>
            </Box>
          )}
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="flex-end"
            mt={{ xs: '12px', tablet: 2 }}
          >
            <Stack flexDirection="row" alignItems="center">
              <Typography
                className="16-to-14"
                fontWeight="bold"
                color="heading"
                mr="6px"
                lineHeight="24px"
              >
                支払い予定額
              </Typography>
            </Stack>
            <Stack direction="row" spacing="2px" alignItems="flex-end">
              <Typography
                fontSize={{ xs: 20, tablet: 24 }}
                fontWeight="bold"
                color="neutral7"
              >
                {Helper.addComma(usedCouponPrice)}
              </Typography>
              <Typography
                className="16-to-14"
                fontWeight="bold"
                color="neutral7"
                lineHeight={{ xs: 1.71, tablet: '30px' }}
              >
                円
              </Typography>
              <Typography
                sx={{
                  lineHeight: '31px',
                  display: { xs: 'none', tablet: 'block' },
                }}
              >
                (税込)
              </Typography>
            </Stack>
          </Stack>
          <Stack
            direction="row"
            spacing={{ xs: 1, tablet: 2 }}
            maxWidth={416}
            margin={{ xs: '32px auto 0px', tablet: '40px auto 0px' }}
          >
            <Button
              variant="outlined"
              size="large"
              fullWidth
              className="tabletStyle"
              onClick={onClose}
            >
              {t('global.cancel')}
            </Button>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              fullWidth
              className="tabletStyle"
              onClick={() => {
                onConfirm();

                if (isLawyer) {
                  Helper.setEventTracking({
                    ID: 'lawyer_reservation_completed',
                  });
                } else {
                  Helper.setEventTracking({
                    ID: 'counselor_reservation_completed',
                  });
                }
              }}
            >
              リクエストを送信
            </Button>
          </Stack>
          <Box mt={{ xs: 2, tablet: 4 }}>
            {data.type === MeetingType.ONLINE ? (
              <Stack>
                <Typography
                  fontSize={{ xs: 12, tablet: 14 }}
                  color={isLawyer ? 'error' : 'neutral7'}
                  fontWeight={isLawyer ? 'normal' : 'bold'}
                  className="14-to-12"
                  mt={1}
                >
                  {isLawyer
                    ? t('booking.descriptionForOnlineMeeting1')
                    : t('booking.descriptionForCounselor1')}
                </Typography>
                <Typography
                  className="14-to-12"
                  fontSize={{ xs: 12, tablet: 14 }}
                  color="hint"
                  mt={isLawyer ? 2 : 0}
                >
                  {isLawyer
                    ? t('booking.descriptionForOnlineMeeting2')
                    : t('booking.descriptionForCounselor2')}
                </Typography>
              </Stack>
            ) : (
              <Typography
                className="14-to-12"
                color="hint"
                whiteSpace="pre-line"
              >
                初回無料相談の設定時間を超過した場合は、費用が発生します。具体的な料金については、直接弁護士にご確認いただくことをお勧めします。
                <br />
                <br />
                弁護士の状況によってお断りする場合がございますので、予めご了承ください。
              </Typography>
            )}
          </Box>
        </DialogContentText>
      </DialogContent>
      <CouponDetailModal
        couponData={data.coupon as ICoupon}
        open={openCouponDetailModal}
        onClose={() => setOpenCouponDetailModal(false)}
      />

      {isLawyer ? (
        <>
          <img
            src="https://step.lme.jp/p/135442/xnnmsZPq"
            id="get_image"
            alt=""
            style={{
              width: '0px',
              height: '0px',
              visibility: 'hidden',
            }}
          />
          <br />
          <Script
            src="https://step.lme.jp/js/conversion/get_url_target_page.js"
            strategy="lazyOnload"
          />
        </>
      ) : (
        <>
          <img
            src="https://step.lme.jp/p/135442/wBJdRjPx"
            id="get_image"
            alt=""
            style={{
              width: '0px',
              height: '0px',
              visibility: 'hidden',
            }}
          />
          <br />
          <Script
            src="https://step.lme.jp/js/conversion/get_url_target_page.js"
            strategy="lazyOnload"
          />
        </>
      )}
    </Dialog>
  );
};

export default ConfirmMeetingModal;

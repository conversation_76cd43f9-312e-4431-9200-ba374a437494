import type { SxProps, Theme } from '@mui/material/styles';

const styles: Record<string, SxProps<Theme>> = {
  dialogTitle: { p: { xs: 2, tablet: '32px 32px 24px' }, letterSpacing: '2px' },
  avatarContainer: {
    width: { xs: 80, tablet: 84 },
    height: { xs: 80, tablet: 84 },
    position: 'relative',
  },
  fullName: {
    fontSize: { xs: 18, tablet: 20 },
    color: 'heading',
    fontWeight: 'bold',
  },
  title: {
    fontSize: { xs: 18, tablet: 24 },
    fontWeight: 'bold',
    color: 'heading',
    textAlign: 'center',
  },
  gridInfo: {
    gridTemplateColumns: { xs: '84px auto', tablet: '96px auto' },
    gridGap: { xs: '12px', tablet: '16px' },
  },
  dialogContent: {
    p: { xs: '0px 16px 16px', tablet: '0px 32px 32px' },
  },
  labelText: {
    fontSize: { xs: 14, tablet: 16 },
    fontWeight: 'bold',
  },
  cardIcon: {
    mr: 2,
    svg: { display: 'block' },
  },
  noteText: {
    whiteSpace: 'nowrap',
    color: 'hint',
  },
};

export default styles;

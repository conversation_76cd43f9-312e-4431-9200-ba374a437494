import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  caseInfo: {
    display: { xs: 'block', tablet: 'flex' },
    justifyContent: 'space-between',
    mb: '4px',
  },
  meetingItem: {
    borderRadius: '12px',
    border: (theme) => `solid 1px ${theme.palette.neutral4}`,
    p: '24px',
  },
  tooltipPopper: {
    '& .MuiTooltip-tooltip': {
      bgcolor: '#fdf6e2',
      boxShadow: '0 8px 16px -8px #d2dce1',
    },
  },
  infoButton: {
    p: 0,
    ml: '4px',
    svg: { color: 'icon', display: 'block' },
  },
  consumerNoteContainer: {
    borderRadius: 1,
    p: 2,
    mt: 2,
    '.truncate-text__ellipsis': {
      pb: 0,
    },
  },
  iconPayment: {
    svg: {
      display: 'block',
      width: 24,
      height: 16,
    },
  },
  cardInfo: {
    fontSize: 12,
    color: 'text.primary',
  },
  textPaymentInfo: {
    fontSize: 14,
    color: 'text.primary',
    textOverflow: 'ellipsis',
  },
  tooltip: {
    bgcolor: '#fdf6e2',
  },
  icon: {
    height: 20,
    width: 20,
    marginBottom: '4px',
    svg: { width: 20, height: 20 },
  },
  textExpectDate: {
    fontSize: 14,
    color: 'heading',
  },
  textUrlMeeting: {
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    fontSize: 14,
    maxHeight: 20,
    maxWidth: 200,
  },
  moreIcon: {
    p: 0,
  },
  dialogTitle: { p: { xs: 2, tablet: '32px 32px 24px' } },
  textTime: {
    fontSize: 12,
    flex: 1,
    whiteSpace: 'pre-line',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

import {
  Box,
  Button,
  Divider,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import ConfirmModal from 'components/ConfirmModal/modal';
import LoadingOverlay from 'components/LoadingOverlay';
import TruncateText from 'components/TruncateText';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import {
  CashIcon,
  ConfirmMeetingIcon,
  EmptyPaymentIcon,
  InfoIcon,
  MoreIcon,
  PaymentCardIcon,
  ProcessPaymentIcon,
  TransferIcon,
} from 'icons';
import { get } from 'lodash';
import type { IMeetingsItem } from 'models/case/interface';
import caseQuery from 'models/case/query';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { useState } from 'react';
import CaseUtil from 'utils/caseUtil';
import {
  dropdownMenus,
  MeetingStatusType,
  MeetingType,
  MomentFormat,
  PAYMENT_TEXT,
  PaymentMethod,
  PaymentStatusType,
} from 'utils/constants';
import Helper from 'utils/helpers';

import CouponTag from '../CouponTag';
import ExpectDate from '../ExpectDate';
import MeetingTag from '../MeetingTag';
import MeetingUrl from '../MeetingUrl';
import PaymentTooltipButton from '../PaymentTooltipButton';
import { cancelModalProps, finishModalProps } from './const';
import styles from './styles';

dayjs.extend(isSameOrAfter);

const PaymentErrorContent = dynamic(() => import('../PaymentErrorContent'));
const FinishMeetingContent = dynamic(
  () => import('../FinishMeetingContent/Lawyer'),
);
const CancelMeetingForm = dynamic(() => import('../CancelMeetingForm'));
const DropdownMenu = dynamic(() => import('components/UI/DropdownMenu'));
const ConfirmMeetingModal = dynamic(
  () => import('../LawyerConfirmMeetingModal'),
);

export interface ILawyerMeeting {
  meeting: IMeetingsItem;
  loading?: boolean;
  refetch: () => void;
  refetchCaseDetail: () => void;
  refetchMeetingList: () => void;
  hasReview: boolean;
}

const LawyerMeetingItem = ({
  meeting,
  loading,
  refetch,
  hasReview,
  refetchCaseDetail,
  refetchMeetingList,
}: ILawyerMeeting) => {
  const { query, push } = useRouter();
  const caseId = query.caseId as string;

  const { setConfirmModal } = useGlobalState();
  const [openModalConfirmMeeting, setOpenModalConfirmMeeting] = useState(false);
  const [openFinishMeeting, setOpenFinishMeeting] = useState(false);
  const [openRemoveCouponModal, setOpenRemoveCouponModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const handleClickMoreButton = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    setAnchorEl(event.currentTarget);
  };

  const {
    status: meetingStatus,
    meetingUrl,
    type,
    finalizedDate,
    _id: meetingId,
  } = meeting;
  const {
    duration,
    finalPrice = 0,
    unitPrice,
    status: paymentStatus = PaymentStatusType.PENDING,
    method: paymentMethod,
    discount,
  } = meeting.paymentInfo;
  const displayStatus = CaseUtil.getDisplayPaymentStatus({
    paymentStatus,
    meetingStatus,
  });
  const basicPrice = Helper.addComma(Helper.getBasicPrice(unitPrice, duration));
  const isFinishedMeeting = meetingStatus === MeetingStatusType.FINISHED;
  const isWaitingMeeting =
    meetingStatus === MeetingStatusType.WAITING_ANWSER_FROM_PROVIDER ||
    meetingStatus === MeetingStatusType.WAITING_FOR_REPLY_FROM_CUSTOMER;
  const isDecidedMeeting =
    meetingStatus === MeetingStatusType.SCHEDULE_IS_DECIDED;
  const canRechargePayment =
    meetingStatus === MeetingStatusType.FINISHED &&
    paymentStatus === PaymentStatusType.ERROR;

  const isMeetingClosed =
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
    ].includes(meetingStatus) ||
    (meetingStatus === MeetingStatusType.FINISHED &&
      paymentStatus === PaymentStatusType.COMPLETED);

  const isShowMeetingUrl = meetingUrl && type === MeetingType.ONLINE;
  const isDisableMeetingUrl =
    isFinishedMeeting ||
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
    ].includes(meetingStatus);

  const { mutateAsync: confirmPayment, isLoading: isConfirming } = useMutate(
    caseQuery.confirmPayment,
  );
  const { mutateAsync: finishMeeting, isLoading: isFinishing } = useMutate(
    caseQuery.finishMeeting,
  );
  const { mutateAsync: cancelMeeting, isLoading: isCanceling } = useMutate<{
    status: MeetingStatusType;
    meetingId: string;
  }>(caseQuery.cancelMeeting);
  const { mutateAsync: removeCoupon } = useMutate(
    caseQuery.removeCoupon(meetingId),
  );
  const { mutateAsync: updateMeetingStatus, isLoading: isUpdatingStatus } =
    useMutate<{ status: MeetingStatusType }>(
      caseQuery.updateProviderMeetingStatus(meetingId),
    );

  const { couponInfo } = meeting.paymentInfo;
  const handleUpdateMeetingStatus = async (_id: string) => {
    try {
      await updateMeetingStatus({
        status: MeetingStatusType.WAITING_FOR_REPLY_FROM_CUSTOMER,
      });
      Helper.toast(t('validation.completed'));
      refetchCaseDetail();
      refetchMeetingList();
    } catch (error) {
      if (get(error, 'code') === 'E_VALIDATION_FAILURE') {
        Helper.toast(
          get(error, 'data.messages[0].message') || '保存できませんでした。',
          {
            type: 'error',
          },
        );
      }
      refetchMeetingList();
    }
  };
  const handleRechargePayment = async () => {
    try {
      await confirmPayment({ meetingId: meeting._id });
      refetch();
      refetchCaseDetail();
      if (!hasReview) {
        refetchCaseDetail();
      }
    } catch (error) {
      if (get(error, 'code') === 'MEETING_PAYMENT_ERROR') {
        setConfirmModal({
          icon: 'warning',
          content: (
            <PaymentErrorContent finalizedDate={finalizedDate} cardError />
          ),
          confirmText: 'OK',
          hideCancelButton: true,
        });
      }
      if (
        get(error, 'code') === 'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT'
      ) {
        setConfirmModal({
          icon: 'warning',
          content: <PaymentErrorContent finalizedDate={finalizedDate} />,
          confirmText: 'OK',
          hideCancelButton: true,
        });
      }
    }
  };

  const handleFinishMeeting = async () => {
    try {
      await finishMeeting({ meetingId });
      await confirmPayment({ meetingId });
      if (!hasReview) {
        refetchCaseDetail();
      }
      setOpenFinishMeeting(false);
      refetch();
    } catch (error) {
      if (get(error, 'code') === 'MEETING_PAYMENT_ERROR') {
        setConfirmModal({
          icon: 'warning',
          content: (
            <PaymentErrorContent finalizedDate={finalizedDate} cardError />
          ),
          confirmText: 'OK',
          hideCancelButton: true,
          onConfirm: () => {
            refetch();
            refetchCaseDetail();
          },
        });
      }
      if (
        get(error, 'code') === 'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT'
      ) {
        setConfirmModal({
          icon: 'warning',
          content: <PaymentErrorContent finalizedDate={finalizedDate} />,
          confirmText: 'OK',
          hideCancelButton: true,
          onConfirm: () => {
            refetch();
            refetchCaseDetail();
          },
        });
      }
    }
  };

  const handleCancelMeeting = async (values: {
    status: MeetingStatusType;
    cancelReason?: string;
  }) => {
    cancelMeeting(
      { ...values, meetingId },
      {
        onSuccess: () => refetch(),
      },
    );
  };

  const handleMeetingAction = (key: string) => {
    setAnchorEl(null);
    if (key === 'edit') {
      push({
        pathname: '/lawyer/my-page/cases/[caseId]/create',
        query: {
          caseId,
          meetingId,
        },
      });
    }
    if (key === 'cancel') {
      setConfirmModal({
        ...cancelModalProps,
        content: (
          <CancelMeetingForm
            finalizedDate={meeting.finalizedDate}
            onSubmit={handleCancelMeeting}
          />
        ),
      });
    }
    if (key === 'finish') {
      setOpenFinishMeeting(true);
    }
  };

  return (
    <Box>
      <Box mt={2} position="relative">
        <Box
          sx={styles.meetingItem}
          bgcolor={isMeetingClosed ? 'backgroundColor' : 'white'}
        >
          <Box sx={styles.caseInfo}>
            {finalizedDate ? (
              <Typography fontWeight="bold" fontSize={18} color="heading">
                {dayjs(finalizedDate).format(
                  MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                )}
              </Typography>
            ) : (
              <Stack direction="row" spacing={1}>
                <Typography fontWeight="bold" fontSize={18} color="hint">
                  {t('caseDetail.undecidedDate')}
                </Typography>
                <Tooltip
                  title={<ExpectDate expectDate={meeting.expectDate} />}
                  arrow
                  placement="right"
                  slotProps={{
                    arrow: {
                      sx: { color: '#fdf6e2' },
                    },
                  }}
                  PopperProps={{
                    sx: styles.tooltipPopper,
                  }}
                >
                  <IconButton size="small" sx={styles.infoButton}>
                    <InfoIcon />
                  </IconButton>
                </Tooltip>
              </Stack>
            )}
            <MeetingTag
              status={meeting.status}
              isLawyer
              isLoadingUpdate={isUpdatingStatus}
              handleUpdateMeeting={() => handleUpdateMeetingStatus(meeting._id)}
            />
          </Box>
          <Box display="flex">
            <Typography sx={styles.textPaymentInfo}>
              {t('caseDetail.duration', {
                number: duration,
              })}
              &nbsp;&nbsp;
              {'•'}
              &nbsp;&nbsp;
              {type === MeetingType.ONLINE
                ? t('caseDetail.methodOnline')
                : t('caseDetail.methodInPerson')}
              {isShowMeetingUrl && <>&nbsp;&nbsp;:&nbsp;&nbsp;</>}
            </Typography>
            {isShowMeetingUrl && (
              <MeetingUrl
                meetingUrl={meetingUrl}
                isDisableMeetingUrl={isDisableMeetingUrl}
              />
            )}
          </Box>
          {meeting.cancelReason && (
            <Box
              sx={styles.consumerNoteContainer}
              bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
              border="1px solid #DBE3E7"
            >
              <Typography
                fontSize={14}
                fontWeight="bold"
                mb={1}
                color="#db5a42"
              >
                キャンセル理由
              </Typography>
              <TruncateText
                textProps={{
                  sx: {
                    whiteSpace: 'pre-line',
                    color: 'heading',
                    fontSize: 14,
                  },
                }}
                lines={2}
                text={meeting.cancelReason}
              />
            </Box>
          )}
          {meeting.consumerNote && (
            <Box
              sx={styles.consumerNoteContainer}
              bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
              border="1px solid #DBE3E7"
            >
              <Typography fontSize={14} fontWeight="bold" mb={1}>
                {t('caseDetail.customerNote')}
              </Typography>
              <TruncateText
                textProps={{
                  sx: {
                    whiteSpace: 'pre-line',
                    color: 'heading',
                    fontSize: 14,
                  },
                }}
                lines={2}
                text={meeting.consumerNote}
              />
            </Box>
          )}
          <Divider sx={{ my: { xs: 2, tablet: 3 } }} />
          <Box
            sx={styles.consumerNoteContainer}
            bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
            border="1px solid #DBE3E7"
            mb={2}
          >
            <Stack flexDirection="row" gap={1} justifyContent="space-between">
              <Typography fontSize={14}>合計金額</Typography>
              <Typography fontSize={14} fontWeight={700}>
                {Helper.addComma(basicPrice)}円
              </Typography>
            </Stack>
            {couponInfo && (
              <Stack
                flexDirection="row"
                gap={1}
                justifyContent="space-between"
                mt={1}
                flexWrap="wrap"
              >
                <Stack flexDirection="row">
                  <Typography fontSize={14} mr={1}>
                    クーポン値引
                  </Typography>
                  <CouponTag
                    code={couponInfo.coupon.code}
                    wrapperProps={{ mr: '6px', height: 'fit-content' }}
                    circleColor="#F6F8F9"
                  />
                  <PaymentTooltipButton />
                </Stack>
                <Typography fontSize={14}>
                  - {Helper.addComma(couponInfo.couponAmount || 0)}円
                </Typography>
              </Stack>
            )}
            {!!discount && discount > 0 && (
              <Stack flexDirection="row" justifyContent="space-between" mt={1}>
                <Typography fontSize={14}>割引金額</Typography>
                <Typography fontSize={14}>
                  - {Helper.addComma(discount)}円
                </Typography>
              </Stack>
            )}
          </Box>
          <Box sx={styles.caseInfo}>
            <Box>
              <Stack direction="row" spacing="2px" mb={1} alignItems="center">
                <Typography fontWeight={500} color="text.primary">
                  {t('caseDetail.finalPrice')}
                  <Typography
                    ml="4px"
                    fontWeight={700}
                    component="span"
                    color="heading"
                  >
                    {Helper.addComma(finalPrice)}円
                  </Typography>
                </Typography>
                <PaymentTooltipButton
                  title="お支払い予定金額に関して"
                  content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
                />
              </Stack>
              <Stack direction="row" spacing={1}>
                <Box sx={styles.iconPayment}>
                  {paymentMethod === PaymentMethod.CREDIT_CARD && (
                    <PaymentCardIcon />
                  )}
                  {paymentMethod === PaymentMethod.CASH && <CashIcon />}
                  {paymentMethod === PaymentMethod.BANK_TRANSFER && (
                    <TransferIcon />
                  )}
                </Box>
                <Typography sx={styles.cardInfo}>
                  {paymentMethod === PaymentMethod.CREDIT_CARD &&
                    t('caseDetail.creditCard')}
                  {paymentMethod === PaymentMethod.CASH &&
                    t('caseDetail.cashPayment')}
                  {paymentMethod === PaymentMethod.BANK_TRANSFER &&
                    t('caseDetail.bankTransfer')}
                </Typography>
                <Typography sx={styles.cardInfo}>{'•'}</Typography>
                {paymentStatus ? (
                  <Typography
                    fontSize={12}
                    fontWeight={500}
                    color={PAYMENT_TEXT[displayStatus].color}
                  >
                    {PAYMENT_TEXT[displayStatus].text}
                  </Typography>
                ) : (
                  <Typography
                    fontSize={12}
                    fontWeight={500}
                    color={PAYMENT_TEXT[PaymentStatusType.PENDING].color}
                  >
                    {PAYMENT_TEXT[PaymentStatusType.PENDING].text}
                  </Typography>
                )}
              </Stack>
            </Box>
            <Stack direction="row" spacing={1} alignItems="center">
              {isWaitingMeeting && (
                <Button
                  variant="whiteOutlined"
                  size="medium"
                  startIcon={<ConfirmMeetingIcon />}
                  style={{
                    textTransform: 'initial',
                  }}
                  onClick={() => setOpenModalConfirmMeeting(true)}
                >
                  {t('caseDetail.confirmMeeting')}
                </Button>
              )}
              {isDecidedMeeting && (
                <Button
                  variant="whiteOutlined"
                  startIcon={<ProcessPaymentIcon />}
                  disabled={dayjs(finalizedDate).isSameOrAfter(
                    dayjs(),
                    'minute',
                  )}
                  onClick={() => handleMeetingAction('finish')}
                >
                  {t('caseDetail.finishMeeting')}
                </Button>
              )}
              {canRechargePayment && (
                <Button
                  variant="whiteOutlined"
                  size="medium"
                  startIcon={<EmptyPaymentIcon />}
                  style={{ textTransform: 'initial' }}
                  onClick={handleRechargePayment}
                >
                  {t('caseDetail.recharge')}
                </Button>
              )}
              {!isMeetingClosed && (
                <IconButton
                  size="medium"
                  sx={styles.moreIcon}
                  onClick={handleClickMoreButton}
                  id="more-button"
                >
                  <MoreIcon />
                </IconButton>
              )}
            </Stack>
          </Box>
        </Box>
        <DropdownMenu
          menus={dropdownMenus.map((item) => {
            if (item.key === 'cancel' && canRechargePayment) {
              return {
                ...item,
                disabled: true,
              };
            }
            return item;
          })}
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
          onMenuItemClick={handleMeetingAction}
        />
        {isWaitingMeeting && (
          <ConfirmMeetingModal
            data={meeting}
            open={openModalConfirmMeeting}
            setOpenModalConfirmMeeting={setOpenModalConfirmMeeting}
            onSuccess={refetch}
            onClose={() => setOpenModalConfirmMeeting(false)}
          />
        )}
        <LoadingOverlay
          visible={loading || isConfirming || isFinishing || isCanceling}
        />
        <ConfirmModal
          open={openRemoveCouponModal}
          title="クーポンの削除"
          content="このクーポンを削除しますか？"
          onCancel={() => setOpenRemoveCouponModal(false)}
          onConfirm={() => {
            setOpenRemoveCouponModal(false);
            removeCoupon(null, {
              onSuccess: () => refetchMeetingList(),
            });
          }}
        />
        <ConfirmModal
          {...finishModalProps}
          open={openFinishMeeting}
          onConfirm={handleFinishMeeting}
          confirmLoading={isFinishing}
          onCancel={() => setOpenFinishMeeting(false)}
          content={
            <FinishMeetingContent
              data={meeting}
              onRemoveCoupon={() => {
                setOpenRemoveCouponModal(true);
              }}
            />
          }
        />
      </Box>
    </Box>
  );
};

export default LawyerMeetingItem;

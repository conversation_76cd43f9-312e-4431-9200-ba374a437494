import { Box, Typography } from '@mui/material';
import { Radio } from 'components/Form';
import { t } from 'i18n';
import { useForm } from 'react-hook-form';
import { CaseStatusType } from 'utils/constants';

export interface CancelCaseFormProps {
  onSubmit: (values: { status: CaseStatusType }) => void;
  customerName: string;
}

const CancelCaseForm = ({ onSubmit, customerName }: CancelCaseFormProps) => {
  const { control, handleSubmit } = useForm<{ status: CaseStatusType }>({
    defaultValues: {
      status: CaseStatusType.CANCELED_BY_CONSUMER,
    },
  });
  return (
    <div>
      <Typography fontSize={14} textAlign="center">
        <Typography fontSize={14} fontWeight="bold" component="span">
          {customerName}
        </Typography>
        様のご相談を受任なしで終了します。
      </Typography>
      <Typography fontSize={14} textAlign="center">
        受任なしを設定すると今後の面談の追加はできません。
      </Typography>
      <Typography fontSize={14} textAlign="center" mb="36px">
        (
        <Typography fontSize={14} fontWeight="bold" component="span">
          {customerName}
        </Typography>
        様から再度面談リクエストが届いた場合、面談を作成できるようになります。）
      </Typography>

      <Box
        component="form"
        id="cancel-form"
        onSubmit={handleSubmit(onSubmit)}
        ml={7}
      >
        <Radio
          labelCol={5}
          columns={14}
          label={t('caseDetail.whoCancel')}
          control={control}
          name="status"
          data={[
            { _id: CaseStatusType.CANCELED_BY_CONSUMER, value: '相談者' },
            { _id: CaseStatusType.CANCELED_BY_PROVIDER, value: '弁護士' },
          ]}
        />
      </Box>
    </div>
  );
};

export default CancelCaseForm;

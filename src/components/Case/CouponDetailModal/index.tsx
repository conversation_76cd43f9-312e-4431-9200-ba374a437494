import { Box, Button, IconButton, SvgIcon, Typography } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import dayjs from 'dayjs';
import useBreakpoint from 'hooks/useBreakpoint';
import { ClockIcon, CloseIcon, CouponGraphic, CouponIcon } from 'icons';
import type { ICoupon } from 'models/coupon/interface';
import { memo } from 'react';
import { MomentFormat } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

export interface CouponModalProps {
  open: boolean;
  onClose: () => void;
  couponData: ICoupon;
}

const renderCouponDetail = ({
  data,
  onBack,
}: {
  data: ICoupon;
  onBack: () => void;
}) => {
  return (
    <Box
      p={{ xs: 2, tablet: '0px 32px 32px' }}
      height="100%"
      display="flex"
      flexDirection="column"
    >
      <Box flex={1}>
        <Box sx={{ borderRadius: { xs: '12px', tablet: 2 } }} bgcolor="#FFF5CC">
          <Box
            p={{ xs: '12px', tablet: 3 }}
            sx={{
              borderBottom: data?.description ? '1px dashed #FAD59E' : 'unset',
            }}
            display="flex"
            alignItems="flex-end"
            justifyContent="space-between"
          >
            <Box>
              <Typography color="heading" fontWeight={700} mb={1}>
                {data?.title}
              </Typography>
              <Typography
                color="primary"
                fontWeight={900}
                fontSize={{ xs: 32, tablet: 36 }}
              >
                {data?.currency === '%'
                  ? data.amount
                  : Helper.addComma(data?.amount)}
                <Typography
                  fontSize={20}
                  fontWeight={500}
                  component="span"
                  lineHeight="42px"
                  sx={{ verticalAlign: 'bottom' }}
                >
                  {data?.currency === '%' ? data?.currency : '円'}
                </Typography>
              </Typography>
              <Box
                display="flex"
                mt={1}
                gap={{ xs: '4px', tablet: 1 }}
                alignItems="center"
              >
                <SvgIcon
                  component={CouponIcon}
                  color="primary"
                  sx={{
                    width: { xs: 16, tablet: 20 },
                    height: { xs: 16, tablet: 20 },
                  }}
                  viewBox="0 0 20 20"
                />
                <Typography fontSize={{ xs: 14, tablet: 16 }}>
                  クーポンコード：
                  <Typography
                    color="heading"
                    component="span"
                    fontWeight={500}
                    fontSize={{ xs: 14, tablet: 16 }}
                  >
                    {data?.code}
                  </Typography>
                </Typography>
              </Box>
              {data?.rules?.end && (
                <Box
                  mt={1}
                  display="flex"
                  gap={{ xs: '4px', tablet: 1 }}
                  alignItems="center"
                >
                  <SvgIcon
                    component={ClockIcon}
                    inheritViewBox
                    color="primary"
                    viewBox="0 0 20 20"
                    sx={{
                      width: { xs: 16, tablet: 20 },
                      height: { xs: 16, tablet: 20 },
                    }}
                  />
                  <Typography fontSize={{ xs: 14, tablet: 16 }}>
                    有効期限：
                    <Typography
                      color="heading"
                      component="span"
                      fontWeight={500}
                      marginLeft="4px"
                      fontSize={{ xs: 14, tablet: 16 }}
                    >
                      {dayjs(data.rules.end).format(
                        MomentFormat.YEAR_MONTH_DATE,
                      )}
                    </Typography>
                  </Typography>
                </Box>
              )}
            </Box>
            <Box
              sx={{
                svg: {
                  width: { xs: 87.27, tablet: 120 },
                  height: { xs: 80, tablet: 110 },
                },
              }}
            >
              <CouponGraphic />
            </Box>
          </Box>
          {data?.description && (
            <Box p={{ xs: '12px', tablet: 3 }}>
              <Typography fontSize={14} mb={1}>
                注意事項
              </Typography>
              <Typography fontSize={14} color="heading" whiteSpace="pre-line">
                {data?.description}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
      <Box display="flex" justifyContent="center" mt={5}>
        <Button
          onClick={onBack}
          variant="outlined"
          size="large"
          className="tabletStyle"
          sx={{
            width: { xs: 146, tablet: 168 },
          }}
        >
          戻る
        </Button>
      </Box>
    </Box>
  );
};
const CouponDetailModal = ({ open, onClose, couponData }: CouponModalProps) => {
  const isBreakpoint = useBreakpoint({});

  return (
    <div>
      <Dialog
        onClose={(_, reason) => {
          if (reason === 'backdropClick') {
            onClose();
          }
        }}
        open={open}
        aria-labelledby="checkbox-modal-title"
        aria-describedby="checkbox-modal-description"
        maxWidth="mw"
        sx={styles.dialog}
        fullScreen={isBreakpoint}
      >
        <DialogTitle id="checkbox-modal-title" sx={styles.dialogTitle}>
          <Typography
            fontSize={{ xs: 18, sm: 32 }}
            fontWeight={700}
            textAlign="center"
            color="heading"
            lineHeight={{ xs: '28px', sm: '36px' }}
          >
            クーポン詳細
          </Typography>
          <IconButton
            className="whiteOutlined"
            size="small"
            onClick={() => {
              onClose();
            }}
            sx={{
              svg: { color: 'neutral7' },
              position: 'absolute',
              right: 16,
              top: 16,
              display: { xs: 'none', tablet: 'inline-flex' },
            }}
          >
            <CloseIcon />
          </IconButton>
          <IconButton size="small" sx={styles.closeButton} onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={styles.dialogContent}>
          {renderCouponDetail({
            data: couponData,
            onBack: () => onClose(),
          })}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default memo(CouponDetailModal);

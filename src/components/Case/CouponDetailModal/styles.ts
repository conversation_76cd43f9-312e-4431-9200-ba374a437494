import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  dialogTitle: {
    p: '32px 32px 40px',
    '@media (max-width: 768px)': {
      fontSize: '18px',
      padding: '14px 16px',
    },
  },
  dialogContent: { p: '0px' },

  dialog: {
    '.MuiPaper-root': {
      '@media (max-width: 768px)': {
        borderRadius: 0,
        maxHeight: '100%',
      },
    },
  },

  closeButton: {
    position: 'absolute',
    top: 9,
    left: 11,
    svg: { width: 28, height: 28, color: 'primary.main' },
    display: { tablet: 'none' },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
} from '@mui/material';
import ConfirmModal from 'components/ConfirmModal/modal';
import type { IBlock } from 'components/Customer/Profile/blocks';
import {
  divorceBlock,
  marriageBlock,
  partnerBlock,
  profileBlock,
  propertyBlock,
} from 'components/Customer/Profile/blocks';
import { TextField } from 'components/Form';
import Label from 'components/Form/Label';
import LoadingOverlay from 'components/LoadingOverlay';
import ViewEditBlock from 'components/UI/ViewEditBlock';
import type { Dayjs } from 'dayjs';
import type { ConsultationRecord } from 'hooks/customer/useConsultationForm/types';
import type { CustomerData } from 'hooks/useFetchUser/types';
import i18n from 'i18n';
import { t } from 'i18next';
import { CloseIcon, KidIcon } from 'icons';
import { isEmpty, isUndefined } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import type { CaseStatusType, GenderType, ISchoolType } from 'utils/constants';
import { Gender, RadioOptions, SchoolTypeOptions } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

import CaseTag from '../CaseTag';
import { cancelModalProps } from '../CounselorMeetingItem/const';

export interface NewCaseModalProps {
  customerProfile: CustomerData;
  caseStatus?: CaseStatusType;
  loading?: boolean;
  consultationDetail?: ConsultationRecord;
  open: boolean;
  onClose?: () => void;
  onCancel: (values: { cancelReason: string }) => void;
  onConfirm: () => void;
  isConfirming?: boolean;
  isCanceling?: boolean;
}

const cancelSchema = object({
  cancelReason: string()
    .required(i18n.t('validation.requiredField'))
    .max(200, i18n.t('validation.maxLength', { number: 200 }))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
});

const NewCaseModal = ({
  customerProfile,
  consultationDetail,
  open,
  caseStatus,
  onCancel,
  onConfirm,
  loading,
  isConfirming,
  isCanceling,
  onClose,
}: NewCaseModalProps) => {
  const [openCancelModal, setOpenCancelModal] = useState(false);
  const { control, handleSubmit, resetField } = useForm<
    InferType<typeof cancelSchema>
  >({
    resolver: yupResolver(cancelSchema),
    values: {
      cancelReason: `この度は面談のリクエストをいただきありがとうございました。
      \n大変申し訳ないのですが、リクエストいただいた面談日時の都合が合わないため面談をキャンセルさせていただきました。
      \nお手数をお掛けいたしますが、改めて別日でリクエストをいただけますと幸いです。引き続きよろしくお願いいたします。`,
    },
  });

  useEffect(() => {
    if (openCancelModal) {
      resetField('cancelReason', { keepError: false });
    }
  }, [openCancelModal, resetField]);

  const customerBlockProfile = {
    title: profileBlock.title,
    icon: profileBlock.icon,
    fields: profileBlock.fields.filter(
      (item) => item.path !== 'email' && item.path !== 'phone',
    ),
  };

  const kids = consultationDetail?.kids?.kids;

  const childrenFields = useMemo(
    () =>
      (kids || []).map((_: unknown, index: number) => [
        { label: t('consultationRecord.kidOrder', { number: index + 1 }) },
        {
          label: t('consultationRecord.kidGender'),
          path: `kids.kids[${index}].gender`,
          renderValue: (value: GenderType) => Gender[value],
        },
        {
          label: t('consultationRecord.kidAge'),
          path: `kids.kids[${index}].age`,
          renderValue: (value: Dayjs) =>
            !isUndefined(value) &&
            t('consultationRecord.ageNumber', { number: value }),
        },
        {
          label: t('consultationRecord.schoolType'),
          path: `kids.kids[${index}].schoolType`,
          renderValue: (value: ISchoolType) =>
            value && SchoolTypeOptions[value],
        },
      ]),
    [kids],
  );

  const kidsFields: IBlock = {
    title: t('consultationRecord.kidInformation'),
    icon: <KidIcon />,
    fields: [
      {
        label: t('consultationRecord.kids'),
        path: 'kids.kids',
        renderValue: (value: unknown) =>
          value && (
            <>
              <Typography>
                {isEmpty(value) ? RadioOptions.no : RadioOptions.yes}
              </Typography>
              <Typography fontSize={14} mt={1} color="hint">
                {t('consultationRecord.under18')}
              </Typography>
            </>
          ),
      },
    ].concat(childrenFields.flat() as never),
  };
  return (
    <Dialog open={!!open} maxWidth="xtablet" sx={{ zIndex: 1501 }}>
      <DialogTitle sx={{ p: '32px 32px 8px' }}>
        <Box display="flex" component="span" gap={1} alignItems="center">
          <Typography fontSize={28} fontWeight={700} color="heading">
            {customerProfile.fullName}
          </Typography>
          {caseStatus && (
            <Box>
              <CaseTag status={caseStatus} />
            </Box>
          )}
        </Box>
        <Typography fontSize={14} component="span">
          {customerProfile.katakanaName}
        </Typography>
        <IconButton
          onClick={onClose}
          sx={{ position: 'absolute', top: 9, right: 9 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        sx={{
          position: 'relative',
          '>div': {
            border: '1px solid #d2dce1',
            borderRadius: 2,
            '.view-edit-title': {
              fontSize: 20,
            },
          },
        }}
      >
        <LoadingOverlay visible={loading} />
        <ViewEditBlock
          formId="profile-block"
          detail={customerProfile}
          block={customerBlockProfile}
          hideEditModeButton
        />
        <ViewEditBlock
          detail={consultationDetail}
          block={divorceBlock}
          formId="divorce-form"
          hideEditModeButton
        />

        <ViewEditBlock
          detail={consultationDetail}
          block={partnerBlock}
          formId="partner-form"
          hideEditModeButton
        />

        <ViewEditBlock
          detail={consultationDetail}
          block={marriageBlock}
          formId="marriage-form"
          hideEditModeButton
        />

        <ViewEditBlock
          detail={consultationDetail}
          block={propertyBlock}
          formId="property-form"
          hideEditModeButton
        />
        <ViewEditBlock
          detail={consultationDetail}
          block={kidsFields}
          formId="kid-block"
          hideEditModeButton
        />
      </DialogContent>
      <DialogActions
        sx={{
          p: '24px 32px',
          boxShadow: '0 -8px 16px -12px #d2dce1',
          zIndex: 1,
        }}
      >
        <Typography color="heading">
          新しい面談リクエストが届いています。
        </Typography>
        <Box display="flex" justifyContent="flex-end" flex={1} gap={1}>
          <LoadingButton
            fullWidth
            sx={{ maxWidth: 112 }}
            variant="outlined"
            onClick={() => setOpenCancelModal(true)}
            loading={isCanceling}
          >
            非承認
          </LoadingButton>
          <LoadingButton
            loading={isConfirming}
            fullWidth
            sx={{ maxWidth: 112 }}
            variant="contained"
            color="secondary"
            onClick={onConfirm}
          >
            詳細を確認
          </LoadingButton>
        </Box>
      </DialogActions>
      {openCancelModal && (
        <ConfirmModal
          open={openCancelModal}
          {...cancelModalProps}
          title="面談キャンセル"
          onCancel={() => setOpenCancelModal(false)}
          confirmButtonProps={{ form: 'cancel-meeting', type: 'submit' }}
          dialogProps={{ sx: { zIndex: 1502 } }}
          content={
            <Box>
              <Typography fontSize={14} textAlign="center">
                この面談リクエストをキャンセルすると
                <Typography
                  component="span"
                  fontSize={14}
                  textAlign="center"
                  fontWeight={700}
                >
                  {customerProfile.fullName}
                </Typography>
                様の案件は終了します。{'\n'}(
                <Typography component="span" fontSize={14} fontWeight={700}>
                  {customerProfile.fullName}
                </Typography>
                様から再度面談リクエストが届いた場合、面談を作成できるようになります。)
              </Typography>
              <Box mt={2}>
                <form
                  id="cancel-meeting"
                  onSubmit={handleSubmit((values) => {
                    setOpenCancelModal(false);
                    onCancel(values);
                  })}
                >
                  <Label labelCol={12} label="キャンセル理由" required />
                  <TextField
                    labelCol={0}
                    name="cancelReason"
                    control={control}
                    maxLength={200}
                    multiline
                    minRows={7}
                    placeholder="キャンセル理由"
                  />
                </form>
              </Box>
            </Box>
          }
        />
      )}
    </Dialog>
  );
};

export default NewCaseModal;

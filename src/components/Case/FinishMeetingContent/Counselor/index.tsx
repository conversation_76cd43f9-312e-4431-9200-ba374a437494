/* eslint-disable import/extensions */
import { Box, Divider, Link, Stack, Typography } from '@mui/material';
import CouponTag from 'components/Case/CouponTag';
import dayjs from 'dayjs';
import { t } from 'i18n';
import type { IMeetingsItem } from 'models/case/interface';
import type { IMeetingType } from 'models/consultation/interface';
import { IMeetingTypeLabel } from 'models/consultation/interface';
import {
  MeetingType,
  MomentFormat,
  PAYMENT_METHOD_TEXT,
} from 'utils/constants';
import Helper from 'utils/helpers';

export interface FinishMeetingContentProps {
  data: IMeetingsItem;
  onRemoveCoupon?: () => void;
}
const FinishMeetingContent = ({
  data,
  onRemoveCoupon,
}: FinishMeetingContentProps) => {
  const { duration, unitPrice, discount, couponInfo } = data.paymentInfo;
  const isShowExtendMenu = data.extendMenus && data.extendMenus.length > 0;
  const extendMenuDuration = data.extendMenus && data.extendMenus[0]?.duration;
  const extendMenuPrice = data.extendMenus && data.extendMenus[0]?.price;
  const basicPrice = Helper.addComma(
    Helper.getBasicPrice(unitPrice, duration) + (extendMenuPrice || 0),
  );

  return (
    <div>
      <Typography fontSize={14} textAlign="center" mb={3}>
        {t('finishMeeting.paymentCompletedContent')}
      </Typography>
      <Box bgcolor="#F6F8F9" borderRadius={2} border="1px solid #E4EAED">
        <Box
          display="grid"
          p="24px 24px 16px"
          sx={{ gridTemplateColumns: 'max-content auto', gridGap: '16px' }}
        >
          <Typography fontWeight="bold">
            {t('finishMeeting.interviewDate')}
          </Typography>
          <Typography textAlign="right">
            {data.finalizedDate &&
              dayjs(data.finalizedDate).format(
                MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
              )}
          </Typography>
          <Typography fontWeight="bold">メニュー</Typography>
          <Box display="flex" justifyContent="flex-end">
            <Typography>{data.paymentInfo.duration}分</Typography>
            &nbsp;&nbsp;
            {'-'}
            &nbsp;&nbsp;
            <Typography
              textOverflow="ellipsis"
              whiteSpace="nowrap"
              overflow="hidden"
              maxWidth={170}
            >
              {data.consultationMenu?.title}
            </Typography>
            &nbsp;&nbsp;
            {'-'}
            &nbsp;&nbsp;
            <Typography>
              {
                IMeetingTypeLabel[
                  data.consultationMenu?.meetingType as IMeetingType
                ]
              }
            </Typography>
          </Box>
          {isShowExtendMenu && (
            <>
              <Typography fontWeight="bold">延長メニュー</Typography>
              <Box display="flex" justifyContent="flex-end">
                <Typography>
                  {t('caseDetail.duration', {
                    number: extendMenuDuration,
                  })}
                </Typography>
                &nbsp;&nbsp;
                {'-'}
                &nbsp;&nbsp;
                <Typography>
                  {Helper.addComma(extendMenuPrice || 0)}円
                </Typography>
              </Box>
            </>
          )}
          <Typography fontWeight="bold">
            {t('finishMeeting.interviewMethod')}
          </Typography>
          <Box>
            <Typography textAlign="right">
              {t('finishMeeting.interviewMeetingType')}
            </Typography>
            {data.meetingUrl && data.type === MeetingType.ONLINE && (
              <Link
                mt={1}
                display="block"
                fontSize={14}
                href={data.meetingUrl}
                target="_blank"
                underline="hover"
                className="line-clamp"
                style={{ textAlign: 'right' }}
              >
                {data.meetingUrl}
              </Link>
            )}
          </Box>
        </Box>
        <Divider />
        <Box
          display="grid"
          p="24px 24px 16px"
          sx={{ gridTemplateColumns: 'max-content auto', gridGap: '16px' }}
        >
          <Typography fontWeight="bold">
            {t('finishMeeting.paymentMethod')}
          </Typography>
          <Typography textAlign="right">
            {PAYMENT_METHOD_TEXT.CREDIT_CARD}
          </Typography>
          <Typography fontWeight="bold">合計金額</Typography>

          <Typography textAlign="right">
            {Helper.addComma(basicPrice)}円
          </Typography>
          <Stack direction="row" alignItems="center">
            <Typography mr={1} fontWeight="bold">
              クーポン値引
            </Typography>
            {couponInfo && (
              <CouponTag
                circleColor="#F6F8F9"
                onDelete={onRemoveCoupon}
                code={couponInfo.coupon.code}
              />
            )}
          </Stack>
          <Typography
            textAlign="right"
            color={couponInfo ? 'text.primary' : '#C1BCB3'}
          >
            {couponInfo
              ? `- ${Helper.addComma(couponInfo.couponAmount)}円`
              : 'なし'}
          </Typography>
          {!!discount && discount > 0 && (
            <>
              <Typography fontWeight="bold">割引金額</Typography>
              <Typography textAlign="right">
                {discount > 0 && '- '}
                {Helper.addComma(discount)}円
              </Typography>
            </>
          )}
          <Typography fontWeight="bold" alignSelf="center">
            {t('finishMeeting.interviewAmount')}
          </Typography>
          <Typography
            fontWeight={700}
            fontSize={24}
            color="heading"
            textAlign="right"
          >
            {Helper.addComma(data.paymentInfo.finalPrice || 0)}円
          </Typography>
        </Box>
      </Box>
    </div>
  );
};

export default FinishMeetingContent;

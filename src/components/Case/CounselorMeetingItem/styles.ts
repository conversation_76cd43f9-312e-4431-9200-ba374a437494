import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  caseInfo: {
    display: { xs: 'block', tablet: 'flex' },
    justifyContent: 'space-between',
    mb: '8px',
  },
  meetingItem: {
    borderRadius: '12px',
    border: (theme) => `solid 1px ${theme.palette.neutral4}`,
    p: '24px',
  },
  consumerNoteContainer: {
    borderRadius: 1,
    p: 2,
    mt: 2,
    '.truncate-text__ellipsis': {
      pb: 0,
    },
  },
  iconPayment: {
    svg: {
      display: 'block',
      width: 24,
      height: 16,
    },
  },
  cardInfo: {
    fontSize: 12,
    color: 'text.primary',
  },
  textPaymentInfo: {
    fontSize: 14,
    color: 'text.primary',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
  },
  icon: {
    mt: { tablet: '2px' },
    svg: { width: 24, height: 24 },
  },
  textUrlMeeting: {
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    fontSize: 14,
    maxHeight: 20,
    maxWidth: 200,
  },
  moreIcon: {
    p: 0,
  },
  dialogTitle: { p: { xs: 2, tablet: '32px' } },
  titleText: {
    fontSize: 14,
    color: 'text.primary',
    maxWidth: { xs: 80, tablet: 120, xl: 293 },
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    wordBreak: 'break-word',
    verticalAlign: 'bottom',
  },
  dialogContent: { px: '32px' },
  iconMeetingNote: {
    svg: {
      display: 'block',
      width: { xs: 20, tablet: 24 },
      height: { xs: 20, tablet: 24 },
    },
  },
} as Record<string, SxProps<Theme>>;

export default styles;

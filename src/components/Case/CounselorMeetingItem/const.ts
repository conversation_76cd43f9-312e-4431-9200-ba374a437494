import { t } from 'i18n';

export const cancelModalProps = {
  title: t('confirmMeeting.cancelMeeting'),
  cancelText: t('global.cancel'),
  confirmText: t('finishMeeting.confirmCancel'),
  contentAlign: 'left',
  confirmButtonProps: {
    form: 'cancel-form',
    type: 'submit',
  },
} as const;

export const finishModalProps = {
  title: t('finishMeeting.paymentCompletedTitle'),
  cancelText: t('global.cancel'),
  confirmText: t('finishMeeting.paymentCompleted'),
  contentAlign: 'left',
};

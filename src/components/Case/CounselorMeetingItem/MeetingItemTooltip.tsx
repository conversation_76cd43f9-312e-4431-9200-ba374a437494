import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box,
  Divider,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import ConfirmMeetingModal from 'components/Case/CounselorConfirmMeetingModal';
import FinishMeetingContent from 'components/Case/FinishMeetingContent/Counselor';
import PaymentErrorContent from 'components/Case/PaymentErrorContent';
import ConfirmModal from 'components/ConfirmModal/modal';
import { TextField } from 'components/Form';
import Label from 'components/Form/Label';
import MiuLink from 'components/Link';
import LoadingOverlay from 'components/LoadingOverlay';
import TruncateText from 'components/TruncateText';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import i18n, { t } from 'i18n';
import {
  ConfirmMeetingIcon,
  EmptyPaymentIcon,
  MoreIcon,
  PaymentCardIcon,
  ProcessPaymentIcon,
} from 'icons';
import { get } from 'lodash';
import caseQuery from 'models/case/query';
import type { ISlotMeeting } from 'models/shift/interface';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import CaseUtil from 'utils/caseUtil';
import {
  CaseStatusType,
  ConsultationMenuMeetingType,
  dropdownMenus,
  MeetingStatusType,
  MeetingType,
  MomentFormat,
  PAYMENT_TEXT,
  PaymentMethod,
  PaymentStatusType,
} from 'utils/constants';
import Helper from 'utils/helpers';
import type { InferType } from 'yup';
import { object, string } from 'yup';

import CouponTag from '../CouponTag';
import MeetingTag from '../MeetingTag';
import MeetingUrl from '../MeetingUrl';
import PaymentTooltipButton from '../PaymentTooltipButton';
import { cancelModalProps, finishModalProps } from './const';
import styles from './styles';

dayjs.extend(isSameOrAfter);

const DropdownMenu = dynamic(() => import('components/UI/DropdownMenu'));

const cancelSchema = object({
  cancelReason: string()
    .required(t('validation.requiredField'))
    .max(200, i18n.t('validation.maxLength', { number: 200 }))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
});

export interface ICounselorMeeting {
  meeting: ISlotMeeting;
}

const MeetingItemTooltip = ({ meeting }: ICounselorMeeting) => {
  const isWaitingCase =
    meeting.case.status === CaseStatusType.WAITING_ANWSER_FROM_PROVIDER;
  const [openMenuTooltip, setOpenMenuTooltip] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const handleClickMoreButton = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };
  const { push } = useRouter();
  const { setConfirmModal } = useGlobalState();
  const [openModalConfirmMeeting, setOpenModalConfirmMeeting] = useState(false);
  const {
    status: meetingStatus,
    meetingUrl,
    expectDate,
    finalizedDate,
  } = meeting;
  const meetingId = meeting._id;
  const {
    duration,
    finalPrice = 0,
    unitPrice,
    status: paymentStatus = PaymentStatusType.PENDING,
    method: paymentMethod,
    discount,
    couponInfo,
  } = meeting.paymentInfo || {};
  const menuTitle = meeting.consultationMenu?.title;
  const displayStatus = CaseUtil.getDisplayPaymentStatus({
    paymentStatus,
    meetingStatus,
  });
  const isShowExtendMenu =
    meeting.extendMenus && meeting.extendMenus.length > 0;
  const extendMenuDuration =
    meeting.extendMenus && meeting.extendMenus[0]?.duration;
  const extendMenuPrice = meeting.extendMenus && meeting.extendMenus[0]?.price;
  const basicPrice = Helper.addComma(
    Helper.getBasicPrice(unitPrice, duration) + (extendMenuPrice || 0),
  );
  const isFinishedMeeting = meetingStatus === MeetingStatusType.FINISHED;
  const isWaitingMeeting =
    meetingStatus === MeetingStatusType.WAITING_ANWSER_FROM_PROVIDER;
  const isDecidedMeeting =
    meetingStatus === MeetingStatusType.SCHEDULE_IS_DECIDED;
  const canRechargePayment =
    meetingStatus === MeetingStatusType.FINISHED &&
    paymentStatus === PaymentStatusType.ERROR;

  const isMeetingClosed =
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
      MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
    ].includes(meetingStatus) ||
    (meetingStatus === MeetingStatusType.FINISHED &&
      paymentStatus === PaymentStatusType.COMPLETED);
  const isDisableMeetingUrl =
    isFinishedMeeting ||
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
      MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
    ].includes(meetingStatus);

  const queryClient = useQueryClient();
  const { mutateAsync: confirmPayment, isLoading: isConfirming } = useMutate(
    caseQuery.confirmPayment,
  );
  const { mutateAsync: finishMeeting, isLoading: isFinishing } = useMutate(
    caseQuery.finishMeeting,
  );

  const [openCancelModal, setOpenCancelModal] = useState(false);
  const { control, handleSubmit, resetField } = useForm<
    InferType<typeof cancelSchema>
  >({
    resolver: yupResolver(cancelSchema),
    values: {
      cancelReason: `この度は面談のリクエストをいただきありがとうございました。
      \n大変申し訳ないのですが、リクエストいただいた面談日時の都合が合わないため面談をキャンセルさせていただきました。
      \nお手数をお掛けいたしますが、改めて別日でリクエストをいただけますと幸いです。引き続きよろしくお願いいたします。`,
    },
  });

  useEffect(() => {
    if (openCancelModal) {
      resetField('cancelReason', { keepError: false });
    }
  }, [openCancelModal, resetField]);

  const { mutateAsync: cancelMeeting, isLoading: isCanceling } = useMutate<{
    status: MeetingStatusType;
    meetingId: string;
  }>(caseQuery.cancelMeeting);

  const refetchCurrentCalendar = () => {
    queryClient.refetchQueries({
      queryKey: ['currentUser', 'calendars'],
      type: 'active',
    });
  };
  const handleRechargePayment = async () => {
    try {
      await confirmPayment({ meetingId: meeting._id });
      refetchCurrentCalendar();
    } catch (error) {
      if (get(error, 'code') === 'MEETING_PAYMENT_ERROR') {
        setConfirmModal({
          icon: 'error',
          content: (
            <PaymentErrorContent
              finalizedDate={expectDate[0].start}
              cardError
            />
          ),
          confirmText: 'OK',
          hideCancelButton: true,
        });
      }
      if (
        get(error, 'code') === 'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT'
      ) {
        setConfirmModal({
          icon: 'error',
          content: <PaymentErrorContent finalizedDate={expectDate[0].start} />,
          confirmText: 'OK',
          hideCancelButton: true,
        });
      }
    }
  };

  const handleFinishMeeting = async () => {
    try {
      await finishMeeting({ meetingId });
      await confirmPayment({ meetingId });
      refetchCurrentCalendar();
    } catch (error) {
      if (get(error, 'code') === 'MEETING_PAYMENT_ERROR') {
        setConfirmModal({
          icon: 'error',
          content: (
            <PaymentErrorContent
              finalizedDate={expectDate[0].start}
              cardError
            />
          ),
          confirmText: 'OK',
          hideCancelButton: true,
          onConfirm: () => refetchCurrentCalendar(),
        });
      }
      if (
        get(error, 'code') === 'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT'
      ) {
        setConfirmModal({
          icon: 'error',
          content: <PaymentErrorContent finalizedDate={expectDate[0].start} />,
          confirmText: 'OK',
          hideCancelButton: true,
          onConfirm: () => refetchCurrentCalendar(),
        });
      }
    }
  };

  const handleCancelMeeting: SubmitHandler<
    InferType<typeof cancelSchema>
  > = async (values) => {
    setOpenCancelModal(false);
    try {
      await cancelMeeting({
        status: MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
        meetingId,
        ...values,
      });
    } finally {
      refetchCurrentCalendar();
    }
  };

  const handleMeetingAction = (key: string) => {
    setAnchorEl(null);
    if (key === 'edit') {
      push({
        pathname: '/counselor/my-page/cases/[caseId]/edit/[meetingId]',
        query: {
          caseId: meeting.case._id,
          meetingId: meeting._id,
        },
      });
    }
    if (key === 'cancel') {
      setOpenCancelModal(true);
    }
    if (key === 'finish') {
      setConfirmModal({
        ...finishModalProps,
        onConfirm: handleFinishMeeting,
        dialogProps: { sx: { zIndex: 1501 } },
        content: (
          <FinishMeetingContent
            data={{ ...meeting, type: MeetingType.ONLINE }}
          />
        ),
      });
    }
  };
  return (
    <Box width={1} position="relative" onClick={(e) => e.stopPropagation()}>
      <Box
        sx={styles.meetingItem}
        bgcolor={isMeetingClosed ? 'backgroundColor' : 'white'}
      >
        <Box sx={styles.caseInfo}>
          <MiuLink
            underline="none"
            href={`/counselor/my-page/cases/${meeting.case._id}`}
          >
            <Typography
              fontWeight="bold"
              fontSize={18}
              color="heading"
              className="line-clamp"
            >
              {meeting.consumer.fullName}
            </Typography>
          </MiuLink>
          <MeetingTag status={meeting.status} />
        </Box>
        <Box display="flex">
          <Typography sx={styles.textPaymentInfo}>
            {menuTitle && (
              <Tooltip
                title={menuTitle}
                open={openMenuTooltip}
                onClick={(e) => {
                  e.stopPropagation();
                  setOpenMenuTooltip(true);
                }}
                onMouseOver={() => setOpenMenuTooltip(true)}
                onMouseLeave={() => setOpenMenuTooltip(false)}
              >
                <Typography sx={styles.titleText}>{menuTitle}</Typography>
              </Tooltip>
            )}
            {menuTitle && ' - '}
            {t('caseDetail.duration', {
              number: duration,
            })}
            {/* &nbsp;&nbsp;
            {'•'}
            &nbsp;&nbsp;
            {IMeetingTypeLabel[menuMeetingtype]} */}
            {meetingUrl && (
              <>
                &nbsp;&nbsp;{'•'}
                &nbsp;&nbsp;オンライン面談:&nbsp;&nbsp;
              </>
            )}
          </Typography>
          {meetingUrl && (
            <MeetingUrl
              meetingUrl={meetingUrl}
              isDisableMeetingUrl={isDisableMeetingUrl}
            />
          )}
        </Box>
        {isShowExtendMenu && (
          <Box display="flex">
            <Typography sx={styles.textPaymentInfo}>
              延長メニュー&nbsp;&nbsp;:&nbsp;&nbsp;
              {t('caseDetail.duration', {
                number: extendMenuDuration,
              })}
              &nbsp;-&nbsp;{Helper.addComma(extendMenuPrice || 0)}円
            </Typography>
          </Box>
        )}
        {meeting?.consultationMenu?.meetingType && (
          <Box display="flex">
            <Typography
              // fontWeight="bold"
              fontSize={14}
              color="text.primary"
              className="line-clamp"
              mt={1}
            >
              {
                ConsultationMenuMeetingType[
                  `${
                    meeting?.consultationMenu?.meetingType || ''
                  }` as keyof typeof ConsultationMenuMeetingType
                ] as string
              }
            </Typography>
          </Box>
        )}
        {meeting.consumerNote && (
          <Box
            sx={styles.consumerNoteContainer}
            bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
            border="1px solid #DBE3E7"
          >
            <Typography fontSize={14} fontWeight="bold" mb={1} color="heading">
              {t('caseDetail.customerNote')}
            </Typography>
            <TruncateText
              textProps={{
                sx: {
                  whiteSpace: 'pre-line',
                  color: 'heading',
                  fontSize: 14,
                },
              }}
              lines={1}
              text={meeting.consumerNote}
            />
          </Box>
        )}
        <Divider sx={{ my: { xs: 2, tablet: 3 } }} />
        <Box
          sx={styles.consumerNoteContainer}
          bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
          border="1px solid #DBE3E7"
          mb={2}
          color="text.primary"
        >
          <Stack flexDirection="row" gap={1} justifyContent="space-between">
            <Typography fontSize={14}>合計金額</Typography>
            <Typography fontSize={14} fontWeight={700}>
              {Helper.addComma(basicPrice)}円
            </Typography>
          </Stack>
          {couponInfo && (
            <Stack
              flexDirection="row"
              gap={1}
              justifyContent="space-between"
              mt={1}
              flexWrap="wrap"
            >
              <Stack flexDirection="row">
                <Typography fontSize={14} mr={1}>
                  クーポン値引
                </Typography>
                <CouponTag
                  code={couponInfo.coupon.code}
                  wrapperProps={{ mr: '6px', height: 'fit-content' }}
                  circleColor="#F6F8F9"
                />
                <PaymentTooltipButton />
              </Stack>
              <Typography fontSize={14}>
                - {Helper.addComma(couponInfo.couponAmount || 0)}円
              </Typography>
            </Stack>
          )}
          {!!discount && discount > 0 && (
            <Stack flexDirection="row" justifyContent="space-between" mt={1}>
              <Typography fontSize={14}>割引金額</Typography>
              <Typography fontSize={14}>
                - {Helper.addComma(discount)}円
              </Typography>
            </Stack>
          )}
        </Box>
        <Box sx={styles.caseInfo}>
          <Box>
            <Stack direction="row" spacing={1} mb={1} alignItems="center">
              <Typography fontWeight={500} color="text.primary">
                {t('caseDetail.finalPrice')}
                <Typography
                  ml="4px"
                  fontWeight={700}
                  component="span"
                  color="heading"
                >
                  {Helper.addComma(finalPrice)}円
                </Typography>
              </Typography>
              <PaymentTooltipButton
                title="お支払い予定金額に関して"
                content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
              />
            </Stack>
            <Stack direction="row" spacing={1}>
              <Box sx={styles.iconPayment}>
                {paymentMethod === PaymentMethod.CREDIT_CARD && (
                  <PaymentCardIcon />
                )}
              </Box>
              <Typography sx={styles.cardInfo}>
                {paymentMethod === PaymentMethod.CREDIT_CARD &&
                  t('caseDetail.creditCard')}
              </Typography>
              <Typography sx={styles.cardInfo}>{'•'}</Typography>
              {paymentStatus ? (
                <Typography
                  fontSize={12}
                  fontWeight={500}
                  color={PAYMENT_TEXT[displayStatus].color}
                >
                  {PAYMENT_TEXT[displayStatus].text}
                </Typography>
              ) : (
                <Typography
                  fontSize={12}
                  fontWeight={500}
                  color={PAYMENT_TEXT[PaymentStatusType.PENDING].color}
                >
                  {PAYMENT_TEXT[PaymentStatusType.PENDING].text}
                </Typography>
              )}
            </Stack>
          </Box>
          <Stack direction="row" spacing={1} alignItems="center">
            {isWaitingMeeting && !isWaitingCase && (
              <IconButton
                className="whiteOutlined"
                size="medium"
                onClick={() => setOpenModalConfirmMeeting(true)}
              >
                <ConfirmMeetingIcon />
              </IconButton>
            )}
            {isDecidedMeeting && (
              <IconButton
                className="whiteOutlined"
                disabled={dayjs(finalizedDate).isSameOrAfter(dayjs(), 'minute')}
                onClick={() => handleMeetingAction('finish')}
              >
                <ProcessPaymentIcon />
              </IconButton>
            )}
            {canRechargePayment && (
              <IconButton
                className="whiteOutlined"
                size="medium"
                onClick={handleRechargePayment}
              >
                <EmptyPaymentIcon />
              </IconButton>
            )}
            {!isMeetingClosed && !isWaitingCase && (
              <IconButton
                size="medium"
                sx={styles.moreIcon}
                onClick={handleClickMoreButton}
                id="more-button"
              >
                <MoreIcon />
              </IconButton>
            )}
          </Stack>
        </Box>
      </Box>
      <DropdownMenu
        PaperProps={{
          sx: { px: 1, zIndex: 10000 },
        }}
        menus={dropdownMenus.map((item) => {
          if (item.key === 'cancel' && canRechargePayment) {
            return {
              ...item,
              disabled: true,
            };
          }
          return item;
        })}
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        onMenuItemClick={handleMeetingAction}
      />
      {isWaitingMeeting && (
        <ConfirmMeetingModal
          data={{ ...meeting, type: MeetingType.ONLINE }}
          open={openModalConfirmMeeting}
          setOpenModalConfirmMeeting={setOpenModalConfirmMeeting}
          onSuccess={() => {
            queryClient.refetchQueries({
              queryKey: ['currentUser', 'calendars'],
              type: 'active',
            });
          }}
          onClose={() => setOpenModalConfirmMeeting(false)}
        />
      )}
      <ConfirmModal
        dialogProps={{ sx: { zIndex: 1501 } }}
        open={openCancelModal}
        {...cancelModalProps}
        onCancel={() => setOpenCancelModal(false)}
        confirmButtonProps={{ form: 'cancel-meeting', type: 'submit' }}
        content={
          <Box>
            <Typography
              component="span"
              fontSize={14}
              textAlign="center"
              display="flex"
              justifyContent="center"
            >
              <Typography
                fontSize={14}
                fontWeight="bold"
                color="text.primary"
                component="span"
                textAlign="center"
              >
                {`${dayjs(meeting.startSlot).format(
                  MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                )}`}
              </Typography>
              &nbsp;&nbsp;の面談をキャンセルします
            </Typography>
            <Box mt={2}>
              <form
                id="cancel-meeting"
                onSubmit={handleSubmit(handleCancelMeeting)}
              >
                <Label labelCol={12} label="キャンセル理由" required />
                <TextField
                  labelCol={0}
                  name="cancelReason"
                  control={control}
                  maxLength={200}
                  multiline
                  minRows={7}
                  placeholder="キャンセル理由"
                />
              </form>
            </Box>
          </Box>
        }
      />
      <LoadingOverlay visible={isConfirming || isFinishing || isCanceling} />
    </Box>
  );
};

export default MeetingItemTooltip;

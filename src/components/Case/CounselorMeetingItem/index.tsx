import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box,
  Button,
  Divider,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import ConfirmModal from 'components/ConfirmModal/modal';
import { TextField } from 'components/Form';
import Label from 'components/Form/Label';
import LoadingOverlay from 'components/LoadingOverlay';
import TruncateText from 'components/TruncateText';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import useGlobalState from 'hooks/useGlobalState';
import useMutate from 'hooks/useMutate';
import i18n, { t } from 'i18n';
import {
  ConfirmMeetingIcon,
  EmptyPaymentIcon,
  MeetingNoteIcon,
  MenuExtend,
  MoreIcon,
  PaymentCardIcon,
  ProcessPaymentIcon,
} from 'icons';
import { get } from 'lodash';
import type { IMeetingsItem } from 'models/case/interface';
import caseQuery from 'models/case/query';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import CaseUtil from 'utils/caseUtil';
import {
  ConsultationMenuMeetingType,
  dropdownMenus,
  MeetingStatusType,
  MeetingType,
  MomentFormat,
  PAYMENT_TEXT,
  PaymentStatusType,
} from 'utils/constants';
import Helper from 'utils/helpers';
import type { InferType } from 'yup';
import { object, string } from 'yup';

import CouponTag from '../CouponTag';
import FinishMeetingContent from '../FinishMeetingContent/Counselor';
import { cancelModalProps, finishModalProps } from '../LawyerMeetingItem/const';
import MeetingTag from '../MeetingTag';
import MeetingUrl from '../MeetingUrl';
import PaymentErrorContent from '../PaymentErrorContent';
import PaymentTooltipButton from '../PaymentTooltipButton';
import styles from './styles';

const ConfirmMeetingModal = dynamic(
  () => import('../CounselorConfirmMeetingModal'),
);

dayjs.extend(isSameOrAfter);

const cancelSchema = object({
  cancelReason: string()
    .required(t('validation.requiredField'))
    .max(200, i18n.t('validation.maxLength', { number: 200 }))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
});

const DropdownMenu = dynamic(() => import('components/UI/DropdownMenu'));

export interface ICounselorMeeting {
  meeting: IMeetingsItem;
  loading?: boolean;
  refetch: () => void;
  refetchCaseDetail: () => void;
  refetchMeetingList: () => void;
  hasReview: boolean;
}

const CounselorMeetingItem = ({
  meeting,
  loading,
  refetch,
  refetchCaseDetail,
  refetchMeetingList,
}: ICounselorMeeting) => {
  const { query, push } = useRouter();
  const caseId = query.caseId as string;
  const { setConfirmModal } = useGlobalState();
  const [openMenuTooltip, setOpenMenuTooltip] = useState(false);
  const [openRemoveCouponModal, setOpenRemoveCouponModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openModalConfirmMeeting, setOpenModalConfirmMeeting] = useState(false);
  const [openFinishMeeting, setOpenFinishMeeting] = useState(false);
  const handleClickMoreButton = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    setAnchorEl(event.currentTarget);
  };

  const extendMenuDuration =
    meeting.extendMenus && meeting.extendMenus[0]?.duration;
  const extendMenuPrice = meeting.extendMenus && meeting.extendMenus[0]?.price;
  const {
    status: meetingStatus,
    meetingUrl,
    type,
    finalizedDate,
    _id: meetingId,
  } = meeting;
  const {
    duration,
    finalPrice = 0,
    unitPrice,
    status: paymentStatus = PaymentStatusType.PENDING,
    discount,
  } = meeting.paymentInfo;
  const menuTitle = meeting.consultationMenu?.title;
  const meetingType = meeting.consultationMenu?.meetingType;
  const displayStatus = CaseUtil.getDisplayPaymentStatus({
    paymentStatus,
    meetingStatus,
  });
  const basicPrice = Helper.addComma(
    Helper.getBasicPrice(unitPrice, duration) + (extendMenuPrice || 0),
  );

  const isFinishedMeeting = meetingStatus === MeetingStatusType.FINISHED;
  const isWaitingMeeting =
    meetingStatus === MeetingStatusType.WAITING_ANWSER_FROM_PROVIDER;
  const isDecidedMeeting =
    meetingStatus === MeetingStatusType.SCHEDULE_IS_DECIDED;
  const canRechargePayment =
    meetingStatus === MeetingStatusType.FINISHED &&
    paymentStatus === PaymentStatusType.ERROR;

  const isMeetingClosed =
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
      MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
    ].includes(meetingStatus) ||
    (meetingStatus === MeetingStatusType.FINISHED &&
      paymentStatus === PaymentStatusType.COMPLETED);

  const isShowMeetingUrl = meetingUrl && type === MeetingType.ONLINE;
  const isShowExtendMenu =
    meeting.extendMenus && meeting.extendMenus.length > 0;
  const isDisableMeetingUrl =
    isFinishedMeeting ||
    [
      MeetingStatusType.CANCELED_BY_CONSUMER,
      MeetingStatusType.CANCELED_BY_PROVIDER,
      MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR,
    ].includes(meetingStatus);

  const { couponInfo } = meeting.paymentInfo;

  const { mutateAsync: removeCoupon } = useMutate(
    caseQuery.removeCoupon(meetingId),
  );

  const { mutateAsync: confirmPayment, isLoading: isConfirming } = useMutate(
    caseQuery.confirmPayment,
  );
  const { mutateAsync: finishMeeting, isLoading: isFinishing } = useMutate(
    caseQuery.finishMeeting,
  );

  const [openCancelModal, setOpenCancelModal] = useState(false);
  const { control, handleSubmit, resetField } = useForm<
    InferType<typeof cancelSchema>
  >({
    resolver: yupResolver(cancelSchema),
    values: {
      cancelReason: `この度は面談のリクエストをいただきありがとうございました。
      \n大変申し訳ないのですが、リクエストいただいた面談日時の都合が合わないため面談をキャンセルさせていただきました。
      \nお手数をお掛けいたしますが、改めて別日でリクエストをいただけますと幸いです。引き続きよろしくお願いいたします。`,
    },
  });
  const { mutateAsync: cancelMeeting, isLoading: isCanceling } = useMutate<{
    status: MeetingStatusType;
    meetingId: string;
    cancelReason: string;
  }>(caseQuery.cancelMeeting);

  useEffect(() => {
    if (openCancelModal) {
      resetField('cancelReason', { keepError: false });
    }
  }, [openCancelModal, resetField]);

  const handleFinishMeeting = async () => {
    try {
      await finishMeeting({ meetingId });
      await confirmPayment({ meetingId });
      refetchCaseDetail();
      setOpenFinishMeeting(false);
      refetch();
    } catch (error) {
      if (get(error, 'code') === 'MEETING_PAYMENT_ERROR') {
        setConfirmModal({
          icon: 'warning',
          content: (
            <PaymentErrorContent finalizedDate={finalizedDate} cardError />
          ),
          confirmText: 'OK',
          hideCancelButton: true,
          onConfirm: () => {
            refetch();
            refetchCaseDetail();
          },
        });
      }
      if (
        get(error, 'code') === 'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT'
      ) {
        setConfirmModal({
          icon: 'warning',
          content: <PaymentErrorContent finalizedDate={finalizedDate} />,
          confirmText: 'OK',
          hideCancelButton: true,
          onConfirm: () => {
            refetch();
            refetchCaseDetail();
          },
        });
      }
    }
  };

  const handleRechargePayment = async () => {
    try {
      await confirmPayment({ meetingId: meeting._id });
      refetch();
      refetchCaseDetail();
    } catch (error) {
      if (get(error, 'code') === 'MEETING_PAYMENT_ERROR') {
        setConfirmModal({
          icon: 'warning',
          content: (
            <PaymentErrorContent finalizedDate={finalizedDate} cardError />
          ),
          confirmText: 'OK',
          hideCancelButton: true,
        });
      }
      if (
        get(error, 'code') === 'MEETING_FINAL_PRICE_WITH_CREDIT_CARD_OVER_LIMIT'
      ) {
        setConfirmModal({
          icon: 'warning',
          content: <PaymentErrorContent finalizedDate={finalizedDate} />,
          confirmText: 'OK',
          hideCancelButton: true,
        });
      }
    }
  };

  const handleCancelMeeting: SubmitHandler<
    InferType<typeof cancelSchema>
  > = async (values) => {
    const status = MeetingStatusType.CANCELED_BY_PROVIDER_COUNSELOR;
    setOpenCancelModal(false);
    cancelMeeting(
      { status, meetingId, ...values },
      {
        onSuccess: () => {
          refetchCaseDetail();
          refetch();
        },
      },
    );
  };

  const handleMeetingAction = (key: string) => {
    setAnchorEl(null);
    if (key === 'finish') {
      setOpenFinishMeeting(true);
    }
    if (key === 'cancel') {
      setOpenCancelModal(true);
    }
    if (key === 'edit') {
      push({
        pathname: '/counselor/my-page/cases/[caseId]/edit/[meetingId]',
        query: {
          caseId,
          meetingId,
        },
      });
    }
  };

  return (
    <Box>
      <Box mt={2} position="relative">
        <Box
          sx={styles.meetingItem}
          bgcolor={isMeetingClosed ? 'backgroundColor' : 'white'}
        >
          <Box sx={styles.caseInfo}>
            {finalizedDate ? (
              <Typography fontWeight="bold" fontSize={18} color="heading">
                {dayjs(finalizedDate).format(
                  MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                )}
              </Typography>
            ) : (
              <Typography fontWeight="bold" fontSize={18} color="hint">
                {dayjs(meeting.expectDate[0]?.start).format(
                  MomentFormat.JP_YEAR_MONTH_DATE_HOUR_MS,
                )}
              </Typography>
            )}
            <MeetingTag status={meeting.status} />
          </Box>
          <Box display="flex" alignItems="center">
            <Box
              sx={styles.iconMeetingNote}
              marginRight={{ xs: '4px', tablet: '8px' }}
            >
              <MeetingNoteIcon />
            </Box>
            <Typography sx={styles.textPaymentInfo}>
              {menuTitle && (
                <Tooltip
                  title={menuTitle}
                  open={openMenuTooltip}
                  onClick={() => setOpenMenuTooltip(true)}
                  onMouseOver={() => setOpenMenuTooltip(true)}
                  onMouseLeave={() => setOpenMenuTooltip(false)}
                >
                  <Typography sx={styles.titleText}>{menuTitle}</Typography>
                </Tooltip>
              )}
              {menuTitle && ' - '}
              {t('caseDetail.duration', {
                number: duration,
              })}
              {meetingType ? (
                <>
                  &nbsp;&nbsp;
                  {'•'}
                  &nbsp;&nbsp;
                  {`${
                    ConsultationMenuMeetingType[
                      `${
                        meeting?.consultationMenu?.meetingType || ''
                      }` as keyof typeof ConsultationMenuMeetingType
                    ] as string
                  }`}
                </>
              ) : (
                <></>
              )}
              {isShowMeetingUrl && (
                <>
                  {' '}
                  &nbsp;&nbsp;
                  {'•'}
                  &nbsp;&nbsp;オンライン面談:&nbsp;&nbsp;
                </>
              )}
            </Typography>
            {isShowMeetingUrl && (
              <MeetingUrl
                meetingUrl={meetingUrl}
                isDisableMeetingUrl={isDisableMeetingUrl}
              />
            )}
          </Box>
          {isShowExtendMenu && (
            <Box
              display="flex"
              alignItems="center"
              sx={{ marginTop: { xs: '8px', tablet: '12px' } }}
            >
              <Box
                sx={styles.iconMeetingNote}
                marginRight={{ xs: '4px', tablet: '8px' }}
              >
                <MenuExtend />
              </Box>
              <Typography sx={styles.textPaymentInfo}>
                延長メニュー &nbsp;&nbsp;
                {'•'}
                &nbsp;&nbsp;
                {t('caseDetail.duration', {
                  number: extendMenuDuration,
                })}
                &nbsp;
                {'-'}
                &nbsp;
                {Helper.addComma(extendMenuPrice || 0)}円
              </Typography>
            </Box>
          )}
          {meeting.cancelReason && (
            <Box
              sx={styles.consumerNoteContainer}
              bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
              border="1px solid #DBE3E7"
            >
              <Typography
                fontSize={14}
                fontWeight="bold"
                mb={1}
                color="#db5a42"
              >
                キャンセル理由
              </Typography>
              <TruncateText
                textProps={{
                  sx: {
                    whiteSpace: 'pre-line',
                    color: 'heading',
                    fontSize: 14,
                  },
                }}
                lines={2}
                text={meeting.cancelReason}
              />
            </Box>
          )}
          {meeting.consumerNote && (
            <Box
              sx={styles.consumerNoteContainer}
              bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
              border="1px solid #DBE3E7"
            >
              <Typography
                fontSize={14}
                fontWeight="bold"
                mb={1}
                color="heading"
              >
                {t('caseDetail.customerNote')}
              </Typography>
              <TruncateText
                textProps={{
                  sx: {
                    whiteSpace: 'pre-line',
                    color: 'heading',
                    fontSize: 14,
                  },
                }}
                lines={2}
                text={meeting.consumerNote}
              />
            </Box>
          )}
          <Divider sx={{ my: { xs: 2, tablet: 3 } }} />
          <Box
            sx={styles.consumerNoteContainer}
            bgcolor={isMeetingClosed ? '#EDF1F3' : '#F6F8F9'}
            border="1px solid #DBE3E7"
            mb={2}
          >
            <Stack flexDirection="row" gap={1} justifyContent="space-between">
              <Typography fontSize={14}>合計金額</Typography>
              <Typography fontSize={14} fontWeight={700}>
                {Helper.addComma(basicPrice)}円
              </Typography>
            </Stack>
            {couponInfo && (
              <Stack
                flexDirection="row"
                gap={1}
                justifyContent="space-between"
                mt={1}
                flexWrap="wrap"
              >
                <Stack flexDirection="row">
                  <Typography fontSize={14} mr={1}>
                    クーポン値引
                  </Typography>
                  <CouponTag
                    code={couponInfo.coupon.code}
                    wrapperProps={{ mr: '6px', height: 'fit-content' }}
                    circleColor="#F6F8F9"
                  />
                  <PaymentTooltipButton />
                </Stack>
                <Typography fontSize={14}>
                  - {Helper.addComma(couponInfo.couponAmount || 0)}円
                </Typography>
              </Stack>
            )}
            {!!discount && discount > 0 && (
              <Stack flexDirection="row" justifyContent="space-between" mt={1}>
                <Typography fontSize={14}>割引金額</Typography>
                <Typography fontSize={14}>
                  - {Helper.addComma(discount)}円
                </Typography>
              </Stack>
            )}
          </Box>
          <Box sx={styles.caseInfo}>
            <Box>
              <Stack direction="row" spacing="2px" mb={1} alignItems="center">
                <Typography fontWeight={500} color="text.primary">
                  {t('caseDetail.finalPrice')}
                  <Typography
                    ml="4px"
                    fontWeight={700}
                    component="span"
                    color="heading"
                  >
                    {Helper.addComma(finalPrice)}円
                  </Typography>
                </Typography>
                <PaymentTooltipButton
                  title="お支払い予定金額に関して"
                  content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
                />
              </Stack>
              <Stack direction="row" spacing={1}>
                <Box sx={styles.iconPayment}>
                  <PaymentCardIcon />
                </Box>
                <Typography sx={styles.cardInfo}>
                  {t('caseDetail.creditCard')}
                </Typography>
                <Typography sx={styles.cardInfo}>{'•'}</Typography>
                {paymentStatus ? (
                  <Typography
                    fontSize={12}
                    fontWeight={500}
                    color={PAYMENT_TEXT[displayStatus].color}
                  >
                    {PAYMENT_TEXT[displayStatus].text}
                  </Typography>
                ) : (
                  <Typography
                    fontSize={12}
                    fontWeight={500}
                    color={PAYMENT_TEXT[PaymentStatusType.PENDING].color}
                  >
                    {PAYMENT_TEXT[PaymentStatusType.PENDING].text}
                  </Typography>
                )}
              </Stack>
            </Box>
            <Stack direction="row" spacing={1} alignItems="center">
              {isWaitingMeeting && (
                <Button
                  variant="whiteOutlined"
                  size="medium"
                  startIcon={<ConfirmMeetingIcon />}
                  style={{
                    textTransform: 'initial',
                  }}
                  onClick={() => setOpenModalConfirmMeeting(true)}
                >
                  {t('confirmMeeting.titleModal')}
                </Button>
              )}
              {isDecidedMeeting && (
                <Button
                  variant="whiteOutlined"
                  startIcon={<ProcessPaymentIcon />}
                  disabled={dayjs(finalizedDate).isSameOrAfter(
                    dayjs(),
                    'minute',
                  )}
                  onClick={() => handleMeetingAction('finish')}
                >
                  {t('caseDetail.finishMeeting')}
                </Button>
              )}
              {canRechargePayment && (
                <Button
                  variant="whiteOutlined"
                  size="medium"
                  startIcon={<EmptyPaymentIcon />}
                  style={{ textTransform: 'initial' }}
                  onClick={handleRechargePayment}
                >
                  {t('caseDetail.recharge')}
                </Button>
              )}
              {!isMeetingClosed && (
                <IconButton
                  size="medium"
                  sx={styles.moreIcon}
                  onClick={handleClickMoreButton}
                  id="more-button"
                >
                  <MoreIcon />
                </IconButton>
              )}
            </Stack>
          </Box>
        </Box>
        <DropdownMenu
          menus={dropdownMenus.map((item) => {
            if (canRechargePayment) {
              return {
                ...item,
                disabled: true,
              };
            }
            return item;
          })}
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
          onMenuItemClick={handleMeetingAction}
        />
        {isWaitingMeeting && (
          <ConfirmMeetingModal
            data={meeting}
            open={openModalConfirmMeeting}
            setOpenModalConfirmMeeting={setOpenModalConfirmMeeting}
            onSuccess={refetch}
            onClose={() => setOpenModalConfirmMeeting(false)}
          />
        )}
        <LoadingOverlay
          visible={loading || isConfirming || isFinishing || isCanceling}
        />
      </Box>
      <ConfirmModal
        open={openRemoveCouponModal}
        title="クーポンの削除"
        content="このクーポンを削除しますか？"
        onCancel={() => setOpenRemoveCouponModal(false)}
        onConfirm={() => {
          setOpenRemoveCouponModal(false);
          removeCoupon(null, {
            onSuccess: () => refetchMeetingList(),
          });
        }}
      />
      <ConfirmModal
        open={openCancelModal}
        {...cancelModalProps}
        onCancel={() => setOpenCancelModal(false)}
        confirmButtonProps={{ form: 'cancel-meeting', type: 'submit' }}
        content={
          <Box>
            <Typography
              component="span"
              fontSize={14}
              textAlign="center"
              display="flex"
              justifyContent="center"
            >
              <Typography
                fontSize={14}
                fontWeight="bold"
                color="text.primary"
                component="span"
                textAlign="center"
              >
                {finalizedDate
                  ? `${dayjs(finalizedDate).format(
                      MomentFormat.JP_YEAR_MONTH_DATE_TIME,
                    )}`
                  : `${dayjs(meeting.expectDate[0]?.start).format(
                      MomentFormat.JP_YEAR_MONTH_DATE_TIME,
                    )}`}
              </Typography>
              &nbsp;&nbsp;の面談をキャンセルします
            </Typography>
            <Box mt={2}>
              <form
                id="cancel-meeting"
                onSubmit={handleSubmit(handleCancelMeeting)}
              >
                <Label labelCol={12} label="キャンセル理由" required />
                <TextField
                  labelCol={0}
                  name="cancelReason"
                  control={control}
                  maxLength={200}
                  multiline
                  minRows={7}
                  placeholder="キャンセル理由"
                />
              </form>
            </Box>
          </Box>
        }
      />
      <ConfirmModal
        {...finishModalProps}
        open={openFinishMeeting}
        onConfirm={handleFinishMeeting}
        confirmLoading={isFinishing}
        onCancel={() => setOpenFinishMeeting(false)}
        content={
          <FinishMeetingContent
            data={meeting}
            onRemoveCoupon={() => {
              setOpenRemoveCouponModal(true);
            }}
          />
        }
      />
    </Box>
  );
};

export default CounselorMeetingItem;

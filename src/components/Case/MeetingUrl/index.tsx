import { I<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Stack, Tooltip } from '@mui/material';
import { CopyIcon } from 'icons';
import { useState } from 'react';
import Helper from 'utils/helpers';

import styles from './styles';

export interface IMeetingUrl {
  meetingUrl: string;
  isDisableMeetingUrl: boolean;
}

const MeetingUrl = ({ meetingUrl, isDisableMeetingUrl }: IMeetingUrl) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <Stack
      direction="row"
      spacing={{ xs: '4px', tablet: 1 }}
      flex={1}
      alignItems="center"
    >
      <Link
        underline="hover"
        href={Helper.formatUrl(meetingUrl)}
        target="_blank"
        className="line-clamp"
        sx={
          [
            styles.textUrlMeeting,
            {
              pointerEvents: isDisableMeetingUrl ? 'none' : 'all',
            },
          ] as never
        }
        color={isDisableMeetingUrl ? 'icon' : 'primary.main'}
      >
        {meetingUrl}
      </Link>
      <Tooltip
        onClose={() => setShowTooltip(false)}
        open={showTooltip}
        title="コピーしました"
      >
        <IconButton
          size="small"
          onClick={() => {
            setShowTooltip(true);
            navigator.clipboard.writeText(meetingUrl);
          }}
          disableRipple
          disabled={isDisableMeetingUrl}
          sx={{
            display: 'inline-flex',
            p: 0,
            borderRadius: 0,
            svg: {
              width: { xs: 16, tablet: 20 },
              height: { xs: 16, tablet: 20 },
              color: isDisableMeetingUrl ? 'icon' : 'neutral7',
            },
          }}
        >
          <CopyIcon />
        </IconButton>
      </Tooltip>
    </Stack>
  );
};

export default MeetingUrl;

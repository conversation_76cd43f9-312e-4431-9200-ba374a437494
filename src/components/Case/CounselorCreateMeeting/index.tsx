import { LoadingButton } from '@mui/lab';
import {
  Box,
  Dialog,
  DialogContent,
  Grid,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import BookingCounselorMenuList from 'components/ConsultationMenu/BookingCounselorMenuList';
import { Radio, TextField } from 'components/Form';
import TimeTableField from 'components/Form/TimeTableField';
import TermPolicy from 'components/UI/TermPolicy';
import dayjs from 'dayjs';
import { useFetchDetail } from 'hooks';
import useBreakpoint from 'hooks/useBreakpoint';
import { t } from 'i18n';
import { CloseIcon } from 'icons';
import type { ICalendarListCustomer } from 'models/case/interface';
import caseQuery from 'models/case/query';
import { type ICounselorConsultationMenu } from 'models/consultation/interface';
import { useRouter } from 'next/router';
import type { CreateCounselorMeetingValues } from 'pages/customer/counselor-booking/[counselorId]/schema';
import { useEffect, useState } from 'react';
import type { Control } from 'react-hook-form';
import { toast } from 'react-toastify';
import OutlineMenu from 'shared/provider/my-page/outline-menu';
import { MEETING_TYPE_OPTIONS, MEETING_TYPE_TEXT } from 'utils/constants';
import Helper from 'utils/helpers';

export interface CounselorCreateMeetingProps {
  loading?: boolean;
  control: Control<CreateCounselorMeetingValues>;
  menuList: (ICounselorConsultationMenu & { value: string })[];
  counselorId: string;
  duration: number;
  getDisableSubmitBtnState?: ({
    agree,
  }: {
    agree: boolean;
  }) => boolean | undefined;
}
const CounselorCreateMeeting = ({
  loading,
  control,
  menuList,
  counselorId,
  duration,
  getDisableSubmitBtnState,
  ...props
}: CounselorCreateMeetingProps) => {
  const [openMenuList, setOpenMenuList] = useState(false);
  const firstday = dayjs().startOf('d').toISOString();
  const [agree, setAgree] = useState(false);
  const [startTime, setStartTime] = useState(firstday);
  const [hasShownInitialToast, setHasShownInitialToast] = useState(false);
  const isBreakpoint = useBreakpoint({});

  const { detail: calendarDetail, isFetching } =
    useFetchDetail<ICalendarListCustomer>({
      ...caseQuery.getCalendarDetailByGuest(counselorId as string, startTime),
    });

  const calendar = calendarDetail.slots || [];

  const listRadio = OutlineMenu(menuList);
  const {
    onChangeMenu,
  }: Partial<{
    onChangeMenu: (event: React.MouseEvent<HTMLElement>) => void;
  }> = props;
  const { query } = useRouter();

  // Helper function to check if duration is invalid
  const isInvalidDuration = () => {
    const { duration: queryDuration } = query;
    const validDurations = [30, 60, 90, 120];

    if (!queryDuration || queryDuration === null || queryDuration === '') {
      return true;
    }

    const durationNumber = Number(queryDuration);
    return (
      Number.isNaN(durationNumber) || !validDurations.includes(durationNumber)
    );
  };

  useEffect(() => {
    if (query?.duration && menuList.length > 0 && !hasShownInitialToast) {
      const queryDuration = Number(query.duration);
      const queryMeetingType = query?.meetingType;

      // Check if there are any menus available based on URL parameters (matching the filtering logic)
      const hasMatchingMenu = menuList.some((menu) => {
        const menuDuration = menu.unitPrices[0]?.duration || 0;
        const menuMeetingType = menu.meetingType;

        // If URL has meetingType, apply the same filtering logic as the booking page
        if (queryMeetingType) {
          // Always filter by meeting type first
          if (menuMeetingType !== queryMeetingType) return false;

          // Apply duration filter based on URL meetingType parameter (matching booking page logic)
          if (queryMeetingType === 'SINGLE') {
            // For SINGLE: show menus with duration >= URL duration
            return menuDuration >= queryDuration;
          }
          if (queryMeetingType === 'PARTNER') {
            // For PARTNER: special logic based on URL duration
            if (queryDuration === 90 || queryDuration === 120) {
              // Show both 90 and 120 minute menus
              return menuDuration === 90 || menuDuration === 120;
            }
            // For duration=60 or other values, show all PARTNER menus
            return true;
          }
        }

        // If no meetingType in URL, just check duration
        return menuDuration === queryDuration;
      });

      if (!hasMatchingMenu) {
        toast.error('ご利用可能なメニューがありません');
      }

      // Mark that we've shown the initial toast check (regardless of whether toast was shown)
      setHasShownInitialToast(true);
    }
  }, [query?.duration, query?.meetingType, menuList, hasShownInitialToast]);

  return (
    <Box
      bgcolor="white"
      p={{ xs: 2, tablet: 4 }}
      borderRadius={{ xs: 0, tablet: 2 }}
      mt={{ xs: 1, tablet: 2 }}
    >
      <Stack spacing={{ xs: '20px', tablet: 2 }}>
        <Radio
          name="type"
          data={Helper.convertObjectToOptions(MEETING_TYPE_TEXT)}
          labelCol={3}
          columns={13}
          label="面談方法"
          control={control}
          viewMode
        />
        <Radio
          name="meetingType"
          data={MEETING_TYPE_OPTIONS}
          labelCol={3}
          columns={13}
          label={t('consultationMenu.meetingType')}
          control={control}
          required
        />

        {/* Warning message for invalid duration */}
        {isInvalidDuration() && (
          <Box sx={{ mb: 3 }}>
            <Grid container columnSpacing={4} mt={{ tablet: -1 }} columns={13}>
              <Grid item xs={0} tablet={3} />
              <Grid
                item
                xs={13}
                tablet={10}
                display="flex"
                mt={{ xs: -1, tablet: 0 }}
              >
                <Box style={{ width: '100%' }}>
                  <Box sx={{ mt: { xs: 1, tablet: 0 }, width: '100%' }}>
                    <Typography
                      sx={{
                        fontSize: { xs: 11, tablet: 13 },
                        fontWeight: 300,
                        color: 'text.secondary',
                        textAlign: 'left',
                      }}
                    >
                      ご指定のカウンセラーへのご相談が初めての方は、夫婦カウンセリングは90分からご利用いただけます
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        <Radio
          labelCol={3}
          columns={13}
          name="menuId"
          label="メニュー"
          placeholder="メニュー"
          data={listRadio}
          control={control}
          type="outline"
          {...(onChangeMenu && {
            onClick: (e) => onChangeMenu(e),
          })}
          required
        />
        {!isFetching && (
          <TimeTableField
            label="面談日時"
            placeholder="yyyy年mm月dd日 hh時mm分"
            name="expectDate"
            control={control}
            required
            labelCol={3}
            columns={13}
            duration={duration}
            startTime={startTime}
            onNavigate={(date) => setStartTime(date)}
            data={calendar}
            loading={isFetching}
            disabledFutureMinutes={60}
          />
        )}
        <TextField
          label={t('booking.consumerNote')}
          placeholder={t('placeholder.consumerNote')}
          name="consumerNote"
          control={control}
          maxLength={500}
          multiline
          minRows={5}
          labelCol={3}
          columns={13}
        />
      </Stack>
      <Box display={{ xs: 'none', sl: 'block' }}>
        <Box
          mt="20px"
          display="flex"
          justifyContent={{ xs: ' flex-start', tablet: 'center' }}
        >
          <TermPolicy checked={agree} onChange={(data) => setAgree(data)} />
        </Box>
        <Box
          display="flex"
          justifyContent="center"
          mt={{ xs: '18px', tablet: '20px' }}
        >
          <LoadingButton
            color="secondary"
            type="submit"
            variant="contained"
            size="large"
            className="tabletStyle shadow"
            fullWidth
            loading={loading}
            disabled={
              getDisableSubmitBtnState
                ? getDisableSubmitBtnState({ agree })
                : !agree
            }
            sx={{
              maxWidth: 368,
            }}
          >
            {t('booking.submit')}
          </LoadingButton>
        </Box>
      </Box>
      <Dialog
        open={openMenuList}
        maxWidth="mw"
        fullWidth
        scroll="paper"
        fullScreen={!!isBreakpoint}
        onClose={() => setOpenMenuList(false)}
        PaperProps={{
          sx: { borderRadius: { xs: 0, tablet: 2 } },
        }}
      >
        <DialogContent
          className="confirm-dialog-content-container"
          sx={{ p: { xs: 0, tablet: 4 } }}
        >
          <Box
            display="flex"
            justifyContent={{ xs: 'space-between', tablet: 'center' }}
            alignItems="center"
            mb={{ xs: 0, tablet: '20px' }}
            position="sticky"
            sx={{ alignItems: 'center' }}
            top={0}
            p={{ xs: '14px 16px', tablet: 0 }}
            bgcolor="white"
            zIndex={1}
          >
            <IconButton
              className={!isBreakpoint ? 'whiteOutlined' : ''}
              size="small"
              onClick={() => setOpenMenuList(false)}
              sx={{
                p: { xs: 0, tablet: '3px' },
                color: { xs: 'primary.main', tablet: 'unset' },
                position: { tablet: 'absolute' },
                top: { tablet: 0 },
                left: { tablet: 'unset' },
                right: { tablet: 0 },
                svg: {
                  width: { xs: 28, tablet: 'unset' },
                  height: { xs: 28, tablet: 'unset' },
                },
              }}
            >
              <CloseIcon />
            </IconButton>
            <Typography
              fontSize={{ xs: 18, tablet: 24 }}
              fontWeight={{ xs: 500, tablet: 700 }}
              color="heading"
              align="center"
            >
              料金表
            </Typography>
            <Box width={28}></Box>
          </Box>
          <Box p={{ xs: 2, tablet: 0 }}>
            <BookingCounselorMenuList data={menuList} />
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default CounselorCreateMeeting;

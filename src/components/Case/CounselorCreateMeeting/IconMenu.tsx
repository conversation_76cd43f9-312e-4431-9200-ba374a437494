import { IconButton, SvgIcon } from '@mui/material';
import useGlobalState from 'hooks/useGlobalState';
import { InfoIcon } from 'icons';
import type { ICounselorConsultationMenu } from 'models/consultation/interface';
import React from 'react';

export default function IconMenu({
  menu,
}: {
  menu: ICounselorConsultationMenu;
}) {
  const { setConfirmModal } = useGlobalState();
  return (
    <>
      <IconButton
        onClick={() => {
          setConfirmModal({
            icon: 'info',
            onConfirm: () => {},
            content: menu.content || '内容がありません',
            hideCancelButton: true,
            contentAlign: 'center',
            confirmText: '戻る',
          });
        }}
        sx={{
          zIndex: 1,
          p: '2px',
          svg: { color: 'icon' },
        }}
      >
        <SvgIcon
          sx={{
            width: 16,
            height: 16,
          }}
          viewBox="0 0 16 16"
          inheritViewBox
          component={InfoIcon}
        />
      </IconButton>
    </>
  );
}

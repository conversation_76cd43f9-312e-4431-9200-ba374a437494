import { Box, Typography } from '@mui/material';
import { Radio, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import dayjs from 'dayjs';
import i18n, { t } from 'i18n';
import { useForm } from 'react-hook-form';
import { MeetingStatusType, MomentFormat } from 'utils/constants';
import type { InferType } from 'yup';
import { mixed, object, string } from 'yup';

export interface CancelMeetingFormProps {
  onSubmit: (values: InferType<typeof cancelSchema>) => void;
  finalizedDate?: string;
}

const cancelSchema = object({
  cancelReason: string()
    .max(200, i18n.t('validation.maxLength', { number: 200 }))
    .trim(i18n.t('validation.notAllowedWhiteSpace')),
  status: mixed<MeetingStatusType>().required(),
});

const CancelMeetingForm = ({
  onSubmit,
  finalizedDate,
}: CancelMeetingFormProps) => {
  const { control, handleSubmit } = useForm<InferType<typeof cancelSchema>>({
    defaultValues: {
      status: MeetingStatusType.CANCELED_BY_CONSUMER,
      cancelReason: '',
    },
  });

  return (
    <div>
      <Typography fontSize={14} textAlign="center" mb="36px">
        <Typography fontSize={14} fontWeight="bold" component="span">
          {finalizedDate &&
            `${dayjs(finalizedDate).format(MomentFormat.JP_YEAR_MONTH_DATE)}`}
        </Typography>
        {finalizedDate ? 'の' : ''}面談をキャンセルします
      </Typography>

      <Box
        component="form"
        id="cancel-form"
        onSubmit={handleSubmit(onSubmit)}
        ml={7}
      >
        <Radio
          labelCol={5}
          columns={14}
          label={t('caseDetail.whoCancel')}
          control={control}
          name="status"
          data={[
            { _id: MeetingStatusType.CANCELED_BY_CONSUMER, value: '相談者' },
            { _id: MeetingStatusType.CANCELED_BY_PROVIDER, value: '弁護士' },
          ]}
        />
      </Box>
      <Box mt="20px">
        <Label labelCol={12} label="キャンセル理由" />
        <TextField
          labelCol={0}
          name="cancelReason"
          control={control}
          maxLength={200}
          multiline
          minRows={7}
          placeholder="キャンセル理由"
        />
      </Box>
    </div>
  );
};

export default CancelMeetingForm;

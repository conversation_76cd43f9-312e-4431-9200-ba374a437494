import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  dialogTitle: {
    p: '32px 32px 40px',
    '@media (max-width: 768px)': {
      fontSize: '18px',
      padding: '14px 16px',
    },
  },
  dialogContent: { p: '0px' },
  dialogActions: {
    p: '8px 32px 32px',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dialog: {
    '.MuiPaper-root': {
      '@media (max-width: 768px)': {
        borderRadius: 0,
        maxHeight: '100%',
      },
    },
  },
  dialogTitleSection: {
    p: '14px 16px',
    bgcolor: 'white',
  },
  couponWrapper: {
    '&:before': {
      position: 'absolute',
      content: "''",
      width: '22px',
      height: '22px',
      top: 'calc(50% - 12px)',
      left: '-15px',
      background: 'linear-gradient(to right, transparent 15px, #F5E9BA 7px)',
      borderRadius: '50%',
    },
    '&:after': {
      position: 'absolute',
      content: "''",
      width: '22px',
      height: '22px',
      top: 'calc(50% - 12px)',
      right: '-15px',
      background: 'linear-gradient(to right, #F5E9BA 7px, transparent 0px)',
      borderRadius: '50%',
    },
  },
  couponInputStyle: {
    flex: 1,
    '.MuiInputBase-root': {
      bgcolor: 'white',
    },
    '.MuiOutlinedInput-notchedOutline': {
      borderColor: 'white',
    },
  },
  couponInputPropStyle: {
    bgcolor: 'white',
    padding: { xs: '8px 12px', tablet: '12px 16px' },
  },
  closeButton: {
    position: 'absolute',
    top: 9,
    left: 11,
    svg: { width: 28, height: 28, color: 'primary.main' },
    display: { tablet: 'none' },
  },
  newUserTag: {
    backgroundColor: '#F49B15',
    position: 'absolute',
    width: 'calc(100% + 2px)',
    left: -1,
    bottom: -1,
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
  },
  couponCircle: {
    '&:before': {
      position: 'absolute',
      content: "''",
      width: '20px',
      height: '20px',
      top: 'calc(50% - 11px)',
      left: '-14px',
      background: 'linear-gradient(to right, transparent 13px, #fff 7px)',
      borderRadius: '50%',
      zIndex: 1,
    },
    '&:after': {
      position: 'absolute',
      content: "''",
      width: '20px',
      height: '20px',
      top: 'calc(50% - 11px)',
      right: '-14px',
      backgroundColor: '#fff',
      borderRadius: '50%',
      zIndex: 1,
    },
  },
} as const satisfies Record<string, SxProps<Theme>>;

export default styles;

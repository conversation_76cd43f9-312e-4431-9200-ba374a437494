import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  IconButton,
  SvgIcon,
  TextField,
  Typography,
} from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import dayjs from 'dayjs';
import useBreakpoint from 'hooks/useBreakpoint';
import {
  ClockIcon,
  CloseIcon,
  CouponGraphic,
  CouponIcon,
  RemoveInputIcon,
} from 'icons';
import type { ICoupon } from 'models/coupon/interface';
import { memo, useEffect, useState } from 'react';
import { MomentFormat } from 'utils/constants';
import Helper from 'utils/helpers';

import styles from './styles';

export interface CouponModalProps {
  open: boolean;
  onClose: () => void;
  // couponList: ICoupon[];
  onSelect: (coupon: ICoupon) => void;
  // isLoading?: boolean;
  // total?: number;
  checkCoupon: (code: string) => void;
  isCheckingCoupon: boolean;
  // couponPayload?: { code: string; totalPrice: number };
}

const renderCouponDetail = ({
  data,
  onBack,
  onSelect,
  isCheckingCoupon,
}: {
  data: ICoupon;
  onBack: () => void;
  onSelect: (coupon: ICoupon) => void;
  isCheckingCoupon: boolean;
}) => {
  return (
    <Box
      p={{ xs: 2, tablet: '0px 32px 32px' }}
      height="100%"
      display="flex"
      flexDirection="column"
    >
      <Box flex={1}>
        <Box sx={{ borderRadius: { xs: '12px', tablet: 2 } }} bgcolor="#FFF5CC">
          <Box
            p={{ xs: '12px', tablet: 3 }}
            sx={{
              borderBottom: data.description ? '1px dashed #FAD59E' : 'unset',
            }}
            display="flex"
            alignItems="flex-end"
            justifyContent="space-between"
          >
            <Box>
              <Typography color="heading" fontWeight={700} mb={1}>
                {data.title}
              </Typography>
              <Typography
                color="primary"
                fontWeight={900}
                fontSize={{ xs: 32, tablet: 36 }}
              >
                {data.currency === '%'
                  ? data.amount
                  : Helper.addComma(data.amount)}
                <Typography
                  fontSize={20}
                  fontWeight={500}
                  component="span"
                  lineHeight="42px"
                  sx={{ verticalAlign: 'bottom' }}
                >
                  {data.currency === '%' ? data.currency : '円'}
                </Typography>
              </Typography>
              <Box
                display="flex"
                mt={1}
                gap={{ xs: '4px', tablet: 1 }}
                alignItems="center"
              >
                <SvgIcon
                  component={CouponIcon}
                  color="primary"
                  sx={{
                    width: { xs: 16, tablet: 20 },
                    height: { xs: 16, tablet: 20 },
                  }}
                  viewBox="0 0 20 20"
                />
                <Typography fontSize={{ xs: 14, tablet: 16 }}>
                  クーポンコード：
                  <Typography
                    color="heading"
                    component="span"
                    fontWeight={500}
                    fontSize={{ xs: 14, tablet: 16 }}
                  >
                    {data.code}
                  </Typography>
                </Typography>
              </Box>
              {data.rules.end && (
                <Box
                  mt={1}
                  display="flex"
                  gap={{ xs: '4px', tablet: 1 }}
                  alignItems="center"
                >
                  <SvgIcon
                    component={ClockIcon}
                    inheritViewBox
                    color="primary"
                    viewBox="0 0 20 20"
                    sx={{
                      width: { xs: 16, tablet: 20 },
                      height: { xs: 16, tablet: 20 },
                    }}
                  />
                  <Typography fontSize={{ xs: 14, tablet: 16 }}>
                    有効期限：
                    <Typography
                      color="heading"
                      component="span"
                      fontWeight={500}
                      marginLeft="4px"
                      fontSize={{ xs: 14, tablet: 16 }}
                    >
                      {dayjs(data.rules.end).format(
                        MomentFormat.YEAR_MONTH_DATE,
                      )}
                    </Typography>
                  </Typography>
                </Box>
              )}
            </Box>
            <Box
              sx={{
                svg: {
                  width: { xs: 87.27, tablet: 120 },
                  height: { xs: 80, tablet: 110 },
                },
              }}
            >
              <CouponGraphic />
            </Box>
          </Box>
          {data.description && (
            <Box p={{ xs: '12px', tablet: 3 }}>
              <Typography fontSize={14} mb={1}>
                注意事項
              </Typography>
              <Typography fontSize={14} color="heading" whiteSpace="pre-line">
                {data.description}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
      <Box
        gap={2}
        display="flex"
        sx={{ button: { width: 'calc((100% - 16px)/2)' } }}
        mt={5}
      >
        <Button
          onClick={onBack}
          variant="outlined"
          size="large"
          className="tabletStyle"
        >
          戻る
        </Button>
        <LoadingButton
          variant="contained"
          color="secondary"
          size="large"
          className="tabletStyle"
          onClick={() => onSelect(data)}
          loading={isCheckingCoupon}
          disabled={isCheckingCoupon}
        >
          使用する
        </LoadingButton>
      </Box>
    </Box>
  );
};
const CouponModal = ({
  open,
  onClose,
  // couponList,
  onSelect,
  // isLoading,
  // total = 0,
  checkCoupon,
  isCheckingCoupon,
}: // couponPayload,
CouponModalProps) => {
  const isBreakpoint = useBreakpoint({});
  const [selectedCoupon, setSelectedCoupon] = useState<ICoupon | null>(null);
  const [couponInputText, setCouponInputText] = useState('');

  useEffect(() => {
    if (!open) {
      setSelectedCoupon(null);
      setCouponInputText('');
    }
  }, [open]);

  return (
    <div>
      <Dialog
        onClose={(_, reason) => {
          if (reason === 'backdropClick') {
            onClose();
          }
        }}
        open={open}
        aria-labelledby="checkbox-modal-title"
        aria-describedby="checkbox-modal-description"
        maxWidth="mw"
        sx={styles.dialog}
        fullScreen={isBreakpoint}
      >
        <DialogTitle id="checkbox-modal-title" sx={styles.dialogTitle}>
          <Typography
            fontSize={{ xs: 18, sm: 32 }}
            fontWeight={700}
            textAlign="center"
            color="heading"
            lineHeight={{ xs: '28px', sm: '36px' }}
          >
            {selectedCoupon ? 'クーポン詳細' : 'クーポン'}
          </Typography>
          <IconButton
            className="whiteOutlined"
            size="small"
            onClick={() => {
              onClose();
            }}
            sx={{
              svg: { color: 'neutral7' },
              position: 'absolute',
              right: 16,
              top: 16,
              display: { xs: 'none', tablet: 'inline-flex' },
            }}
          >
            <CloseIcon />
          </IconButton>
          <IconButton size="small" sx={styles.closeButton} onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={styles.dialogContent}>
          {selectedCoupon &&
            renderCouponDetail({
              data: selectedCoupon,
              onBack: () => setSelectedCoupon(null),
              onSelect,
              isCheckingCoupon,
            })}

          {!selectedCoupon && (
            <Box p={{ xs: '16px', tablet: '16px 32px 32px' }} bgcolor="divine">
              <Typography fontSize={16} fontWeight={700} color="heading">
                クーポンを使用しましょう
              </Typography>
              <Typography fontSize={14} color="hint" mt={1}>
                クーポンご利用の場合は、クーポンコードを入力してください。
              </Typography>
              <Box display="flex" gap={1} mt={2}>
                <TextField
                  sx={styles.couponInputStyle}
                  inputProps={{
                    sx: styles.couponInputPropStyle,
                  }}
                  InputProps={{
                    endAdornment: couponInputText && (
                      <IconButton
                        onClick={() => setCouponInputText('')}
                        sx={{ p: 0, mr: { xs: '12px', tablet: 2 } }}
                      >
                        <SvgIcon
                          sx={{
                            width: { xs: 20, tablet: 24 },
                            height: { xs: 20, tablet: 24 },
                          }}
                          viewBox="0 0 20 20"
                          component={RemoveInputIcon}
                          inheritViewBox
                        />
                      </IconButton>
                    ),
                  }}
                  placeholder="クーポンコードを入力"
                  onChange={(e) =>
                    setCouponInputText(e.target.value.toUpperCase())
                  }
                  value={couponInputText}
                />
                <LoadingButton
                  variant="contained"
                  color="secondary"
                  sx={{ minWidth: '96px' }}
                  disabled={couponInputText.length === 0 || isCheckingCoupon}
                  loading={isCheckingCoupon}
                  onClick={() => checkCoupon(couponInputText)}
                >
                  使用する
                </LoadingButton>
              </Box>
            </Box>
          )}
          {/* {!isLoading && total === 0 && (
            <Box p="32px">
              <Box
                display="flex"
                justifyContent="center"
                sx={{
                  svg: {
                    width: { xs: 80, tablet: 96 },
                    height: { xs: 80, tablet: 96 },
                  },
                }}
              >
                <EmptyCoupon />
              </Box>
              <Typography
                color="hint"
                fontSize={{ xs: 14, tablet: 16 }}
                textAlign="center"
                mt={1}
                mb={1}
              >
                現在、利用可能なクーポンはありません
              </Typography>
            </Box>
          )}
          {isLoading && (
            <Box p={4} display="flex" justifyContent="center">
              <CircularProgress />
            </Box>
          )}
          {!isLoading && total > 0 && !selectedCoupon && (
            <Box
              p={{ xs: '24px 16px', tablet: '24px 32px 32px' }}
              bgcolor="white"
            >
              <Typography color="heading" fontWeight={700}>
                ご利用可能なクーポン
              </Typography>
              <Box mt={2} display="flex" flexDirection="column" gap={2}>
                {!isLoading &&
                  total > 0 &&
                  couponList.map((coupon) => {
                    return (
                      <Box
                        key={coupon._id}
                        sx={{
                          borderRadius: '12px',
                          border: '1px solid #DBE3E7',
                          cursor: 'pointer',
                        }}
                        position="relative"
                        onClick={() =>
                          setSelectedCoupon({ ...coupon, couponAmount: 0 })
                        }
                      >
                        {typeof coupon.availableCount === 'number' &&
                          coupon.availableCount > 0 && (
                            <Box
                              bgcolor="#DB5A42"
                              position="absolute"
                              right={-1}
                              top={-1}
                              padding={{ xs: '3px 6px', tablet: '4px 6px' }}
                              sx={{
                                borderBottomLeftRadius: '12px',
                                borderTopRightRadius: '12px',
                              }}
                              fontSize={{ xs: 10, tablet: 12 }}
                              fontWeight={700}
                              color="white"
                            >
                              残り{coupon.availableCount}枚
                            </Box>
                          )}
                        <Box
                          p={{ xs: '16px 12px', tablet: 2 }}
                          display="flex"
                          alignItems="center"
                          borderBottom="1px dashed #E4EAED"
                        >
                          <Box
                            paddingX="4px"
                            minWidth={{ xs: 120, tablet: 130 }}
                            minHeight={{ xs: 58, tablet: 70 }}
                            borderRadius={1}
                            bgcolor="#FFF5CC"
                            border="1px solid #F5E9BA"
                            position="relative"
                            sx={styles.couponWrapper}
                          >
                            <Box sx={styles.couponCircle} />
                            <Box
                              display="flex"
                              alignItems="flex-end"
                              justifyContent="center"
                            >
                              <Typography
                                fontSize={{ xs: 20, tablet: 24 }}
                                fontWeight={900}
                                textAlign="center"
                                mt={
                                  coupon.tags && coupon.tags[0]
                                    ? { xs: '8px', tablet: '11px' }
                                    : { xs: '13px', tablet: '18px' }
                                }
                                color="primary.main"
                                lineHeight={{ xs: '32px', tablet: '34px' }}
                              >
                                {coupon.currency === '%'
                                  ? coupon.amount
                                  : Helper.addComma(coupon.amount)}
                              </Typography>
                              <Typography
                                fontSize={{ xs: 16, tablet: 18 }}
                                color="primary.main"
                                paddingBottom="3px"
                              >
                                {coupon.currency === '%'
                                  ? coupon.currency
                                  : '円'}
                              </Typography>
                            </Box>

                            {coupon.tags && coupon.tags[0] && (
                              <Box sx={styles.newUserTag}>
                                <Typography
                                  color="white"
                                  fontSize={{ xs: 10, tablet: 12 }}
                                  textAlign="center"
                                  lineHeight={{ xs: '12px', tablet: '16px' }}
                                >
                                  {coupon.tags[0].name}
                                </Typography>
                              </Box>
                            )}
                          </Box>
                          <Box ml={2}>
                            <Typography
                              fontWeight={700}
                              color="heading"
                              fontSize={{ xs: 14, tablet: 16 }}
                            >
                              {coupon.title}
                            </Typography>
                            <Typography
                              color="hint"
                              mt={1}
                              fontSize={{ xs: 12, tablet: 14 }}
                            >
                              クーポンコード：
                              <Typography
                                component="span"
                                color="heading"
                                fontSize={{ xs: 12, tablet: 14 }}
                              >
                                {coupon.code}
                              </Typography>
                            </Typography>
                          </Box>
                        </Box>
                        <Box
                          display="flex"
                          p={{ xs: '8px 12px', tablet: '8px 16px' }}
                          alignItems="center"
                          justifyContent="space-between"
                        >
                          <Box display="flex" alignItems="center" gap="4px">
                            <SvgIcon
                              sx={{
                                width: { xs: 16, tablet: 20 },
                                height: { xs: 16, tablet: 20 },
                              }}
                              viewBox="0 0 20 20"
                              component={ClockIcon}
                              inheritViewBox
                            />
                            <Typography
                              fontSize={{ xs: 12, tablet: 14 }}
                              color="hint"
                              lineHeight={{ xs: '16px', tablet: '20px' }}
                            >
                              有効期限:{' '}
                              {coupon.rules.end
                                ? dayjs(coupon.rules.end).format(
                                    MomentFormat.YEAR_MONTH_DATE,
                                  )
                                : 'なし'}
                            </Typography>
                          </Box>
                          <LoadingButton
                            sx={{
                              paddingY: { xs: '2px', tablet: '4px' },
                              minWidth: { xs: '72px', tablet: '96px' },
                              fontSize: { xs: 12, tablet: 14 },
                            }}
                            variant="contained"
                            color="secondary"
                            size="small"
                            disabled={
                              isCheckingCoupon &&
                              couponPayload?.code === coupon.code
                            }
                            loading={
                              isCheckingCoupon &&
                              couponPayload?.code === coupon.code
                            }
                            onClick={(e) => {
                              e.stopPropagation();
                              onSelect(coupon);
                            }}
                          >
                            使用する
                          </LoadingButton>
                        </Box>
                      </Box>
                    );
                  })}
              </Box>
            </Box>
          )} */}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default memo(CouponModal);

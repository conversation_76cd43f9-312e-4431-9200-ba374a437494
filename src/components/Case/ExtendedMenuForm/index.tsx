import { yupResolver } from '@hookform/resolvers/yup';
import { Box } from '@mui/material';
import { Select } from 'components/Form';
import Label from 'components/Form/Label';
import NumberField from 'components/Form/NumberField';
import { isNaN } from 'lodash';
import { useForm } from 'react-hook-form';
import { MEETING_DURATION } from 'utils/constants';
import Helper from 'utils/helpers';
import type { InferType } from 'yup';
import { number, object } from 'yup';

const schema = object({
  duration: number()
    .transform((value) => {
      return isNaN(value) || !value ? undefined : value;
    })
    .required(),
  price: number()
    .transform((value) => {
      return isNaN(value) || !value ? undefined : value;
    })
    .required(),
});

export interface ExtendMenuFormProps {
  onSubmit: (values: InferType<typeof schema>) => void;
  defaultValues?: InferType<typeof schema>;
}

const ExtendMenuForm = ({ onSubmit, defaultValues }: ExtendMenuFormProps) => {
  const { control, handleSubmit } = useForm<InferType<typeof schema>>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues,
  });

  return (
    <div>
      <Box
        component="form"
        id="extend-menu-form"
        onSubmit={handleSubmit(onSubmit)}
      >
        <Box mt="20px">
          <Label labelCol={12} label="延長時間" />
          <Select
            labelCol={0}
            name="duration"
            data={Helper.convertObjectToOptions(MEETING_DURATION)}
            control={control}
            placeholder="分"
          />
        </Box>
        <Box mt={2}>
          <Label labelCol={12} label="相談料" />
          <NumberField
            labelCol={0}
            name="price"
            max={99999999}
            control={control}
            placeholder="円"
          />
        </Box>
      </Box>
    </div>
  );
};

export default ExtendMenuForm;

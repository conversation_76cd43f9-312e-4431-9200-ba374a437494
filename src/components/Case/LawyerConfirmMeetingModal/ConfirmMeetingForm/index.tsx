import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Divider, Grid, Stack, Typography } from '@mui/material';
import CouponTag from 'components/Case/CouponTag';
import PaymentTooltipButton from 'components/Case/PaymentTooltipButton';
import { DateTimePicker, Radio, Select, TextField } from 'components/Form';
import Label from 'components/Form/Label';
import NumberField from 'components/Form/NumberField';
import dayjs from 'dayjs';
import { AnimatePresence, motion } from 'framer-motion';
import { useDeepCompareEffect } from 'hooks';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18n';
import type { IMeetingsItem } from 'models/case/interface';
import { useEffect, useMemo } from 'react';
import { useWatch } from 'react-hook-form';
import CaseUtil from 'utils/caseUtil';
import {
  MEETING_DURATION,
  MeetingType,
  PAYMENT_METHOD_OPTIONS,
} from 'utils/constants';
import Helper from 'utils/helpers';

import type { ConfirmMeetingValues } from './schema';
import schema from './schema';
import styles from './styles';

export interface ConfirmMeetingFormProps {
  data: IMeetingsItem;
  onSubmit: (values: ConfirmMeetingValues) => void;
}
const ConfirmMeetingForm = ({ data, onSubmit }: ConfirmMeetingFormProps) => {
  const defaultValues = useMemo(
    () => ({
      finalizedDate: data?.finalizedDate,
      meetingUrl: data.meetingUrl,
      type: data.type,
      discount: data.paymentInfo?.discount,
      duration: data.paymentInfo?.duration || 30,
      method: data.paymentInfo?.method,
    }),
    [data],
  );
  const { unitPrice, couponInfo } = data.paymentInfo;
  const couponAmount = couponInfo?.couponAmount || 0;

  const { control, handleSubmit, setValue, reset } =
    useHookForm<ConfirmMeetingValues>({
      mode: 'all',
      resolver: yupResolver(schema),
      defaultValues,
    });

  useDeepCompareEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const watchType = useWatch({ name: 'type', control });
  const watchDuration = useWatch({ name: 'duration', control });
  const watchDiscount = useWatch({ name: 'discount', control }) || 0;

  const basicPrice = Helper.getBasicPrice(unitPrice, watchDuration);
  const calculatedCouponAmount = data.paymentInfo.couponInfo?.coupon
    ? CaseUtil.calculateFinalCouponAmount({
        totalPrice: basicPrice,
        issuedCoupon: data.paymentInfo.couponInfo.coupon,
      })
    : 0;

  useEffect(() => {
    if (watchType !== MeetingType.ONLINE) {
      setValue('meetingUrl', '', { shouldValidate: true });
    }
  }, [setValue, watchType]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} id="lawyer-confirm-meeting-form">
      <Stack spacing={2}>
        <DateTimePicker
          label={t('confirmMeeting.meetingDate')}
          placeholder="yyyy年mm月dd日 hh時mm分"
          name="finalizedDate"
          control={control}
          minDate={dayjs()}
          required
          labelCol={4}
          columns={12}
        />
        <Grid container columns={17}>
          <Label
            labelCol={5}
            label={t('confirmMeeting.meetingType')}
            paddingTop={3}
            required
          />
          <Grid item xs={12} columnSpacing={4}>
            <Box sx={styles.typeMeeting}>
              <Radio
                labelCol={0}
                control={control}
                name="type"
                row={false}
                data={[
                  { _id: MeetingType.IN_PERSON, value: '対面での面談' },
                  { _id: MeetingType.ONLINE, value: 'オンライン面談' },
                ]}
              />
              <AnimatePresence initial={false}>
                {watchType === MeetingType.ONLINE && (
                  <motion.div
                    initial="collapsed"
                    animate="open"
                    exit="collapsed"
                    variants={{
                      open: { opacity: 1, height: 'auto' },
                      collapsed: { opacity: 0, height: 0 },
                    }}
                    transition={{
                      duration: 0.3,
                    }}
                  >
                    <TextField
                      labelCol={12}
                      name="meetingUrl"
                      control={control}
                      placeholder={t('confirmMeeting.placeholderMeetingUrl')}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </Box>
          </Grid>
        </Grid>
        <Select
          labelCol={4}
          columns={12}
          name="duration"
          label={t('confirmMeeting.duration')}
          placeholder="面談時間"
          data={Helper.convertObjectToOptions(MEETING_DURATION)}
          control={control}
          required
        />
      </Stack>
      <Divider sx={{ mt: 3, mb: 3 }} />
      <Box display="grid">
        <Select
          labelCol={4}
          columns={12}
          name="method"
          control={control}
          label={t('confirmMeeting.paymentMethod')}
          data={PAYMENT_METHOD_OPTIONS}
          placeholder={t('confirmMeeting.placeholderMethod')}
          required
        />
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-end"
          mt={2}
        >
          <Typography fontWeight="bold" color="text.primary">
            {t('confirmMeeting.basicPrice')}
          </Typography>
          <Stack direction="row" spacing="2px" alignItems="flex-end">
            <Typography fontWeight="bold" color="text.primary">
              {Helper.addComma(Helper.getBasicPrice(unitPrice, watchDuration))}
            </Typography>
            <Typography fontWeight="bold" color="text.primary">
              円
            </Typography>
          </Stack>
        </Stack>
        <Typography fontSize={14} color="hint" mt={1} mb={2}>
          {`${t('confirmMeeting.description')}`}
        </Typography>
        <Stack
          direction="row"
          alignItems="center"
          mb={2}
          justifyContent="space-between"
          flexWrap="wrap"
        >
          <Stack direction="row" alignItems="center">
            <Typography fontWeight={700}>クーポン値引</Typography>
            {couponInfo && (
              <CouponTag
                wrapperProps={{ ml: 1, mr: '6px' }}
                code={couponInfo?.coupon.code}
              />
            )}
            {couponInfo && <PaymentTooltipButton />}
          </Stack>
          {couponInfo ? (
            <Typography fontWeight={700}>
              - {Helper.addComma(calculatedCouponAmount || 0)}円
            </Typography>
          ) : (
            <Typography color="#C1BCB3">なし</Typography>
          )}
        </Stack>
        <NumberField
          label={t('confirmMeeting.discount')}
          placeholder="割引金額を入力"
          name="discount"
          control={control}
          labelCol={4}
          columns={12}
          adornment={
            <Typography ml={1} color="text.primary">
              円
            </Typography>
          }
          min={0}
          max={Math.max(basicPrice - couponAmount, 0)}
        />
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-end"
          mt={2}
        >
          <Stack direction="row" gap="6px" alignItems="center">
            <Typography fontWeight="bold" color="heading">
              {t('confirmMeeting.finalPrice')}
            </Typography>
            <PaymentTooltipButton
              title="お支払い予定金額に関して"
              content={`・クーポンをご利用の場合は、割引後の金額になります\n・予約時の選択メニューや決済時のメニュー変更により実際の支払い金額と異なる場合があります `}
            />
          </Stack>
          <Stack direction="row" spacing="2px" alignItems="flex-end">
            <Typography fontWeight="bold" color="neutral7" fontSize={24}>
              {watchDiscount <= basicPrice - couponAmount
                ? Helper.addComma(basicPrice - watchDiscount - couponAmount)
                : 0}
            </Typography>
            <Typography fontWeight="bold" color="neutral7" lineHeight="31px">
              円
            </Typography>
          </Stack>
        </Stack>
      </Box>
    </form>
  );
};

export default ConfirmMeetingForm;

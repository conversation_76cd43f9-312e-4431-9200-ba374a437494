import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  DialogActions,
  Divider,
  Grid,
  Stack,
  Typography,
} from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import ConfirmMeetingForm from 'components/Case/LawyerConfirmMeetingModal/ConfirmMeetingForm';
import dayjs from 'dayjs';
import useMutate from 'hooks/useMutate';
import { t } from 'i18n';
import { Time1Icon, Time2Icon, Time3Icon } from 'icons';
import type { IConfirmMeeting, IMeetingsItem } from 'models/case/interface';
import caseQuery from 'models/case/query';
import type { Dispatch, SetStateAction } from 'react';
import { MomentFormat } from 'utils/constants';

import styles from '../LawyerMeetingItem/styles';
import type { ConfirmMeetingValues } from './ConfirmMeetingForm/schema';

export interface IConfirmMeetingModal {
  open: boolean;
  setOpenModalConfirmMeeting: Dispatch<SetStateAction<boolean>>;
  onClose: () => void;
  data: IMeetingsItem;
  onSuccess: () => void;
}

const ConfirmMeetingModal = ({
  open,
  setOpenModalConfirmMeeting,
  onClose,
  data,
  onSuccess,
}: IConfirmMeetingModal) => {
  const meetingId = data._id;

  const { mutateAsync: confirmMeeting, isLoading } = useMutate<IConfirmMeeting>(
    caseQuery.lawyerConfirmMeeting(meetingId),
  );

  const { unitPrice } = data.paymentInfo;

  const handleSubmitForm = (values: ConfirmMeetingValues) => {
    const { finalizedDate, meetingUrl, duration, type, discount, method } =
      values;
    const payload = {
      finalizedDate: dayjs(finalizedDate).toISOString(),
      meetingUrl,
      type,
      paymentInfo: {
        discount: discount || 0,
        duration,
        unitPrice,
        method,
      },
    };
    confirmMeeting(payload, {
      onSuccess: () => {
        setOpenModalConfirmMeeting(false);
        onSuccess();
      },
    });
  };

  return (
    <Dialog
      open={open}
      aria-labelledby="lawyer-confirm-meeting-modal-title"
      aria-describedby="lawyer-confirm-meeting-modal-description"
      maxWidth="mw"
      fullWidth
    >
      <DialogTitle
        id="lawyer-confirm-meeting-modal-title"
        sx={styles.dialogTitle}
      >
        <Typography
          fontSize={24}
          fontWeight="bold"
          color="heading"
          textAlign="center"
        >
          {t('confirmMeeting.titleModal')}
        </Typography>
        <Typography
          color="text.primary"
          fontSize={14}
          textAlign="center"
          mt={3}
        >
          {t('confirmMeeting.descriptionOfModal1')}
        </Typography>
        <Stack direction="row" justifyContent="center">
          <Typography fontSize={14} color="primary.main">
            「確定」
          </Typography>
          <Typography color="text.primary" fontSize={14}>
            {t('confirmMeeting.descriptionOfModal2')}
          </Typography>
        </Stack>
      </DialogTitle>
      <DialogContent sx={styles.dialogContent}>
        <DialogContentText
          id="lawyer-confirm-meeting-modal-description"
          component="div"
        >
          <Divider />
          <Box display="flex" justifyContent="space-between" mt={1}>
            <Grid container columnSpacing={{ tablet: 3 }}>
              <Grid item tablet={4}>
                <Box>
                  <Box sx={styles.icon}>
                    <Time1Icon />
                  </Box>
                  <Typography sx={styles.textTime}>
                    {dayjs(data.expectDate[0].start).format(
                      MomentFormat.JP_YEAR_MONTH_DATE_DAY,
                    )}
                    {'\n'}
                    {dayjs(data.expectDate[0].start).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}{' '}
                    ~{' '}
                    {dayjs(data.expectDate[0].end).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}
                  </Typography>
                </Box>
              </Grid>
              <Grid item tablet={4}>
                <Box sx={styles.icon}>
                  <Time2Icon />
                </Box>
                {data.expectDate.length === 3 && (
                  <Typography sx={styles.textTime}>
                    {dayjs(data.expectDate[1].start).format(
                      MomentFormat.JP_YEAR_MONTH_DATE_DAY,
                    )}
                    {'\n'}
                    {dayjs(data.expectDate[1].start).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}{' '}
                    ~{' '}
                    {dayjs(data.expectDate[1].end).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}
                  </Typography>
                )}
              </Grid>
              <Grid item tablet={4}>
                <Box sx={styles.icon}>
                  <Time3Icon />
                </Box>
                {data.expectDate.length === 3 && (
                  <Typography sx={styles.textTime}>
                    {dayjs(data.expectDate[2].start).format(
                      MomentFormat.JP_YEAR_MONTH_DATE_DAY,
                    )}
                    {'\n'}
                    {dayjs(data.expectDate[2].start).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}{' '}
                    ~{' '}
                    {dayjs(data.expectDate[2].end).format(
                      MomentFormat.JP_HOUR_MINUTE,
                    )}
                  </Typography>
                )}
              </Grid>
            </Grid>
          </Box>
          <Divider sx={{ mt: 1, mb: 3 }} />
          <Box display="grid">
            <ConfirmMeetingForm data={data} onSubmit={handleSubmitForm} />
          </Box>
        </DialogContentText>
      </DialogContent>
      <DialogActions
        sx={{
          p: '16px 24px 24px',
        }}
      >
        <Stack
          direction="row"
          spacing={2}
          maxWidth={416}
          width="100%"
          margin="0px auto"
        >
          <Button variant="outlined" size="large" fullWidth onClick={onClose}>
            {t('global.cancel')}
          </Button>
          <LoadingButton
            variant="contained"
            color="secondary"
            size="large"
            fullWidth
            loading={isLoading}
            type="submit"
            form="lawyer-confirm-meeting-form"
          >
            {t('global.settle')}
          </LoadingButton>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmMeetingModal;

import type { Theme } from '@mui/material/styles';

const styles = {
  carousel: {
    position: 'relative',
    overflow: 'hidden',
    width: '100%',
  },
  viewport: {
    overflow: 'hidden',
    width: '100%',
  },
  container: (theme: Theme) => ({
    display: 'flex',
    gap: '16px',
    padding: '0 16px',
    width: '100%',
    [theme.breakpoints.up('sm')]: {
      gap: '24px',
    },
  }),
  slide: (theme: Theme) => ({
    flex: '0 0 auto',
    width: '100%',
    [theme.breakpoints.up('sm')]: {
      width: 'calc(100% / 3)',
    },
  }),
  button: {
    position: 'absolute',
    width: '42px',
    height: '42px',
    minWidth: '42px',
    justifyContent: 'center',
    alignItems: 'center',
    display: 'flex',
    top: '50%',
    transform: 'translateY(-50%)',
    background: '#fff',
    color: '#F5AF3C',
    border: '1px solid #F5AF3C',
    borderRadius: '50%',
    cursor: 'pointer',
    zIndex: 1,
    '&:hover': {
      background: '#F5AF3C',
      color: '#fff',
    },
    '&:disabled': {
      opacity: 0.5,
      cursor: 'not-allowed',
    },
  },
  prev: {
    left: 'calc(-42px - 24px)',
  },
  next: {
    right: 'calc(-42px - 24px)',
  },
  // Responsive
  buttonMobile: (theme: Theme) => ({
    [theme.breakpoints.down('sm')]: {
      left: '0',
      right: '0',
    },
  }),
} as const;

export default styles;

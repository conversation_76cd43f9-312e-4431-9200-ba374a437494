import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { Box, Button } from '@mui/material';
import type { EmblaOptionsType } from 'embla-carousel-react';
import useEmblaCarousel from 'embla-carousel-react';
import type { ReactNode } from 'react';
import React, { useCallback, useEffect, useState } from 'react';

import styles from './styles';

type CarouselProps = {
  slides: ReactNode[];
  options?: EmblaOptionsType;
  itemsToShow?: number;
  gap?: number;
  showControls?: boolean;
};

const EmblaCarousel: React.FC<CarouselProps> = ({
  slides,
  options = {},
  itemsToShow = 1,
  gap = 24,
  showControls = true,
}) => {
  const defaultOptions: EmblaOptionsType = {
    loop: true,
    align: 'start',
    skipSnaps: false,
    ...options,
  };

  const slideStyle = {
    width: `${100 / itemsToShow}%`,
  };
  const containerStyle = {
    gap: `${gap}px`,
    padding: `0 ${gap}px`,
  };

  const [emblaRef, emblaApi] = useEmblaCarousel(defaultOptions);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const updateButtons = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', updateButtons);
    updateButtons();
  }, [emblaApi, updateButtons]);

  return (
    <Box sx={{ position: 'relative' }}>
      <Box sx={styles.carousel}>
        <Box sx={styles.viewport} ref={emblaRef}>
          <Box
            sx={(theme) => ({ ...styles.container(theme), ...containerStyle })}
          >
            {slides.map((slide, index) => (
              <Box
                sx={(theme) => ({ ...styles.slide(theme), ...slideStyle })}
                key={index}
              >
                {slide}
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
      {itemsToShow > slides.length || !showControls ? null : (
        <>
          <Button
            sx={{ ...styles.button, ...styles.prev }}
            onClick={() => emblaApi?.scrollPrev()}
            disabled={!canScrollPrev}
          >
            <ArrowBackIosNewIcon />
          </Button>

          <Button
            sx={{ ...styles.button, ...styles.next }}
            onClick={() => emblaApi?.scrollNext()}
            disabled={!canScrollNext}
          >
            <ArrowForwardIosIcon />
          </Button>
        </>
      )}
    </Box>
  );
};

export default EmblaCarousel;

import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { t } from 'i18n';
import type { InferType } from 'yup';
import { object, string } from 'yup';

dayjs.extend(isSameOrAfter);

const schema = object({
  menuId: string(),
  expectDate: string()
    .nullable()
    .test('validDay', t('validation.invalidField'), (value) => {
      if (!dayjs(value).isSameOrAfter(dayjs(), 'minute')) {
        return false;
      }
      if (value && !['00', '30'].includes(dayjs(value).format('mm'))) {
        return false;
      }
      return true;
    }),
});

export type CalendarValues = InferType<typeof schema>;

export default schema;

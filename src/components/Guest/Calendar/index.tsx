import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Divider, Grid, Typography } from '@mui/material';
import { Radio } from 'components/Form';
import TimeTableFieldWithoutDialog from 'components/Form/TimeTableFieldWithoutDialog';
import { useNavigationState } from 'context/NavigationContext';
import dayjs from 'dayjs';
import { useFetchDetail, useFetchList, useFetchUser } from 'hooks';
import useHookForm from 'hooks/useHookForm';
import { t } from 'i18next';
import type { ICalendarListCustomer } from 'models/case/interface';
import caseQuery from 'models/case/query';
import type { ICounselorConsultationMenu } from 'models/consultation/interface';
import consultationQuery from 'models/consultation/query';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useWatch } from 'react-hook-form';
import OutlineMenu from 'shared/provider/my-page/outline-menu';
import { MeetingType } from 'utils/constants';
import Helper from 'utils/helpers';

import schema from './schema';

const Calendar = () => {
  const webCookie = Helper.getWebCookie();
  const { query } = useRouter();
  const { setState } = useNavigationState();
  const firstday = dayjs().startOf('d').toISOString();
  const [startTime, setStartTime] = useState(firstday);

  // Helper function to check if duration is invalid
  const isInvalidDuration = () => {
    const { duration } = query;
    const validDurations = [30, 60, 90, 120];

    if (!duration || duration === null || duration === '') {
      return true;
    }

    const durationNumber = Number(duration);
    return (
      Number.isNaN(durationNumber) || !validDurations.includes(durationNumber)
    );
  };

  // Get current user data for logged-in users
  const { data: currentUser } = useFetchUser({ enabled: !!webCookie?.token });

  const { list: menuList } = useFetchList<ICounselorConsultationMenu>({
    ...(webCookie?.token
      ? consultationQuery?.list(query?.counselorId as string, currentUser?._id)
      : consultationQuery.listByGuest(query?.counselorId as string)),
  });
  const { detail: calendarDetails, isFetching } =
    useFetchDetail<ICalendarListCustomer>({
      ...(webCookie?.token
        ? caseQuery.getCalendarDetail(query?.counselorId as string, startTime)
        : caseQuery.getCalendarDetailByGuest(
            query?.counselorId as string,
            startTime,
          )),
    });
  const formattedMenuList = useMemo(() => {
    return menuList.map((menu) => ({
      ...menu,
      value: `${menu.unitPrices[0].duration}分 - ${menu.title}`,
    }));
  }, [menuList]);

  const queryMenuId = useMemo(() => {
    let currentMenuId = '';

    // Priority 1: Try to match URL parameters if they exist
    if (query?.duration && query?.meetingType) {
      const currentDuration = Number(query?.duration);
      // const currentStartSlot = String(query?.startSlot);
      const currentMenu = formattedMenuList?.find(
        (menu) =>
          menu.meetingType === query?.meetingType &&
          typeof currentDuration === 'number' &&
          menu?.unitPrices?.[0]?.duration === currentDuration,
      );
      if (currentMenu?._id) currentMenuId = currentMenu?._id;
    }

    // Priority 2: If no URL match found or URL parameters don't exist, use default logic
    if (!currentMenuId && formattedMenuList.length > 0) {
      const defaultMenuId = Helper.getDefaultMenuId(formattedMenuList);
      if (defaultMenuId) {
        currentMenuId = defaultMenuId;
      }
    }

    return currentMenuId;
  }, [formattedMenuList, query?.duration, query?.meetingType]);

  const { control, watch, setValue } = useHookForm<any>({
    mode: 'all',
    resolver: yupResolver(schema),
    values: {
      meetingType: MeetingType.ONLINE,
      menuId: queryMenuId,
    },
  });

  const watchMenuId = useWatch({ name: 'menuId', control });
  const watchMenu = useMemo(
    () => formattedMenuList.find((menu) => menu._id === watchMenuId),
    [formattedMenuList, watchMenuId],
  );
  const duration = useMemo(() => {
    // Priority 1: Use resolved watchMenu if available
    if (watchMenu?.unitPrices[0]?.duration) {
      return watchMenu.unitPrices[0].duration;
    }

    // Priority 2: Direct lookup if watchMenuId exists but watchMenu failed to resolve
    if (watchMenuId && formattedMenuList.length > 0) {
      const directMenu = formattedMenuList.find(
        (menu) => menu._id === watchMenuId,
      );
      if (directMenu?.unitPrices[0]?.duration) {
        return directMenu.unitPrices[0].duration;
      }
    }

    // Priority 3: Re-calculate queryMenuId logic if form state is inconsistent
    if (formattedMenuList.length > 0) {
      let recalculatedMenuId = '';

      // Try to match URL parameters if they exist
      if (query?.duration && query?.meetingType) {
        const currentDuration = Number(query?.duration);
        if (!Number.isNaN(currentDuration)) {
          const currentMenu = formattedMenuList.find(
            (menu) =>
              menu.meetingType === query?.meetingType &&
              menu?.unitPrices?.[0]?.duration === currentDuration,
          );
          if (currentMenu?._id) {
            recalculatedMenuId = currentMenu._id;
          }
        }
      }

      // If no URL match, use default logic
      if (!recalculatedMenuId) {
        const defaultMenuId = Helper.getDefaultMenuId(formattedMenuList);
        if (defaultMenuId) {
          recalculatedMenuId = defaultMenuId;
        }
      }

      // Find and return duration from recalculated menu
      if (recalculatedMenuId) {
        const recalculatedMenu = formattedMenuList.find(
          (menu) => menu._id === recalculatedMenuId,
        );
        if (recalculatedMenu?.unitPrices[0]?.duration) {
          return recalculatedMenu.unitPrices[0].duration;
        }
      }
    }

    // Priority 4: Fallback to first menu
    const fallbackDuration = menuList[0]?.unitPrices[0].duration;
    return fallbackDuration;
  }, [
    watchMenu,
    watchMenuId,
    formattedMenuList,
    query?.duration,
    query?.meetingType,
    menuList,
  ]);
  const meetingType = useMemo(() => {
    // Priority 1: Use resolved watchMenu if available
    if (watchMenu?.meetingType) {
      return watchMenu.meetingType;
    }

    // Priority 2: Direct lookup if watchMenuId exists but watchMenu failed to resolve
    if (watchMenuId && formattedMenuList.length > 0) {
      const directMenu = formattedMenuList.find(
        (menu) => menu._id === watchMenuId,
      );
      if (directMenu?.meetingType) {
        return directMenu.meetingType;
      }
    }

    // Priority 3: Re-calculate queryMenuId logic if form state is inconsistent
    if (formattedMenuList.length > 0) {
      let recalculatedMenuId = '';

      // Try to match URL parameters if they exist
      if (query?.duration && query?.meetingType) {
        const currentDuration = Number(query?.duration);
        if (!Number.isNaN(currentDuration)) {
          const currentMenu = formattedMenuList.find(
            (menu) =>
              menu.meetingType === query?.meetingType &&
              menu?.unitPrices?.[0]?.duration === currentDuration,
          );
          if (currentMenu?._id) {
            recalculatedMenuId = currentMenu._id;
          }
        }
      }

      // If no URL match, use default logic
      if (!recalculatedMenuId) {
        const defaultMenuId = Helper.getDefaultMenuId(formattedMenuList);
        if (defaultMenuId) {
          recalculatedMenuId = defaultMenuId;
        }
      }

      // Find and return meetingType from recalculated menu
      if (recalculatedMenuId) {
        const recalculatedMenu = formattedMenuList.find(
          (menu) => menu._id === recalculatedMenuId,
        );
        if (recalculatedMenu?.meetingType) {
          return recalculatedMenu.meetingType;
        }
      }
    }

    // Priority 4: Fallback to first menu
    const fallbackMeetingType = menuList[0]?.meetingType;
    return fallbackMeetingType;
  }, [
    watchMenu,
    watchMenuId,
    formattedMenuList,
    query?.duration,
    query?.meetingType,
    menuList,
  ]);

  // Update navigation state when menu selection changes
  useEffect(() => {
    if (duration && meetingType) {
      const currentFormValues = watch();
      setState({
        ...currentFormValues,
        duration,
        meetingType,
        startSlot: currentFormValues?.expectDate,
      });
    }
  }, [watchMenuId, duration, meetingType, setState]);

  // Also update when menu changes (immediate response to menu selection)
  useEffect(() => {
    if (watchMenuId && watchMenu) {
      const newState = {
        duration: watchMenu.unitPrices[0].duration,
        meetingType: watchMenu.meetingType,
      };

      setState((prevState: any) => ({
        ...prevState,
        ...newState,
      }));
    }
  }, [watchMenuId, watchMenu, setState]);

  // Sync form state if our defensive logic detects a different menu should be selected
  useEffect(() => {
    if (formattedMenuList.length > 0 && watchMenuId) {
      // Check if current watchMenuId points to a valid menu
      const currentMenuExists = formattedMenuList.find(
        (menu) => menu._id === watchMenuId,
      );

      if (!currentMenuExists) {
        // Current menuId is invalid, recalculate correct one
        let correctMenuId = '';

        // Try URL parameters first
        if (query?.duration && query?.meetingType) {
          const currentDuration = Number(query?.duration);
          if (!Number.isNaN(currentDuration)) {
            const urlMenu = formattedMenuList.find(
              (menu) =>
                menu.meetingType === query?.meetingType &&
                menu?.unitPrices?.[0]?.duration === currentDuration,
            );
            if (urlMenu?._id) {
              correctMenuId = urlMenu._id;
            }
          }
        }

        // Fallback to default
        if (!correctMenuId) {
          const defaultMenuId = Helper.getDefaultMenuId(formattedMenuList);
          if (defaultMenuId) {
            correctMenuId = defaultMenuId;
          }
        }

        // Update form if we found a better menu
        if (correctMenuId && correctMenuId !== watchMenuId) {
          setValue('menuId', correctMenuId);
        }
      }
    }
  }, [
    formattedMenuList,
    watchMenuId,
    query?.duration,
    query?.meetingType,
    setValue,
  ]);

  const listRadio = OutlineMenu(formattedMenuList);
  const calendar = calendarDetails.slots || [];

  return (
    <Box>
      <form style={{ paddingTop: 4 }}>
        {/* Warning message for invalid duration */}
        {isInvalidDuration() && (
          <Grid container columnSpacing={4} columns={13} sx={{ mb: 3 }}>
            {/* Empty space for label alignment */}
            <Grid item xs={0} tablet={3} />
            {/* Warning text aligned with radio options */}
            <Grid item xs={13} tablet={10}>
              <Typography
                sx={{
                  fontSize: { xs: 11, tablet: 13 },
                  fontWeight: 300,
                  color: 'text.secondary',
                  textAlign: 'left',
                }}
              >
                ご指定のカウンセラーへのご相談が初めての方は、夫婦カウンセリングは90分からご利用いただけます
              </Typography>
            </Grid>
          </Grid>
        )}

        <Radio
          labelCol={3}
          columns={13}
          name="menuId"
          label="料金表"
          placeholder="料金表"
          data={listRadio}
          control={control}
          type="outline"
          onClick={() => {
            setValue('expectDate', undefined);
          }}
        />

        <Divider sx={{ my: { xs: 2, tablet: 3 } }} />

        <Typography
          color="heading"
          fontWeight="bold"
          fontSize={{ xs: 14, tablet: 16 }}
        >
          {t('calendar.title')}
        </Typography>
        <Typography
          color="hint"
          fontSize={{ xs: 12, tablet: 14 }}
          mt={{ xs: '4px', tablet: 1 }}
          mb={2}
        >
          {t('calendar.description')}
        </Typography>
        <Box
          px={3}
          py={4}
          style={{
            border: '1px solid #D2DCE1',
          }}
          borderRadius={2}
        >
          {!isFetching && (
            <TimeTableFieldWithoutDialog
              name="expectDate"
              control={control}
              required
              duration={duration as number}
              startTime={startTime}
              onNavigate={(date) => {
                setStartTime(date);
              }}
              onChangeValue={() => {
                setState({
                  ...watch(),
                  startSlot: watch()?.expectDate,
                  duration,
                  meetingType,
                });
              }}
              data={calendar}
              loading={true}
              disabledFutureMinutes={60}
            />
          )}
        </Box>
      </form>
    </Box>
  );
};
export default Calendar;

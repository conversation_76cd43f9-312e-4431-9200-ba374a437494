import type { SxProps, Theme } from '@mui/material/styles';

const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    flex: '1 0',
    mt: { xs: 1, tablet: 2 },
    minHeight: 96,
  },
  boxSlot: {
    flex: '1 0',
    height: 48,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  currentDateHeading: {
    width: { xs: 20, tablet: 24 },
    height: { xs: 20, tablet: 24 },
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  slotOpen: {
    border: 'solid 1.5px #FCAB28',
    height: { xs: 13.3, tablet: 16 },
    width: { xs: 13.3, tablet: 16 },
    borderRadius: '100%',
  },
} as Record<string, SxProps<Theme>>;

export default styles;

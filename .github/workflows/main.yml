name: build and deploy
on:
  push:
    branches:
      - develop
      - uat
      - master
jobs:
  call_workflow:
    uses: c2c-techhub/cicd/.github/workflows/auto-deploy.yml@main
    with:
      environment_for_build: ${{ github.ref == 'refs/heads/master' && 'buildproduction' || github.ref == 'refs/heads/uat' && 'builduat' || 'builddevelopment' }}
      environment_for_deploy: ${{ github.ref == 'refs/heads/master' && 'production' || github.ref == 'refs/heads/uat' && 'uat' || 'development' }}
      enable_auto_deploy: true
      enable_auto_build: true
      bash_command_before_build: |
        env > .env.local
    secrets: inherit
FROM node:16-alpine AS builder
WORKDIR /src
COPY package.json yarn.lock ./
RUN npm pkg set scripts.prepare=":" && yarn install   # Override prepare script by null command to ignore husky installation
COPY . .

TODO: copy .env to working directory!

ENV NEXT_PUBLIC_API_SERVER_BASE_URL=https://development-lawyer-api.eks.csp.scrum-dev.com
ENV NEXT_PUBLIC_DEPLOY_ENV=development
ENV NEXT_PUBLIC_IMAGE_DOMAIN=localhost,development-lawyer-api-data.s3.amazonaws.com,i3.ytimg.com,development-lawyer-api-data.s3.ap-northeast-1.amazonaws.com,uat-lawyer-api-data.s3.amazonaws.com,uat-lawyer-api-data.s3.ap-northeast-1.amazonaws.com,production-lawyer-api-data.s3.ap-northeast-1.amazonaws.com,www.businessnetworks.com
ENV VERITRANS_API_URL=https://api.veritrans.co.jp
ENV VERITRANS_API_KEY=cd76ca65-7f54-4dec-8ba3-11c12e36a548
ENV HOME_EXTERNAL_LINK=""
ENV FIREBASE_API_KEY=AIzaSyCkFedCMC3b-Eezxspjwwin0iFYJ3yCx-4
ENV FIREBASE_AUTH_DOMAIN=lawyer-dev-40675.firebaseapp.com
ENV FIREBASE_PROJECT_ID=lawyer-dev-40675
ENV FIREBASE_STORAGE_BUCKET=lawyer-dev-40675.appspot.com
ENV FIREBASE_MESSAGING_SENDER_ID=***********
ENV FIREBASE_APP_ID=1:1:***********:web:e86a59e4e3e1a23fe67d48
ENV FIREBASE_MEASUREMENT_ID=G-H98X81Y66B
ENV OPERATOR_PHONE=070-4075-4201
ENV OPERATOR_LINE=https://line.me
ENV BANK_SERVICE_URL=https://development-nestjs-bank-module.eks.csp.scrum-dev.com

RUN yarn build

# Production image, copy all the files and run next
FROM node:16-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN apk update && apk add --no-cache bash gettext nginx supervisor

RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

COPY package.json ./
COPY --from=builder /src/public ./public
COPY --from=builder --chown=nextjs:nodejs /src/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /src/.next/static ./.next/static
COPY config config
COPY scripts scripts

# set HOSTNAME to localhost to fix deploy issue
ENV HOSTNAME=0.0.0.0

ENV PORT=5050
EXPOSE 5000
CMD ["./scripts/entrypoint.sh"]

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});
const withPlugins = require('next-compose-plugins');

const nextConfig = {
  output: 'standalone',
  eslint: {
    dirs: ['.'],
  },
  poweredByHeader: false,
  // The starter code load resources from `public` folder with `router.basePath` in React components.
  // So, the source code is "basePath-ready".
  // You can remove `basePath` if you don't need it.
  reactStrictMode: false,
  swcMinify: true,
  pageExtensions: ['page.tsx', 'page.ts'],
  modularizeImports: {
    '@mui/material': {
      transform: '@mui/material/{{member}}',
    },
    lodash: {
      transform: 'lodash/{{member}}',
    },
    hooks: {
      transform: 'hooks/{{member}}',
    },
    // 'components/Form': {
    //   transform: 'components/Form/{{member}}',
    // },
  },
  experimental: {
    scrollRestoration: true,
  },
  images: {
    domains: (process.env.NEXT_PUBLIC_IMAGE_DOMAIN || '').split(','),
    minimumCacheTTL: 3600,
    deviceSizes: [828, 1080, 1200, 1920],
    imageSizes: [48, 64, 96, 128],
  },
  i18n: {
    defaultLocale: 'ja',
    locales: ['ja', 'en'],
    localeDetection: false,
  },
  async rewrites() {
    return [
      {
        source: '/health',
        destination: '/api',
      },
      { source: "/login", destination: "/" },
    ]
  },
  env: {
    NEXT_PUBLIC_API_SERVER_BASE_URL: process.env.NEXT_PUBLIC_API_SERVER_BASE_URL,
    NEXT_PUBLIC_DEPLOY_ENV: process.env.NEXT_PUBLIC_DEPLOY_ENV,
    NEXT_PUBLIC_DOMAIN: process.env.NEXT_PUBLIC_DOMAIN,
    NEXT_PUBLIC_IMAGE_DOMAIN: process.env.NEXT_PUBLIC_IMAGE_DOMAIN,
    VERITRANS_API_URL: process.env.VERITRANS_API_URL,
    VERITRANS_API_KEY: process.env.VERITRANS_API_KEY,
    MAX_YEAR_CREDIT_CARD: process.env.MAX_YEAR_CREDIT_CARD,
    HOME_REVALIDATE: process.env.HOME_REVALIDATE,
    HOME_EXTERNAL_LINK: process.env.HOME_EXTERNAL_LINK,
    // New env
    FIREBASE_API_KEY: process.env.FIREBASE_API_KEY,
    FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
    FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
    FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
    FIREBASE_MESSAGING_SENDER_ID: process.env.FIREBASE_MESSAGING_SENDER_ID,
    FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
    FIREBASE_MEASUREMENT_ID: process.env.FIREBASE_MEASUREMENT_ID,
    OPERATOR_PHONE: process.env.OPERATOR_PHONE,
    OPERATOR_LINE: process.env.OPERATOR_LINE,
    SHOW_OPERATOR_CONTACT: process.env.SHOW_OPERATOR_CONTACT,
    BANK_SERVICE_URL: process.env.BANK_SERVICE_URL
  },
  webpack(config) {
    const fileLoaderRule = config.module.rules.find(
      (rule) => rule.test && typeof rule.test.test === 'function' && rule.test.test('.svg'),
    );
    if (fileLoaderRule) {
      // Exclude SVG from the default file-loader
      fileLoaderRule.exclude = /\.svg$/i;
    }
    config.module.rules.push({
      test: /\.svg$/,
      loader: require.resolve('@svgr/webpack'),
      options: {
        svgoConfig: {
          plugins: [{
            name: 'removeViewBox',
            active: false
          }]
        }
      }
    });
    return config;
  },
};

module.exports = withBundleAnalyzer(nextConfig);

{"name": "lawyer-web", "version": "1.0.0", "author": "<EMAIL>", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "export": "next export", "build-prod": "run-s clean build export", "clean": "rimraf .next out", "check-types": "tsc --noEmit --pretty", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest --watch", "prepare": "husky install"}, "lint-staged": {"*.{js,jsx,.ts,.tsx}": ["npm run lint:fix"]}, "dependencies": {"@cyntler/react-doc-viewer": "1.12.0", "@emotion/cache": "11.10.5", "@emotion/react": "11.10.5", "@emotion/server": "11.10.0", "@emotion/styled": "11.10.5", "@hookform/resolvers": "2.9.10", "@mui/icons-material": "5.11.0", "@mui/lab": "5.0.0-alpha.114", "@mui/material": "5.11.3", "@mui/styles": "5.11.2", "@mui/x-date-pickers": "5.0.16", "@next/font": "14.2.15", "@svgr/webpack": "6.5.1", "@tanstack/query-broadcast-client-experimental": "4.20.4", "@tanstack/react-query": "4.20.4", "@tanstack/react-query-devtools": "4.20.4", "@types/nprogress": "0.2.0", "axios": "1.2.2", "card-validator": "8.1.1", "clsx": "1.2.1", "cookies-next": "2.1.1", "dayjs": "1.11.7", "embla-carousel-react": "7.0.5", "firebase": "^10.8.1", "framer-motion": "8.1.3", "i18next": "22.4.6", "linkify-react": "^4.1.3", "linkifyjs": "^4.1.3", "lodash": "4.17.21", "material-react-table": "1.5.4", "next": "13.5.11", "next-compose-plugins": "2.2.1", "next-i18next": "13.0.2", "next-seo": "5.15.0", "nprogress": "0.2.0", "react": "18.2.0", "react-avatar-editor": "13.0.0", "react-device-detect": "^2.2.3", "react-dom": "18.2.0", "react-ga4": "^2.1.0", "react-hook-form": "7.41.3", "react-i18next": "12.1.1", "react-intersection-observer": "9.4.1", "react-number-format": "5.1.2", "react-player": "^2.15.1", "react-scroll": "1.8.9", "react-show-more-text": "^1.7.1", "react-slick": "0.29.0", "react-toastify": "9.1.1", "react-truncate-markup": "5.1.2", "sharp": "0.31.3", "slick-carousel": "1.8.1", "yup": "0.32.11", "zustand": "4.2.0"}, "devDependencies": {"@commitlint/cli": "17.3.0", "@commitlint/config-conventional": "17.3.0", "@next/bundle-analyzer": "13.1.1", "@types/lodash": "4.14.191", "@types/node": "18.11.18", "@types/react": "18.0.26", "@types/react-avatar-editor": "13.0.0", "@types/react-scroll": "1.8.5", "@types/react-show-more-text": "^1.4.5", "@types/react-slick": "0.23.10", "@typescript-eslint/eslint-plugin": "5.47.1", "@typescript-eslint/parser": "5.47.1", "cross-env": "7.0.3", "eslint": "8.31.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "17.0.0", "eslint-config-next": "13.0.1", "eslint-config-prettier": "8.6.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsx-a11y": "6.6.1", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.31.11", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-simple-import-sort": "8.0.0", "eslint-plugin-unused-imports": "2.0.0", "husky": "8.0.2", "lint-staged": "13.1.0", "prettier": "2.8.1", "typescript": "4.9.4"}}
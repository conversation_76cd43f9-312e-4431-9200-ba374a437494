# #!/bin/bash -e
# apk add -q curl 2>/dev/null || true
# check_cms() {
#   TIMEOUT=10
#   RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -m 10 http://localhost:5000/health)
#   if [ $RESPONSE -ge 200 ] && [ $RESPONSE -lt 300 ]; then
#     return 0
#   fi
#   if [ $? -ne 0 ]; then
#     return -1
#   fi
#   return -1
# }
# check_health() {
#   TIMEOUT=10
#   RESPONSE=$(curl -o /dev/null -s -w "%{http_code}\n" $NGINX_VAR_CMS_BASE_URL -m 10)
#   if [ $RESPONSE -ge 200 ] && [ $RESPONSE -lt 300 ]; then
#     return 0
#   fi
#   if [ $? -ne 0 ]; then
#     return -1
#   fi
#   return -1
# }
# check_cms && check_health
# exit $?

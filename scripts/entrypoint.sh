#!/bin/bash -e
NGINX_VAR_CMS_HTPASSWD=${NGINX_VAR_CMS_HTPASSWD:-""}
NGINX_VAR_CMS_X_FORWARDED_TOKEN=${NGINX_VAR_CMS_X_FORWARDED_TOKEN:-""}
NGINX_VAR_CMS_BASE_URL=${NGINX_VAR_CMS_BASE_URL:-""}
NGINX_VAR_SITEMAP_BASE_URL=${NGINX_VAR_SITEMAP_BASE_URL:-""}

export NGINX_VAR_CLIENT_MAX_BODY_SIZE=${NGINX_VAR_CLIENT_MAX_BODY_SIZE:-"10m"}
export NGINX_VAR_CLIENT_BODY_BUFFER_SIZE=${NGINX_VAR_CLIENT_BODY_BUFFER_SIZE:-"128k"}

export NGINX_VAR_CLIENT_BODY_TEMP_PATH=${NGINX_VAR_CLIENT_BODY_TEMP_PATH:-"/var/nginx/client_body_temp"}

export NGINX_VAR_PROXY_CONNECT_TIMEOUT=${NGINX_VAR_PROXY_CONNECT_TIMEOUT:-"90"}
export NGINX_VAR_PROXY_SEND_TIMEOUT=${NGINX_VAR_PROXY_SEND_TIMEOUT:-"90"}
export NGINX_VAR_PROXY_READ_TIMEOUT=${NGINX_VAR_PROXY_READ_TIMEOUT:-"90"}

export NGINX_VAR_PROXY_BUFFER_SIZE=${NGINX_VAR_PROXY_BUFFER_SIZE:-"4k"}
export NGINX_VAR_PROXY_BUFFERS=${NGINX_VAR_PROXY_BUFFERS:-"4 32k"}
export NGINX_VAR_PROXY_BUSY_BUFFERS_SIZE=${NGINX_VAR_PROXY_BUSY_BUFFERS_SIZE:-"64k"}
export NGINX_VAR_PROXY_TEMP_FILE_WRITE_SIZE=${NGINX_VAR_PROXY_TEMP_FILE_WRITE_SIZE:-"64k"}

export NGINX_VAR_PROXY_TEMP_PATH=${NGINX_VAR_PROXY_TEMP_PATH:-"/var/nginx/proxy_temp"}

: "${NGINX_VAR_CMS_HTPASSWD?Need to set NGINX_VAR_CMS_HTPASSWD}"
: "${NGINX_VAR_CMS_X_FORWARDED_TOKEN:?Need to set NGINX_VAR_CMS_X_FORWARDED_TOKEN non-empty}"
: "${NGINX_VAR_CMS_BASE_URL:?Need to set NGINX_VAR_CMS_BASE_URL non-empty}"
: "${NGINX_VAR_SITEMAP_BASE_URL:?Need to set NGINX_VAR_SITEMAP_BASE_URL non-empty}"

mkdir -p "$NGINX_VAR_CLIENT_BODY_TEMP_PATH"
mkdir -p "$NGINX_VAR_PROXY_TEMP_PATH"

if [ "$NGINX_VAR_BASIC_AUTH_ENABLE" = "true" ]; then
    sed -i '/auth_basic/s/^#//g' config/nginx/app.template.conf
    echo "$NGINX_VAR_CMS_HTPASSWD" > /etc/nginx/.htpasswd
else
    sed -i '/auth_basic/s/^/#/g' config/nginx/app.template.conf
fi

envsubst > /etc/nginx/http.d/default.conf < config/nginx/app.template.conf
/usr/bin/supervisord -c config/supervisor/supervisord.conf